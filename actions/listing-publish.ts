"use server";

import { getUserAttributes } from "@/lib/serverUserAttributes";
import { db } from "@/db";
import { eq, and, desc } from "drizzle-orm";
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";

export async function toggleListingPublishStatus(listingId: number) {
  try {
    const userAttributes = await getUserAttributes();
    const partyId = userAttributes?.["custom:db_id"];

    if (!partyId) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    // Verify the listing belongs to the user
    const listing = await db
      .select({
        id: h_listings.id,
        partyId: h_listings.partyId,
      })
      .from(h_listings)
      .where(eq(h_listings.id, listingId))
      .limit(1);

    if (!listing.length || listing[0].partyId !== parseInt(partyId)) {
      return {
        success: false,
        error: "Listing not found or unauthorized",
      };
    }

    // Check if listing is approved
    const approvalStatus = await db
      .select({
        status: h_listing_approval_status.status,
      })
      .from(h_listing_approval_status)
      .where(eq(h_listing_approval_status.listingId, listingId))
      .orderBy(desc(h_listing_approval_status.statusAt))
      .limit(1);

    if (!approvalStatus.length || approvalStatus[0].status !== "approved") {
      return {
        success: false,
        error: "Listing must be approved before publishing",
      };
    }

    // Get current publish status
    const currentPublishStatus = await db
      .select({
        status: h_listing_publish_status.status,
      })
      .from(h_listing_publish_status)
      .where(eq(h_listing_publish_status.listingId, listingId))
      .orderBy(desc(h_listing_publish_status.statusAt))
      .limit(1);

    // Determine new status
    let newStatus: "published" | "archived" | "pending";
    if (
      !currentPublishStatus.length ||
      currentPublishStatus[0].status === "pending"
    ) {
      newStatus = "published";
    } else if (currentPublishStatus[0].status === "published") {
      newStatus = "archived";
    } else {
      newStatus = "published";
    }

    // Insert new publish status record
    await db.insert(h_listing_publish_status).values({
      listingId: listingId,
      status: newStatus,
      statusAt: new Date().toISOString(),
      statusBy: parseInt(partyId),
    });

    return {
      success: true,
      newStatus: newStatus,
      message:
        newStatus === "published"
          ? "Listing published successfully"
          : "Listing unpublished successfully",
    };
  } catch (error) {
    console.error("Error toggling listing publish status:", error);
    return {
      success: false,
      error: "Failed to update listing status",
    };
  }
}

export async function createInitialPublishStatus(
  listingId: number,
  statusBy: number
) {
  try {
    // Check if publish status already exists
    const existingStatus = await db
      .select({ id: h_listing_publish_status.id })
      .from(h_listing_publish_status)
      .where(eq(h_listing_publish_status.listingId, listingId))
      .limit(1);

    if (existingStatus.length > 0) {
      return {
        success: true,
        message: "Publish status already exists",
      };
    }

    // Create initial publish status as "pending"
    await db.insert(h_listing_publish_status).values({
      listingId: listingId,
      status: "pending",
      statusAt: new Date().toISOString(),
      statusBy: statusBy,
    });

    return {
      success: true,
      message: "Initial publish status created",
    };
  } catch (error) {
    console.error("Error creating initial publish status:", error);
    return {
      success: false,
      error: "Failed to create initial publish status",
    };
  }
}
