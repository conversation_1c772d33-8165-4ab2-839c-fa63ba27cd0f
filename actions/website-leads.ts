'use server';

import { getFormSubmissions, getFormSubmissionStats, type FormSubmission, type FormType } from '@/db/queries';

export interface PaginatedFormSubmissions {
  submissions: FormSubmission[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface FormStats {
  formType: FormType;
  count: number;
  emailsSent: number;
}

export async function getFormSubmissionsPaginated(
  page: number = 1,
  limit: number = 20,
  formType?: FormType | 'all'
): Promise<PaginatedFormSubmissions> {
  const offset = (page - 1) * limit;
  
  // Get submissions and stats in parallel
  const [submissions, stats] = await Promise.all([
    getFormSubmissions({ 
      limit, 
      offset, 
      formType: formType !== 'all' ? formType as FormType : undefined 
    }),
    getFormSubmissionStats()
  ]);

  // Calculate total count based on filter
  const totalCount = formType && formType !== 'all' 
    ? stats.find(stat => stat.formType === formType)?.count || 0
    : stats.reduce((sum, stat) => sum + stat.count, 0);

  const totalPages = Math.ceil(totalCount / limit);

  return {
    submissions,
    totalCount,
    currentPage: page,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}

export async function getFormSubmissionStatsAction(): Promise<FormStats[]> {
  const stats = await getFormSubmissionStats();
  return stats as FormStats[];
}

export interface TimeSeriesData {
  date: string;
  label: string;
  business: number;
  'co-own': number;
  management: number;
  rideshare: number;
  monetise: number;
  waitlist: number;
  total: number;
}

export async function getLeadTimeSeriesData(
  period: 'day' | 'month' | 'year' = 'day',
  days: number = 30
): Promise<TimeSeriesData[]> {
  const endDate = new Date();
  const startDate = new Date();
  
  // Set the start date based on period
  switch (period) {
    case 'day':
      startDate.setDate(startDate.getDate() - days);
      break;
    case 'month':
      startDate.setMonth(startDate.getMonth() - 12); // Last 12 months
      break;
    case 'year':
      startDate.setFullYear(startDate.getFullYear() - 5); // Last 5 years
      break;
  }

  const submissions = await getFormSubmissions({ 
    limit: 5000 // Get more data for time series analysis
  });

  // Filter submissions within date range
  const filteredSubmissions = submissions.filter(s => 
    new Date(s.submittedAt) >= startDate && new Date(s.submittedAt) <= endDate
  );

  // Group by time period and form type
  const groupedData: Record<string, Record<FormType, number>> = {};

  filteredSubmissions.forEach(submission => {
    const date = new Date(submission.submittedAt);
    let key: string;

    switch (period) {
      case 'day':
        key = date.toISOString().split('T')[0]; // YYYY-MM-DD
        break;
      case 'month':
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
        break;
      case 'year':
        key = String(date.getFullYear());
        break;
      default:
        key = date.toISOString().split('T')[0];
    }

    if (!groupedData[key]) {
      groupedData[key] = {
        business: 0,
        'co-own': 0,
        management: 0,
        rideshare: 0,
        monetise: 0,
        waitlist: 0
      };
    }

    groupedData[key][submission.formType as FormType] += 1;
  });

  // Generate complete time series with zero counts for missing periods
  const result: TimeSeriesData[] = [];
  const current = new Date(startDate);
  
  while (current <= endDate) {
    let key: string;
    let label: string;

    switch (period) {
      case 'day':
        key = current.toISOString().split('T')[0];
        label = current.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        current.setDate(current.getDate() + 1);
        break;
      case 'month':
        key = `${current.getFullYear()}-${String(current.getMonth() + 1).padStart(2, '0')}`;
        label = current.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });
        current.setMonth(current.getMonth() + 1);
        break;
      case 'year':
        key = String(current.getFullYear());
        label = String(current.getFullYear());
        current.setFullYear(current.getFullYear() + 1);
        break;
    }

    const dayData = groupedData[key] || {
      business: 0,
      'co-own': 0,
      management: 0,
      rideshare: 0,
      monetise: 0,
      waitlist: 0
    };

    const total = Object.values(dayData).reduce((sum, count) => sum + count, 0);

    result.push({
      date: key,
      label,
      ...dayData,
      total
    });
  }

  return result.sort((a, b) => a.date.localeCompare(b.date));
} 