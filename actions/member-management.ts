"use server";

import { db } from "@/db";
import { eq, and, or, sql } from "drizzle-orm";
import {
  groupMemberships,
  groupMemberRoles,
  groupMembershipInvitations,
  groups,
  party,
  individual,
  contactPoint,
  tasks
} from "@/drizzle/schema";
import { GroupRoleEnum, InvitationStatusEnum, GroupMembershipInvitationCreate } from "@/types/groups";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { createGroupInvitationTask } from "@/actions/tasks";

/**
 * Check if the current user has admin role in the specified group
 */
export async function checkUserAdminRole(groupId: number): Promise<boolean> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      return false;
    }

    // Check if user has admin role in this group
    const adminCheck = await db
      .select({
        role: groupMemberRoles.role
      })
      .from(groupMemberRoles)
      .innerJoin(groupMemberships, 
        and(
          eq(groupMemberRoles.groupId, groupMemberships.groupId),
          eq(groupMemberRoles.partyId, groupMemberships.partyId)
        )
      )
      .where(
        and(
          eq(groupMemberRoles.groupId, groupId),
          eq(groupMemberRoles.partyId, partyId),
          eq(groupMemberRoles.role, GroupRoleEnum.ADMIN),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          ),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      );

    return adminCheck.length > 0;
  } catch (error) {
    console.error("Error checking user admin role:", error);
    return false;
  }
}

/**
 * Get all members of a group with their details
 */
export async function getGroupMembers(groupId: number) {
  try {
    const members = await db
      .select({
        id: individual.id,
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        role: groupMemberRoles.role,
        joinedAt: groupMemberships.effectiveFrom,
        isActive: sql<boolean>`${groupMemberships.effectiveTo} IS NULL OR ${groupMemberships.effectiveTo} > NOW()`,
      })
      .from(groupMemberships)
      .innerJoin(party, eq(groupMemberships.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(
        groupMemberRoles,
        and(
          eq(groupMemberRoles.groupId, groupMemberships.groupId),
          eq(groupMemberRoles.partyId, groupMemberships.partyId),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      )
      .where(
        and(
          eq(groupMemberships.groupId, groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .orderBy(individual.firstName, individual.lastName);

    return members.map(member => ({
      id: member.id,
      partyId: member.partyId,
      firstName: member.firstName,
      lastName: member.lastName,
      name: `${member.firstName} ${member.lastName}`,
      role: member.role || GroupRoleEnum.MEMBER,
      joinedAt: member.joinedAt || new Date().toISOString(),
      isActive: member.isActive
    }));
  } catch (error) {
    console.error("Error fetching group members:", error);
    return [];
  }
}

/**
 * Get contact points for members
 */
export async function getMembersContactPoints(partyIds: number[]) {
  try {
    if (partyIds.length === 0) return [];

    const contacts = await db
      .select({
        partyId: contactPoint.partyId,
        contactValue: contactPoint.value,
        isPrimary: contactPoint.isPrimary,
        contactPointTypeId: contactPoint.contactPointTypeId
      })
      .from(contactPoint)
      .where(sql`${contactPoint.partyId} = ANY(${partyIds})`)
      .orderBy(contactPoint.partyId, contactPoint.isPrimary);

    return contacts;
  } catch (error) {
    console.error("Error fetching member contact points:", error);
    return [];
  }
}

/**
 * Get pending invitations for a group
 */
export async function getGroupPendingInvitations(groupId: number) {
  try {
    const invitations = await db
      .select({
        id: groupMembershipInvitations.id,
        email: groupMembershipInvitations.email,
        firstName: groupMembershipInvitations.firstName,
        lastName: groupMembershipInvitations.lastName,
        role: groupMembershipInvitations.role,
        status: groupMembershipInvitations.status,
        createdAt: groupMembershipInvitations.createdAt,
        expiresAt: groupMembershipInvitations.expiresAt,
        invitedBy: groupMembershipInvitations.invitedBy,
        inviterFirstName: individual.firstName,
        inviterLastName: individual.lastName,
      })
      .from(groupMembershipInvitations)
      .leftJoin(party, eq(groupMembershipInvitations.invitedBy, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(groupMembershipInvitations.groupId, groupId),
          or(
            eq(groupMembershipInvitations.status, InvitationStatusEnum.SENT),
            eq(groupMembershipInvitations.status, InvitationStatusEnum.PENDING)
          )
        )
      )
      .orderBy(groupMembershipInvitations.createdAt);

    return invitations.map(invitation => ({
      id: invitation.id,
      email: invitation.email,
      firstName: invitation.firstName,
      lastName: invitation.lastName,
      name: `${invitation.firstName} ${invitation.lastName}`,
      role: invitation.role,
      status: invitation.status,
      sentAt: invitation.createdAt,
      expiresAt: invitation.expiresAt,
      invitedBy: invitation.inviterFirstName && invitation.inviterLastName
        ? `${invitation.inviterFirstName} ${invitation.inviterLastName}`
        : "Unknown"
    }));
  } catch (error) {
    console.error("Error fetching group pending invitations:", error);
    return [];
  }
}

/**
 * Create a new group membership invitation
 */
export async function createGroupInvitation(
  groupId: number,
  invitationData: GroupMembershipInvitationCreate
) {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      return {
        success: false,
        error: "User not authenticated"
      };
    }

    // Check if user has admin role
    const isAdmin = await checkUserAdminRole(groupId);
    if (!isAdmin) {
      return {
        success: false,
        error: "Insufficient permissions. Admin role required."
      };
    }

    // Get group information
    const [group] = await db
      .select({
        id: groups.id,
        name: groups.name
      })
      .from(groups)
      .where(eq(groups.id, groupId));

    if (!group) {
      return {
        success: false,
        error: "Group not found"
      };
    }

    // Check if invitation already exists for this email
    const existingInvitation = await db
      .select({ id: groupMembershipInvitations.id })
      .from(groupMembershipInvitations)
      .where(
        and(
          eq(groupMembershipInvitations.groupId, groupId),
          eq(groupMembershipInvitations.email, invitationData.email),
          or(
            eq(groupMembershipInvitations.status, InvitationStatusEnum.SENT),
            eq(groupMembershipInvitations.status, InvitationStatusEnum.PENDING)
          )
        )
      );

    if (existingInvitation.length > 0) {
      return {
        success: false,
        error: "An invitation for this email already exists"
      };
    }

    // Create the invitation
    const invitationToInsert = {
      groupId: groupId,
      firstName: invitationData.firstName,
      lastName: invitationData.lastName,
      email: invitationData.email,
      role: invitationData.role,
      invitedBy: partyId,
      invitationToken: crypto.randomUUID(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      status: InvitationStatusEnum.SENT
    };

    const [invitation] = await db
      .insert(groupMembershipInvitations)
      .values(invitationToInsert)
      .returning();

    // Create a task for the invitation
    const inviterName = userAttributes?.given_name && userAttributes?.family_name 
      ? `${userAttributes.given_name} ${userAttributes.family_name}`.trim()
      : userAttributes?.email || 'Someone';

    try {
      await createGroupInvitationTask(
        invitation.email,
        invitation.id,
        groupId,
        group.name,
        inviterName,
        invitation.role
      );
    } catch (error) {
      console.error("Failed to create task for invitation:", error);
      // Don't fail the whole operation for task creation errors
    }

    return {
      success: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        firstName: invitation.firstName,
        lastName: invitation.lastName,
        name: `${invitation.firstName} ${invitation.lastName}`,
        role: invitation.role,
        status: invitation.status,
        sentAt: invitation.createdAt,
        expiresAt: invitation.expiresAt
      }
    };
  } catch (error) {
    console.error("Error creating group invitation:", error);
    return {
      success: false,
      error: "Failed to create invitation"
    };
  }
}

/**
 * Cancel a group membership invitation
 */
export async function cancelGroupInvitation(invitationId: number) {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      return {
        success: false,
        error: "User not authenticated"
      };
    }

    // Get invitation details first
    const [invitation] = await db
      .select({
        id: groupMembershipInvitations.id,
        groupId: groupMembershipInvitations.groupId,
        status: groupMembershipInvitations.status
      })
      .from(groupMembershipInvitations)
      .where(eq(groupMembershipInvitations.id, invitationId));

    if (!invitation) {
      return {
        success: false,
        error: "Invitation not found"
      };
    }

    // Check if user has admin role
    const isAdmin = await checkUserAdminRole(invitation.groupId);
    if (!isAdmin) {
      return {
        success: false,
        error: "Insufficient permissions. Admin role required."
      };
    }

    // Only allow cancellation of pending/sent invitations
    if (![InvitationStatusEnum.SENT, InvitationStatusEnum.PENDING].includes(invitation.status)) {
      return {
        success: false,
        error: "Can only cancel pending invitations"
      };
    }

    // Update invitation status to cancelled
    await db
      .update(groupMembershipInvitations)
      .set({
        status: InvitationStatusEnum.CANCELLED,
        updatedAt: new Date().toISOString()
      })
      .where(eq(groupMembershipInvitations.id, invitationId));

    // Update related task status to DISMISSED
    try {
      await db
        .update(tasks)
        .set({
          status: 'DISMISSED',
          updatedAt: new Date().toISOString()
        })
        .where(
          and(
            eq(tasks.type, 'GROUP_INVITATION'),
            eq(tasks.relatedEntityId, invitationId),
            or(
              eq(tasks.status, 'PENDING'),
              eq(tasks.status, 'IN_PROGRESS')
            )
          )
        );
    } catch (error) {
      console.error("Failed to update task status:", error);
      // Don't fail the whole operation for task update errors
    }

    return {
      success: true,
      message: "Invitation cancelled successfully"
    };
  } catch (error) {
    console.error("Error cancelling invitation:", error);
    return {
      success: false,
      error: "Failed to cancel invitation"
    };
  }
}

/**
 * Get group member details with admin check
 */
export async function getGroupMemberManagementData(groupId: number) {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      return {
        success: false,
        error: "User not authenticated",
        isAdmin: false,
        members: [],
        invitations: []
      };
    }

    // Check if user has admin role
    const isAdmin = await checkUserAdminRole(groupId);
    
    if (!isAdmin) {
      return {
        success: false,
        error: "Access denied. Admin permissions required.",
        isAdmin: false,
        members: [],
        invitations: []
      };
    }

    // Get members and invitations in parallel
    const [members, invitations] = await Promise.all([
      getGroupMembers(groupId),
      getGroupPendingInvitations(groupId)
    ]);

    // Get contact points for all members
    const memberPartyIds = members.map(m => m.partyId);
    const contacts = await getMembersContactPoints(memberPartyIds);

    return {
      success: true,
      isAdmin: true,
      members: members.map(member => ({
        ...member,
        contacts: contacts.filter(c => c.partyId === member.partyId)
      })),
      invitations
    };
  } catch (error) {
    console.error("Error getting group member management data:", error);
    return {
      success: false,
      error: "Failed to load member management data",
      isAdmin: false,
      members: [],
      invitations: []
    };
  }
} 