import { db } from '@/db';
import { pushSubscriptions, notificationLogs } from '@/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import { webpush, type PoollyNotificationPayload, type PushSubscriptionData, NOTIFICATION_DEFAULTS } from '@/lib/webpush';

export async function saveSubscription(
  userId: string,
  partyId: number | undefined,
  subscription: PushSubscriptionData,
  userAgent?: string,
  deviceInfo?: any
) {
  try {
    const result = await db.insert(pushSubscriptions).values({
      userId,
      partyId,
      endpoint: subscription.endpoint,
      p256dhKey: subscription.keys.p256dh,
      authKey: subscription.keys.auth,
      userAgent,
      deviceInfo,
      updatedAt: new Date(),
    }).onConflictDoUpdate({
      target: pushSubscriptions.endpoint,
      set: {
        userId,
        partyId,
        p256dhKey: subscription.keys.p256dh,
        authKey: subscription.keys.auth,
        userAgent,
        deviceInfo,
        isActive: true,
        updatedAt: new Date(),
      }
    }).returning();
    
    return { success: true, subscription: result[0] };
  } catch (error) {
    console.error('Error saving subscription:', error);
    return { success: false, error: 'Failed to save subscription' };
  }
}

export async function getUserSubscriptions(userId: string) {
  try {
    return await db.select()
      .from(pushSubscriptions)
      .where(and(
        eq(pushSubscriptions.userId, userId),
        eq(pushSubscriptions.isActive, true)
      ));
  } catch (error) {
    console.error('Error getting user subscriptions:', error);
    return [];
  }
}

export async function getGroupMemberSubscriptions(userIds: string[]) {
  try {
    if (userIds.length === 0) return [];
    
    return await db.select()
      .from(pushSubscriptions)
      .where(and(
        inArray(pushSubscriptions.userId, userIds),
        eq(pushSubscriptions.isActive, true)
      ));
  } catch (error) {
    console.error('Error getting group member subscriptions:', error);
    return [];
  }
}

export async function removeSubscription(userId: string, endpoint: string) {
  try {
    await db.update(pushSubscriptions)
      .set({ isActive: false, updatedAt: new Date() })
      .where(and(
        eq(pushSubscriptions.userId, userId),
        eq(pushSubscriptions.endpoint, endpoint)
      ));
    
    return { success: true };
  } catch (error) {
    console.error('Error removing subscription:', error);
    return { success: false, error: 'Failed to remove subscription' };
  }
}

export async function sendNotificationToUser(
  userId: string, 
  payload: PoollyNotificationPayload
) {
  const subscriptions = await getUserSubscriptions(userId);
  
  if (subscriptions.length === 0) {
    console.log(`No active subscriptions for user ${userId}`);
    return [];
  }
  
  const results = await Promise.allSettled(
    subscriptions.map(async (sub) => {
      const pushSubscription: PushSubscriptionData = {
        endpoint: sub.endpoint,
        keys: {
          p256dh: sub.p256dhKey,
          auth: sub.authKey,
        }
      };
      
      let logEntryId: string | undefined;
      
      try {
        // Log notification attempt
        const logEntry = await db.insert(notificationLogs).values({
          userId,
          subscriptionId: sub.id,
          title: payload.title,
          body: payload.body,
          payload: payload as any,
          deliveryStatus: 'pending',
        }).returning();
        
        logEntryId = logEntry[0].id;
        
        // Merge with defaults for this notification type
        const defaults = NOTIFICATION_DEFAULTS[payload.type] || NOTIFICATION_DEFAULTS.general;
        const finalPayload = {
          ...payload,
          icon: payload.icon || defaults.icon,
          badge: payload.badge || defaults.badge,
        };
        
        await webpush.sendNotification(
          pushSubscription,
          JSON.stringify(finalPayload)
        );
        
        // Update log as sent
        await db.update(notificationLogs)
          .set({ deliveryStatus: 'sent' })
          .where(eq(notificationLogs.id, logEntryId));
        
        return { success: true, endpoint: sub.endpoint };
      } catch (error: any) {
        console.error('Push notification failed:', error);
        
        // Update log with error
        if (logEntryId) {
          await db.update(notificationLogs)
            .set({ 
              deliveryStatus: 'failed',
              errorMessage: error.message 
            })
            .where(eq(notificationLogs.id, logEntryId));
        }
        
        // Handle expired subscriptions
        if (error.statusCode === 410 || error.statusCode === 404) {
          await db.update(pushSubscriptions)
            .set({ isActive: false, updatedAt: new Date() })
            .where(eq(pushSubscriptions.endpoint, sub.endpoint));
        }
        
        return { success: false, endpoint: sub.endpoint, error: error.message };
      }
    })
  );
  
  return results;
}

export async function sendNotificationToUsers(
  userIds: string[],
  payload: PoollyNotificationPayload
) {
  const subscriptions = await getGroupMemberSubscriptions(userIds);
  
  if (subscriptions.length === 0) {
    console.log(`No active subscriptions for users ${userIds.join(', ')}`);
    return [];
  }
  
  // Process in batches to avoid overwhelming push services
  const batchSize = 50;
  const results = [];
  
  for (let i = 0; i < subscriptions.length; i += batchSize) {
    const batch = subscriptions.slice(i, i + batchSize);
    const batchResults = await Promise.allSettled(
      batch.map(async (sub) => {
        const pushSubscription: PushSubscriptionData = {
          endpoint: sub.endpoint,
          keys: {
            p256dh: sub.p256dhKey,
            auth: sub.authKey,
          }
        };
        
        let logEntryId: string | undefined;
        
        try {
          // Log notification attempt
          const logEntry = await db.insert(notificationLogs).values({
            userId: sub.userId,
            subscriptionId: sub.id,
            title: payload.title,
            body: payload.body,
            payload: payload as any,
            deliveryStatus: 'pending',
          }).returning();
          
          logEntryId = logEntry[0].id;
          
          // Merge with defaults for this notification type
          const defaults = NOTIFICATION_DEFAULTS[payload.type] || NOTIFICATION_DEFAULTS.general;
          const finalPayload = {
            ...payload,
            icon: payload.icon || defaults.icon,
            badge: payload.badge || defaults.badge,
          };
          
          await webpush.sendNotification(
            pushSubscription,
            JSON.stringify(finalPayload)
          );
          
          // Update log as sent
          await db.update(notificationLogs)
            .set({ deliveryStatus: 'sent' })
            .where(eq(notificationLogs.id, logEntryId));
          
          return { success: true, userId: sub.userId };
        } catch (error: any) {
          console.error('Push notification failed:', error);
          
          // Update log with error
          if (logEntryId) {
            await db.update(notificationLogs)
              .set({ 
                deliveryStatus: 'failed',
                errorMessage: error.message 
              })
              .where(eq(notificationLogs.id, logEntryId));
          }
          
          // Handle expired subscriptions
          if (error.statusCode === 410 || error.statusCode === 404) {
            await db.update(pushSubscriptions)
              .set({ isActive: false, updatedAt: new Date() })
              .where(eq(pushSubscriptions.endpoint, sub.endpoint));
          }
          
          return { success: false, userId: sub.userId, error: error.message };
        }
      })
    );
    results.push(...batchResults);
    
    // Add a small delay between batches
    if (i + batchSize < subscriptions.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  return results;
}

// Poolly-specific notification helpers
export async function sendBookingNotification(
  userId: string,
  bookingId: string,
  type: 'confirmed' | 'reminder' | 'cancelled' | 'started' | 'completed',
  vehicleName: string,
  additionalInfo?: string
) {
  const titles = {
    confirmed: 'Booking Confirmed',
    reminder: 'Upcoming Booking',
    cancelled: 'Booking Cancelled',
    started: 'Booking Started',
    completed: 'Booking Completed'
  };
  
  const bodies = {
    confirmed: `Your booking for ${vehicleName} is confirmed`,
    reminder: `Your booking for ${vehicleName} starts soon`,
    cancelled: `Your booking for ${vehicleName} has been cancelled`,
    started: `Your booking for ${vehicleName} has started`,
    completed: `Your booking for ${vehicleName} is complete`
  };
  
  const payload: PoollyNotificationPayload = {
    title: titles[type],
    body: additionalInfo ? `${bodies[type]}. ${additionalInfo}` : bodies[type],
    type: 'booking',
    url: `/booking-details/${bookingId}`,
    bookingId,
    vehicleId: vehicleName, // Temporary until we have proper vehicle ID
    tag: `booking-${bookingId}`,
    actions: type === 'reminder' ? [
      { action: 'view', title: 'View Details' }
    ] : undefined,
  };
  
  return await sendNotificationToUser(userId, payload);
}

export async function sendGroupInviteNotification(
  userId: string,
  groupName: string,
  inviterName: string,
  groupId: string
) {
  const payload: PoollyNotificationPayload = {
    title: 'Group Invitation',
    body: `${inviterName} invited you to join "${groupName}"`,
    type: 'group',
    url: `/group-details/${groupId}`,
    groupId,
    tag: `group-invite-${groupId}`,
    actions: [
      { action: 'accept', title: 'Accept' },
      { action: 'decline', title: 'Decline' }
    ],
  };
  
  return await sendNotificationToUser(userId, payload);
}

export async function sendMaintenanceNotification(
  userId: string,
  vehicleName: string,
  maintenanceType: string,
  dueDate: string,
  vehicleId: string
) {
  const payload: PoollyNotificationPayload = {
    title: 'Maintenance Required',
    body: `${vehicleName} needs ${maintenanceType} by ${dueDate}`,
    type: 'maintenance',
    url: `/maintenance-schedule`,
    vehicleId,
    tag: `maintenance-${vehicleId}`,
    actions: [
      { action: 'schedule', title: 'Schedule Now' },
      { action: 'view', title: 'View Details' }
    ],
  };
  
  return await sendNotificationToUser(userId, payload);
}

export async function sendFinancialNotification(
  userId: string,
  type: 'payment_due' | 'payment_received' | 'payout_ready',
  amount: string,
  description: string,
  actionUrl?: string
) {
  const titles = {
    payment_due: 'Payment Due',
    payment_received: 'Payment Received', 
    payout_ready: 'Payout Ready'
  };
  
  const payload: PoollyNotificationPayload = {
    title: titles[type],
    body: `${amount} - ${description}`,
    type: 'financial',
    url: actionUrl || '/profile/payment',
    tag: `financial-${type}-${Date.now()}`,
    actions: type === 'payment_due' ? [
      { action: 'pay', title: 'Pay Now' }
    ] : undefined,
  };
  
  return await sendNotificationToUser(userId, payload);
} 