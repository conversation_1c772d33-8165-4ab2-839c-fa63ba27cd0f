# User Tasks & Actions System Implementation Plan

## Overview
Replace the Profile icon in bottom navigation with a Tasks/Actions drawer that presents users with actionable tasks they need to complete. The system should prevent users from continuing when there are mandatory (blocking) tasks pending.

## **Key Distinction: Tasks ≠ Notifications**
- **Tasks**: Action items requiring user decisions/completion
- **Notifications**: Informational messages about events that happened
- **This system**: Action-oriented task completion interface

## Current System Analysis
- **Screen Registration**: Screens are registered in `components/screens/index.ts` with dynamic imports
- **Screen Structure**: Full page components with their own state management 
- **Navigation**: Bottom navigation defined in `app/components/bottom-navigation.tsx`
- **UI Components**: Full shadcn/ui component library available including drawer, sheet, dialog, etc.
- **Design Language**: Need task-specific design focused on action completion

## Implementation Plan

### Phase 1: Core Infrastructure

#### 1.1 Create TasksScreen Component
- **Location**: `components/screens/TasksScreen.tsx`
- **Registration**: Add to `components/screens/index.ts` as `'tasks': () => import('./TasksScreen')`
- **Base Structure**: Follow existing screen patterns (similar to HomeScreen.tsx)
- **Design Focus**: Action completion, not information consumption

#### 1.2 Modify Bottom Navigation
- **File**: `app/components/bottom-navigation.tsx`
- **Change**: Replace Profile tab with Tasks tab
- **Icon**: Use `CheckSquare` from lucide-react
- **Badge**: Show count of pending blocking/urgent tasks
- **Route**: Point to tasks screen

#### 1.3 Task-Specific Drawer/Modal Behavior
- **Dismissible**: When no blocking tasks present
- **Modal/Blocking**: When blocking tasks present (prevent dismissal)
- **Implementation**: Use shadcn/ui `drawer` with conditional behavior
- **Visual Priority**: Priority-based layout, not time-based

### Phase 2: Task System Architecture

#### 2.1 Task Type System
```typescript
type TaskType = 
  | 'onboarding'     // Profile setup, initial steps
  | 'compliance'     // Terms acceptance, document uploads  
  | 'invitations'    // Group invites, member approvals
  | 'approvals'      // Pending decisions user must make
  | 'maintenance'    // Time-sensitive vehicle actions
  | 'financial'     // Payment actions, billing setup

type TaskPriority = 
  | 'blocking'       // Must complete to use app
  | 'urgent'         // Time-sensitive, high priority  
  | 'normal'         // Should complete soon
  | 'optional'       // Nice to have, can dismiss

type TaskStatus = 
  | 'pending'        // Not started
  | 'in_progress'    // User has begun action
  | 'completed'      // Action completed
  | 'dismissed'      // User chose to skip (if allowed)
  | 'expired'        // Time-sensitive task expired
```

#### 2.2 Task Data Schema
```typescript
interface UserTask {
  id: string
  userId: string
  type: TaskType
  priority: TaskPriority
  status: TaskStatus
  title: string
  description: string
  actionText: string // "Accept Invitation", "Upload Document", "Review Terms"
  completionText?: string // "Invitation Accepted", "Document Uploaded"
  dueDate?: Date
  estimatedMinutes?: number // How long task takes
  metadata: {
    // Task-specific data
    groupId?: string
    vehicleId?: string
    documentType?: string
    paymentId?: string
    termsVersion?: string
    relatedEntityId?: string
  }
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dismissedAt?: Date
  expiresAt?: Date
}
```

### Phase 3: Task Template Components

#### 3.1 Core Task Components
**Location**: `components/tasks/`

1. **TasksScreen.tsx** - Main screen component
2. **TasksList.tsx** - Task list container with priority sorting
3. **TaskItem.tsx** - Individual task display with action buttons
4. **TaskProgress.tsx** - Progress indicators and completion status
5. **EmptyStates.tsx** - Various empty state scenarios

#### 3.2 Task Template Components
**Location**: `components/tasks/templates/`

1. **OnboardingTask.tsx**
   - Profile setup steps
   - Document verification
   - Blocking priority

2. **ComplianceTask.tsx**
   - Terms acceptance
   - Policy acknowledgments
   - Blocking priority

3. **InvitationTask.tsx**
   - Group invitations
   - Member requests
   - Accept/Decline actions
   - Urgent priority

4. **ApprovalTask.tsx**
   - Application reviews
   - Member approvals
   - Approve/Reject actions
   - Normal priority

5. **MaintenanceTask.tsx**
   - Vehicle maintenance scheduling
   - Inspection preparations
   - Time-sensitive actions
   - Urgent priority

6. **FinancialTask.tsx**
   - Payment setup
   - Billing confirmations
   - Financial verifications
   - Priority varies

7. **DocumentTask.tsx**
   - Document uploads
   - Verification submissions
   - Progress tracking
   - Blocking/Urgent priority

### Phase 4: Empty States & User Experience

#### 4.1 Empty State Scenarios
```typescript
type EmptyStateType = 
  | 'all_complete'     // No pending tasks
  | 'loading'          // Loading tasks
  | 'error'            // Failed to load
  | 'filtered_empty'   // No tasks match filters
  | 'completed_only'   // Only completed tasks exist
  | 'no_blocking'      // No blocking tasks, optional only
```

#### 4.2 Empty State Components
1. **AllCompleteState**: Celebratory "all caught up" message
2. **LoadingState**: Skeleton loaders for task cards
3. **ErrorState**: Retry mechanism with support options
4. **FilteredEmptyState**: Clear filters suggestion
5. **CompletedOnlyState**: Option to show/hide completed tasks
6. **NoBlockingState**: Emphasize optional nature of remaining tasks

### Phase 5: Visual Design System (Task-Specific)

#### 5.1 Priority Visual Hierarchy
- **Blocking**: Red border, urgent icon, prominent action button
- **Urgent**: Orange border, clock icon, emphasized button
- **Normal**: Blue border, standard icon, normal button
- **Optional**: Gray border, subtle icon, secondary button

#### 5.2 Task State Indicators
- **Pending**: Clean card with action button
- **In Progress**: Progress bar or spinner
- **Completed**: Green checkmark, muted colors, completion timestamp
- **Dismissed**: Crossed out or hidden
- **Expired**: Red warning, "expired" badge

#### 5.3 Action-Focused Layout
```
┌─────────────────────────────────────┐
│ [Priority Icon] Task Title          │
│ Description text                    │
│ ⏱ Est. 5 minutes | 📅 Due tomorrow  │
│                    [ACTION BUTTON]  │
└─────────────────────────────────────┘
```

### Phase 6: Implementation Steps

#### Step 1: Core Task Screen ✅ [IMPLEMENTING NOW]
1. Create `TasksScreen.tsx` with task-focused design
2. Update bottom navigation with Tasks tab
3. Register screen in index.ts
4. Implement comprehensive mock data for all scenarios
5. Create basic empty states

#### Step 2: Task Components & Templates
1. Create task template components
2. Implement task rendering system with priority sorting
3. Add completion/dismissal actions
4. Test with comprehensive mock data

#### Step 3: Advanced Task Behavior
1. Implement blocking vs non-blocking logic
2. Add progress tracking
3. Create task completion workflows
4. Add navigation integration

#### Step 4: Polish & Integration
1. Add filtering and search
2. Connect with existing navigation system
3. Implement deep linking
4. Add animations and transitions

### Phase 7: Success Criteria
- ✅ Users see action-oriented tasks, not just information
- ✅ Blocking tasks prevent app usage until completed
- ✅ Clear visual priority hierarchy
- ✅ Proper empty states for all scenarios
- ✅ Task completion provides clear feedback
- ✅ Optional tasks can be dismissed
- ✅ Progress tracking works for multi-step tasks
- ✅ Integration with existing app navigation

## Mock Data Structure for Testing

```typescript
const mockTasks: UserTask[] = [
  // Blocking tasks
  {
    id: '1',
    type: 'compliance',
    priority: 'blocking',
    status: 'pending',
    title: 'Accept Updated Terms & Conditions',
    description: 'Review and accept updated terms to continue using Poolly.',
    actionText: 'Review & Accept Terms',
    estimatedMinutes: 5,
    metadata: { termsVersion: 'v2.1' }
  },
  {
    id: '2',
    type: 'onboarding',
    priority: 'blocking', 
    status: 'pending',
    title: 'Complete Profile Setup',
    description: 'Add required information to activate your account.',
    actionText: 'Complete Profile',
    estimatedMinutes: 10,
    metadata: { completionPercentage: 60 }
  },
  
  // Urgent tasks
  {
    id: '3',
    type: 'invitations',
    priority: 'urgent',
    status: 'pending',
    title: 'Group Invitation from John Doe',
    description: 'You\'ve been invited to join "Weekend Warriors" vehicle group.',
    actionText: 'View Invitation',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days
    metadata: { groupId: 'group_123', inviterName: 'John Doe' }
  },
  
  // Normal tasks
  {
    id: '4',
    type: 'maintenance',
    priority: 'normal',
    status: 'pending',
    title: 'Schedule Vehicle Inspection',
    description: 'Toyota Camry requires annual inspection.',
    actionText: 'Schedule Inspection',
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week
    metadata: { vehicleId: 'vehicle_456' }
  },
  
  // Optional tasks
  {
    id: '5',
    type: 'financial',
    priority: 'optional',
    status: 'pending',
    title: 'Set Up Direct Debit',
    description: 'Automate your monthly contributions for convenience.',
    actionText: 'Set Up Payment',
    estimatedMinutes: 3,
    metadata: { paymentType: 'direct_debit' }
  },
  
  // Completed tasks (for testing completed state)
  {
    id: '6',
    type: 'compliance',
    priority: 'blocking',
    status: 'completed',
    title: 'Verify Phone Number',
    description: 'Confirm your phone number for security.',
    actionText: 'Verify Phone',
    completionText: 'Phone number verified',
    completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
  }
];
```

---

**Next: Starting Step 1 Implementation**

# User Tasks & Actions System Implementation Plan

## Overview
Replace the Profile icon in bottom navigation with a Tasks/Actions drawer that presents users with actionable tasks they need to complete. The system should prevent users from continuing when there are mandatory (blocking) tasks pending.

## **Key Distinction: Tasks ≠ Notifications**
- **Tasks**: Action items requiring user decisions/completion
- **Notifications**: Informational messages about events that happened
- **This system**: Action-oriented task completion interface

## Current System Analysis
- **Screen Registration**: Screens are registered in `components/screens/index.ts` with dynamic imports
- **Screen Structure**: Full page components with their own state management 
- **Navigation**: Bottom navigation defined in `app/components/bottom-navigation.tsx`
- **UI Components**: Full shadcn/ui component library available including drawer, sheet, dialog, etc.
- **Design Language**: Need task-specific design focused on action completion

## Implementation Plan

### Phase 1: Core Infrastructure

#### 1.1 Create TasksScreen Component
- **Location**: `components/screens/TasksScreen.tsx`
- **Registration**: Add to `components/screens/index.ts` as `'tasks': () => import('./TasksScreen')`
- **Base Structure**: Follow existing screen patterns (similar to HomeScreen.tsx)
- **Design Focus**: Action completion, not information consumption

#### 1.2 Modify Bottom Navigation
- **File**: `app/components/bottom-navigation.tsx`
- **Change**: Replace Profile tab with Tasks tab
- **Icon**: Use `CheckSquare` from lucide-react
- **Badge**: Show count of pending blocking/urgent tasks
- **Route**: Point to tasks screen

#### 1.3 Task-Specific Drawer/Modal Behavior
- **Dismissible**: When no blocking tasks present
- **Modal/Blocking**: When blocking tasks present (prevent dismissal)
- **Implementation**: Use shadcn/ui `drawer` with conditional behavior
- **Visual Priority**: Priority-based layout, not time-based

### Phase 2: Task System Architecture

#### 2.1 Task Type System
```typescript
type TaskType = 
  | 'onboarding'     // Profile setup, initial steps
  | 'compliance'     // Terms acceptance, document uploads  
  | 'invitations'    // Group invites, member approvals
  | 'approvals'      // Pending decisions user must make
  | 'maintenance'    // Time-sensitive vehicle actions
  | 'financial'     // Payment actions, billing setup

type TaskPriority = 
  | 'blocking'       // Must complete to use app
  | 'urgent'         // Time-sensitive, high priority  
  | 'normal'         // Should complete soon
  | 'optional'       // Nice to have, can dismiss

type TaskStatus = 
  | 'pending'        // Not started
  | 'in_progress'    // User has begun action
  | 'completed'      // Action completed
  | 'dismissed'      // User chose to skip (if allowed)
  | 'expired'        // Time-sensitive task expired
```

#### 2.2 Task Data Schema
```typescript
interface UserTask {
  id: string
  userId: string
  type: TaskType
  priority: TaskPriority
  status: TaskStatus
  title: string
  description: string
  actionText: string // "Accept Invitation", "Upload Document", "Review Terms"
  completionText?: string // "Invitation Accepted", "Document Uploaded"
  dueDate?: Date
  estimatedMinutes?: number // How long task takes
  metadata: {
    // Task-specific data
    groupId?: string
    vehicleId?: string
    documentType?: string
    paymentId?: string
    termsVersion?: string
    relatedEntityId?: string
  }
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dismissedAt?: Date
  expiresAt?: Date
}
```

### Phase 3: Task Template Components

#### 3.1 Core Task Components
**Location**: `components/tasks/`

1. **TasksScreen.tsx** - Main screen component
2. **TasksList.tsx** - Task list container with priority sorting
3. **TaskItem.tsx** - Individual task display with action buttons
4. **TaskProgress.tsx** - Progress indicators and completion status
5. **EmptyStates.tsx** - Various empty state scenarios

#### 3.2 Task Template Components
**Location**: `components/tasks/templates/`

1. **OnboardingTask.tsx**
   - Profile setup steps
   - Document verification
   - Blocking priority

2. **ComplianceTask.tsx**
   - Terms acceptance
   - Policy acknowledgments
   - Blocking priority

3. **InvitationTask.tsx**
   - Group invitations
   - Member requests
   - Accept/Decline actions
   - Urgent priority

4. **ApprovalTask.tsx**
   - Application reviews
   - Member approvals
   - Approve/Reject actions
   - Normal priority

5. **MaintenanceTask.tsx**
   - Vehicle maintenance scheduling
   - Inspection preparations
   - Time-sensitive actions
   - Urgent priority

6. **FinancialTask.tsx**
   - Payment setup
   - Billing confirmations
   - Financial verifications
   - Priority varies

7. **DocumentTask.tsx**
   - Document uploads
   - Verification submissions
   - Progress tracking
   - Blocking/Urgent priority

### Phase 4: Empty States & User Experience

#### 4.1 Empty State Scenarios
```typescript
type EmptyStateType = 
  | 'all_complete'     // No pending tasks
  | 'loading'          // Loading tasks
  | 'error'            // Failed to load
  | 'filtered_empty'   // No tasks match filters
  | 'completed_only'   // Only completed tasks exist
  | 'no_blocking'      // No blocking tasks, optional only
```

#### 4.2 Empty State Components
1. **AllCompleteState**: Celebratory "all caught up" message
2. **LoadingState**: Skeleton loaders for task cards
3. **ErrorState**: Retry mechanism with support options
4. **FilteredEmptyState**: Clear filters suggestion
5. **CompletedOnlyState**: Option to show/hide completed tasks
6. **NoBlockingState**: Emphasize optional nature of remaining tasks

### Phase 5: Visual Design System (Task-Specific)

#### 5.1 Priority Visual Hierarchy
- **Blocking**: Red border, urgent icon, prominent action button
- **Urgent**: Orange border, clock icon, emphasized button
- **Normal**: Blue border, standard icon, normal button
- **Optional**: Gray border, subtle icon, secondary button

#### 5.2 Task State Indicators
- **Pending**: Clean card with action button
- **In Progress**: Progress bar or spinner
- **Completed**: Green checkmark, muted colors, completion timestamp
- **Dismissed**: Crossed out or hidden
- **Expired**: Red warning, "expired" badge

#### 5.3 Action-Focused Layout
```
┌─────────────────────────────────────┐
│ [Priority Icon] Task Title          │
│ Description text                    │
│ ⏱ Est. 5 minutes | 📅 Due tomorrow  │
│                    [ACTION BUTTON]  │
└─────────────────────────────────────┘
```

### Phase 6: Implementation Steps

#### Step 1: Core Task Screen ✅ [IMPLEMENTING NOW]
1. Create `TasksScreen.tsx` with task-focused design
2. Update bottom navigation with Tasks tab
3. Register screen in index.ts
4. Implement comprehensive mock data for all scenarios
5. Create basic empty states

#### Step 2: Task Components & Templates
1. Create task template components
2. Implement task rendering system with priority sorting
3. Add completion/dismissal actions
4. Test with comprehensive mock data

#### Step 3: Advanced Task Behavior
1. Implement blocking vs non-blocking logic
2. Add progress tracking
3. Create task completion workflows
4. Add navigation integration

#### Step 4: Polish & Integration
1. Add filtering and search
2. Connect with existing navigation system
3. Implement deep linking
4. Add animations and transitions

### Phase 7: Success Criteria
- ✅ Users see action-oriented tasks, not just information
- ✅ Blocking tasks prevent app usage until completed
- ✅ Clear visual priority hierarchy
- ✅ Proper empty states for all scenarios
- ✅ Task completion provides clear feedback
- ✅ Optional tasks can be dismissed
- ✅ Progress tracking works for multi-step tasks
- ✅ Integration with existing app navigation

## Mock Data Structure for Testing

```typescript
const mockTasks: UserTask[] = [
  // Blocking tasks
  {
    id: '1',
    type: 'compliance',
    priority: 'blocking',
    status: 'pending',
    title: 'Accept Updated Terms & Conditions',
    description: 'Review and accept updated terms to continue using Poolly.',
    actionText: 'Review & Accept Terms',
    estimatedMinutes: 5,
    metadata: { termsVersion: 'v2.1' }
  },
  {
    id: '2',
    type: 'onboarding',
    priority: 'blocking', 
    status: 'pending',
    title: 'Complete Profile Setup',
    description: 'Add required information to activate your account.',
    actionText: 'Complete Profile',
    estimatedMinutes: 10,
    metadata: { completionPercentage: 60 }
  },
  
  // Urgent tasks
  {
    id: '3',
    type: 'invitations',
    priority: 'urgent',
    status: 'pending',
    title: 'Group Invitation from John Doe',
    description: 'You\'ve been invited to join "Weekend Warriors" vehicle group.',
    actionText: 'View Invitation',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days
    metadata: { groupId: 'group_123', inviterName: 'John Doe' }
  },
  
  // Normal tasks
  {
    id: '4',
    type: 'maintenance',
    priority: 'normal',
    status: 'pending',
    title: 'Schedule Vehicle Inspection',
    description: 'Toyota Camry requires annual inspection.',
    actionText: 'Schedule Inspection',
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week
    metadata: { vehicleId: 'vehicle_456' }
  },
  
  // Optional tasks
  {
    id: '5',
    type: 'financial',
    priority: 'optional',
    status: 'pending',
    title: 'Set Up Direct Debit',
    description: 'Automate your monthly contributions for convenience.',
    actionText: 'Set Up Payment',
    estimatedMinutes: 3,
    metadata: { paymentType: 'direct_debit' }
  },
  
  // Completed tasks (for testing completed state)
  {
    id: '6',
    type: 'compliance',
    priority: 'blocking',
    status: 'completed',
    title: 'Verify Phone Number',
    description: 'Confirm your phone number for security.',
    actionText: 'Verify Phone',
    completionText: 'Phone number verified',
    completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000) // 2 hours ago
  }
];
```

---

**Next: Starting Step 1 Implementation**

