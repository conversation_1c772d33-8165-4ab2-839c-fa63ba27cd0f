"use server";

import { db } from "@/db";
import { eq, and, sql, desc, inArray } from "drizzle-orm";
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";
import { h_vehicleCatalogImages } from "@/drizzle/h_schema/vehicle-catalog";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
} from "@/drizzle/schema";
// Removed server-side URL signing for client-side consistency

// Get published e-hailing opportunities (catalog-based)
export async function getEhailingOpportunities() {
  try {
    const ehailingListings = await db
      .select({
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,
        // Catalog details
        catalogMake: h_vehicleCatalog.make,
        catalogModel: h_vehicleCatalog.model,
        catalogDescription: h_vehicleCatalog.description,
        catalogImage: h_vehicleCatalogImages.imageUrl,
        // Publish status
        publishStatus: h_listing_publish_status.status,
      })
      .from(h_listings)
      .leftJoin(h_vehicleCatalog, eq(h_listings.sourceId, h_vehicleCatalog.id))
      .leftJoin(
        h_vehicleCatalogImages,
        and(
          eq(h_vehicleCatalog.id, h_vehicleCatalogImages.catalogId),
          eq(h_vehicleCatalogImages.isPrimary, true)
        )
      )
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listing_approval_status.status, "approved"),
          eq(h_listing_publish_status.status, "published"),
          sql`${h_listings.effectiveTo} > NOW()`
        )
      )
      .orderBy(desc(h_listings.effectiveFrom));

    // Debug logging to check catalog image data
    console.log("🔍 E-hailing listings debug:", {
      totalListings: ehailingListings.length,
      sampleListing: ehailingListings[0]
        ? {
            id: ehailingListings[0].id,
            catalogMake: ehailingListings[0].catalogMake,
            catalogModel: ehailingListings[0].catalogModel,
            catalogImage: ehailingListings[0].catalogImage,
            sourceId: ehailingListings[0].sourceId,
          }
        : null,
    });

    // Convert to unified media array format for consistent client-side processing
    const listingsWithMedia = ehailingListings.map((listing) => {
      const mediaArray = listing.catalogImage
        ? [
            {
              id: listing.id,
              vehicle_id: listing.sourceId,
              media_path: listing.catalogImage,
              created_at: listing.effectiveFrom,
            },
          ]
        : [];

      console.log(`📸 Listing ${listing.id} media conversion:`, {
        originalCatalogImage: listing.catalogImage,
        convertedMedia: mediaArray,
      });

      return {
        ...listing,
        media: mediaArray,
      };
    });

    return {
      success: true,
      data: listingsWithMedia,
    };
  } catch (error) {
    console.error("Error fetching e-hailing opportunities:", error);
    return {
      success: false,
      error: "Failed to fetch e-hailing opportunities",
    };
  }
}

// Get published rental opportunities
export async function getRentalOpportunities() {
  try {
    const rentalListings = await db
      .select({
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        makeName: vehicleMake.name,
        modelName: vehicleModel.model,
        // Publish status
        publishStatus: h_listing_publish_status.status,
      })
      .from(h_listings)
      .leftJoin(vehicles, eq(h_listings.sourceId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.listingType, "rental"),
          eq(h_listing_approval_status.status, "approved"),
          eq(h_listing_publish_status.status, "published"),
          sql`${h_listings.effectiveTo} > NOW()`
        )
      )
      .orderBy(desc(h_listings.effectiveFrom));

    // Get vehicle media for rental listings
    const vehicleIds = rentalListings
      .map((listing) => listing.sourceId)
      .filter(Boolean);
    let vehicleMediaRecords: any[] = [];

    if (vehicleIds.length > 0) {
      vehicleMediaRecords = await db
        .select({
          id: vehicleMedia.id,
          vehicleId: vehicleMedia.vehicleId,
          mediaPath: vehicleMedia.mediaPath,
          createdAt: vehicleMedia.createdAt,
        })
        .from(vehicleMedia)
        .where(inArray(vehicleMedia.vehicleId, vehicleIds));
    }

    // Group vehicle media by vehicle ID
    const mediaByVehicleId = vehicleMediaRecords.reduce(
      (acc, media) => {
        if (!acc[media.vehicleId]) {
          acc[media.vehicleId] = [];
        }
        acc[media.vehicleId].push({
          id: media.id,
          vehicle_id: media.vehicleId,
          media_path: media.mediaPath,
          created_at: media.createdAt || "",
        });
        return acc;
      },
      {} as Record<number, any[]>
    );

    // Add media to listings
    const enrichedListings = rentalListings.map((listing) => ({
      ...listing,
      media: mediaByVehicleId[listing.sourceId] || [],
    }));

    return {
      success: true,
      data: enrichedListings,
    };
  } catch (error) {
    console.error("Error fetching rental opportunities:", error);
    return {
      success: false,
      error: "Failed to fetch rental opportunities",
    };
  }
}

// Get published fractional ownership opportunities (buy)
export async function getFractionalOpportunities() {
  try {
    const fractionalListings = await db
      .select({
        id: h_listings.id,
        partyId: h_listings.partyId,
        sourceId: h_listings.sourceId,
        listingType: h_listings.listingType,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        listingDetails: h_listings.listingDetails,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        makeName: vehicleMake.name,
        modelName: vehicleModel.model,
        // Publish status
        publishStatus: h_listing_publish_status.status,
      })
      .from(h_listings)
      .leftJoin(vehicles, eq(h_listings.sourceId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.listingType, "fractional"),
          eq(h_listing_approval_status.status, "approved"),
          eq(h_listing_publish_status.status, "published"),
          sql`${h_listings.effectiveTo} > NOW()`
        )
      )
      .orderBy(desc(h_listings.effectiveFrom));

    // Get vehicle media for fractional listings
    const vehicleIds = fractionalListings
      .map((listing) => listing.sourceId)
      .filter(Boolean);
    let vehicleMediaRecords: any[] = [];

    if (vehicleIds.length > 0) {
      vehicleMediaRecords = await db
        .select({
          id: vehicleMedia.id,
          vehicleId: vehicleMedia.vehicleId,
          mediaPath: vehicleMedia.mediaPath,
          createdAt: vehicleMedia.createdAt,
        })
        .from(vehicleMedia)
        .where(inArray(vehicleMedia.vehicleId, vehicleIds));
    }

    // Group vehicle media by vehicle ID
    const mediaByVehicleId = vehicleMediaRecords.reduce(
      (acc, media) => {
        if (!acc[media.vehicleId]) {
          acc[media.vehicleId] = [];
        }
        acc[media.vehicleId].push({
          id: media.id,
          vehicle_id: media.vehicleId,
          media_path: media.mediaPath,
          created_at: media.createdAt || "",
        });
        return acc;
      },
      {} as Record<number, any[]>
    );

    // Add media to listings
    const enrichedListings = fractionalListings.map((listing) => ({
      ...listing,
      media: mediaByVehicleId[listing.sourceId] || [],
    }));

    return {
      success: true,
      data: enrichedListings,
    };
  } catch (error) {
    console.error("Error fetching fractional opportunities:", error);
    return {
      success: false,
      error: "Failed to fetch fractional opportunities",
    };
  }
}
