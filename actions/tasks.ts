"use server";

import { db } from "@/db";
import { tasks, groupMembershipInvitations, groupMemberships, groupMemberRoles, party, individual, groups, vehicleInspectionsImmutable } from "@/drizzle/schema";
import { 
  TaskCreate, 
  TaskTypeEnum, 
  TaskStatusEnum, 
  TaskPriorityEnum, 
  GroupInvitationTaskMetadata,
  BookingAcknowledgmentTaskMetadata,
  VehicleHandoverGiverTaskMetadata,
  VehicleHandoverReceiverTaskMetadata
} from "@/types/tasks";
import { InvitationStatusEnum, GroupRoleEnum } from "@/types/groups";
import { InspectionType, ConditionEnhanced } from "@/types/bookings";
import { eq, and, or } from "drizzle-orm";

/**
 * Check for pending tasks for a user by email or party ID
 */
export async function checkPendingTasks(email: string, partyId?: number) {
  try {
    const pendingTasks = await db
      .select({
        id: tasks.id,
        type: tasks.type,
        status: tasks.status,
        priority: tasks.priority,
        title: tasks.title,
        description: tasks.description,
        email: tasks.email,
        partyId: tasks.partyId,
        relatedEntityId: tasks.relatedEntityId,
        metadata: tasks.metadata,
        estimatedMinutes: tasks.estimatedMinutes,
        expiresAt: tasks.expiresAt,
        createdAt: tasks.createdAt
      })
      .from(tasks)
      .where(
        and(
          or(
            eq(tasks.email, email),
            partyId ? eq(tasks.partyId, partyId) : undefined
          ),
          eq(tasks.status, TaskStatusEnum.PENDING)
        )
      )
      .orderBy(tasks.priority, tasks.createdAt);

    return {
      success: true,
      tasks: pendingTasks,
      hasGroupInvitations: pendingTasks.some(task => task.type === TaskTypeEnum.GROUP_INVITATION)
    };
  } catch (error) {
    console.error("Error checking pending tasks:", error);
    return {
      success: false,
      error: "Failed to check pending tasks",
      tasks: []
    };
  }
}

/**
 * Create a group invitation task
 */
export async function createGroupInvitationTask(
  email: string,
  invitationId: number,
  groupId: number,
  groupName: string,
  inviterName: string,
  role: GroupRoleEnum,
  partyId?: number
) {
  try {
    const metadata: GroupInvitationTaskMetadata = {
      groupId,
      groupName,
      invitationId,
      inviterName,
      role
    };

    const taskData: TaskCreate = {
      type: TaskTypeEnum.GROUP_INVITATION,
      status: TaskStatusEnum.PENDING,
      priority: TaskPriorityEnum.URGENT,
      title: `Group Invitation: ${groupName}`,
      description: `${inviterName} invited you to join the group "${groupName}" as a ${role.toLowerCase()}.`,
      email,
      partyId,
      relatedEntityId: invitationId,
      metadata,
      estimatedMinutes: 2,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
    };

    const [task] = await db.insert(tasks).values(taskData).returning();

    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("Error creating group invitation task:", error);
    return {
      success: false,
      error: "Failed to create group invitation task"
    };
  }
}

/**
 * Accept a group invitation task
 */
export async function acceptGroupInvitation(taskId: number, partyId: number) {
  try {
    // Get the task
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task || task.type !== TaskTypeEnum.GROUP_INVITATION) {
      return {
        success: false,
        error: "Task not found or invalid type"
      };
    }

    const metadata = task.metadata as GroupInvitationTaskMetadata;
    const invitationId = task.relatedEntityId;

    if (!invitationId || !metadata) {
      return {
        success: false,
        error: "Invalid task data"
      };
    }

    // Get the invitation
    const [invitation] = await db
      .select()
      .from(groupMembershipInvitations)
      .where(eq(groupMembershipInvitations.id, invitationId));

    if (!invitation || invitation.status !== InvitationStatusEnum.SENT) {
      return {
        success: false,
        error: "Invitation not found or already processed"
      };
    }

    // Start transaction to accept invitation
    const result = await db.transaction(async (tx) => {
      // Update invitation status
      await tx
        .update(groupMembershipInvitations)
        .set({
          status: InvitationStatusEnum.ACCEPTED,
          acceptedAt: new Date().toISOString(),
          acceptedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(groupMembershipInvitations.id, invitationId));

      // Add to group memberships
      await tx.insert(groupMemberships).values({
        groupId: metadata.groupId,
        partyId: partyId,
        effectiveFrom: new Date().toISOString()
      });

      // Add group member role
      await tx.insert(groupMemberRoles).values({
        groupId: metadata.groupId,
        partyId: partyId,
        role: invitation.role,
        effectiveFrom: new Date().toISOString()
      });

      // Complete the task
      await tx
        .update(tasks)
        .set({
          status: TaskStatusEnum.COMPLETED,
          completedAt: new Date().toISOString(),
          completedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(tasks.id, taskId));

      return { success: true };
    });

    return result;
  } catch (error) {
    console.error("Error accepting group invitation:", error);
    return {
      success: false,
      error: "Failed to accept group invitation"
    };
  }
}

/**
 * Decline a group invitation task
 */
export async function declineGroupInvitation(taskId: number, partyId: number) {
  try {
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task || task.type !== TaskTypeEnum.GROUP_INVITATION) {
      return {
        success: false,
        error: "Task not found or invalid type"
      };
    }

    const invitationId = task.relatedEntityId;
    if (!invitationId) {
      return {
        success: false,
        error: "Invalid task data"
      };
    }

    // Start transaction to decline invitation
    const result = await db.transaction(async (tx) => {
      // Update invitation status
      await tx
        .update(groupMembershipInvitations)
        .set({
          status: InvitationStatusEnum.DECLINED,
          updatedAt: new Date().toISOString()
        })
        .where(eq(groupMembershipInvitations.id, invitationId));

      // Complete the task
      await tx
        .update(tasks)
        .set({
          status: TaskStatusEnum.COMPLETED,
          completedAt: new Date().toISOString(),
          completedBy: partyId,
          updatedAt: new Date().toISOString()
        })
        .where(eq(tasks.id, taskId));

      return { success: true };
    });

    return result;
  } catch (error) {
    console.error("Error declining group invitation:", error);
    return {
      success: false,
      error: "Failed to decline group invitation"
    };
  }
}

/**
 * Get task details by ID
 */
export async function getTaskById(taskId: number) {
  try {
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task) {
      return {
        success: false,
        error: "Task not found"
      };
    }

    // If it's a group invitation task, get additional details
    if (task.type === TaskTypeEnum.GROUP_INVITATION && task.relatedEntityId) {
      const invitationDetails = await db
        .select({
          invitation: groupMembershipInvitations,
          groupName: groups.name,
          inviterFirstName: individual.firstName,
          inviterLastName: individual.lastName
        })
        .from(groupMembershipInvitations)
        .leftJoin(groups, eq(groupMembershipInvitations.groupId, groups.id))
        .leftJoin(party, eq(groupMembershipInvitations.invitedBy, party.id))
        .leftJoin(individual, eq(party.id, individual.partyId))
        .where(eq(groupMembershipInvitations.id, task.relatedEntityId));

      if (invitationDetails.length > 0) {
        const details = invitationDetails[0];
        return {
          success: true,
          task: {
            ...task,
            invitationDetails: {
              ...details.invitation,
              groupName: details.groupName,
              inviterName: details.inviterFirstName && details.inviterLastName 
                ? `${details.inviterFirstName} ${details.inviterLastName}`
                : "Unknown"
            }
          }
        };
      }
    }

    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("Error getting task by ID:", error);
    return {
      success: false,
      error: "Failed to get task details"
    };
  }
}

/**
 * Create a booking acknowledgment task for the current vehicle possessor
 */
export async function createBookingAcknowledgmentTask(
  bookingReference: string,
  vehicleId: number,
  vehicleName: string,
  possessorEmail: string,
  possessorName: string,
  possessorPartyId: number,
  borrowerName: string,
  requestedStart: string,
  requestedEnd: string
) {
  try {
    console.log("📝 === createBookingAcknowledgmentTask START ===");
    console.log("📝 Parameters:", {
      bookingReference,
      vehicleId,
      vehicleName,
      possessorEmail,
      possessorName,
      possessorPartyId,
      borrowerName,
      requestedStart,
      requestedEnd
    });

    const metadata: BookingAcknowledgmentTaskMetadata = {
      bookingReference,
      vehicleId,
      vehicleName,
      requestedStart,
      requestedEnd,
      fromPartyName: borrowerName
    };

    const taskData: TaskCreate = {
      type: TaskTypeEnum.BOOKING_ACKNOWLEDGMENT,
      status: TaskStatusEnum.PENDING,
      priority: TaskPriorityEnum.URGENT,
      title: `Booking Request: ${vehicleName}`,
      description: `${borrowerName} has requested to book ${vehicleName} for ${new Date(requestedStart).toLocaleDateString()}. Please review and acknowledge this booking request.`,
      email: possessorEmail,
      partyId: possessorPartyId,
      relatedEntityId: vehicleId,
      metadata,
      estimatedMinutes: 15,
      expiresAt: new Date(requestedStart).toISOString() // Expires when booking starts
    };

    console.log("📝 Task data to insert:", taskData);

    const [task] = await db.insert(tasks).values(taskData).returning();

    console.log("📝 Successfully inserted task:", task);
    console.log(`✅ Created BOOKING_ACKNOWLEDGMENT task for possessor ${possessorEmail}`);
    
    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("❌ Error creating booking acknowledgment task:", error);
    return {
      success: false,
      error: "Failed to create booking acknowledgment task"
    };
  }
}

/**
 * Create a vehicle handover task for the party currently holding the vehicle
 */
export async function createVehicleHandoverGiverTask(
  bookingReference: string,
  vehicleId: number,
  vehicleName: string,
  currentHolderEmail: string,
  borrowerName: string,
  borrowerEmail: string,
  requestedStart: string,
  requestedEnd: string,
  handoverLocation?: string,
  currentHolderPartyId?: number
) {
  try {
    console.log("🚚 === createVehicleHandoverGiverTask START ===");
    console.log("🚚 Parameters:", {
      bookingReference,
      vehicleId,
      vehicleName,
      currentHolderEmail,
      borrowerName,
      borrowerEmail,
      requestedStart,
      requestedEnd,
      handoverLocation,
      currentHolderPartyId
    });

    const metadata: VehicleHandoverGiverTaskMetadata = {
      bookingReference,
      vehicleId,
      vehicleName,
      borrowerName,
      borrowerEmail,
      requestedStart,
      requestedEnd,
      handoverLocation
    };

    const taskData: TaskCreate = {
      type: TaskTypeEnum.VEHICLE_HANDOVER_GIVER,
      status: TaskStatusEnum.PENDING,
      priority: TaskPriorityEnum.URGENT,
      title: `Vehicle Handover Required: ${vehicleName}`,
      description: `You need to hand over ${vehicleName} to ${borrowerName} for their booking starting ${new Date(requestedStart).toLocaleDateString()}.`,
      email: currentHolderEmail,
      partyId: currentHolderPartyId,
      relatedEntityId: vehicleId,
      metadata,
      estimatedMinutes: 30,
      expiresAt: new Date(requestedStart).toISOString() // Expires when booking starts
    };

    console.log("🚚 Task data to insert:", taskData);

    const [task] = await db.insert(tasks).values(taskData).returning();

    console.log("🚚 Successfully inserted task:", task);
    console.log(`✅ Created VEHICLE_HANDOVER_GIVER task for ${currentHolderEmail}`);
    
    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("❌ Error creating vehicle handover giver task:", error);
    return {
      success: false,
      error: "Failed to create vehicle handover giver task"
    };
  }
}

/**
 * Create a vehicle receiving task for the party who will receive the vehicle
 */
export async function createVehicleHandoverReceiverTask(
  bookingReference: string,
  vehicleId: number,
  vehicleName: string,
  borrowerEmail: string,
  currentHolderName: string,
  currentHolderEmail: string,
  requestedStart: string,
  requestedEnd: string,
  handoverLocation?: string,
  borrowerPartyId?: number
) {
  try {
    console.log("📦 === createVehicleHandoverReceiverTask START ===");
    console.log("📦 Parameters:", {
      bookingReference,
      vehicleId,
      vehicleName,
      borrowerEmail,
      currentHolderName,
      currentHolderEmail,
      requestedStart,
      requestedEnd,
      handoverLocation,
      borrowerPartyId
    });

    const metadata: VehicleHandoverReceiverTaskMetadata = {
      bookingReference,
      vehicleId,
      vehicleName,
      currentHolderName,
      currentHolderEmail,
      requestedStart,
      requestedEnd,
      handoverLocation
    };

    const taskData: TaskCreate = {
      type: TaskTypeEnum.VEHICLE_HANDOVER_RECEIVER,
      status: TaskStatusEnum.PENDING,
      priority: TaskPriorityEnum.URGENT,
      title: `Vehicle Pickup: ${vehicleName}`,
      description: `Receive ${vehicleName} from ${currentHolderName} for your booking starting ${new Date(requestedStart).toLocaleDateString()}.`,
      email: borrowerEmail,
      partyId: borrowerPartyId,
      relatedEntityId: vehicleId,
      metadata,
      estimatedMinutes: 30,
      expiresAt: new Date(requestedStart).toISOString() // Expires when booking starts
    };

    console.log("📦 Task data to insert:", taskData);

    const [task] = await db.insert(tasks).values(taskData).returning();

    console.log("📦 Successfully inserted task:", task);
    console.log(`✅ Created VEHICLE_HANDOVER_RECEIVER task for ${borrowerEmail}`);
    
    return {
      success: true,
      task
    };
  } catch (error) {
    console.error("❌ Error creating vehicle handover receiver task:", error);
    return {
      success: false,
      error: "Failed to create vehicle handover receiver task"
    };
  }
}

/**
 * Create all vehicle handover tasks for a booking
 * This is the main function to call when a booking is created
 */
export async function createVehicleHandoverTasks(
  bookingReference: string,
  vehicleId: number,
  vehicleName: string,
  borrowerEmail: string,
  borrowerName: string,
  borrowerPartyId: number,
  requestedStart: string,
  requestedEnd: string,
  currentHolderEmail?: string,
  currentHolderName?: string,
  currentHolderPartyId?: number,
  handoverLocation?: string
) {
  try {
    console.log("🎯 === createVehicleHandoverTasks START ===");
    console.log("🎯 Parameters received:", {
      bookingReference,
      vehicleId,
      vehicleName,
      borrowerEmail,
      borrowerName,
      borrowerPartyId,
      requestedStart,
      requestedEnd,
      currentHolderEmail,
      currentHolderName,
      currentHolderPartyId,
      handoverLocation
    });

    const results = [];

    // 1. Create booking acknowledgment task for the possessor (if we have their info)
    if (currentHolderName && currentHolderPartyId) {
      console.log("📝 Creating booking acknowledgment task for possessor...");
      const acknowledgmentResult = await createBookingAcknowledgmentTask(
        bookingReference,
        vehicleId,
        vehicleName,
        currentHolderEmail || '', // Email can be empty
        currentHolderName,
        currentHolderPartyId,
        borrowerName,
        requestedStart,
        requestedEnd
      );
      console.log("📝 Acknowledgment task result:", acknowledgmentResult);
      results.push(acknowledgmentResult);
    } else {
      console.warn("⚠️ Skipping booking acknowledgment task - missing required holder info:", {
        hasCurrentHolderName: !!currentHolderName,
        hasCurrentHolderPartyId: !!currentHolderPartyId,
        currentHolderEmail: currentHolderEmail || 'none'
      });
    }

    // 2. Create handover giver task if we know who currently has the vehicle
    if (currentHolderName && currentHolderPartyId) {
      console.log("🚚 Creating handover giver task...");
      const giverResult = await createVehicleHandoverGiverTask(
        bookingReference,
        vehicleId,
        vehicleName,
        currentHolderEmail || '', // Email can be empty
        borrowerName,
        borrowerEmail,
        requestedStart,
        requestedEnd,
        handoverLocation,
        currentHolderPartyId
      );
      console.log("🚚 Giver task result:", giverResult);
      results.push(giverResult);

      // 3. Create handover receiver task for the borrower
      console.log("📦 Creating handover receiver task...");
      const receiverResult = await createVehicleHandoverReceiverTask(
        bookingReference,
        vehicleId,
        vehicleName,
        borrowerEmail,
        currentHolderName,
        currentHolderEmail || '', // Email can be empty
        requestedStart,
        requestedEnd,
        handoverLocation,
        borrowerPartyId
      );
      console.log("📦 Receiver task result:", receiverResult);
      results.push(receiverResult);
    } else {
      console.warn("⚠️ Skipping handover giver/receiver tasks - missing required holder info:", {
        hasCurrentHolderName: !!currentHolderName,
        hasCurrentHolderPartyId: !!currentHolderPartyId,
        currentHolderEmail: currentHolderEmail || 'none'
      });
    }

    const successfulTasks = results.filter(r => r.success);
    const failedTasks = results.filter(r => !r.success);

    console.log("🎯 Task creation summary:", {
      totalAttempted: results.length,
      successful: successfulTasks.length,
      failed: failedTasks.length,
      successfulTasks: successfulTasks.map(t => t.task?.type || 'unknown'),
      failedTasks: failedTasks.map(t => t.error || 'unknown error')
    });

    console.log("🎯 === createVehicleHandoverTasks END ===");

    return {
      success: failedTasks.length === 0,
      tasksCreated: successfulTasks.length,
      tasksFailed: failedTasks.length,
      results,
      message: `Created ${successfulTasks.length} tasks for booking ${bookingReference}`
    };
  } catch (error) {
    console.error("❌ Error in createVehicleHandoverTasks:", error);
    return {
      success: false,
      error: "Failed to create vehicle handover tasks",
      tasksCreated: 0,
      tasksFailed: 0
    };
  }
}

/**
 * Complete a booking acknowledgment task
 */
export async function completeBookingAcknowledgmentTask(taskId: number, partyId: number) {
  try {
    console.log("📝 === completeBookingAcknowledgmentTask START ===");
    console.log("📝 Parameters:", { taskId, partyId });

    // Get the task
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task) {
      return {
        success: false,
        error: "Task not found"
      };
    }

    if (task.type !== TaskTypeEnum.BOOKING_ACKNOWLEDGMENT) {
      return {
        success: false,
        error: "Invalid task type"
      };
    }

    if (task.status === TaskStatusEnum.COMPLETED) {
      return {
        success: false,
        error: "Task already completed"
      };
    }

    // Verify the user is authorized to complete this task
    if (task.partyId !== partyId) {
      return {
        success: false,
        error: "Unauthorized to complete this task"
      };
    }

    // Complete the task
    const [updatedTask] = await db
      .update(tasks)
      .set({
        status: TaskStatusEnum.COMPLETED,
        completedAt: new Date().toISOString(),
        completedBy: partyId,
        updatedAt: new Date().toISOString()
      })
      .where(eq(tasks.id, taskId))
      .returning();

    console.log("✅ Booking acknowledgment task completed:", {
      taskId,
      bookingReference: (task.metadata as any)?.bookingReference,
      completedBy: partyId
    });

    return {
      success: true,
      task: updatedTask,
      message: "Booking acknowledgment completed successfully"
    };
  } catch (error) {
    console.error("❌ Error completing booking acknowledgment task:", error);
    return {
      success: false,
      error: "Failed to complete booking acknowledgment task"
    };
  }
}

/**
 * Complete a general task (for tasks that don't require specific business logic)
 */
export async function completeTask(taskId: number, partyId: number, completionData?: any) {
  try {
    console.log("📝 === completeTask START ===");
    console.log("📝 Parameters:", { taskId, partyId, completionData });

    // Get the task
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task) {
      return {
        success: false,
        error: "Task not found"
      };
    }

    if (task.status === TaskStatusEnum.COMPLETED) {
      return {
        success: false,
        error: "Task already completed"
      };
    }

    // Verify the user is authorized to complete this task
    if (task.partyId !== partyId) {
      return {
        success: false,
        error: "Unauthorized to complete this task"
      };
    }

    // Complete the task
    const [updatedTask] = await db
      .update(tasks)
      .set({
        status: TaskStatusEnum.COMPLETED,
        completedAt: new Date().toISOString(),
        completedBy: partyId,
        updatedAt: new Date().toISOString()
      })
      .where(eq(tasks.id, taskId))
      .returning();

    console.log("✅ Task completed:", {
      taskId,
      taskType: task.type,
      completedBy: partyId
    });

    return {
      success: true,
      task: updatedTask,
      message: "Task completed successfully"
    };
  } catch (error) {
    console.error("❌ Error completing task:", error);
    return {
      success: false,
      error: "Failed to complete task"
    };
  }
}

/**
 * Complete a vehicle handover task with inspection data
 */
export async function completeVehicleHandoverTask(
  taskId: number, 
  partyId: number, 
  handoverData: {
    vehicleId: number;
    handingOver: boolean;
    scratches: string;
    dents: string;
    tires: string;
    lights: string;
    cleanliness: string;
    seats: string;
    dashboard_controls: string;
    odors: string;
    odometer: number;
    fuel_level: number;
    known_issues: string;
    front_view: string;
    rear_view: string;
    left_view: string;
    right_view: string;
    dashboard: string;
    seats_view: string;
    additional_photos: string[];
  }
) {
  try {
    console.log("📝 === completeVehicleHandoverTask START ===");
    console.log("📝 Parameters:", { taskId, partyId, handoverData });

    // Get the task
    const [task] = await db
      .select()
      .from(tasks)
      .where(eq(tasks.id, taskId));

    if (!task) {
      return {
        success: false,
        error: "Task not found"
      };
    }

    if (task.type !== TaskTypeEnum.VEHICLE_HANDOVER_GIVER && task.type !== TaskTypeEnum.VEHICLE_HANDOVER_RECEIVER) {
      return {
        success: false,
        error: "Invalid task type for handover completion"
      };
    }

    if (task.status === TaskStatusEnum.COMPLETED) {
      return {
        success: false,
        error: "Task already completed"
      };
    }

    // Verify the user is authorized to complete this task
    if (task.partyId !== partyId) {
      return {
        success: false,
        error: "Not authorized to complete this task"
      };
    }

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // 1. Create vehicle inspection record
      const [inspection] = await tx
        .insert(vehicleInspectionsImmutable)
        .values({
          vehicleId: handoverData.vehicleId,
          inspectorPartyId: partyId,
          inspectionType: handoverData.handingOver ? 'PRE_HANDOVER' : 'POST_HANDOVER',
          odometer: handoverData.odometer,
          fuelLevel: handoverData.fuel_level,
          scratches: handoverData.scratches,
          dents: handoverData.dents,
          tires: handoverData.tires,
          lights: handoverData.lights,
          cleanliness: handoverData.cleanliness,
          seats: handoverData.seats,
          dashboardControls: handoverData.dashboard_controls,
          odors: handoverData.odors,
          overallCondition: ConditionEnhanced.GOOD, // Default to good, could be calculated
          knownIssues: handoverData.known_issues,
          inspectionCompletedAt: new Date().toISOString(),
        })
        .returning({ id: vehicleInspectionsImmutable.id });

      // 2. Update task status to completed
      await tx
        .update(tasks)
        .set({
          status: TaskStatusEnum.COMPLETED,
          completedAt: new Date().toISOString(),
          completedBy: partyId,
        })
        .where(eq(tasks.id, taskId));

      return {
        taskId,
        inspectionId: inspection.id,
        completedBy: partyId
      };
    });

    console.log("✅ Vehicle handover task completed successfully:", result);

    return {
      success: true,
      message: `Vehicle ${handoverData.handingOver ? 'handover' : 'receipt'} inspection completed`,
      taskId: result.taskId,
      inspectionId: result.inspectionId,
      completedBy: result.completedBy
    };

  } catch (error: any) {
    console.error("❌ Error completing vehicle handover task:", error);
    return {
      success: false,
      error: error.message || "Failed to complete handover task"
    };
  }
} 