"use server";

import {
  getListingByIdDrizzle,
  updateListingDrizzle,
  endListingDrizzle,
  type ListingRead,
} from "@/drizzle-actions/listings";
import type {
  ListingCreate,
  ConditionEnum,
  AudienceEnum,
} from "@/types/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

interface UpdateListingRequest {
  listingId: number;
  askingPrice: number;
  fraction?: number;
  effectiveFrom: string;
  effectiveTo: string;
  condition: ConditionEnum;
  mileage?: number;
  audience: AudienceEnum;
}

// Update listing with ownership verification
export async function updateListing(
  request: UpdateListingRequest
): Promise<{ success: boolean; message: string; data?: ListingRead }> {
  try {
    // Get current user's party ID
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    
    if (!dbId) {
      return {
        success: false,
        message: "User not authenticated or missing party ID",
      };
    }

    const partyId = parseInt(dbId);
    if (isNaN(partyId)) {
      return {
        success: false,
        message: "Invalid user party ID",
      };
    }

    // Verify listing ownership
    const existingListing = await getListingByIdDrizzle(request.listingId);
    if (!existingListing) {
      return {
        success: false,
        message: "Listing not found",
      };
    }

    if (existingListing.party_id !== partyId) {
      return {
        success: false,
        message: "You don't have permission to edit this listing",
      };
    }

    // Prepare update data
    const updateData: Partial<ListingCreate> = {
      asking_price: request.askingPrice,
      effective_from: request.effectiveFrom,
      effective_to: request.effectiveTo,
      condition: request.condition,
      audience: request.audience,
    };

    // Add optional fields
    if (request.fraction !== undefined) {
      updateData.fraction = request.fraction / 100; // Convert percentage to decimal
    }
    if (request.mileage !== undefined) {
      updateData.mileage = request.mileage;
    }

    console.log("Attempting to update listing with:", {
      listingId: request.listingId,
      updateData
    });

    // Update the listing
    const updatedListing = await updateListingDrizzle(request.listingId, updateData);

    console.log("Listing updated successfully:", updatedListing.id);

    return {
      success: true,
      message: "Listing updated successfully",
      data: updatedListing,
    };

  } catch (error) {
    console.error("Error updating listing:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to update listing",
    };
  }
}

// End listing with ownership verification
export async function endListing(
  listingId: number
): Promise<{ success: boolean; message: string }> {
  try {
    // Get current user's party ID
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    
    if (!dbId) {
      return {
        success: false,
        message: "User not authenticated or missing party ID",
      };
    }

    const partyId = parseInt(dbId);
    if (isNaN(partyId)) {
      return {
        success: false,
        message: "Invalid user party ID",
      };
    }

    // Verify listing ownership
    const existingListing = await getListingByIdDrizzle(listingId);
    if (!existingListing) {
      return {
        success: false,
        message: "Listing not found",
      };
    }

    if (existingListing.party_id !== partyId) {
      return {
        success: false,
        message: "You don't have permission to end this listing",
      };
    }

    // Check if listing is already ended
    const currentDate = new Date();
    
    if (existingListing.effective_to) {
      const effectiveToDate = new Date(existingListing.effective_to);
      if (effectiveToDate <= currentDate) {
        return {
          success: false,
          message: "This listing has already ended",
        };
      }
    }

    // End the listing
    await endListingDrizzle(listingId);

    return {
      success: true,
      message: "Listing ended successfully",
    };

  } catch (error) {
    console.error("Error ending listing:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to end listing",
    };
  }
} 