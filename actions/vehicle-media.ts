"use server";

import { revalidatePath } from "next/cache";
import { eq, and } from "drizzle-orm";
import { db } from "@/db";
import { vehicleMedia } from "@/drizzle/schema";
import type {
  VehicleMediaBase,
  VehicleMediaCreate,
  VehicleMediaRead,
} from "@/types/vehicles";
import { apiErrorFormater } from "@/lib/utils";

export async function createVehicleMedia(
  vehicleMediaData: VehicleMediaCreate
): Promise<VehicleMediaRead> {
  try {
    const result = await db
      .insert(vehicleMedia)
      .values({
        vehicleId: vehicleMediaData.vehicle_id,
        mediaPath: vehicleMediaData.media_path,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      });

    if (result.length === 0) {
      throw new Error("Failed to create vehicle media");
    }

    const media = result[0];
    return {
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt || "",
    };
  } catch (error) {
    console.error("Error creating vehicle media:", error);
    throw error;
  }
}

export async function getVehicleMediaById(
  mediaId: number
): Promise<VehicleMediaRead> {
  try {
    const result = await db
      .select({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .where(eq(vehicleMedia.id, mediaId))
      .limit(1);

    if (result.length === 0) {
      throw new Error(`Vehicle media with ID ${mediaId} not found`);
    }

    const media = result[0];
    return {
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt || "",
    };
  } catch (error) {
    console.error("Error fetching vehicle media by ID:", error);
    throw error;
  }
}

export async function getAllVehicleMedia(): Promise<VehicleMediaRead[]> {
  try {
    const result = await db
      .select({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .orderBy(vehicleMedia.createdAt);

    return result.map(media => ({
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt || "",
    }));
  } catch (error) {
    console.error("Error fetching all vehicle media:", error);
    throw error;
  }
}

export async function getVehicleMediaByVehicleId(
  vehicleId: number
): Promise<VehicleMediaRead[]> {
  try {
    const result = await db
      .select({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .where(eq(vehicleMedia.vehicleId, vehicleId))
      .orderBy(vehicleMedia.createdAt);

    return result.map(media => ({
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt || "",
    }));
  } catch (error) {
    console.error("Error fetching vehicle media by vehicle ID:", error);
    throw error;
  }
}

export async function updateVehicleMedia(
  mediaId: number,
  vehicleMediaData: VehicleMediaBase
): Promise<VehicleMediaRead> {
  try {
    const result = await db
      .update(vehicleMedia)
      .set({
        vehicleId: vehicleMediaData.vehicle_id,
        mediaPath: vehicleMediaData.media_path,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(vehicleMedia.id, mediaId))
      .returning({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      });

    if (result.length === 0) {
      throw new Error(`Vehicle media with ID ${mediaId} not found`);
    }

    const media = result[0];
    return {
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt || "",
    };
  } catch (error) {
    console.error("Error updating vehicle media:", error);
    throw error;
  }
}

export async function deleteVehicleMedia(
  mediaId: number
): Promise<{ message: string }> {
  try {
    const result = await db
      .delete(vehicleMedia)
      .where(eq(vehicleMedia.id, mediaId))
      .returning({ id: vehicleMedia.id });

    if (result.length === 0) {
      throw new Error(`Vehicle media with ID ${mediaId} not found`);
    }

    return { message: "Vehicle media deleted successfully" };
  } catch (error) {
    console.error("Error deleting vehicle media:", error);
    throw error;
  }
}

export async function deleteVehicleDocument(id: number): Promise<void> {
  try {
    await deleteVehicleMedia(id);
  } catch (error) {
    console.error("Error deleting vehicle document:", error);
    throw error;
  }
}

export async function addVehicleMedia(_: any, formData: FormData) {
  try {
    const vehicleID = Number(formData.get("vehicleID"));
    const documentImageUrl = (formData.get("documentUrl") as string)?.trim();

    const errors = [];

    if (!vehicleID) errors.push("Select a vehicle");
    if (!documentImageUrl) errors.push("File required");

    if (errors.length > 0) {
      return { success: false, message: errors.join(", ") };
    }

    const vehicleDocument = {
      vehicle_id: vehicleID,
      media_path: documentImageUrl,
    } as VehicleMediaCreate;

    await createVehicleMedia(vehicleDocument);
    revalidatePath("/vehicle-dashboard");

    return { success: true, message: "Image uploaded successfully." };
  } catch (error: any) {
    return apiErrorFormater(error);
  }
}
