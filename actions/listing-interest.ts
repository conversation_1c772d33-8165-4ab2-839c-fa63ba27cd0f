"use server";

import { 
  createListingInterestExpressionDrizzle, 
  deleteListingInterestExpressionDrizzle,
  checkUserInterestDrizzle,
  getListingInterestExpressionsDrizzle,
  type ListingInterestExpressionRead 
} from "@/drizzle-actions/listing-interest";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Get user's party ID from authentication
async function getCurrentUserPartyId(): Promise<number> {
  try {
    const userAttributes = await getUserAttributes();
    console.log("User attributes:", userAttributes); // Debug logging
    
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    
    if (!userAttributes) {
      throw new Error("Failed to get user attributes - user may not be authenticated");
    }
    
    if (!dbId) {
      console.error("Missing custom:db_id in user attributes:", userAttributes);
      throw new Error("User missing party ID (custom:db_id) - user may need to complete setup");
    }

    const partyId = parseInt(dbId);
    if (isNaN(partyId)) {
      console.error("Invalid party ID format:", dbId);
      throw new Error(`Invalid user party ID format: ${dbId}`);
    }

    console.log("Successfully got party ID:", partyId); // Debug logging
    return partyId;
  } catch (error) {
    console.error("Error in getCurrentUserPartyId:", error);
    throw error;
  }
}

// Express interest in a listing
export async function expressInterestInListing(listingId: number): Promise<void> {
  try {
    const partyId = await getCurrentUserPartyId();
    await createListingInterestExpressionDrizzle({ listingId, partyId });
  } catch (error) {
    console.error("Error expressing interest:", error);
    throw error;
  }
}

// Remove interest from a listing
export async function removeInterestFromListing(listingId: number): Promise<void> {
  try {
    const partyId = await getCurrentUserPartyId();
    await deleteListingInterestExpressionDrizzle(listingId, partyId);
  } catch (error) {
    console.error("Error removing interest:", error);
    throw error;
  }
}

// Check if current user has expressed interest
export async function checkCurrentUserInterest(listingId: number): Promise<boolean> {
  try {
    const partyId = await getCurrentUserPartyId();
    return await checkUserInterestDrizzle(listingId, partyId);
  } catch (error) {
    console.error("Error checking user interest:", error);
    return false;
  }
}

// Get all interest expressions for a listing (for listing authors)
export async function getListingInterestExpressions(listingId: number): Promise<ListingInterestExpressionRead[]> {
  try {
    return await getListingInterestExpressionsDrizzle(listingId);
  } catch (error) {
    console.error("Error fetching interest expressions:", error);
    throw error;
  }
} 