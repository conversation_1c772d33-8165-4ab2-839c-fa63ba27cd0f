'use server';

import { getFormSubmissions, type FormSubmission, type FormType } from '@/db/queries';

export interface WordCloudWord {
  word: string;
  frequency: number;
  weight: number;
  category?: string;
}

export interface WordCloudPhrase {
  phrase: string;
  wordCount: number;
  frequency: number;
  weight: number;
}

export interface WordCloudAnalysis {
  id: number;
  formType?: FormType | 'all';
  analysisType: string;
  fieldName?: string;
  totalSubmissions: number;
  words: WordCloudWord[];
  phrases: WordCloudPhrase[];
  createdAt: Date;
  status: 'processing' | 'completed' | 'failed';
}

// Common stop words to exclude
const STOP_WORDS = new Set([
  // Articles & Prepositions
  'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
  'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
  'between', 'among', 'under', 'over', 'around', 'near', 'beside', 'beyond', 'within',
  
  // Pronouns & Possessives
  'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves', 'you', 'your', 'yours',
  'yourself', 'yourselves', 'he', 'him', 'his', 'himself', 'she', 'her', 'hers', 'herself',
  'it', 'its', 'itself', 'they', 'them', 'their', 'theirs', 'themselves', 'what', 'which',
  'who', 'whom', 'this', 'that', 'these', 'those',
  
  // Verbs & Auxiliaries
  'am', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'having',
  'do', 'does', 'did', 'doing', 'would', 'should', 'could', 'will', 'shall', 'may', 'might', 
  'must', 'can', 'cannot', 'cant', 'get', 'got', 'getting', 'want', 'wants', 'wanted',
  'need', 'needs', 'needed', 'like', 'likes', 'liked', 'love', 'loves', 'loved',
  
  // Common connectors & transitions
  'because', 'since', 'although', 'though', 'however', 'therefore', 'thus', 'hence',
  'meanwhile', 'moreover', 'furthermore', 'nevertheless', 'otherwise', 'instead',
  
  // Quantifiers & Adjectives
  'yes', 'no', 'not', 'very', 'much', 'many', 'more', 'most', 'some', 'any', 'all', 'each',
  'every', 'other', 'another', 'same', 'different', 'new', 'old', 'first', 'last', 'next',
  'previous', 'good', 'bad', 'big', 'small', 'high', 'low', 'long', 'short', 'wide', 'narrow',
  'great', 'little', 'large', 'best', 'better', 'worse', 'worst', 'right', 'wrong', 'true', 'false',
  
  // Common conversation words
  'please', 'thank', 'thanks', 'hello', 'hi', 'hey', 'ok', 'okay', 'sure', 'well', 'now', 'then',
  'here', 'there', 'where', 'when', 'why', 'how', 'just', 'only', 'also', 'too', 'even', 'still',
  'again', 'back', 'down', 'out', 'off', 'away', 'than', 'such', 'both', 'either', 'neither',
  
  // Form-specific common words
  'form', 'submit', 'click', 'button', 'link', 'page', 'website', 'site', 'email', 'phone',
  'name', 'address', 'contact', 'info', 'information', 'details', 'enter', 'fill', 'complete'
]);

// Business-specific stop words
const BUSINESS_STOP_WORDS = new Set([
  // Corporate terms
  'ltd', 'llc', 'inc', 'corp', 'company', 'business', 'service', 'services', 'solution', 'solutions',
  
  // Vehicle industry generic terms
  'car', 'cars', 'drive', 'driver', 'driving', 'drivers', 'license', 
  'vehicle', 'fleet', 'rental', 'rent',
  
  // Financial/business process terms
  'income', 'payment', 'option', 'interest', 'ownership', 'applicant', 'work', 'working',
  
  // Form/process related terms
  'papers', 'message', 'mobile', 'switchreason', 'looking', 'own', 'use',
  
  // Time-related generic terms
  'week', 'weekly',
]);

// Text preprocessing utility
function preprocessText(text: string): string[] {
  if (!text || typeof text !== 'string') return [];
  
  return text
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Remove punctuation
    .replace(/\d+/g, '') // Remove numbers
    .split(/\s+/)
    .filter(word => {
      return word.length >= 3 && 
             !STOP_WORDS.has(word) && 
             !BUSINESS_STOP_WORDS.has(word) &&
             !word.match(/^(http|www|com|org|net)/) // Remove web-related terms
    });
}

// Extract text from form data JSON
function extractTextFromFormData(formData: any): string[] {
  const allText: string[] = [];
  
  if (!formData || typeof formData !== 'object') return allText;
  
  function extractRecursive(obj: any): void {
    if (typeof obj === 'string') {
      const words = preprocessText(obj);
      allText.push(...words);
    } else if (Array.isArray(obj)) {
      obj.forEach(item => extractRecursive(item));
    } else if (obj && typeof obj === 'object') {
      Object.values(obj).forEach(value => extractRecursive(value));
    }
  }
  
  extractRecursive(formData);
  return allText;
}

// Generate n-grams (phrases)
function generateNGrams(words: string[], n: number): string[] {
  const ngrams: string[] = [];
  for (let i = 0; i <= words.length - n; i++) {
    ngrams.push(words.slice(i, i + n).join(' '));
  }
  return ngrams;
}

// Categorize words based on patterns
function categorizeWord(word: string): string {
  const categories = {
    location: /^(city|state|country|region|area|zone|district|suburb|town|village|street|road|avenue|drive|lane|boulevard)$/,
    business: /^(revenue|profit|income|salary|budget|cost|price|investment|funding|capital|finance|money|cash|dollar|payment|fee)$/,
    action: /^(buy|sell|purchase|rent|lease|hire|employ|manage|operate|drive|deliver|transport|move|start|begin|launch|create|build|develop)$/,
    time: /^(day|week|month|year|daily|weekly|monthly|yearly|morning|afternoon|evening|night|today|tomorrow|yesterday|soon|later|now|schedule)$/,
    quantity: /^(one|two|three|four|five|few|many|several|multiple|single|double|triple|half|quarter|dozen|hundred|thousand|million)$/,
    vehicle: /^(car|vehicle|truck|van|bus|motorcycle|bike|auto|automobile|fleet|uber|lyft|taxi|cab)$/
  };
  
  for (const [category, pattern] of Object.entries(categories)) {
    if (pattern.test(word)) {
      return category;
    }
  }
  
  return 'general';
}

// Main word cloud analysis function
export async function generateWordCloudAnalysis(options: {
  formTypes?: FormType[] | 'all';
  fieldNames?: string[];
  dateRangeStart?: Date;
  dateRangeEnd?: Date;
  maxWords?: number;
  includesPhrases?: boolean;
}): Promise<WordCloudAnalysis> {
  
  const {
    formTypes = 'all',
    fieldNames,
    dateRangeStart,
    dateRangeEnd,
    maxWords = 100,
    includesPhrases = true
  } = options;

  // Fetch submissions based on criteria
  const submissions = await getFormSubmissions({ 
    limit: 5000,
    // Add date filtering if provided
  });

  // Filter submissions by form type and date range
  let filteredSubmissions = submissions;
  
  if (formTypes !== 'all') {
    filteredSubmissions = filteredSubmissions.filter(s => 
      formTypes.includes(s.formType as FormType)
    );
  }
  
  if (dateRangeStart && dateRangeEnd) {
    filteredSubmissions = filteredSubmissions.filter(s => {
      const submissionDate = new Date(s.submittedAt);
      return submissionDate >= dateRangeStart && submissionDate <= dateRangeEnd;
    });
  }

  // Extract all text
  const allWords: string[] = [];
  const allTexts: string[] = [];
  
  filteredSubmissions.forEach(submission => {
    // Extract from structured fields
    if (submission.fullName) {
      const nameWords = preprocessText(submission.fullName);
      allWords.push(...nameWords);
    }
    
    if (submission.companyName) {
      const companyWords = preprocessText(submission.companyName);
      allWords.push(...companyWords);
    }
    
    // Extract from form data JSON
    if (fieldNames) {
      // Extract specific fields
      fieldNames.forEach(fieldName => {
        const fieldValue = submission.formData?.[fieldName];
        if (fieldValue) {
          const fieldWords = extractTextFromFormData(fieldValue);
          allWords.push(...fieldWords);
          if (typeof fieldValue === 'string') {
            allTexts.push(fieldValue);
          }
        }
      });
    } else {
      // Extract all text from form data
      const formWords = extractTextFromFormData(submission.formData);
      allWords.push(...formWords);
      
      // Collect full text for phrase analysis
      Object.values(submission.formData || {}).forEach(value => {
        if (typeof value === 'string') {
          allTexts.push(value);
        }
      });
    }
  });

  // Calculate word frequencies
  const wordFreq: Record<string, number> = {};
  allWords.forEach(word => {
    wordFreq[word] = (wordFreq[word] || 0) + 1;
  });

  // Sort and limit words
  const sortedWords = Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, maxWords);

  const maxFrequency = sortedWords[0]?.[1] || 1;

  // Create word objects with weights and categories
  const words: WordCloudWord[] = sortedWords.map(([word, frequency]) => ({
    word,
    frequency,
    weight: frequency / maxFrequency,
    category: categorizeWord(word)
  }));

  // Generate phrases if requested
  let phrases: WordCloudPhrase[] = [];
  if (includesPhrases && allTexts.length > 0) {
    const phraseFreq: Record<string, number> = {};
    
    allTexts.forEach(text => {
      const textWords = preprocessText(text);
      if (textWords.length >= 2) {
        // Generate bigrams and trigrams
        const bigrams = generateNGrams(textWords, 2);
        const trigrams = generateNGrams(textWords, 3);
        
        [...bigrams, ...trigrams].forEach(phrase => {
          phraseFreq[phrase] = (phraseFreq[phrase] || 0) + 1;
        });
      }
    });

    const sortedPhrases = Object.entries(phraseFreq)
      .filter(([, freq]) => freq >= 2) // Only include phrases that appear at least twice
      .sort(([,a], [,b]) => b - a)
      .slice(0, 20); // Limit to top 20 phrases

    const maxPhraseFreq = sortedPhrases[0]?.[1] || 1;

    phrases = sortedPhrases.map(([phrase, frequency]) => ({
      phrase,
      wordCount: phrase.split(' ').length,
      frequency,
      weight: frequency / maxPhraseFreq
    }));
  }

  // Create analysis result
  const analysis: WordCloudAnalysis = {
    id: Date.now(), // Temporary ID - in real implementation, save to database
    formType: formTypes === 'all' ? 'all' : formTypes[0],
    analysisType: fieldNames ? 'specific_field' : 'full_text',
    fieldName: fieldNames?.[0],
    totalSubmissions: filteredSubmissions.length,
    words,
    phrases,
    createdAt: new Date(),
    status: 'completed'
  };

  return analysis;
}

// Get saved word cloud analyses
export async function getWordCloudAnalyses(): Promise<WordCloudAnalysis[]> {
  // In real implementation, fetch from database
  // For now, return empty array
  return [];
}

// Generate word cloud for specific presets
export async function generatePresetWordCloud(presetName: string): Promise<WordCloudAnalysis> {
  const presets = {
    'all_leads': {
      formTypes: 'all' as const,
      maxWords: 50
    },
    'business_focus': {
      formTypes: ['business'] as FormType[],
      maxWords: 75
    },
    'investment_interest': {
      formTypes: ['co-own'] as FormType[],
      maxWords: 60
    },
    'service_requests': {
      formTypes: ['management', 'rideshare'] as FormType[],
      maxWords: 40
    }
  };

  const preset = presets[presetName as keyof typeof presets];
  if (!preset) {
    throw new Error(`Unknown preset: ${presetName}`);
  }

  return generateWordCloudAnalysis(preset);
} 