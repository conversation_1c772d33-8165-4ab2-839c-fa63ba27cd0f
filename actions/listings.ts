"use server";

import { getUserAttributes } from "@/lib/serverUserAttributes";
import { db } from "@/db";
import { eq, and, desc, inArray, sql } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  vehicleMedia,
} from "@/drizzle/schema";

// Import the h_schema tables
import {
  h_listings,
  h_listing_approval_status,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";

export async function getUserListingsAction() {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes || !userAttributes.sub) {
      return {
        success: false,
        error: "User not authenticated",
      };
    }

    // Get the user's party ID from custom attributes
    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return {
        success: false,
        error: "User party ID not found",
      };
    }

    // 🔧 FIXED: Get user's listings with LATEST approval and publish status only
    const userListings = await db
      .select({
        id: h_listings.id,
        partyId: h_listings.partyId,
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,
        effectiveFrom: h_listings.effectiveFrom,
        effectiveTo: h_listings.effectiveTo,
        // Get latest approval status
        approvalStatus: h_listing_approval_status.status,
        approvalStatusAt: h_listing_approval_status.statusAt,
        approvalStatusReason: h_listing_approval_status.reason,
        // Get latest publish status
        publishStatus: h_listing_publish_status.status,
        publishStatusAt: h_listing_publish_status.statusAt,
      })
      .from(h_listings)
      .leftJoin(
        h_listing_approval_status,
        sql`${h_listings.id} = ${h_listing_approval_status.listingId} AND ${h_listing_approval_status.id} = (
          SELECT id FROM h_listing_approval_status AS las 
          WHERE las.listing_id = ${h_listings.id} 
          ORDER BY las.status_at DESC 
          LIMIT 1
        )`
      )
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(eq(h_listings.partyId, parseInt(partyId)))
      .orderBy(desc(h_listings.effectiveFrom));

    // Get vehicle details for vehicle-type listings
    const vehicleListings = userListings.filter(
      (listing) => listing.sourceType === "vehicle"
    );
    const vehicleIds = vehicleListings.map((listing) => listing.sourceId);

    let vehicleDetails: any[] = [];
    let vehicleMediaRecords: any[] = [];

    if (vehicleIds.length > 0) {
      // Get vehicle details
      vehicleDetails = await db
        .select({
          id: vehicles.id,
          vinNumber: vehicles.vinNumber,
          vehicleRegistration: vehicles.vehicleRegistration,
          color: vehicles.color,
          manufacturingYear: vehicles.manufacturingYear,
          modelId: vehicles.modelId,
          make: vehicleMake.name,
          model: vehicleModel.model,
        })
        .from(vehicles)
        .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(inArray(vehicles.id, vehicleIds));

      // Get vehicle media
      vehicleMediaRecords = await db
        .select({
          id: vehicleMedia.id,
          vehicleId: vehicleMedia.vehicleId,
          mediaPath: vehicleMedia.mediaPath,
          createdAt: vehicleMedia.createdAt,
        })
        .from(vehicleMedia)
        .where(inArray(vehicleMedia.vehicleId, vehicleIds));
    }

    // Group vehicle media by vehicle ID
    const mediaByVehicleId = vehicleMediaRecords.reduce(
      (acc, media) => {
        if (!acc[media.vehicleId]) {
          acc[media.vehicleId] = [];
        }
        acc[media.vehicleId].push({
          id: media.id,
          vehicle_id: media.vehicleId,
          media_path: media.mediaPath,
          created_at: media.createdAt || "",
        });
        return acc;
      },
      {} as Record<number, any[]>
    );

    // For catalog listings, we would need to get from h_vehicleCatalog
    // but for now we'll focus on vehicle listings

    // Combine the data
    const enrichedListings = userListings.map((listing) => {
      let vehicle = null;
      let media: any[] = [];

      if (listing.sourceType === "vehicle") {
        vehicle = vehicleDetails.find((v) => v.id === listing.sourceId);
        // Get vehicle media for this listing
        media = mediaByVehicleId[listing.sourceId] || [];
      }

      // Parse listing details JSON
      let parsedDetails = {};
      try {
        parsedDetails = JSON.parse(listing.listingDetails);
      } catch (e) {
        console.error("Error parsing listing details:", e);
      }

      return {
        ...listing,
        vehicle,
        media,
        listingDetails: parsedDetails,
        // Use actual approval status or default to pending
        latestDecision: {
          decision: listing.approvalStatus || "pending",
          reason: listing.approvalStatusReason || null,
          decisionAt: listing.approvalStatusAt || listing.effectiveFrom,
        },
        // Add publish status information
        publishStatus: listing.publishStatus || null,
        publishStatusAt: listing.publishStatusAt || null,
        documents: [], // Placeholder for documents
      };
    });

    return {
      success: true,
      listings: enrichedListings,
    };
  } catch (error) {
    console.error("Error fetching user listings:", error);
    return {
      success: false,
      error: "Failed to fetch listings",
    };
  }
}
