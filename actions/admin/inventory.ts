"use server";

import { db } from "@/db";
import { eq, and, desc, sql, inArray } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
  party,
  inventory,
  individual,
} from "@/drizzle/schema";
import type { InventoryVehicle } from "@/app/(admin)/admin/types/inventory";

// ✅ Fixed: Use correct InventoryVehicle properties
export async function createInventoryVehicle(
  vehicleData: InventoryVehicle
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Validate required fields
    if (!vehicleData.vinNumber) {
      return { success: false, error: "VIN number is required" };
    }

    // Check if VIN already exists
    const existingVehicle = await db
      .select({ id: vehicles.id })
      .from(vehicles)
      .where(eq(vehicles.vinNumber, vehicleData.vinNumber))
      .limit(1);

    if (existingVehicle.length > 0) {
      return {
        success: false,
        error: "A vehicle with this VIN number already exists",
      };
    }

    // Find the model ID based on make/model names
    let modelId: number | null = null;

    if (vehicleData.make && vehicleData.model) {
      const modelResult = await db
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(
          and(
            eq(vehicleMake.name, vehicleData.make),
            eq(vehicleModel.model, vehicleData.model)
          )
        )
        .limit(1);

      if (modelResult.length > 0) {
        modelId = modelResult[0].id;
      }
    }

    if (!modelId) {
      return {
        success: false,
        error: `Vehicle model not found for ${vehicleData.make} ${vehicleData.model}`,
      };
    }

    // Create vehicle AND add to inventory in single transaction
    const result = await db.transaction(async (tx) => {
      // 1. Create the actual vehicle record
      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: adminPartyId,
          modelId: modelId,
          vinNumber: vehicleData.vinNumber,
          vehicleRegistration: vehicleData.registrationNumber || null,
          countryId: 1,
          manufacturingYear: vehicleData.year || null,
          purchaseDate: new Date().toISOString(),
          color: vehicleData.color || null,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      const vehicleId = newVehicle[0].id;

      // 2. Add vehicle images if provided
      if (vehicleData.images && vehicleData.images.length > 0) {
        const mediaInserts = vehicleData.images.map((image) => ({
          vehicleId: vehicleId,
          mediaPath: image.imageUrl,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(mediaInserts);
        console.log(
          `✅ Inserted ${mediaInserts.length} vehicle media records for inventory vehicle ${vehicleId}`
        );
      }

      // 3. ✅ CREATE: inventory record with catalogId
      const inventoryRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          catalogId: vehicleData.catalogId, // ✅ Store the catalog reference
          status: "available",
          location: vehicleData.location || null,
          notes: vehicleData.notes || "Added to inventory",
          effectiveFrom: new Date().toISOString(),
          // effectiveTo defaults to infinity
        })
        .returning();

      return {
        vehicleId,
        vehicle: newVehicle[0],
        inventoryRecord: inventoryRecord[0],
      };
    });

    console.log(
      `✅ Created vehicle ${result.vehicleId} and added to inventory`
    );

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Error creating inventory vehicle:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to create inventory vehicle",
    };
  }
}

// ✅ Fixed: Update inventory vehicle using correct properties
export async function updateInventoryVehicle(
  vehicleId: number,
  vehicleData: InventoryVehicle
): Promise<{
  success: boolean;
  data?: any;
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Verify vehicle exists and belongs to admin
    const existingVehicle = await db
      .select({
        id: vehicles.id,
        partyId: vehicles.partyId,
      })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (existingVehicle.length === 0) {
      return { success: false, error: "Vehicle not found" };
    }

    if (existingVehicle[0].partyId !== adminPartyId) {
      return { success: false, error: "Not authorized to update this vehicle" };
    }

    // Find the model ID if make/model provided
    let modelId: number | undefined = undefined;

    if (vehicleData.make && vehicleData.model) {
      const modelResult = await db
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(
          and(
            eq(vehicleMake.name, vehicleData.make),
            eq(vehicleModel.model, vehicleData.model)
          )
        )
        .limit(1);

      if (modelResult.length > 0) {
        modelId = modelResult[0].id;
      }
    }

    // Update vehicle in transaction
    const result = await db.transaction(async (tx) => {
      const updateData: any = {
        updatedAt: new Date().toISOString(),
      };

      // Only update fields that are provided
      if (modelId) updateData.modelId = modelId;
      if (vehicleData.vinNumber) updateData.vinNumber = vehicleData.vinNumber;
      if (vehicleData.registrationNumber)
        updateData.vehicleRegistration = vehicleData.registrationNumber;
      if (vehicleData.year) updateData.manufacturingYear = vehicleData.year;
      if (vehicleData.color) updateData.color = vehicleData.color;

      const updatedVehicle = await tx
        .update(vehicles)
        .set(updateData)
        .where(eq(vehicles.id, vehicleId))
        .returning();

      // Update vehicle images if provided
      if (vehicleData.images !== undefined) {
        console.log(
          "🔄 Updating images for inventory vehicle:",
          vehicleId,
          "New images:",
          vehicleData.images
        );

        // Delete existing images
        await tx
          .delete(vehicleMedia)
          .where(eq(vehicleMedia.vehicleId, vehicleId));
        console.log("🗑️  Deleted existing images for vehicle:", vehicleId);

        // Insert new images
        if (vehicleData.images.length > 0) {
          const mediaInserts = vehicleData.images.map((image) => ({
            vehicleId: vehicleId,
            mediaPath: image.imageUrl,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          }));

          await tx.insert(vehicleMedia).values(mediaInserts);
          console.log(
            `✅ Inserted ${mediaInserts.length} updated vehicle media records for inventory vehicle ${vehicleId}`
          );
        } else {
          console.log("ℹ️  No new images to insert for vehicle:", vehicleId);
        }
      }

      return {
        vehicleId,
        vehicle: updatedVehicle[0],
      };
    });

    console.log(`✅ Updated inventory vehicle ${vehicleId}`);

    return {
      success: true,
      data: result,
    };
  } catch (error) {
    console.error("Error updating inventory vehicle:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update inventory vehicle",
    };
  }
}

// Add vehicle to inventory (creates initial record)
export async function addVehicleToInventory(
  vehicleId: number,
  location?: string,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Check if vehicle is already in active inventory
    const existingInventory = await db
      .select({ id: inventory.id })
      .from(inventory)
      .where(
        and(
          eq(inventory.vehicleId, vehicleId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .limit(1);

    if (existingInventory.length > 0) {
      return { success: false, error: "Vehicle is already in inventory" };
    }

    const result = await db
      .insert(inventory)
      .values({
        vehicleId,
        adminPartyId,
        status: "available",
        location: location || null,
        notes: notes || null,
        effectiveFrom: new Date().toISOString(),
        // effectiveTo defaults to infinity
      })
      .returning();

    return { success: true, data: result[0] };
  } catch (error) {
    console.error("Error adding vehicle to inventory:", error);
    return { success: false, error: "Failed to add vehicle to inventory" };
  }
}

// Update vehicle status (creates new immutable record)
export async function updateVehicleInventoryStatus(
  vehicleId: number,
  newStatus: "available" | "assigned" | "maintenance" | "inspection",
  assignedToPartyId?: number,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with updated status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: newStatus,
          assignedToPartyId: assignedToPartyId || null,
          location: currentRecord[0].location, // Inherit location
          notes: notes || `Status changed to ${newStatus}`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(`✅ Updated vehicle ${vehicleId} status to ${newStatus}`);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating vehicle inventory status:", error);
    return { success: false, error: "Failed to update vehicle status" };
  }
}

// Get inventory vehicles (admin-owned vehicles)
export async function getInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // ✅ FIXED: Query from inventory table and join with vehicles
    const inventoryVehicles = await db
      .select({
        id: vehicles.id,
        catalogId: inventory.catalogId, // ✅ Get catalogId from inventory table
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        status: inventory.status, // ✅ Get status from inventory
        location: inventory.location, // ✅ Get location from inventory
        // Vehicle model info
        makeId: vehicleMake.id,
        make: vehicleMake.name,
        modelId: vehicleModel.id,
        model: vehicleModel.model,
      })
      .from(inventory) // ✅ Start from inventory table
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp` // Only active records
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    // Transform to match InventoryVehicle interface
    const transformedVehicles = inventoryVehicles.map((vehicle) => ({
      id: vehicle.id?.toString() || "",
      catalogId: vehicle.catalogId || "", // ✅ Now this exists!
      make: vehicle.make || "Unknown",
      model: vehicle.model || "Unknown",
      year: vehicle.manufacturingYear || 0,
      color: vehicle.color || "Unknown",
      vinNumber: vehicle.vinNumber || "Unknown",
      registrationNumber: vehicle.vehicleRegistration || "Unknown",
      mileage: 0,
      condition: "used" as const,
      status: vehicle.status || "available",
      location: vehicle.location || "Unknown",
      vehicleDocuments: [],
      images: [], // TODO: Implement image fetching
      lastInspection: undefined,
      nextService: undefined,
      createdAt: vehicle.createdAt || new Date().toISOString(),
      notes: `Inventory vehicle`,
    }));

    return {
      success: true,
      data: transformedVehicles,
    };
  } catch (error) {
    console.error("Error fetching inventory vehicles:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch inventory vehicles",
    };
  }
}

// Get current inventory (only active records)
export async function getCurrentInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const inventoryVehicles = await db
      .select({
        inventoryId: inventory.id,
        vehicleId: inventory.vehicleId,
        status: inventory.status,
        assignedToPartyId: inventory.assignedToPartyId,
        location: inventory.location,
        notes: inventory.notes,
        effectiveFrom: inventory.effectiveFrom,
        // Vehicle details
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        // Make/Model info
        make: vehicleMake.name,
        model: vehicleModel.model,
        // Assigned driver info
        assignedDriverName: sql<string>`CONCAT(${individual.firstName}, ' ', ${individual.lastName})`,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(inventory.assignedToPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp` // Only current records
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: inventoryVehicles };
  } catch (error) {
    console.error("Error fetching current inventory:", error);
    return { success: false, error: "Failed to fetch inventory" };
  }
}

// Get available vehicles for assignment
export async function getAvailableVehiclesInventory(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const availableVehicles = await db
      .select({
        vehicleId: inventory.vehicleId,
        inventoryId: inventory.id,
        catalogId: inventory.catalogId, // ✅ Include catalogId
        make: vehicleMake.name,
        model: vehicleModel.model,
        year: vehicles.manufacturingYear,
        color: vehicles.color,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        location: inventory.location,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          eq(inventory.status, "available"),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: availableVehicles };
  } catch (error) {
    console.error("Error fetching available vehicles:", error);
    return { success: false, error: "Failed to fetch available vehicles" };
  }
}

// Get inventory stats
export async function getInventoryStats(): Promise<{
  success: boolean;
  data?: {
    total: number;
    available: number;
    assigned: number;
    maintenance: number;
  };
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const stats = await db
      .select({
        total: sql<number>`COUNT(*)`,
        available: sql<number>`COUNT(CASE WHEN status = 'available' THEN 1 END)`,
        assigned: sql<number>`COUNT(CASE WHEN status = 'assigned' THEN 1 END)`,
        maintenance: sql<number>`COUNT(CASE WHEN status = 'maintenance' THEN 1 END)`,
      })
      .from(inventory)
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      );

    return { success: true, data: stats[0] };
  } catch (error) {
    console.error("Error fetching inventory stats:", error);
    return { success: false, error: "Failed to fetch inventory stats" };
  }
}

// Mark vehicle as assigned
export async function markVehicleAsAssigned(
  vehicleId: number,
  assignedToPartyId: number,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with assigned status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: "assigned",
          assignedToPartyId: assignedToPartyId,
          location: currentRecord[0].location, // Inherit location
          notes: notes || `Vehicle assigned to party ${assignedToPartyId}`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(
      `✅ Marked vehicle ${vehicleId} as assigned to party ${assignedToPartyId}`
    );
    return { success: true, data: result };
  } catch (error) {
    console.error("Error marking vehicle as assigned:", error);
    return { success: false, error: "Failed to mark vehicle as assigned" };
  }
}

// Mark vehicle as available again
export async function markVehicleAsAvailable(
  vehicleId: number
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with available status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: "available",
          assignedToPartyId: null,
          location: currentRecord[0].location, // Inherit location
          notes: `Vehicle returned to inventory`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(`✅ Marked vehicle ${vehicleId} as available`);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error marking vehicle as available:", error);
    return { success: false, error: "Failed to mark vehicle as available" };
  }
}
