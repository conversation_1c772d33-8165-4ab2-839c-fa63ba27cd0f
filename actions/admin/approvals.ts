"use server";

import { approveListing, rejectListing } from "@/drizzle-actions/admin/listings";
import { revalidatePath } from "next/cache";

/**
 * Server action to approve a listing
 */
export async function approveListingAction(
  listingId: number,
  reason?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const result = await approveListing(listingId, reason);
    
    if (result.success) {
      // Revalidate the approvals pages to show updated data
      revalidatePath("/admin/approvals");
      revalidatePath(`/admin/approvals/${listingId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error in approveListingAction:", error);
    return { success: false, message: "Failed to approve listing" };
  }
}

/**
 * Server action to reject a listing
 */
export async function rejectListingAction(
  listingId: number,
  reason: string
): Promise<{ success: boolean; message: string }> {
  try {
    if (!reason.trim()) {
      return { success: false, message: "Rejection reason is required" };
    }

    const result = await rejectListing(listingId, reason);
    
    if (result.success) {
      // Revalidate the approvals pages to show updated data
      revalidatePath("/admin/approvals");
      revalidatePath(`/admin/approvals/${listingId}`);
    }
    
    return result;
  } catch (error) {
    console.error("Error in rejectListingAction:", error);
    return { success: false, message: "Failed to reject listing" };
  }
}
