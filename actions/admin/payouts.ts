"use server";

import { revalidatePath } from "next/cache";
import {
  calculatePayoutAmount,
  processDriverPayout,
  approveDriverPayout,
  rejectDriverPayout,
  getAssignmentPayouts,
  getPayoutsSummary,
  validatePayoutProcessing,
} from "@/drizzle-actions/admin/payouts";
import {
  PayoutProcessingFormSchema,
  DateRangeSchema,
} from "@/schemas/payment-contract";
import {
  PayoutRecord,
  PayoutProcessingInput,
  DebtRecord,
  PaymentError,
  PayoutSummary,
  PayoutCalculation,
} from "@/types/payment-contract";

// =====================================================
// PAYOUT CALCULATION ACTIONS
// =====================================================

/**
 * Server action to calculate payout amount for an assignment
 */
export async function calculatePayoutAmountAction(
  assignmentId: number,
  startDate: string,
  endDate: string
): Promise<{
  success: boolean;
  data?: PayoutCalculation;
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    if (!startDate || !endDate) {
      return {
        success: false,
        error: "Start and end dates are required",
      };
    }

    // Validate date range
    const dateRangeValidation = DateRangeSchema.safeParse({
      startDate,
      endDate,
    });

    if (!dateRangeValidation.success) {
      return {
        success: false,
        error: "Invalid date range",
      };
    }

    const result = await calculatePayoutAmount(assignmentId, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to calculate payout amount",
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [calculatePayoutAmountAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to calculate payout amount",
    };
  }
}

// =====================================================
// PAYOUT PROCESSING ACTIONS
// =====================================================

/**
 * Server action to process a driver payout
 */
export async function processDriverPayoutAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: PayoutRecord;
  debtsResolved?: DebtRecord[];
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const assignmentId = Number(formData.get("assignmentId"));
    const periodStart = formData.get("periodStart") as string;
    const periodEnd = formData.get("periodEnd") as string;
    const payoutAmount = Number(formData.get("payoutAmount"));
    const paymentMethod = formData.get("paymentMethod") as string;
    const paymentReference = formData.get("paymentReference") as string | null;
    const bankDetails = formData.get("bankDetails") as string | null;
    const notes = formData.get("notes") as string | null;
    const requiresApproval = formData.get("requiresApproval") === "true";

    // Basic validation
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (!periodStart || !periodEnd) {
      return {
        success: false,
        error: "Period dates are required",
        fieldErrors: {
          periodStart: !periodStart ? "Period start date is required" : "",
          periodEnd: !periodEnd ? "Period end date is required" : "",
        },
      };
    }

    if (isNaN(payoutAmount) || payoutAmount < 0) {
      return {
        success: false,
        error: "Invalid payout amount",
        fieldErrors: {
          payoutAmount: "Payout amount must be a non-negative number",
        },
      };
    }

    if (!paymentMethod) {
      return {
        success: false,
        error: "Payment method is required",
        fieldErrors: {
          paymentMethod: "Payment method is required",
        },
      };
    }

    // Prepare input
    const input: PayoutProcessingInput = {
      assignmentId,
      periodStart,
      periodEnd,
      payoutAmount,
      paymentMethod: paymentMethod as any,
      paymentReference: paymentReference || undefined,
      bankDetails: bankDetails || undefined,
      notes: notes || undefined,
      requiresApproval,
    };

    // Validate with Zod schema
    const validationResult = PayoutProcessingFormSchema.safeParse(input);
    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        error: "Please correct the form errors",
        fieldErrors,
      };
    }

    // Additional business validation
    const businessValidation = validatePayoutProcessing(input);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        error: "Validation failed",
        fieldErrors,
      };
    }

    // Process the payout
    const result = await processDriverPayout(input);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to process driver payout",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
      debtsResolved: result.debtsResolved,
    };
  } catch (error) {
    console.error("❌ [processDriverPayoutAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to process driver payout",
    };
  }
}

/**
 * Server action to approve a pending payout
 */
export async function approveDriverPayoutAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: PayoutRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const payoutId = Number(formData.get("payoutId"));
    const approvalNotes = formData.get("approvalNotes") as string | null;

    if (!payoutId || payoutId <= 0) {
      return {
        success: false,
        error: "Invalid payout ID",
        fieldErrors: {
          payoutId: "Payout ID is required",
        },
      };
    }

    const result = await approveDriverPayout(payoutId, approvalNotes || undefined);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to approve payout",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    if (result.data) {
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [approveDriverPayoutAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to approve driver payout",
    };
  }
}

/**
 * Server action to reject a pending payout
 */
export async function rejectDriverPayoutAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const payoutId = Number(formData.get("payoutId"));
    const rejectionReason = formData.get("rejectionReason") as string | null;

    if (!payoutId || payoutId <= 0) {
      return {
        success: false,
        error: "Invalid payout ID",
        fieldErrors: {
          payoutId: "Payout ID is required",
        },
      };
    }

    if (!rejectionReason?.trim()) {
      return {
        success: false,
        error: "Rejection reason is required",
        fieldErrors: {
          rejectionReason: "Please provide a reason for rejecting this payout",
        },
      };
    }

    const result = await rejectDriverPayout(payoutId, rejectionReason);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to reject payout",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [rejectDriverPayoutAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to reject driver payout",
    };
  }
}

// =====================================================
// PAYOUT RETRIEVAL ACTIONS
// =====================================================

/**
 * Server action to get payouts for an assignment
 */
export async function getAssignmentPayoutsAction(
  assignmentId: number,
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: PayoutRecord[];
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getAssignmentPayouts(assignmentId, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch assignment payouts",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getAssignmentPayoutsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch assignment payouts",
    };
  }
}

/**
 * Server action to get payout summaries for multiple assignments
 */
export async function getPayoutsSummaryAction(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: PayoutSummary[];
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getPayoutsSummary(assignmentIds, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch payouts summary",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getPayoutsSummaryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch payouts summary",
    };
  }
}

// =====================================================
// BULK PAYOUT OPERATIONS
// =====================================================

/**
 * Server action to process multiple payouts (bulk operation)
 */
export async function processBulkPayoutsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: {
    successful: PayoutRecord[];
    failed: Array<{ assignmentId: number; error: string }>;
    totalDebtsResolved: number;
  };
  error?: string;
}> {
  try {
    const bulkDataStr = formData.get("bulkData") as string;
    const requiresApproval = formData.get("requiresApproval") === "true";

    if (!bulkDataStr) {
      return {
        success: false,
        error: "Bulk data is required",
      };
    }

    let bulkData: PayoutProcessingInput[];
    try {
      bulkData = JSON.parse(bulkDataStr);
    } catch (parseError) {
      return {
        success: false,
        error: "Invalid bulk data format",
      };
    }

    if (!Array.isArray(bulkData) || bulkData.length === 0) {
      return {
        success: false,
        error: "Bulk data must be a non-empty array",
      };
    }

    // Process each payout
    const results = {
      successful: [] as PayoutRecord[],
      failed: [] as Array<{ assignmentId: number; error: string }>,
      totalDebtsResolved: 0,
    };

    for (const payoutInput of bulkData) {
      try {
        // Set approval requirement
        payoutInput.requiresApproval = requiresApproval;

        const result = await processDriverPayout(payoutInput);

        if (result.success && result.data) {
          results.successful.push(result.data);
          if (result.debtsResolved) {
            results.totalDebtsResolved += result.debtsResolved.length;
          }
        } else {
          results.failed.push({
            assignmentId: payoutInput.assignmentId,
            error: result.error?.message || "Unknown error",
          });
        }
      } catch (processError) {
        results.failed.push({
          assignmentId: payoutInput.assignmentId,
          error: processError instanceof Error ? processError.message : "Processing error",
        });
      }
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    
    // Revalidate assignment pages for successful payouts
    for (const payout of results.successful) {
      revalidatePath(`/admin/assignments/${payout.assignmentId}`);
    }

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error("❌ [processBulkPayoutsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to process bulk payouts",
    };
  }
}

/**
 * Server action for bulk payout approval
 */
export async function bulkApprovePayoutsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: {
    successful: PayoutRecord[];
    failed: Array<{ payoutId: number; error: string }>;
  };
  error?: string;
}> {
  try {
    const payoutIdsStr = formData.get("payoutIds") as string;
    const approvalNotes = formData.get("approvalNotes") as string | null;

    if (!payoutIdsStr) {
      return {
        success: false,
        error: "Payout IDs are required",
      };
    }

    let payoutIds: number[];
    try {
      payoutIds = JSON.parse(payoutIdsStr);
    } catch (parseError) {
      return {
        success: false,
        error: "Invalid payout IDs format",
      };
    }

    if (!Array.isArray(payoutIds) || payoutIds.length === 0) {
      return {
        success: false,
        error: "Payout IDs must be a non-empty array",
      };
    }

    // Process each approval
    const results = {
      successful: [] as PayoutRecord[],
      failed: [] as Array<{ payoutId: number; error: string }>,
    };

    for (const payoutId of payoutIds) {
      try {
        const result = await approveDriverPayout(payoutId, approvalNotes || undefined);

        if (result.success && result.data) {
          results.successful.push(result.data);
        } else {
          results.failed.push({
            payoutId,
            error: result.error?.message || "Unknown error",
          });
        }
      } catch (approvalError) {
        results.failed.push({
          payoutId,
          error: approvalError instanceof Error ? approvalError.message : "Approval error",
        });
      }
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error("❌ [bulkApprovePayoutsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to bulk approve payouts",
    };
  }
}

// =====================================================
// UTILITY ACTIONS
// =====================================================

/**
 * Server action to refresh payout data
 */
export async function refreshPayoutsAction(
  assignmentId?: number
): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    
    if (assignmentId) {
      revalidatePath(`/admin/assignments/${assignmentId}`);
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [refreshPayoutsAction] Error:", error);
    return {
      success: false,
      error: "Failed to refresh payouts data",
    };
  }
}

/**
 * Server action to validate payout form data
 */
export async function validatePayoutFormAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  fieldErrors?: Record<string, string>;
  calculatedAmount?: PayoutCalculation;
  error?: string;
}> {
  try {
    // Extract form data (similar to processDriverPayoutAction but for validation only)
    const assignmentId = Number(formData.get("assignmentId"));
    const periodStart = formData.get("periodStart") as string;
    const periodEnd = formData.get("periodEnd") as string;
    const payoutAmount = Number(formData.get("payoutAmount"));
    const paymentMethod = formData.get("paymentMethod") as string;
    const paymentReference = formData.get("paymentReference") as string | null;
    const bankDetails = formData.get("bankDetails") as string | null;
    const notes = formData.get("notes") as string | null;
    const requiresApproval = formData.get("requiresApproval") === "true";

    const input: PayoutProcessingInput = {
      assignmentId,
      periodStart,
      periodEnd,
      payoutAmount,
      paymentMethod: paymentMethod as any,
      paymentReference: paymentReference || undefined,
      bankDetails: bankDetails || undefined,
      notes: notes || undefined,
      requiresApproval,
    };

    // Validate with Zod schema
    const validationResult = PayoutProcessingFormSchema.safeParse(input);
    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        fieldErrors,
      };
    }

    // Business validation
    const businessValidation = validatePayoutProcessing(input);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        fieldErrors,
      };
    }

    // Get calculation for validation
    let calculatedAmount: PayoutCalculation | undefined;
    if (assignmentId && periodStart && periodEnd) {
      const calculationResult = await calculatePayoutAmount(assignmentId, periodStart, periodEnd);
      if (calculationResult.success) {
        calculatedAmount = calculationResult.data;
      }
    }

    return {
      success: true,
      calculatedAmount,
    };
  } catch (error) {
    console.error("❌ [validatePayoutFormAction] Error:", error);
    return {
      success: false,
      error: "Form validation failed",
    };
  }
}

/**
 * Get payout statistics for dashboard widgets
 */
export async function getPayoutStatsAction(
  assignmentIds: number[],
  period: "week" | "month" | "quarter" | "year" = "month"
): Promise<{
  success: boolean;
  data?: {
    totalPayouts: string;
    totalDebtDeductions: string;
    averagePayoutAmount: string;
    payoutCount: number;
    pendingApprovalCount: number;
    approvedCount: number;
    rejectedCount: number;
    lastPayoutDate?: string;
  };
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Calculate date range based on period
    const now = new Date();
    let startDate: string;
    
    switch (period) {
      case "week":
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        startDate = weekStart.toISOString().split('T')[0];
        break;
        
      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
        break;
        
      case "quarter":
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        startDate = quarterStart.toISOString().split('T')[0];
        break;
        
      case "year":
        startDate = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0];
        break;
        
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
    }

    const endDate = now.toISOString().split('T')[0];

    const summaryResult = await getPayoutsSummary(assignmentIds, startDate, endDate);

    if (!summaryResult.success || !summaryResult.data || summaryResult.data.length === 0) {
      return {
        success: true,
        data: {
          totalPayouts: "0",
          totalDebtDeductions: "0",
          averagePayoutAmount: "0",
          payoutCount: 0,
          pendingApprovalCount: 0,
          approvedCount: 0,
          rejectedCount: 0,
        },
      };
    }

    // Aggregate statistics across all assignments
    const aggregated = summaryResult.data.reduce(
      (acc, summary) => ({
        totalPayouts: acc.totalPayouts + Number(summary.totalPayouts),
        totalDebtDeductions: acc.totalDebtDeductions + Number(summary.totalDebtDeductions),
        payoutCount: acc.payoutCount + summary.payoutCount,
        pendingApprovalCount: acc.pendingApprovalCount + summary.pendingApprovalCount,
        approvedCount: acc.approvedCount + summary.approvedCount,
        rejectedCount: acc.rejectedCount + summary.rejectedCount,
        lastPayoutDate: summary.lastPayoutDate && (!acc.lastPayoutDate || summary.lastPayoutDate > acc.lastPayoutDate)
          ? summary.lastPayoutDate
          : acc.lastPayoutDate,
      }),
      {
        totalPayouts: 0,
        totalDebtDeductions: 0,
        payoutCount: 0,
        pendingApprovalCount: 0,
        approvedCount: 0,
        rejectedCount: 0,
        lastPayoutDate: undefined as string | undefined,
      }
    );

    const averagePayoutAmount = aggregated.payoutCount > 0 
      ? (aggregated.totalPayouts / aggregated.payoutCount).toString()
      : "0";

    return {
      success: true,
      data: {
        totalPayouts: aggregated.totalPayouts.toString(),
        totalDebtDeductions: aggregated.totalDebtDeductions.toString(),
        averagePayoutAmount,
        payoutCount: aggregated.payoutCount,
        pendingApprovalCount: aggregated.pendingApprovalCount,
        approvedCount: aggregated.approvedCount,
        rejectedCount: aggregated.rejectedCount,
        lastPayoutDate: aggregated.lastPayoutDate,
      },
    };
  } catch (error) {
    console.error("❌ [getPayoutStatsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch payout statistics",
    };
  }
} 