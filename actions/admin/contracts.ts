"use server";

import { revalidatePath } from "next/cache";
import {
  uploadAssignmentContract,
  getAssignmentContract,
  getAssignmentContractHistory,
  generateContractDownloadUrl,
  deleteAssignmentContract,
  getContractsSummary,
  validateContractFile,
} from "../../drizzle-actions/admin/contracts";
import { ContractUploadFormSchema } from "../../schemas/payment-contract";
import {
  ContractRecord,
  PaymentError,
  ContractResponse,
} from "../../types/payment-contract";

// =====================================================
// CONTRACT UPLOAD ACTIONS
// =====================================================

/**
 * Server action to upload a contract for an assignment
 */
export async function uploadAssignmentContractAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: ContractRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const assignmentId = Number(formData.get("assignmentId"));
    const file = formData.get("file") as File;
    const notes = formData.get("notes") as string | null;

    // Basic validation
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (!file || !(file instanceof File)) {
      return {
        success: false,
        error: "File is required",
        fieldErrors: {
          file: "Contract file is required",
        },
      };
    }

    // Validate with Zod schema (for form validation)
    const validationResult = ContractUploadFormSchema.safeParse({
      assignmentId,
      file,
      notes: notes || undefined,
    });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        error: "Please correct the form errors",
        fieldErrors,
      };
    }

    // Additional file validation
    const fileValidation = validateContractFile(file);
    if (!fileValidation.isValid) {
      return {
        success: false,
        error: fileValidation.error?.message || "File validation failed",
        fieldErrors: {
          file: fileValidation.error?.message || "Invalid file",
        },
      };
    }

    // Upload the contract
    const result = await uploadAssignmentContract(
      assignmentId,
      file,
      notes || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to upload contract",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/assignments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [uploadAssignmentContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to upload assignment contract",
    };
  }
}

/**
 * Server action to replace an existing contract
 */
export async function replaceAssignmentContractAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: ContractRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const assignmentId = Number(formData.get("assignmentId"));
    const file = formData.get("file") as File;
    const notes = formData.get("notes") as string | null;
    const replacementReason = formData.get("replacementReason") as
      | string
      | null;

    // Basic validation
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (!file || !(file instanceof File)) {
      return {
        success: false,
        error: "File is required",
        fieldErrors: {
          file: "Replacement contract file is required",
        },
      };
    }

    // Validate file
    const fileValidation = validateContractFile(file);
    if (!fileValidation.isValid) {
      return {
        success: false,
        error: fileValidation.error?.message || "File validation failed",
        fieldErrors: {
          file: fileValidation.error?.message || "Invalid file",
        },
      };
    }

    // Build notes with replacement reason
    const contractNotes = [
      replacementReason && `Replacement reason: ${replacementReason}`,
      notes && `Additional notes: ${notes}`,
    ]
      .filter(Boolean)
      .join("\n");

    // Upload the replacement contract (this automatically handles replacement logic)
    const result = await uploadAssignmentContract(
      assignmentId,
      file,
      contractNotes || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to replace contract",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/assignments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [replaceAssignmentContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to replace assignment contract",
    };
  }
}

// =====================================================
// CONTRACT RETRIEVAL ACTIONS
// =====================================================

/**
 * Server action to get the active contract for an assignment
 */
export async function getAssignmentContractAction(
  assignmentId: number
): Promise<{
  success: boolean;
  data?: ContractRecord | null;
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    const result = await getAssignmentContract(assignmentId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch assignment contract",
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [getAssignmentContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch assignment contract",
    };
  }
}

/**
 * Server action to get contract history for an assignment
 */
export async function getAssignmentContractHistoryAction(
  assignmentId: number
): Promise<{
  success: boolean;
  data?: ContractRecord[];
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    const result = await getAssignmentContractHistory(assignmentId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch contract history",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getAssignmentContractHistoryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch contract history",
    };
  }
}

/**
 * Server action to get contracts summary for multiple assignments
 */
export async function getContractsSummaryAction(
  assignmentIds: number[]
): Promise<{
  success: boolean;
  data?: Array<{
    assignmentId: number;
    hasActiveContract: boolean;
    contractCount: number;
    latestContractDate?: string;
    latestContractFilename?: string;
  }>;
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    const result = await getContractsSummary(assignmentIds);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch contracts summary",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getContractsSummaryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch contracts summary",
    };
  }
}

// =====================================================
// CONTRACT DOWNLOAD ACTIONS
// =====================================================

/**
 * Server action to generate a download URL for a contract
 */
export async function generateContractDownloadAction(
  contractId: number
): Promise<{
  success: boolean;
  data?: string;
  error?: string;
}> {
  try {
    if (!contractId || contractId <= 0) {
      return {
        success: false,
        error: "Invalid contract ID",
      };
    }

    const result = await generateContractDownloadUrl(contractId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to generate download URL",
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [generateContractDownloadAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to generate contract download URL",
    };
  }
}

/**
 * Server action to download a contract (redirect to signed URL)
 */
export async function downloadContractAction(contractId: number): Promise<{
  success: boolean;
  redirectUrl?: string;
  error?: string;
}> {
  try {
    const result = await generateContractDownloadAction(contractId);

    if (!result.success || !result.data) {
      return {
        success: false,
        error: result.error || "Failed to generate download URL",
      };
    }

    return {
      success: true,
      redirectUrl: result.data,
    };
  } catch (error) {
    console.error("❌ [downloadContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to download contract",
    };
  }
}

// =====================================================
// CONTRACT MANAGEMENT ACTIONS
// =====================================================

/**
 * Server action to delete a contract
 */
export async function deleteAssignmentContractAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const contractId = Number(formData.get("contractId"));
    const reason = formData.get("reason") as string | null;

    if (!contractId || contractId <= 0) {
      return {
        success: false,
        error: "Invalid contract ID",
        fieldErrors: {
          contractId: "Contract ID is required",
        },
      };
    }

    if (!reason?.trim()) {
      return {
        success: false,
        error: "Deletion reason is required",
        fieldErrors: {
          reason: "Please provide a reason for deleting this contract",
        },
      };
    }

    const result = await deleteAssignmentContract(contractId, reason);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to delete contract",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/assignments");

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [deleteAssignmentContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to delete assignment contract",
    };
  }
}

/**
 * Server action to refresh contract data
 */
export async function refreshContractsAction(assignmentId?: number): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate relevant pages
    revalidatePath("/admin/assignments");

    if (assignmentId) {
      revalidatePath(`/admin/assignments/${assignmentId}`);
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [refreshContractsAction] Error:", error);
    return {
      success: false,
      error: "Failed to refresh contracts data",
    };
  }
}

// =====================================================
// UTILITY ACTIONS
// =====================================================

/**
 * Validate contract file on the client side
 */
export async function validateContractFileAction(formData: FormData): Promise<{
  success: boolean;
  errors?: Record<string, string>;
}> {
  try {
    const file = formData.get("file") as File;

    if (!file || !(file instanceof File)) {
      return {
        success: false,
        errors: {
          file: "File is required",
        },
      };
    }

    const validationResult = validateContractFile(file);

    if (!validationResult.isValid) {
      return {
        success: false,
        errors: {
          file: validationResult.error?.message || "Invalid file",
        },
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [validateContractFileAction] Error:", error);
    return {
      success: false,
      errors: {
        general: "File validation failed",
      },
    };
  }
}

/**
 * Check if assignment has an active contract
 */
export async function checkAssignmentHasContractAction(
  assignmentId: number
): Promise<{
  success: boolean;
  hasContract?: boolean;
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    const result = await getAssignmentContract(assignmentId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to check contract status",
      };
    }

    return {
      success: true,
      hasContract: !!result.data,
    };
  } catch (error) {
    console.error("❌ [checkAssignmentHasContractAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to check contract status",
    };
  }
}
