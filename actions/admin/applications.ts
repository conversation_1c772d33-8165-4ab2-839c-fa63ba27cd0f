"use server";

import { revalidatePath } from "next/cache";
import {
  AdminApplicationWithDetails,
  getApplicationStatisticsForAdmin,
  getAllApplicationsForAdmin,
} from "@/drizzle-actions/admin/applications";

// ==================== SERVER ACTIONS ====================

/**
 * Admin Application Filters
 */
export interface AdminApplicationFilters {
  applicationType?: "all" | "ehailing-platform" | "rental" | "fractional";
  status?: "all" | "pending" | "under_review" | "approved" | "rejected";
  dateRange?: {
    from: Date;
    to: Date;
  };
}

/**
 * Get all applications for admin with optional filters
 */
export async function getAllApplicationsForAdminAction(
  filters?: AdminApplicationFilters
): Promise<{
  success: boolean;
  applications?: AdminApplicationWithDetails[];
  error?: string;
}> {
  try {
    // Use the working function from drizzle-actions/applications.ts
    const result = await getAllApplicationsForAdmin();

    if (!result.success) {
      return {
        success: false,
        error: result.error,
      };
    }

    // Convert ApplicationWithDetails to AdminApplicationWithDetails (string dates to Date objects)
    let filteredApplications: AdminApplicationWithDetails[] = (
      result.applications || []
    ).map((app) => ({
      ...app,
      createdAt: new Date(app.createdAt),
      listing: {
        ...app.listing,
        sourceType: app.listing.sourceType || "",
        sourceId: app.listing.sourceId || 0,
      },
      documents: app.documents.map((doc) => ({
        ...doc,
        uploadedAt: new Date(doc.uploadedAt),
        statusAt: doc.statusAt ? new Date(doc.statusAt) : undefined,
      })),
      latestDecision: app.latestDecision
        ? {
            ...app.latestDecision,
            decisionAt: new Date(app.latestDecision.decisionAt),
          }
        : undefined,
    }));

    // Apply filters if needed
    if (filters?.applicationType && filters.applicationType !== "all") {
      filteredApplications = filteredApplications.filter(
        (app) => app.listing.listingType === filters.applicationType
      );
    }

    if (filters?.status && filters.status !== "all") {
      filteredApplications = filteredApplications.filter((app) => {
        const status = app.latestDecision?.decision || "pending";
        return status === filters.status;
      });
    }

    return {
      success: true,
      applications: filteredApplications,
    };
  } catch (error) {
    console.error("❌ [Admin Applications] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch applications",
    };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStatisticsForAdminAction(): Promise<{
  success: boolean;
  statistics?: {
    total: number;
    pending: number;
    underReview: number;
    approved: number;
    rejected: number;
    byType: Record<string, number>;
  };
  error?: string;
}> {
  try {
    const statistics = await getApplicationStatisticsForAdmin();

    return {
      success: true,
      statistics,
    };
  } catch (error) {
    console.error("❌ [Admin Statistics] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application statistics",
    };
  }
}

/**
 * Refresh admin applications data
 */
export async function refreshAdminApplicationsAction(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate the admin applications page
    revalidatePath("/admin/applications");

    return {
      success: true,
    };
  } catch (error) {
    console.error("Error in refreshAdminApplicationsAction:", error);
    return {
      success: false,
      error: "Failed to refresh applications data",
    };
  }
}
