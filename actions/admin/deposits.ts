"use server";

import { revalidatePath } from "next/cache";
import {
  recordDepositPayment,
  addDepositPayment,
  getAssignmentDeposits,
  getDepositsSummary,
  getAssignmentDepositBalance,
  validateDepositPayment,
} from "../../drizzle-actions/admin/deposits";
import {
  DepositPaymentFormSchema,
  MoneyAmountSchema,
} from "../../schemas/payment-contract";
import {
  DepositRecord,
  DepositPaymentInput,
  PaymentError,
  DepositResponse,
} from "../../types/payment-contract";

// =====================================================
// DEPOSIT PAYMENT RECORDING
// =====================================================

/**
 * Server action to record a new deposit payment
 */
export async function recordDepositPaymentAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DepositRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract and validate form data
    const rawData = {
      assignmentId: Number(formData.get("assignmentId")),
      depositAmount: Number(formData.get("depositAmount")),
      amountPaid: Number(formData.get("amountPaid") || 0),
      paymentMethod: formData.get("paymentMethod") as string | null,
      paymentReference: formData.get("paymentReference") as string | null,
      notes: formData.get("notes") as string | null,
    };

    // Validate form data with Zod schema
    const validationResult = DepositPaymentFormSchema.safeParse({
      ...rawData,
      paymentMethod: rawData.paymentMethod || undefined,
      paymentReference: rawData.paymentReference || undefined,
      notes: rawData.notes || undefined,
    });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        error: "Please correct the form errors",
        fieldErrors,
      };
    }

    const validatedData = validationResult.data;

    // Additional business rule validation
    const businessValidation = validateDepositPayment(validatedData);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        error: businessValidation.errors[0]?.message || "Validation failed",
        fieldErrors,
      };
    }

    // Record the deposit payment
    const result = await recordDepositPayment(validatedData);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to record deposit payment",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/assignments");
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${validatedData.assignmentId}`);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [recordDepositPaymentAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to record deposit payment",
    };
  }
}

/**
 * Server action to add additional payment to existing deposit
 */
export async function addDepositPaymentAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DepositRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const rawData = {
      depositId: Number(formData.get("depositId")),
      additionalPayment: Number(formData.get("additionalPayment")),
      paymentMethod: formData.get("paymentMethod") as string | null,
      paymentReference: formData.get("paymentReference") as string | null,
      notes: formData.get("notes") as string | null,
    };

    // Validate additional payment amount
    const amountValidation = MoneyAmountSchema.safeParse(
      rawData.additionalPayment
    );
    if (!amountValidation.success) {
      return {
        success: false,
        error: "Invalid payment amount",
        fieldErrors: {
          additionalPayment:
            amountValidation.error.errors[0]?.message || "Invalid amount",
        },
      };
    }

    // Validate deposit ID
    if (!rawData.depositId || rawData.depositId <= 0) {
      return {
        success: false,
        error: "Invalid deposit ID",
        fieldErrors: {
          depositId: "Deposit ID is required",
        },
      };
    }

    // Add the payment
    const result = await addDepositPayment(
      rawData.depositId,
      amountValidation.data,
      rawData.paymentMethod || undefined,
      rawData.paymentReference || undefined,
      rawData.notes || undefined
    );

    if (!result.success) {
      // Handle specific error cases
      if (result.error?.code === "PAYMENT_EXCEEDS_DEPOSIT") {
        return {
          success: false,
          error: result.error.message,
          fieldErrors: {
            additionalPayment: result.error.message,
          },
        };
      }

      return {
        success: false,
        error: result.error?.message || "Failed to add deposit payment",
      };
    }

    // Revalidate relevant pages
    if (result.data) {
      revalidatePath("/admin/assignments");
      revalidatePath("/admin/payments");
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [addDepositPaymentAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to add deposit payment",
    };
  }
}

// =====================================================
// DEPOSIT RETRIEVAL ACTIONS
// =====================================================

/**
 * Server action to get deposits for a specific assignment
 */
export async function getAssignmentDepositsAction(
  assignmentId: number
): Promise<{
  success: boolean;
  data?: DepositRecord[];
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    const result = await getAssignmentDeposits(assignmentId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch assignment deposits",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getAssignmentDepositsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch assignment deposits",
    };
  }
}

/**
 * Server action to get deposit balance for an assignment
 */
export async function getAssignmentDepositBalanceAction(
  assignmentId: number
): Promise<{
  success: boolean;
  data?: number;
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    const result = await getAssignmentDepositBalance(assignmentId);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to calculate deposit balance",
      };
    }

    return {
      success: true,
      data: result.data || 0,
    };
  } catch (error) {
    console.error("❌ [getAssignmentDepositBalanceAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to calculate deposit balance",
    };
  }
}

/**
 * Server action to get deposit summaries for multiple assignments
 */
export async function getDepositsSummaryAction(
  assignmentIds: number[]
): Promise<{
  success: boolean;
  data?: Array<{
    assignmentId: number;
    totalDepositAmount: number;
    totalAmountPaid: number;
    totalBalanceRemaining: number;
    paymentCount: number;
    lastPaymentDate?: string;
  }>;
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    const result = await getDepositsSummary(assignmentIds);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch deposits summary",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getDepositsSummaryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch deposits summary",
    };
  }
}

// =====================================================
// UTILITY ACTIONS
// =====================================================

/**
 * Server action to refresh deposit data
 */
export async function refreshDepositsAction(assignmentId?: number): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate relevant pages
    revalidatePath("/admin/assignments");
    revalidatePath("/admin/payments");

    if (assignmentId) {
      revalidatePath(`/admin/assignments/${assignmentId}`);
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [refreshDepositsAction] Error:", error);
    return {
      success: false,
      error: "Failed to refresh deposits data",
    };
  }
}

/**
 * Validate deposit form data on the client side
 */
export async function validateDepositFormAction(formData: FormData): Promise<{
  success: boolean;
  errors?: Record<string, string>;
}> {
  try {
    const rawData = {
      assignmentId: Number(formData.get("assignmentId")),
      depositAmount: Number(formData.get("depositAmount")),
      amountPaid: Number(formData.get("amountPaid") || 0),
      paymentMethod: formData.get("paymentMethod") as string | null,
      paymentReference: formData.get("paymentReference") as string | null,
      notes: formData.get("notes") as string | null,
    };

    const validationResult = DepositPaymentFormSchema.safeParse({
      ...rawData,
      paymentMethod: rawData.paymentMethod || undefined,
      paymentReference: rawData.paymentReference || undefined,
      notes: rawData.notes || undefined,
    });

    if (!validationResult.success) {
      const errors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        errors[field] = error.message;
      });

      return {
        success: false,
        errors,
      };
    }

    // Additional business rule validation
    const businessValidation = validateDepositPayment(validationResult.data);
    if (!businessValidation.isValid) {
      const errors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          errors[error.field] = error.message;
        }
      });

      return {
        success: false,
        errors,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [validateDepositFormAction] Error:", error);
    return {
      success: false,
      errors: {
        general: "Validation failed",
      },
    };
  }
}
