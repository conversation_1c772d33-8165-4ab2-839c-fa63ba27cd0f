"use server";

import { revalidatePath } from "next/cache";
import {
  createManualDebt,
  adjustDebtAmount,
  forgiveDebt,
  restoreDebt,
  consolidateDebts,
  bulkForgiveDebts,
  getAssignmentDebts,
  getDebtsSummary,
  generateDebtReport,
  validateDebtCreation,
} from "@/drizzle-actions/admin/debt-management";
import {
  DebtAdjustmentFormSchema,
  DateRangeSchema,
} from "@/schemas/payment-contract";
import {
  DebtRecord,
  DebtCreationInput,
  DebtAdjustmentInput,
  PaymentError,
  DebtSummary,
  DebtReportData,
} from "@/types/payment-contract";

// =====================================================
// DEBT CREATION ACTIONS
// =====================================================

/**
 * Server action to create a manual debt
 */
export async function createManualDebtAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DebtRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    // Extract form data
    const assignmentId = Number(formData.get("assignmentId"));
    const amount = Number(formData.get("amount"));
    const description = formData.get("description") as string;
    const sourceType = formData.get("sourceType") as string | null;
    const sourceRecordId = formData.get("sourceRecordId")
      ? Number(formData.get("sourceRecordId"))
      : undefined;
    const sourceWeek = formData.get("sourceWeek") as string | null;
    const notes = formData.get("notes") as string | null;

    // Basic validation
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (isNaN(amount) || amount <= 0) {
      return {
        success: false,
        error: "Invalid debt amount",
        fieldErrors: {
          amount: "Debt amount must be a positive number",
        },
      };
    }

    if (!description?.trim()) {
      return {
        success: false,
        error: "Description is required",
        fieldErrors: {
          description: "Debt description is required",
        },
      };
    }

    // Prepare input
    const input: DebtCreationInput = {
      assignmentId,
      amount,
      description,
      sourceType: (sourceType as any) || "adjustment",
      sourceRecordId,
      sourceWeek: sourceWeek || undefined,
      notes: notes || undefined,
    };

    // Business validation
    const businessValidation = validateDebtCreation(input);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        error: "Validation failed",
        fieldErrors,
      };
    }

    // Create the debt
    const result = await createManualDebt(input);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to create manual debt",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [createManualDebtAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create manual debt",
    };
  }
}

// =====================================================
// DEBT ADJUSTMENT ACTIONS
// =====================================================

/**
 * Server action to adjust debt amount
 */
export async function adjustDebtAmountAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DebtRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const debtId = Number(formData.get("debtId"));
    const newAmount = Number(formData.get("newAmount"));
    const adjustmentReason = formData.get("adjustmentReason") as string;
    const notes = formData.get("notes") as string | null;

    if (!debtId || debtId <= 0) {
      return {
        success: false,
        error: "Invalid debt ID",
        fieldErrors: {
          debtId: "Debt ID is required",
        },
      };
    }

    if (isNaN(newAmount) || newAmount <= 0) {
      return {
        success: false,
        error: "Invalid new amount",
        fieldErrors: {
          newAmount: "New amount must be a positive number",
        },
      };
    }

    if (!adjustmentReason?.trim()) {
      return {
        success: false,
        error: "Adjustment reason is required",
        fieldErrors: {
          adjustmentReason: "Please provide a reason for the adjustment",
        },
      };
    }

    // Prepare input
    const input: DebtAdjustmentInput = {
      adjustmentAmount: newAmount,
      reason: adjustmentReason,
      notes: notes || undefined,
    };

    // Validate with Zod schema
    const validationResult = DebtAdjustmentFormSchema.safeParse(input);
    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        fieldErrors[field] = error.message;
      });

      return {
        success: false,
        error: "Please correct the form errors",
        fieldErrors,
      };
    }

    // Adjust the debt
    const result = await adjustDebtAmount(debtId, input);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to adjust debt amount",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    if (result.data) {
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [adjustDebtAmountAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to adjust debt amount",
    };
  }
}

// =====================================================
// DEBT FORGIVENESS ACTIONS
// =====================================================

/**
 * Server action to forgive a debt
 */
export async function forgiveDebtAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DebtRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const debtId = Number(formData.get("debtId"));
    const forgivenessReason = formData.get("forgivenessReason") as string;
    const notes = formData.get("notes") as string | null;

    if (!debtId || debtId <= 0) {
      return {
        success: false,
        error: "Invalid debt ID",
        fieldErrors: {
          debtId: "Debt ID is required",
        },
      };
    }

    if (!forgivenessReason?.trim()) {
      return {
        success: false,
        error: "Forgiveness reason is required",
        fieldErrors: {
          forgivenessReason: "Please provide a reason for debt forgiveness",
        },
      };
    }

    // Forgive the debt
    const result = await forgiveDebt(
      debtId,
      forgivenessReason,
      notes || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to forgive debt",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    if (result.data) {
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [forgiveDebtAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to forgive debt",
    };
  }
}

/**
 * Server action to restore a resolved debt
 */
export async function restoreDebtAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DebtRecord;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const debtId = Number(formData.get("debtId"));
    const restorationReason = formData.get("restorationReason") as string;
    const notes = formData.get("notes") as string | null;

    if (!debtId || debtId <= 0) {
      return {
        success: false,
        error: "Invalid debt ID",
        fieldErrors: {
          debtId: "Debt ID is required",
        },
      };
    }

    if (!restorationReason?.trim()) {
      return {
        success: false,
        error: "Restoration reason is required",
        fieldErrors: {
          restorationReason: "Please provide a reason for debt restoration",
        },
      };
    }

    // Restore the debt
    const result = await restoreDebt(
      debtId,
      restorationReason,
      notes || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to restore debt",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    if (result.data) {
      revalidatePath(`/admin/assignments/${result.data.assignmentId}`);
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [restoreDebtAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to restore debt",
    };
  }
}

// =====================================================
// BULK DEBT OPERATIONS
// =====================================================

/**
 * Server action to consolidate multiple debts
 */
export async function consolidateDebtsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: DebtRecord;
  consolidatedCount?: number;
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const assignmentId = Number(formData.get("assignmentId"));
    const debtIdsStr = formData.get("debtIds") as string;
    const consolidationNotes = formData.get("consolidationNotes") as string;

    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
        fieldErrors: {
          assignmentId: "Assignment ID is required",
        },
      };
    }

    if (!debtIdsStr) {
      return {
        success: false,
        error: "Debt IDs are required",
        fieldErrors: {
          debtIds: "Please select debts to consolidate",
        },
      };
    }

    if (!consolidationNotes?.trim()) {
      return {
        success: false,
        error: "Consolidation notes are required",
        fieldErrors: {
          consolidationNotes: "Please provide notes for the consolidation",
        },
      };
    }

    let debtIds: number[];
    try {
      debtIds = JSON.parse(debtIdsStr);
    } catch (parseError) {
      return {
        success: false,
        error: "Invalid debt IDs format",
        fieldErrors: {
          debtIds: "Invalid debt IDs format",
        },
      };
    }

    if (!Array.isArray(debtIds) || debtIds.length < 2) {
      return {
        success: false,
        error: "At least 2 debts are required for consolidation",
        fieldErrors: {
          debtIds: "Please select at least 2 debts to consolidate",
        },
      };
    }

    // Consolidate the debts
    const result = await consolidateDebts(
      assignmentId,
      debtIds,
      consolidationNotes
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to consolidate debts",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");
    revalidatePath(`/admin/assignments/${assignmentId}`);

    return {
      success: true,
      data: result.data,
      consolidatedCount: result.consolidatedCount,
    };
  } catch (error) {
    console.error("❌ [consolidateDebtsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to consolidate debts",
    };
  }
}

/**
 * Server action to bulk forgive debts
 */
export async function bulkForgiveDebtsAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  data?: {
    successful: DebtRecord[];
    failed: Array<{ debtId: number; error: string }>;
    totalForgivenAmount: number;
  };
  error?: string;
  fieldErrors?: Record<string, string>;
}> {
  try {
    const debtIdsStr = formData.get("debtIds") as string;
    const forgivenessReason = formData.get("forgivenessReason") as string;
    const notes = formData.get("notes") as string | null;

    if (!debtIdsStr) {
      return {
        success: false,
        error: "Debt IDs are required",
        fieldErrors: {
          debtIds: "Please select debts to forgive",
        },
      };
    }

    if (!forgivenessReason?.trim()) {
      return {
        success: false,
        error: "Forgiveness reason is required",
        fieldErrors: {
          forgivenessReason:
            "Please provide a reason for bulk debt forgiveness",
        },
      };
    }

    let debtIds: number[];
    try {
      debtIds = JSON.parse(debtIdsStr);
    } catch (parseError) {
      return {
        success: false,
        error: "Invalid debt IDs format",
        fieldErrors: {
          debtIds: "Invalid debt IDs format",
        },
      };
    }

    if (!Array.isArray(debtIds) || debtIds.length === 0) {
      return {
        success: false,
        error: "No debts selected for forgiveness",
        fieldErrors: {
          debtIds: "Please select at least one debt to forgive",
        },
      };
    }

    // Bulk forgive debts
    const result = await bulkForgiveDebts(
      debtIds,
      forgivenessReason,
      notes || undefined
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to bulk forgive debts",
      };
    }

    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    // Revalidate assignment pages for successful forgiveness
    const assignmentIds = new Set<number>();
    if (result.data) {
      result.data.successful.forEach((debt) => {
        assignmentIds.add(debt.assignmentId);
      });
      assignmentIds.forEach((assignmentId) => {
        revalidatePath(`/admin/assignments/${assignmentId}`);
      });
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [bulkForgiveDebtsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to bulk forgive debts",
    };
  }
}

// =====================================================
// DEBT RETRIEVAL ACTIONS
// =====================================================

/**
 * Server action to get debts for an assignment
 */
export async function getAssignmentDebtsAction(
  assignmentId: number,
  includeResolved: boolean = false,
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: DebtRecord[];
  error?: string;
}> {
  try {
    if (!assignmentId || assignmentId <= 0) {
      return {
        success: false,
        error: "Invalid assignment ID",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getAssignmentDebts(
      assignmentId,
      includeResolved,
      startDate,
      endDate
    );

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch assignment debts",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getAssignmentDebtsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch assignment debts",
    };
  }
}

/**
 * Server action to get debt summaries for multiple assignments
 */
export async function getDebtsSummaryAction(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: DebtSummary[];
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await getDebtsSummary(assignmentIds, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to fetch debts summary",
      };
    }

    return {
      success: true,
      data: result.data || [],
    };
  } catch (error) {
    console.error("❌ [getDebtsSummaryAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch debts summary",
    };
  }
}

/**
 * Server action to generate comprehensive debt report
 */
export async function generateDebtReportAction(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{
  success: boolean;
  data?: DebtReportData;
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Validate all assignment IDs are positive numbers
    const invalidIds = assignmentIds.filter((id) => !id || id <= 0);
    if (invalidIds.length > 0) {
      return {
        success: false,
        error: "All assignment IDs must be positive numbers",
      };
    }

    // Validate date range if provided
    if ((startDate && !endDate) || (!startDate && endDate)) {
      return {
        success: false,
        error: "Both start and end dates must be provided if using date range",
      };
    }

    if (startDate && endDate) {
      const dateRangeValidation = DateRangeSchema.safeParse({
        startDate,
        endDate,
      });

      if (!dateRangeValidation.success) {
        return {
          success: false,
          error: "Invalid date range",
        };
      }
    }

    const result = await generateDebtReport(assignmentIds, startDate, endDate);

    if (!result.success) {
      return {
        success: false,
        error: result.error?.message || "Failed to generate debt report",
      };
    }

    return {
      success: true,
      data: result.data,
    };
  } catch (error) {
    console.error("❌ [generateDebtReportAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to generate debt report",
    };
  }
}

// =====================================================
// UTILITY ACTIONS
// =====================================================

/**
 * Server action to refresh debt data
 */
export async function refreshDebtsAction(assignmentId?: number): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    // Revalidate relevant pages
    revalidatePath("/admin/payments");

    if (assignmentId) {
      revalidatePath(`/admin/assignments/${assignmentId}`);
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [refreshDebtsAction] Error:", error);
    return {
      success: false,
      error: "Failed to refresh debts data",
    };
  }
}

/**
 * Server action to validate debt creation form
 */
export async function validateDebtCreationFormAction(
  prevState: any,
  formData: FormData
): Promise<{
  success: boolean;
  fieldErrors?: Record<string, string>;
  error?: string;
}> {
  try {
    // Extract form data (similar to createManualDebtAction but for validation only)
    const assignmentId = Number(formData.get("assignmentId"));
    const amount = Number(formData.get("amount"));
    const description = formData.get("description") as string;
    const sourceType = formData.get("sourceType") as string | null;
    const sourceRecordId = formData.get("sourceRecordId")
      ? Number(formData.get("sourceRecordId"))
      : undefined;
    const sourceWeek = formData.get("sourceWeek") as string | null;
    const notes = formData.get("notes") as string | null;

    const input: DebtCreationInput = {
      assignmentId,
      amount,
      description,
      sourceType: (sourceType as any) || "adjustment",
      sourceRecordId,
      sourceWeek: sourceWeek || undefined,
      notes: notes || undefined,
    };

    // Business validation
    const businessValidation = validateDebtCreation(input);
    if (!businessValidation.isValid) {
      const fieldErrors: Record<string, string> = {};
      businessValidation.errors.forEach((error) => {
        if (error.field) {
          fieldErrors[error.field] = error.message;
        }
      });

      return {
        success: false,
        fieldErrors,
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [validateDebtCreationFormAction] Error:", error);
    return {
      success: false,
      error: "Form validation failed",
    };
  }
}

/**
 * Get debt statistics for dashboard widgets
 */
export async function getDebtStatsAction(
  assignmentIds: number[],
  period: "week" | "month" | "quarter" | "year" = "month"
): Promise<{
  success: boolean;
  data?: {
    totalOutstandingDebt: string;
    totalResolvedDebt: string;
    totalDebtCount: number;
    outstandingDebtCount: number;
    resolvedDebtCount: number;
    averageDebtAmount: string;
    resolutionRate: string;
    earningsShortfallCount: number;
    adjustmentCount: number;
    debtRecoveryCount: number;
    latestDebtDate?: string;
  };
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // Calculate date range based on period
    const now = new Date();
    let startDate: string;

    switch (period) {
      case "week":
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        startDate = weekStart.toISOString().split("T")[0];
        break;

      case "month":
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          .toISOString()
          .split("T")[0];
        break;

      case "quarter":
        const quarterStart = new Date(
          now.getFullYear(),
          Math.floor(now.getMonth() / 3) * 3,
          1
        );
        startDate = quarterStart.toISOString().split("T")[0];
        break;

      case "year":
        startDate = new Date(now.getFullYear(), 0, 1)
          .toISOString()
          .split("T")[0];
        break;

      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          .toISOString()
          .split("T")[0];
    }

    const endDate = now.toISOString().split("T")[0];

    const summaryResult = await getDebtsSummary(
      assignmentIds,
      startDate,
      endDate
    );

    if (
      !summaryResult.success ||
      !summaryResult.data ||
      summaryResult.data.length === 0
    ) {
      return {
        success: true,
        data: {
          totalOutstandingDebt: "0",
          totalResolvedDebt: "0",
          totalDebtCount: 0,
          outstandingDebtCount: 0,
          resolvedDebtCount: 0,
          averageDebtAmount: "0",
          resolutionRate: "0",
          earningsShortfallCount: 0,
          adjustmentCount: 0,
          debtRecoveryCount: 0,
        },
      };
    }

    // Aggregate statistics across all assignments
    const aggregated = summaryResult.data.reduce(
      (acc, summary) => ({
        totalOutstandingDebt:
          acc.totalOutstandingDebt + Number(summary.totalOutstandingDebt),
        totalResolvedDebt:
          acc.totalResolvedDebt + Number(summary.totalResolvedDebt),
        totalDebtCount: acc.totalDebtCount + summary.totalDebtCount,
        outstandingDebtCount:
          acc.outstandingDebtCount + summary.outstandingDebtCount,
        resolvedDebtCount: acc.resolvedDebtCount + summary.resolvedDebtCount,
        earningsShortfallCount:
          acc.earningsShortfallCount + summary.earningsShortfallCount,
        adjustmentCount: acc.adjustmentCount + summary.adjustmentCount,
        debtRecoveryCount: acc.debtRecoveryCount + summary.debtRecoveryCount,
        latestDebtDate:
          summary.latestDebtDate &&
          (!acc.latestDebtDate || summary.latestDebtDate > acc.latestDebtDate)
            ? summary.latestDebtDate
            : acc.latestDebtDate,
      }),
      {
        totalOutstandingDebt: 0,
        totalResolvedDebt: 0,
        totalDebtCount: 0,
        outstandingDebtCount: 0,
        resolvedDebtCount: 0,
        earningsShortfallCount: 0,
        adjustmentCount: 0,
        debtRecoveryCount: 0,
        latestDebtDate: undefined as string | undefined,
      }
    );

    const averageDebtAmount =
      aggregated.totalDebtCount > 0
        ? (
            (aggregated.totalOutstandingDebt + aggregated.totalResolvedDebt) /
            aggregated.totalDebtCount
          ).toString()
        : "0";

    const resolutionRate =
      aggregated.totalDebtCount > 0
        ? (
            (aggregated.resolvedDebtCount / aggregated.totalDebtCount) *
            100
          ).toFixed(2)
        : "0";

    return {
      success: true,
      data: {
        totalOutstandingDebt: aggregated.totalOutstandingDebt.toString(),
        totalResolvedDebt: aggregated.totalResolvedDebt.toString(),
        totalDebtCount: aggregated.totalDebtCount,
        outstandingDebtCount: aggregated.outstandingDebtCount,
        resolvedDebtCount: aggregated.resolvedDebtCount,
        averageDebtAmount,
        resolutionRate,
        earningsShortfallCount: aggregated.earningsShortfallCount,
        adjustmentCount: aggregated.adjustmentCount,
        debtRecoveryCount: aggregated.debtRecoveryCount,
        latestDebtDate: aggregated.latestDebtDate,
      },
    };
  } catch (error) {
    console.error("❌ [getDebtStatsAction] Error:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch debt statistics",
    };
  }
}

/**
 * Server action to search debts across multiple assignments
 */
export async function searchDebtsAction(
  assignmentIds: number[],
  searchQuery: string,
  filters?: {
    sourceType?: string;
    isResolved?: boolean;
    amountRange?: { min: number; max: number };
    dateRange?: { startDate: string; endDate: string };
  }
): Promise<{
  success: boolean;
  data?: DebtRecord[];
  totalCount?: number;
  error?: string;
}> {
  try {
    if (!Array.isArray(assignmentIds) || assignmentIds.length === 0) {
      return {
        success: false,
        error: "Invalid assignment IDs array",
      };
    }

    // For each assignment, get debts and filter
    const allDebts: DebtRecord[] = [];

    for (const assignmentId of assignmentIds) {
      const result = await getAssignmentDebts(
        assignmentId,
        true, // include resolved debts for search
        filters?.dateRange?.startDate,
        filters?.dateRange?.endDate
      );

      if (result.success && result.data) {
        allDebts.push(...result.data);
      }
    }

    // Apply search and filters
    let filteredDebts = allDebts;

    // Text search in notes and debt source type
    if (searchQuery?.trim()) {
      const query = searchQuery.toLowerCase();
      filteredDebts = filteredDebts.filter(
        (debt) =>
          (debt.notes && debt.notes.toLowerCase().includes(query)) ||
          debt.debtSourceType.toLowerCase().includes(query) ||
          debt.debtAmount.toString().includes(query) ||
          debt.runningBalance.toString().includes(query)
      );
    }

    // Source type filter
    if (filters?.sourceType) {
      filteredDebts = filteredDebts.filter(
        (debt) => debt.debtSourceType === filters.sourceType
      );
    }

    // Resolution status filter (based on running balance)
    if (filters?.isResolved !== undefined) {
      filteredDebts = filteredDebts.filter((debt) => {
        const isResolved = debt.runningBalance <= 0;
        return isResolved === filters.isResolved;
      });
    }

    // Amount range filter
    if (filters?.amountRange) {
      const { min, max } = filters.amountRange;
      filteredDebts = filteredDebts.filter((debt) => {
        const amount = Number(debt.debtAmount);
        return amount >= min && amount <= max;
      });
    }

    // Sort by transaction date (newest first)
    filteredDebts.sort(
      (a, b) =>
        new Date(b.transactionDate).getTime() -
        new Date(a.transactionDate).getTime()
    );

    return {
      success: true,
      data: filteredDebts,
      totalCount: filteredDebts.length,
    };
  } catch (error) {
    console.error("❌ [searchDebtsAction] Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to search debts",
    };
  }
}
