"use server";

import { db } from "@/db";
import { eq, and, desc, sql } from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { createPoollyGroup } from "@/drizzle-actions/admin/admin-groups";
import {
  h_applications,
  h_applicationDecisions,
} from "@/drizzle/h_schema/applications";
import {
  h_listings,
  h_listing_approval_status,
} from "@/drizzle/h_schema/listings";
import {
  vehicles,
  party,
  individual,
  vehicleModel,
  vehicleMake,
  groups,
  groupMemberships,
  groupSharedVehicles,
  inventory,
} from "@/drizzle/schema";
import { GroupRoleEnum } from "@/types/groups";
import { CompanyPurposeEnum } from "@/types/company";
import { PartyStatusEnum } from "@/types/party";
import { createInitialVehiclePossession } from "@/drizzle-actions/bookings";

// ADD: Missing constants
const groupPartyTypeId = 3;

// Get approved e-hailing drivers for assignment
export async function getApprovedEhailingDrivers() {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Get applications with latest approved decision for e-hailing
    const approvedDrivers = await db
      .select({
        applicationId: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id = 1
          AND cp.is_primary = true 
          LIMIT 1
        )`,
        phone: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id = 2
          AND cp.is_primary = true 
          LIMIT 1
        )`,
        approvedAt: h_applicationDecisions.decisionAt,
        approvedBy: h_applicationDecisions.reviewerId,
      })
      .from(h_applications)
      .innerJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .where(
        and(
          eq(h_listings.listingType, "ehailing-platform"),
          eq(h_applicationDecisions.decision, "approved")
        )
      )
      .orderBy(desc(h_applicationDecisions.decisionAt));

    return {
      success: true,
      data: approvedDrivers,
      message: "Approved e-hailing drivers retrieved successfully",
    };
  } catch (error) {
    console.error("Error fetching approved e-hailing drivers:", error);
    return {
      success: false,
      error: "Failed to fetch approved e-hailing drivers",
      details: error,
    };
  }
}

// ✅ UPDATED: Get vehicles from actual inventory (vehicles table)
export async function getAvailableVehiclesInventory() {
  return await getAvailableInventoryVehicles();
}

// ✅ UPDATED: Create vehicle-driver assignment using real vehicle IDs
export async function createVehicleDriverAssignment(
  driverPartyId: number,
  vehicleId: number,
  groupName?: string
) {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Verify driver has approved e-hailing application
    const driverApplication = await db
      .select({
        applicationId: h_applications.id,
        applicantId: h_applications.applicantId,
      })
      .from(h_applications)
      .innerJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .where(
        and(
          eq(h_applications.applicantId, driverPartyId),
          eq(h_listings.listingType, "ehailing-platform"),
          eq(h_applicationDecisions.decision, "approved")
        )
      )
      .limit(1);

    if (!driverApplication.length) {
      return {
        success: false,
        error: "Driver not found or not approved for e-hailing",
      };
    }

    // ✅ UPDATED: Verify vehicle exists in inventory and is admin-owned
    const vehicleCheck = await db
      .select({
        id: vehicles.id,
        partyId: vehicles.partyId,
        isActive: vehicles.isActive,
      })
      .from(vehicles)
      .leftJoin(
        groupSharedVehicles,
        eq(vehicles.id, groupSharedVehicles.vehicleId)
      )
      .where(
        and(
          eq(vehicles.id, vehicleId),
          eq(vehicles.partyId, adminPartyId),
          eq(vehicles.isActive, true),
          sql`${groupSharedVehicles.vehicleId} IS NULL` // Not already assigned
        )
      )
      .limit(1);

    if (!vehicleCheck.length) {
      return {
        success: false,
        error: "Vehicle not found, not available, or already assigned",
      };
    }

    // Add debugging to see what's happening:
    console.log(`🔍 Looking for driver with party ID: ${driverPartyId}`);

    const driverInfo = await db
      .select({
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
      })
      .from(party)
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(party.id, driverPartyId))
      .limit(1);

    console.log(`🔍 Driver query result:`, driverInfo);

    // ✅ IMPROVED: Better driver name resolution
    let driverName = "Driver"; // Default fallback

    if (driverInfo.length > 0) {
      const firstName = driverInfo[0].firstName || "";
      const lastName = driverInfo[0].lastName || "";
      const fullName = `${firstName} ${lastName}`.trim();

      if (fullName) {
        driverName = fullName;
      }
    }

    console.log(`🔍 Final driver name: "${driverName}"`);

    // Use the createPoollyGroup function
    const finalGroupName =
      groupName || `Poolly ehailing group with ${driverName}`;

    console.log(
      `🚀 Creating Poolly group: "${finalGroupName}" for driver ${driverName}`
    );

    const groupResult = await createPoollyGroup(
      adminPartyId,
      driverPartyId,
      vehicleId,
      finalGroupName
    );

    if (groupResult.success) {
      // ✅ Mark vehicle as assigned in inventory
      await updateVehicleInventoryStatus(
        vehicleId,
        "assigned",
        driverPartyId,
        `Assigned to driver for group: ${finalGroupName}`
      );
    }

    if (!groupResult.success || !groupResult.data) {
      console.error("❌ Failed to create Poolly group:", groupResult.error);
      return {
        success: false,
        error: "Failed to create vehicle-driver assignment group",
        details: groupResult.error || "Unknown error",
      };
    }

    console.log(
      `✅ Successfully created vehicle-driver assignment for ${driverName}`
    );

    return {
      success: true,
      data: {
        groupId: groupResult.data.group.id,
        groupName: groupResult.data.group.name,
        adminPartyId,
        driverPartyId,
        vehicleId,
        driverName,
        assignment: {
          groupId: groupResult.data.group.id,
          groupName: groupResult.data.group.name,
          adminPartyId,
          driverPartyId,
          vehicleId,
          createdAt: new Date().toISOString(),
        },
      },
      message: "Vehicle-driver assignment created successfully",
    };
  } catch (error) {
    console.error("❌ Error creating vehicle-driver assignment:", error);
    return {
      success: false,
      error: "Failed to create vehicle-driver assignment",
      details: error,
    };
  }
}

// Get existing assignments
export async function getVehicleDriverAssignments() {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Get all Poolly groups created by this admin
    const assignments = await db
      .select({
        groupId: sql<number>`groups.id`,
        groupName: sql<string>`groups.name`,
        createdAt: sql<string>`groups.created_at`,
        vehicleId: sql<number>`group_shared_vehicles.vehicle_id`,
        driverPartyId: sql<number>`driver_memberships.party_id`,
        driverFirstName: sql<string>`driver_individuals.first_name`,
        driverLastName: sql<string>`driver_individuals.last_name`,
        driverEmail: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = driver_memberships.party_id
          AND cp.contact_point_type_id = 1
          AND cp.is_primary = true 
          LIMIT 1
        )`,
        vehicleMake: sql<string>`COALESCE(${vehicleMake.name}, 'Unknown')`,
        vehicleModel: sql<string>`COALESCE(${vehicleModel.model}, 'Unknown')`,
        vehicleYear: sql<number>`vehicles.manufacturing_year`,
        vehicleColor: sql<string>`vehicles.color`,
        isActive: sql<boolean>`group_shared_vehicles.is_active`,
      })
      .from(sql`groups`)
      .innerJoin(
        sql`group_shared_vehicles`,
        sql`groups.id = group_shared_vehicles.group_id`
      )
      .innerJoin(
        sql`vehicles`,
        sql`group_shared_vehicles.vehicle_id = vehicles.id`
      )
      .innerJoin(
        sql`group_memberships AS driver_memberships`,
        sql`groups.id = driver_memberships.group_id AND driver_memberships.party_id != ${adminPartyId}`
      )
      .innerJoin(
        sql`party AS driver_party`,
        sql`driver_memberships.party_id = driver_party.id`
      )
      .leftJoin(
        sql`individual AS driver_individuals`,
        sql`driver_party.id = driver_individuals.party_id`
      )
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          sql`groups.created_by = ${adminPartyId}`,
          sql`groups.description = 'Poolly managed vehicle assignment group'`
        )
      )
      .orderBy(sql`groups.created_at DESC`);

    return {
      success: true,
      data: assignments,
      message: "Vehicle-driver assignments retrieved successfully",
    };
  } catch (error) {
    console.error("Error fetching vehicle-driver assignments:", error);
    return {
      success: false,
      error: "Failed to fetch vehicle-driver assignments",
      details: error,
    };
  }
}

// Create vehicle-driver assignment for inventory vehicles (catalog-based)
export async function createInventoryVehicleDriverAssignment(
  driverPartyId: number,
  listingId: number,
  groupName?: string
) {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Verify driver has approved e-hailing application
    const driverApplication = await db
      .select({
        applicationId: h_applications.id,
        applicantId: h_applications.applicantId,
      })
      .from(h_applications)
      .innerJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .where(
        and(
          eq(h_applications.applicantId, driverPartyId),
          eq(h_listings.listingType, "ehailing-platform"),
          eq(h_applicationDecisions.decision, "approved")
        )
      )
      .limit(1);

    if (!driverApplication.length) {
      return {
        success: false,
        error: "Driver not found or not approved for e-hailing",
      };
    }

    // Verify inventory vehicle is available and admin-owned
    const inventoryVehicleCheck = await db
      .select({
        listingId: h_listings.id,
        listingDetails: h_listings.listingDetails,
      })
      .from(h_listings)
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .where(
        and(
          eq(h_listings.partyId, adminPartyId),
          eq(h_listings.id, listingId),
          eq(h_listings.sourceType, "catalog"),
          eq(h_listing_approval_status.status, "approved"),
          sql`${h_listings.listingDetails}::jsonb ? 'isInventoryItem'`,
          sql`${h_listings.effectiveTo} > NOW()`
        )
      )
      .limit(1);

    if (!inventoryVehicleCheck.length) {
      return {
        success: false,
        error: "Inventory vehicle not found or not available for assignment",
      };
    }

    // Parse vehicle details from listing
    const vehicleDetails = inventoryVehicleCheck[0].listingDetails
      ? JSON.parse(inventoryVehicleCheck[0].listingDetails)
      : {};

    // Create a simple assignment record in the database
    // For now, we'll create a simple group assignment without using the complex vehicle sharing system
    const assignmentResult = await db.transaction(async (tx) => {
      // Create an assignment record (we can use a simple table or JSON storage)
      // For now, let's update the listing to mark it as assigned
      const updatedDetails = {
        ...vehicleDetails,
        assignedDriver: {
          driverPartyId,
          assignedAt: new Date().toISOString(),
          assignedBy: adminPartyId,
          status: "active",
        },
      };

      await tx
        .update(h_listings)
        .set({
          listingDetails: JSON.stringify(updatedDetails),
        })
        .where(eq(h_listings.id, listingId));

      return {
        listingId,
        driverPartyId,
        adminPartyId,
        vehicleDetails: updatedDetails,
      };
    });

    return {
      success: true,
      data: {
        assignmentId: listingId.toString(),
        listingId,
        driverPartyId,
        adminPartyId,
        vehicleDetails: assignmentResult.vehicleDetails,
      },
      message: "Inventory vehicle-driver assignment created successfully",
    };
  } catch (error) {
    console.error("Error creating inventory vehicle-driver assignment:", error);
    return {
      success: false,
      error: "Failed to create inventory vehicle-driver assignment",
      details: error,
    };
  }
}

// Get inventory vehicle assignments
export async function getInventoryVehicleDriverAssignments() {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Get inventory vehicles that have been assigned to drivers
    const assignments = await db
      .select({
        listingId: h_listings.id,
        listingDetails: h_listings.listingDetails,
        createdAt: h_listings.effectiveFrom,
        approvedAt: h_listing_approval_status.statusAt,
      })
      .from(h_listings)
      .innerJoin(
        h_listing_approval_status,
        eq(h_listings.id, h_listing_approval_status.listingId)
      )
      .where(
        and(
          eq(h_listings.partyId, adminPartyId),
          eq(h_listings.sourceType, "catalog"),
          eq(h_listing_approval_status.status, "approved"),
          sql`${h_listings.listingDetails}::jsonb ? 'isInventoryItem'`,
          sql`${h_listings.listingDetails}::jsonb ? 'assignedDriver'`,
          sql`${h_listings.effectiveTo} > NOW()`
        )
      )
      .orderBy(desc(h_listing_approval_status.statusAt));

    // Transform the data to include driver information
    const transformedAssignments = await Promise.all(
      assignments.map(async (assignment) => {
        const details = assignment.listingDetails
          ? JSON.parse(assignment.listingDetails)
          : {};

        const assignedDriver = details.assignedDriver;

        // Get driver information
        let driverInfo = null;
        if (assignedDriver?.driverPartyId) {
          const driverResult = await db
            .select({
              firstName: individual.firstName,
              lastName: individual.lastName,
              email: sql<string>`(
                SELECT cp.value 
                FROM contact_point cp 
                WHERE cp.party_id = ${party.id} 
                AND cp.contact_point_type_id = 1
                AND cp.is_primary = true 
                LIMIT 1
              )`,
            })
            .from(party)
            .leftJoin(individual, eq(party.id, individual.partyId))
            .where(eq(party.id, assignedDriver.driverPartyId))
            .limit(1);

          if (driverResult.length > 0) {
            driverInfo = driverResult[0];
          }
        }

        return {
          groupId: assignment.listingId, // Use listing ID as group ID
          groupName: `${details.make || "Unknown"} ${details.model || "Unknown"} - ${driverInfo?.firstName || "Unknown"} ${driverInfo?.lastName || ""}`,
          createdAt: assignedDriver?.assignedAt || assignment.createdAt,
          vehicleId: assignment.listingId,
          driverPartyId: assignedDriver?.driverPartyId || 0,
          driverFirstName: driverInfo?.firstName || "Unknown",
          driverLastName: driverInfo?.lastName || "",
          driverEmail: driverInfo?.email || "",
          vehicleMake: details.make || "Unknown",
          vehicleModel: details.model || "Unknown",
          vehicleYear: details.year || 0,
          vehicleColor: details.color || "Unknown",
          isActive: assignedDriver?.status === "active",
        };
      })
    );

    return {
      success: true,
      data: transformedAssignments,
      message: "Inventory vehicle-driver assignments retrieved successfully",
    };
  } catch (error) {
    console.error(
      "Error fetching inventory vehicle-driver assignments:",
      error
    );
    return {
      success: false,
      error: "Failed to fetch inventory vehicle-driver assignments",
      details: error,
    };
  }
}

// Add vehicle to inventory (creates initial record)
export async function addVehicleToInventory(
  vehicleId: number,
  location?: string,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    // Check if vehicle is already in active inventory
    const existingInventory = await db
      .select({ id: inventory.id })
      .from(inventory)
      .where(
        and(
          eq(inventory.vehicleId, vehicleId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .limit(1);

    if (existingInventory.length > 0) {
      return { success: false, error: "Vehicle is already in inventory" };
    }

    const result = await db
      .insert(inventory)
      .values({
        vehicleId,
        adminPartyId,
        status: "available",
        location: location || null,
        notes: notes || null,
        effectiveFrom: new Date().toISOString(),
        // effectiveTo defaults to infinity
      })
      .returning();

    return { success: true, data: result[0] };
  } catch (error) {
    console.error("Error adding vehicle to inventory:", error);
    return { success: false, error: "Failed to add vehicle to inventory" };
  }
}

// Update vehicle status (creates new immutable record)
export async function updateVehicleInventoryStatus(
  vehicleId: number,
  newStatus: "available" | "assigned" | "maintenance" | "inspection",
  assignedToPartyId?: number,
  notes?: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const now = new Date().toISOString();

    const result = await db.transaction(async (tx) => {
      // 1. Close current active record
      const currentRecord = await tx
        .update(inventory)
        .set({
          effectiveTo: now,
          updatedAt: now,
        })
        .where(
          and(
            eq(inventory.vehicleId, vehicleId),
            eq(inventory.adminPartyId, adminPartyId),
            sql`${inventory.effectiveTo} = 'infinity'::timestamp`
          )
        )
        .returning();

      if (currentRecord.length === 0) {
        throw new Error("Vehicle not found in active inventory");
      }

      // 2. Create new record with updated status
      const newRecord = await tx
        .insert(inventory)
        .values({
          vehicleId,
          adminPartyId,
          status: newStatus,
          assignedToPartyId: assignedToPartyId || null,
          location: currentRecord[0].location, // Inherit location
          notes: notes || `Status changed to ${newStatus}`,
          effectiveFrom: now,
          // effectiveTo defaults to infinity
        })
        .returning();

      return { currentRecord: currentRecord[0], newRecord: newRecord[0] };
    });

    console.log(`✅ Updated vehicle ${vehicleId} status to ${newStatus}`);
    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating vehicle inventory status:", error);
    return { success: false, error: "Failed to update vehicle status" };
  }
}

// Get current inventory (only active records)
export async function getCurrentInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const inventoryVehicles = await db
      .select({
        inventoryId: inventory.id,
        vehicleId: inventory.vehicleId,
        status: inventory.status,
        assignedToPartyId: inventory.assignedToPartyId,
        location: inventory.location,
        notes: inventory.notes,
        effectiveFrom: inventory.effectiveFrom,
        // Vehicle details
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        // Make/Model info
        make: vehicleMake.name,
        model: vehicleModel.model,
        // Assigned driver info
        assignedDriverName: sql<string>`CONCAT(${individual.firstName}, ' ', ${individual.lastName})`,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(inventory.assignedToPartyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp` // Only current records
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: inventoryVehicles };
  } catch (error) {
    console.error("Error fetching current inventory:", error);
    return { success: false, error: "Failed to fetch inventory" };
  }
}

// Get available vehicles for assignment
export async function getAvailableInventoryVehicles(): Promise<{
  success: boolean;
  data?: any[];
  error?: string;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      throw new Error("Admin party ID not found");
    }

    const availableVehicles = await db
      .select({
        vehicleId: inventory.vehicleId,
        inventoryId: inventory.id,
        make: vehicleMake.name,
        model: vehicleModel.model,
        year: vehicles.manufacturingYear,
        color: vehicles.color,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        location: inventory.location,
      })
      .from(inventory)
      .leftJoin(vehicles, eq(inventory.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(inventory.adminPartyId, adminPartyId),
          eq(inventory.status, "available"),
          sql`${inventory.effectiveTo} = 'infinity'::timestamp`
        )
      )
      .orderBy(desc(inventory.effectiveFrom));

    return { success: true, data: availableVehicles };
  } catch (error) {
    console.error("Error fetching available vehicles:", error);
    return { success: false, error: "Failed to fetch available vehicles" };
  }
}
