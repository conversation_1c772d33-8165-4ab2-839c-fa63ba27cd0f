/**
 * Tests for DocumentStatusBadge component
 */

import React from "react";
import { render, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import DocumentStatusBadge from "@/components/DocumentStatusBadge";
import type { DocumentWithStatus } from "@/lib/document-grouping";

// Mock document data for testing
const createMockDocument = (
  overrides: Partial<DocumentWithStatus> = {}
): DocumentWithStatus => ({
  id: 1,
  documentType: "ID Document",
  documentUrl: "https://example.com/doc.pdf",
  uploadedAt: "2024-01-15T10:30:00Z",
  isLatest: true,
  version: 1,
  ...overrides,
});

describe("DocumentStatusBadge", () => {
  it("should render pending status correctly", () => {
    const document = createMockDocument({
      status: "pending",
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Pending Review")).toBeInTheDocument();
    expect(screen.getByRole("status")).toHaveAttribute(
      "aria-label",
      "Document status: Pending Review"
    );
  });

  it("should render verified status correctly", () => {
    const document = createMockDocument({
      status: "verified",
      statusAt: "2024-01-15T14:30:00Z",
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Verified")).toBeInTheDocument();
    expect(screen.getByText(/Updated/)).toBeInTheDocument();
  });

  it("should render rejected status correctly", () => {
    const document = createMockDocument({
      status: "rejected",
      statusAt: "2024-01-15T14:30:00Z",
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Rejected")).toBeInTheDocument();
    expect(screen.getByText(/Updated/)).toBeInTheDocument();
  });

  it("should render superseded status correctly", () => {
    const document = createMockDocument({
      status: "superseded",
      isLatest: false,
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Superseded")).toBeInTheDocument();
  });

  it("should render uploaded status correctly", () => {
    const document = createMockDocument({
      status: "uploaded",
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Uploaded")).toBeInTheDocument();
  });

  it("should hide timestamp when showTimestamp is false", () => {
    const document = createMockDocument({
      status: "verified",
      statusAt: "2024-01-15T14:30:00Z",
    });

    render(<DocumentStatusBadge document={document} showTimestamp={false} />);

    expect(screen.getByText("Verified")).toBeInTheDocument();
    expect(screen.queryByText(/Updated/)).not.toBeInTheDocument();
  });

  it("should show upload timestamp when no status timestamp exists", () => {
    const document = createMockDocument({
      status: "pending",
      uploadedAt: "2024-01-15T10:30:00Z",
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText(/Uploaded/)).toBeInTheDocument();
  });

  it("should handle different sizes correctly", () => {
    const document = createMockDocument({
      status: "verified",
    });

    const { rerender } = render(
      <DocumentStatusBadge document={document} size="sm" />
    );
    expect(screen.getByRole("status")).toHaveClass("px-2", "py-1", "text-xs");

    rerender(<DocumentStatusBadge document={document} size="lg" />);
    expect(screen.getByRole("status")).toHaveClass("px-4", "py-2", "text-base");
  });

  it("should default to pending status when no status is provided", () => {
    const document = createMockDocument({
      status: undefined,
    });

    render(<DocumentStatusBadge document={document} />);

    expect(screen.getByText("Pending Review")).toBeInTheDocument();
  });

  it("should apply correct color classes for each status", () => {
    const statuses = [
      { status: "verified", colorClass: "text-green-700" },
      { status: "rejected", colorClass: "text-red-700" },
      { status: "superseded", colorClass: "text-gray-700" },
      { status: "uploaded", colorClass: "text-blue-700" },
      { status: "pending", colorClass: "text-yellow-700" },
    ] as const;

    statuses.forEach(({ status, colorClass }) => {
      const document = createMockDocument({ status });
      const { container } = render(<DocumentStatusBadge document={document} />);

      expect(container.querySelector(`.${colorClass}`)).toBeInTheDocument();
    });
  });
});
