import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ApplicationDocumentUploader, { type UploadedDocument } from '@/components/ApplicationDocumentUploader';

// Mock the dependencies
jest.mock('@/lib/utils', () => ({
  DocumentUpload: jest.fn(),
}));

jest.mock('@/actions/applications', () => ({
  uploadApplicationDocumentsAction: jest.fn(),
}));

const mockDocumentUpload = require('@/lib/utils').DocumentUpload;
const mockUploadApplicationDocumentsAction = require('@/actions/applications').uploadApplicationDocumentsAction;

describe('ApplicationDocumentUploader', () => {
  const mockDocuments: UploadedDocument[] = [
    {
      name: "Driver's License",
      documentType: "drivers_license",
      uploaded: false,
      required: true,
    },
    {
      name: "<PERSON><PERSON>",
      documentType: "selfie",
      uploaded: false,
      required: true,
      isSpecial: true,
    },
  ];

  const mockSetDocuments = jest.fn();
  const mockOnUploadComplete = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders document upload sections', () => {
    render(
      <ApplicationDocumentUploader
        applicationType="ehailing"
        documents={mockDocuments}
        setDocuments={mockSetDocuments}
        onUploadComplete={mockOnUploadComplete}
        onError={mockOnError}
      />
    );

    expect(screen.getByText("Driver's License")).toBeInTheDocument();
    expect(screen.getByText("Selfie")).toBeInTheDocument();
  });

  it('handles successful file upload', async () => {
    mockDocumentUpload.mockResolvedValue({ path: 'test-path/file.pdf' });
    mockUploadApplicationDocumentsAction.mockResolvedValue({ success: true });

    render(
      <ApplicationDocumentUploader
        applicationType="ehailing"
        applicationId={123}
        documents={mockDocuments}
        setDocuments={mockSetDocuments}
        onUploadComplete={mockOnUploadComplete}
        onError={mockOnError}
      />
    );

    // Create a test file
    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    
    // Find the first file input (for driver's license)
    const fileInput = screen.getAllByRole('button')[0].parentElement?.querySelector('input[type="file"]');
    
    if (fileInput) {
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(mockDocumentUpload).toHaveBeenCalledWith(file, 'applications');
      });

      await waitFor(() => {
        expect(mockSetDocuments).toHaveBeenCalled();
      });

      await waitFor(() => {
        expect(mockUploadApplicationDocumentsAction).toHaveBeenCalledWith(123, [
          {
            documentType: 'drivers_license',
            documentUrl: 'test-path/file.pdf',
          },
        ]);
      });
    }
  });

  it('handles upload errors', async () => {
    mockDocumentUpload.mockRejectedValue(new Error('Upload failed'));

    render(
      <ApplicationDocumentUploader
        applicationType="ehailing"
        documents={mockDocuments}
        setDocuments={mockSetDocuments}
        onUploadComplete={mockOnUploadComplete}
        onError={mockOnError}
      />
    );

    const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
    const fileInput = screen.getAllByRole('button')[0].parentElement?.querySelector('input[type="file"]');
    
    if (fileInput) {
      fireEvent.change(fileInput, { target: { files: [file] } });

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('Failed to upload document. Please try again.');
      });
    }
  });

  it('shows uploaded state for completed documents', () => {
    const uploadedDocuments: UploadedDocument[] = [
      {
        name: "Driver's License",
        documentType: "drivers_license",
        uploaded: true,
        required: true,
        s3Path: 'test-path/license.pdf',
      },
    ];

    render(
      <ApplicationDocumentUploader
        applicationType="ehailing"
        documents={uploadedDocuments}
        setDocuments={mockSetDocuments}
        onUploadComplete={mockOnUploadComplete}
        onError={mockOnError}
      />
    );

    // Should show checkmark for uploaded document
    expect(screen.getByText('Uploaded')).toBeInTheDocument();
  });
});
