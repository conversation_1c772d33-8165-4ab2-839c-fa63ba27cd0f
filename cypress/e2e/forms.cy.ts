describe('Form Tests', () => {
  beforeEach(() => {
    cy.visit("/home");
    cy.fixture('user').as('user'); // Load the fixture and alias it
  });

  it("test login form", () => {
    cy.contains(/Sign In/i).click();

    cy.get('@user').then((user) => {
      cy.get('form[data-amplify-authenticator-signin] input[name="username"]').type(user.email);
      cy.get('form[data-amplify-authenticator-signin] input[name="password"]').type(user.password);
      cy.get('form[data-amplify-authenticator-signin]')
        .find('button[type="submit"]')
        .click();

      cy.contains(/Hi there/i).should('not.exist');
    });
  });
});
