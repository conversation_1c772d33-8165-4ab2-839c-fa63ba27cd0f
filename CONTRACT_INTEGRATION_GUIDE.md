# Contract Integration Guide - Task 9

## Overview
This guide shows how to integrate our comprehensive contract management system into the assignments page, building on the deposit functionality from Task 8.

## New Components Created

### 1. AssignmentContractIntegration.tsx
A comprehensive contract management component with:
- ✅ **Contract Upload**: PDF file upload with validation
- ✅ **Contract Replacement**: Version control with replacement reasons
- ✅ **Contract Download**: Secure download with audit trails
- ✅ **Contract History**: Complete version history tracking
- ✅ **Contract Deletion**: Soft delete with deletion reasons
- ✅ **Contract Status**: Real-time active/inactive status tracking

### 2. EnhancedAssignmentsTable.tsx
An enhanced assignments table featuring:
- ✅ **Financial Dashboard**: Overview of deposits, contracts, and outstanding amounts
- ✅ **Contract Status Display**: Visual indicators for contract status
- ✅ **Integrated Management**: Direct access to deposit and contract management
- ✅ **Search & Filter**: Advanced filtering by status and search criteria
- ✅ **Real-time Updates**: Automatic refresh after contract operations

## Integration Options

### Option 1: Replace Existing Assignments Table

**File:** `app/(admin)/admin/assignments/page.tsx`

Replace the existing table with our enhanced version:

```tsx
// Import the enhanced table
import EnhancedAssignmentsTable from "./components/EnhancedAssignmentsTable";

// Replace the existing table component
<EnhancedAssignmentsTable
  assignments={assignments}
  loading={loading}
  onAssignmentSelect={setSelectedAssignment}
  onRefresh={loadData}
/>
```

### Option 2: Add Contract Tab to Existing Dialog

**File:** `app/(admin)/admin/assignments/components/AssignmentDetailsDialog.tsx`

Add contract management alongside existing tabs:

```tsx
// Add import
import AssignmentContractIntegration from "./AssignmentContractIntegration";

// In the TabsList, add:
<TabsTrigger value="contracts">
  Contracts
  {assignment.hasActiveContract && (
    <Badge variant="secondary" className="ml-2">Active</Badge>
  )}
</TabsTrigger>

// In the TabsContent sections, add:
<TabsContent value="contracts" className="space-y-6">
  <AssignmentContractIntegration
    assignmentId={assignment.id}
    driverName={assignment.driverName}
    onContractStatusChange={(hasContract) => {
      // Update assignment status
      onUpdate?.({ ...assignment, hasActiveContract: hasContract });
    }}
  />
</TabsContent>
```

### Option 3: Hybrid Approach (Recommended)

Combine both approaches for maximum functionality:

```tsx
// Use enhanced table for main view
<EnhancedAssignmentsTable
  assignments={assignments}
  loading={loading}
  onAssignmentSelect={setSelectedAssignment}
  onRefresh={loadData}
/>

// Keep existing detailed dialog for comprehensive management
<AssignmentDetailsDialog
  isOpen={isDetailsDialogOpen}
  onClose={() => setIsDetailsDialogOpen(false)}
  assignment={selectedAssignment}
  mode="view"
  onUpdate={handleUpdateAssignment}
  onRecordPayment={handleRecordPayment}
  onTerminate={handleTerminate}
/>
```

## Implementation Steps

### Step 1: Enhanced Data Loading

Update assignment data loading to include contract status:

```tsx
// In your assignments page component
import { getContractsSummaryAction } from "@/actions/admin/contracts";
import { getDepositsSummaryAction } from "@/actions/admin/deposits";

const loadAssignmentsWithFinancialData = async () => {
  try {
    setLoading(true);
    
    // Load base assignments
    const assignmentsResult = await getInventoryVehicleDriverAssignments();
    
    if (assignmentsResult.success && assignmentsResult.data) {
      const assignmentIds = assignmentsResult.data.map(a => parseInt(a.id));
      
      // Load financial data in parallel
      const [depositsResult, contractsResult] = await Promise.all([
        getDepositsSummaryAction(assignmentIds),
        getContractsSummaryAction(assignmentIds),
      ]);
      
      // Enhance assignments with financial data
      const enhancedAssignments = assignmentsResult.data.map(assignment => {
        const depositSummary = depositsResult.success 
          ? depositsResult.data?.find(d => d.assignmentId === parseInt(assignment.id))
          : null;
        
        const contractSummary = contractsResult.success
          ? contractsResult.data?.find(c => c.assignmentId === parseInt(assignment.id))
          : null;

        return {
          ...assignment,
          depositBalance: depositSummary?.totalBalanceRemaining || 0,
          totalDeposits: depositSummary?.totalDepositAmount || 0,
          hasActiveContract: contractSummary?.hasActiveContract || false,
          contractCount: contractSummary?.contractCount || 0,
          lastContractUpload: contractSummary?.lastUploadDate,
        };
      });
      
      setAssignments(enhancedAssignments);
    }
  } catch (error) {
    console.error("Error loading assignments:", error);
    toast.error("Failed to load assignments data");
  } finally {
    setLoading(false);
  }
};
```

### Step 2: Add Contract Status Column

Enhance the existing assignments table with contract status:

```tsx
// Add to table headers
<TableHead>Contract Status</TableHead>

// Add to table body
<TableCell>
  <div className="flex items-center gap-2">
    {assignment.hasActiveContract ? (
      <Badge variant="outline" className="bg-green-100 text-green-800 border-green-200">
        <CheckCircle size={12} className="mr-1" />
        Active
      </Badge>
    ) : (
      <Badge variant="outline" className="bg-red-100 text-red-800 border-red-200">
        <AlertTriangle size={12} className="mr-1" />
        Missing
      </Badge>
    )}
    {assignment.contractCount > 0 && (
      <span className="text-xs text-gray-500">({assignment.contractCount})</span>
    )}
  </div>
</TableCell>
```

### Step 3: Add Quick Contract Actions

Add contract management to the actions dropdown:

```tsx
// In the actions dropdown menu
<DropdownMenuItem onClick={() => handleContractAction(assignment, "upload")}>
  <Upload className="mr-2 h-4 w-4" />
  <span>{assignment.hasActiveContract ? "Replace Contract" : "Upload Contract"}</span>
</DropdownMenuItem>

{assignment.hasActiveContract && (
  <DropdownMenuItem onClick={() => handleContractAction(assignment, "download")}>
    <Download className="mr-2 h-4 w-4" />
    <span>Download Contract</span>
  </DropdownMenuItem>
)}

<DropdownMenuItem onClick={() => handleContractAction(assignment, "manage")}>
  <FileText className="mr-2 h-4 w-4" />
  <span>Manage Contracts</span>
</DropdownMenuItem>
```

### Step 4: Contract Management Handlers

Implement handlers for contract operations:

```tsx
const [selectedAssignmentForContract, setSelectedAssignmentForContract] = useState<Assignment | null>(null);
const [contractAction, setContractAction] = useState<"upload" | "download" | "manage" | null>(null);

const handleContractAction = (assignment: Assignment, action: "upload" | "download" | "manage") => {
  setSelectedAssignmentForContract(assignment);
  setContractAction(action);
  
  if (action === "manage") {
    // Open contract management dialog
    setIsContractDialogOpen(true);
  } else if (action === "download" && assignment.hasActiveContract) {
    // Handle direct download
    handleDirectDownload(assignment);
  } else if (action === "upload") {
    // Open upload dialog
    setIsUploadDialogOpen(true);
  }
};

const handleDirectDownload = async (assignment: Assignment) => {
  try {
    const contractResult = await getAssignmentContractAction(parseInt(assignment.id));
    if (contractResult.success && contractResult.data) {
      const downloadResult = await generateContractDownloadAction(contractResult.data.id);
      if (downloadResult.success && downloadResult.data) {
        const link = document.createElement('a');
        link.href = downloadResult.data;
        link.download = `contract-${assignment.id}-${contractResult.data.originalFilename}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        toast.success("Contract download started");
      }
    }
  } catch (error) {
    console.error("Error downloading contract:", error);
    toast.error("Failed to download contract");
  }
};
```

## Advanced Features

### 1. Contract Status Filtering

Add contract status to filter options:

```tsx
// Enhanced filter dropdown
<DropdownMenuContent>
  <DropdownMenuItem onClick={() => setFilterStatus("all")}>
    All Assignments
  </DropdownMenuItem>
  <DropdownMenuSeparator />
  <DropdownMenuItem onClick={() => setFilterStatus("has_contract")}>
    With Active Contract
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => setFilterStatus("missing_contract")}>
    Missing Contract
  </DropdownMenuItem>
  <DropdownMenuSeparator />
  <DropdownMenuItem onClick={() => setFilterStatus("active")}>
    Active Status
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => setFilterStatus("pending_setup")}>
    Pending Setup
  </DropdownMenuItem>
</DropdownMenuContent>

// Enhanced filtering logic
const filteredAssignments = assignments.filter(assignment => {
  const matchesSearch = /* ... existing search logic ... */;
  
  let matchesFilter = true;
  if (filterStatus === "has_contract") {
    matchesFilter = assignment.hasActiveContract === true;
  } else if (filterStatus === "missing_contract") {
    matchesFilter = assignment.hasActiveContract !== true;
  } else if (filterStatus !== "all") {
    matchesFilter = assignment.status === filterStatus;
  }
  
  return matchesSearch && matchesFilter;
});
```

### 2. Contract Compliance Dashboard

Add contract compliance metrics to the financial dashboard:

```tsx
// Enhanced financial dashboard with contract metrics
<div className="grid grid-cols-6 gap-4">
  {/* Existing cards */}
  <Card>
    <CardContent className="p-4">
      <div className="flex items-center">
        <FileText className="h-8 w-8 text-purple-600" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Contract Compliance</p>
          <p className="text-2xl font-bold">
            {Math.round((contractsUploaded / assignments.length) * 100)}%
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
  
  <Card>
    <CardContent className="p-4">
      <div className="flex items-center">
        <AlertTriangle className="h-8 w-8 text-orange-600" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Missing Contracts</p>
          <p className="text-2xl font-bold">
            {assignments.length - contractsUploaded}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
```

### 3. Bulk Contract Operations

Add bulk contract management capabilities:

```tsx
// Bulk contract operations
const [selectedAssignments, setSelectedAssignments] = useState<string[]>([]);

const handleBulkContractReminder = async () => {
  const assignmentsWithoutContract = selectedAssignments.filter(id => {
    const assignment = assignments.find(a => a.id === id);
    return assignment && !assignment.hasActiveContract;
  });
  
  if (assignmentsWithoutContract.length === 0) {
    toast.error("No assignments without contracts selected");
    return;
  }
  
  // Send reminder emails or notifications
  toast.success(`Contract reminders sent to ${assignmentsWithoutContract.length} drivers`);
};

// In the table, add checkbox selection
<TableCell>
  <input
    type="checkbox"
    checked={selectedAssignments.includes(assignment.id)}
    onChange={(e) => {
      if (e.target.checked) {
        setSelectedAssignments(prev => [...prev, assignment.id]);
      } else {
        setSelectedAssignments(prev => prev.filter(id => id !== assignment.id));
      }
    }}
  />
</TableCell>
```

## Contract Management Features

### ✅ Complete Contract Lifecycle
- **Upload**: PDF contract upload with file validation
- **Replace**: Version-controlled contract replacement with audit trails
- **Download**: Secure contract download with access logging
- **History**: Complete version history with replacement reasons
- **Delete**: Soft delete with deletion reasons and audit trails

### ✅ Contract Status Tracking
- **Active/Inactive Status**: Real-time contract status monitoring
- **Version Control**: Track multiple contract versions
- **Upload Dates**: Monitor contract upload and replacement dates
- **File Metadata**: Track file size, type, and original filename

### ✅ Integration with Assignments
- **Status Display**: Visual contract status in assignments table
- **Quick Actions**: Direct contract operations from assignments list
- **Compliance Metrics**: Dashboard showing contract compliance rates
- **Filtering**: Filter assignments by contract status

### ✅ Security & Compliance
- **File Validation**: PDF-only uploads with size limits
- **Access Control**: Admin-only contract management
- **Audit Trails**: Complete logging of all contract operations
- **Secure Storage**: S3-based contract storage with signed URLs

## Benefits of Integration

### 1. Streamlined Workflow
- **Single Interface**: Manage both deposits and contracts from assignments page
- **Quick Actions**: Upload, replace, or download contracts without navigation
- **Status Visibility**: Immediate contract status visibility in assignments list

### 2. Enhanced Compliance
- **Contract Tracking**: Monitor which assignments lack contracts
- **Compliance Dashboard**: Real-time compliance metrics and statistics
- **Audit Trails**: Complete history of all contract operations

### 3. Improved User Experience
- **Integrated Management**: Deposits and contracts in unified interface
- **Visual Indicators**: Clear status badges and progress indicators
- **Efficient Operations**: Bulk operations and quick actions

### 4. Scalable Architecture
- **Modular Components**: Easy to extend with additional features
- **Clean APIs**: Well-structured server actions and database operations
- **Type Safety**: Full TypeScript support with proper error handling

## Next Steps

After implementing contract integration:

1. **Add Weekly Earnings Integration** (Task 10): Track weekly earnings alongside deposits and contracts
2. **Implement Payout Management** (Task 11): Complete financial cycle with payout processing
3. **Add Debt Management** (Task 12): Track outstanding debts and shortfalls
4. **Create Financial Reports** (Task 13): Comprehensive reporting across all financial aspects

This creates a complete assignment management system with integrated financial and contractual tracking.

## Quick Start Implementation

### Minimal Integration (15 minutes)
```tsx
// 1. Add contract status column to existing table
<TableHead>Contract</TableHead>

// 2. Add contract status display
<TableCell>
  {assignment.hasActiveContract ? (
    <Badge className="bg-green-100 text-green-800">Active</Badge>
  ) : (
    <Badge className="bg-red-100 text-red-800">Missing</Badge>
  )}
</TableCell>

// 3. Add contract action to dropdown
<DropdownMenuItem onClick={() => handleContractManagement(assignment)}>
  <FileText className="mr-2 h-4 w-4" />
  Manage Contract
</DropdownMenuItem>
```

### Full Integration (1-2 hours)
1. Replace assignments table with `EnhancedAssignmentsTable`
2. Add contract status loading in data fetching
3. Integrate `AssignmentContractIntegration` component
4. Update financial dashboard with contract metrics
5. Add contract status filtering and search
6. Test all contract operations (upload, replace, download, delete)

The contract integration provides immediate value with minimal effort while offering comprehensive functionality for advanced use cases. 