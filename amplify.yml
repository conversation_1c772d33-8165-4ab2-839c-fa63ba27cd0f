version: 1
backend:
  phases:
    build:
      commands:
        - echo "🔧 Building backend infrastructure..."
        - echo "DATABASE_URL=$DATABASE_URL" >> .env
        - echo "WEB_DATABASE_URL=$WEB_DATABASE_URL" >> .env
        - echo "NEXT_PUBLIC_VAPID_PUBLIC_KEY=$NEXT_PUBLIC_VAPID_PUBLIC_KEY" >> .env
        - echo "VAPID_PRIVATE_KEY=$VAPID_PRIVATE_KEY" >> .env
        - echo "VAPID_EMAIL=$VAPID_EMAIL" >> .env
        - echo "VAPID_PUBLIC_KEY=$VAPID_PUBLIC_KEY" >> .env
        - echo "NEXT_PUBLIC_NOTIFICATION_TYPES=$NEXT_PUBLIC_NOTIFICATION_TYPES" >> .env
        - npm install --cache .npm --prefer-offline --legacy-peer-deps
        - npx ampx pipeline-deploy --branch $AWS_BRANCH --app-id $AWS_APP_ID
frontend:
  phases:
    build:
      commands:
        - echo "🚀 Building frontend..."
        - echo "DATABASE_URL=$DATABASE_URL" >> .env
        - echo "WEB_DATABASE_URL=$WEB_DATABASE_URL" >> .env
        - echo "NEXT_PUBLIC_VAPID_PUBLIC_KEY=$NEXT_PUBLIC_VAPID_PUBLIC_KEY" >> .env
        - echo "VAPID_PRIVATE_KEY=$VAPID_PRIVATE_KEY" >> .env
        - echo "VAPID_EMAIL=$VAPID_EMAIL" >> .env
        - echo "VAPID_PUBLIC_KEY=$VAPID_PUBLIC_KEY" >> .env
        - echo "NEXT_PUBLIC_NOTIFICATION_TYPES=$NEXT_PUBLIC_NOTIFICATION_TYPES" >> .env
        - export NODE_OPTIONS="--max-old-space-size=8192"
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - "**/*"
  cache:
    paths:
      - .next/cache/**/*
      - .npm/**/*
      - node_modules/**/*
