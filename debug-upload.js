// Debug script for document upload issues
// Run this in browser console if you still have problems

console.log("=== Document Upload Debug ===");

// Check 1: Amplify Configuration
console.log("1. Checking Amplify configuration...");
try {
  const { Amplify } = window.aws_amplify || {};
  if (Amplify) {
    console.log("✅ Amplify is loaded");
    console.log("Config:", Amplify.getConfig());
  } else {
    console.log("❌ Amplify not found");
  }
} catch (e) {
  console.log("❌ Error checking Amplify:", e);
}

// Check 2: Authentication Status
console.log("2. Checking authentication...");
try {
  // This will be available if user is authenticated
  if (window.localStorage.getItem('amplify-signin-with-hostedUI')) {
    console.log("✅ User appears to be authenticated");
  } else {
    console.log("❌ User may not be authenticated");
  }
} catch (e) {
  console.log("❌ Error checking auth:", e);
}

// Check 3: Storage Permissions
console.log("3. Checking storage access...");
try {
  // Try to access uploadData function
  const { uploadData } = window.aws_amplify_storage || {};
  if (uploadData) {
    console.log("✅ Storage functions available");
  } else {
    console.log("❌ Storage functions not available");
  }
} catch (e) {
  console.log("❌ Error checking storage:", e);
}

// Check 4: Network Connectivity
console.log("4. Testing network connectivity...");
fetch('/api/health')
  .then(response => {
    if (response.ok) {
      console.log("✅ Network connectivity OK");
    } else {
      console.log("❌ Network issues detected");
    }
  })
  .catch(e => {
    console.log("❌ Network error:", e);
  });

// Check 5: Console Errors
console.log("5. Check for any console errors above this message");
console.log("6. Check Network tab for failed requests");
console.log("7. Try uploading a small test file (< 1MB)");

// Test function to create and upload a small file
window.testSmallUpload = async function() {
  try {
    console.log("Creating test file...");
    const canvas = document.createElement('canvas');
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'blue';
    ctx.fillRect(0, 0, 10, 10);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve));
    const testFile = new File([blob], 'test.png', { type: 'image/png' });
    
    console.log("Test file created:", testFile);
    console.log("File size:", testFile.size, "bytes");
    
    // Try to import and use DocumentUpload
    const { DocumentUpload } = await import('/lib/utils.ts');
    console.log("DocumentUpload function imported");
    
    const result = await DocumentUpload(testFile, 'test');
    console.log("✅ Upload test successful:", result);
    
    return result;
  } catch (error) {
    console.log("❌ Upload test failed:", error);
    return null;
  }
};

console.log("Run testSmallUpload() to test with a tiny file");
