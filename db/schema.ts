import { pgTable, text, timestamp, integer, boolean, json } from "drizzle-orm/pg-core";

export const formSubmissions = pgTable("form_submissions", {
  // Primary key and metadata
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  formType: text("form_type", { 
    enum: ["business", "co-own", "management", "rideshare", "monetise", "waitlist"] 
  }).notNull(),
  submittedAt: timestamp("submitted_at").defaultNow().notNull(),
  
  // Common customer information fields (always present)
  fullName: text("full_name").notNull(),
  email: text("email").notNull(),
  companyName: text("company_name"), // nullable
  phoneNumber: text("phone_number"), // nullable
  
  // Form-specific data stored as JSON
  formData: json("form_data").notNull(), // All form-specific fields
  
  // Email processing status
  emailSent: boolean("email_sent").default(false).notNull(),
  emailSentAt: timestamp("email_sent_at"), // nullable
  emailError: text("email_error"), // nullable
});

// Push subscriptions table for PWA notifications
export const pushSubscriptions = pgTable("push_subscriptions", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").notNull(), // AWS Cognito user ID
  partyId: integer("party_id"), // Reference to your party system
  endpoint: text("endpoint").notNull(),
  p256dhKey: text("p256dh_key").notNull(),
  authKey: text("auth_key").notNull(),
  expirationTime: timestamp("expiration_time"),
  userAgent: text("user_agent"),
  deviceInfo: json("device_info"), // Store device details
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Notification log for tracking
export const notificationLogs = pgTable("notification_logs", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").notNull(),
  subscriptionId: text("subscription_id").references(() => pushSubscriptions.id),
  title: text("title").notNull(),
  body: text("body").notNull(),
  payload: json("payload").notNull(),
  sentAt: timestamp("sent_at").defaultNow().notNull(),
  deliveryStatus: text("delivery_status", { 
    enum: ["pending", "sent", "failed", "expired"] 
  }).default("pending"),
  errorMessage: text("error_message"),
  clickedAt: timestamp("clicked_at"),
});

export type FormSubmission = typeof formSubmissions.$inferSelect;
export type InsertFormSubmission = typeof formSubmissions.$inferInsert;
export type PushSubscription = typeof pushSubscriptions.$inferSelect;
export type InsertPushSubscription = typeof pushSubscriptions.$inferInsert;
export type NotificationLog = typeof notificationLogs.$inferSelect;
export type InsertNotificationLog = typeof notificationLogs.$inferInsert;
