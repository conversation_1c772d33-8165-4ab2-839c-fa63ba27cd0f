-- Word Cloud Analysis Tables

-- Main word cloud sessions table
CREATE TABLE word_cloud_analysis (
    id SERIAL PRIMARY KEY,
    form_type VARCHAR(20), -- specific form type or 'all' for combined analysis
    analysis_type VARCHAR(50) NOT NULL, -- 'form_fields', 'full_text', 'specific_field'
    field_name VARCHAR(100), -- specific field name if analyzing single field
    date_range_start TIMESTAMP WITH TIME ZONE,
    date_range_end TIMESTAMP WITH TIME ZONE,
    total_submissions_analyzed INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'processing', -- 'processing', 'completed', 'failed'
    metadata JSONB -- store processing parameters, filters, etc.
);

-- Word frequency results
CREATE TABLE word_cloud_words (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES word_cloud_analysis(id) ON DELETE CASCADE,
    word VARCHAR(100) NOT NULL,
    frequency INTEGER NOT NULL,
    weight DECIMAL(5,4) NOT NULL, -- normalized weight 0-1 for visualization
    category VARCHAR(50), -- categorize words: 'location', 'business_term', 'action', etc.
    sentiment_score DECIMAL(3,2), -- optional sentiment analysis -1 to 1
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Word phrases/n-grams (for capturing multi-word insights)
CREATE TABLE word_cloud_phrases (
    id SERIAL PRIMARY KEY,
    analysis_id INTEGER REFERENCES word_cloud_analysis(id) ON DELETE CASCADE,
    phrase TEXT NOT NULL,
    word_count INTEGER NOT NULL, -- 2 for bigrams, 3 for trigrams, etc.
    frequency INTEGER NOT NULL,
    weight DECIMAL(5,4) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_word_cloud_analysis_form_type ON word_cloud_analysis(form_type);
CREATE INDEX idx_word_cloud_analysis_created_at ON word_cloud_analysis(created_at);
CREATE INDEX idx_word_cloud_words_analysis_id ON word_cloud_words(analysis_id);
CREATE INDEX idx_word_cloud_words_frequency ON word_cloud_words(analysis_id, frequency DESC);
CREATE INDEX idx_word_cloud_phrases_analysis_id ON word_cloud_phrases(analysis_id);

-- Optional: Pre-computed word cloud configurations for common views
CREATE TABLE word_cloud_presets (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    form_types VARCHAR(200)[], -- array of form types to include
    field_names VARCHAR(100)[], -- specific fields to analyze
    exclude_words VARCHAR(50)[], -- custom stop words
    min_word_length INTEGER DEFAULT 3,
    max_words INTEGER DEFAULT 100,
    date_range_days INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert some default presets
INSERT INTO word_cloud_presets (name, description, form_types, field_names, max_words) VALUES
('All Leads Overview', 'Word cloud from all form types and fields', ARRAY['business', 'co-own', 'management', 'rideshare', 'monetise', 'waitlist'], NULL, 50),
('Business Solutions Focus', 'Word cloud focusing on business solution leads', ARRAY['business'], NULL, 75),
('Investment Interest', 'Word cloud from co-ownership applications', ARRAY['co-own'], NULL, 60),
('Service Requests', 'Word cloud from management and e-hailing requests', ARRAY['management', 'rideshare'], NULL, 40); 