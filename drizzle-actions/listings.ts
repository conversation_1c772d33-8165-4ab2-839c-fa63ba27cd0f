"use server";

/**
 * DRIZZLE ACTIONS - LISTINGS & VEHICLES
 * 
 * This file contains all direct database operations for listings and vehicles using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Schema Tables Used:
 * - listings: Vehicle listings for co-ownership
 * - vehicles: Vehicle records
 * - vehicleModel: Vehicle model information
 * - vehicleMake: Vehicle manufacturer information
 * - party: Vehicle owners
 * - individual: Owner details
 */

import { db } from "../db";
import { eq, and, desc, asc, gte, lte, between, sql, inArray } from "drizzle-orm";
import {
  listings,
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  individual,
  listingMedia,
  vehicleMedia,
  listingInterestExpressions,
  company,
  companyOwnership,
  vehicleVariant,
} from "../drizzle/schema";
import { createInitialVehiclePossession } from "./bookings";
import type {
  ListingCreate,
  ListingTypeEnum,
  AudienceEnum,
  ConditionEnum
} from "@/types/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import type { VehicleMakeRead } from "../types/vehicle-makes";
import type { VehicleModelRead, VehicleModelReadWithMake } from "../types/vehicle-model";
import type { VehicleRead, VehicleCreate } from "../types/vehicles";
import type { ListingRead as BaseListingRead } from "../types/listings";

// ==================== TYPES ====================

// Extended ListingRead with database relations - export for use in components  
export interface ListingRead extends Omit<BaseListingRead, 'effective_to'> {
  // Override effective_to to allow null (database can have null for open-ended listings)
  effective_to: string | null;
  
  media?: Array<{
    id: number;
    listingId: number;
    mediaPath: string;
    createdAt?: string;
    updatedAt?: string;
  }>;
  vehicle?: {
    vin_number: string | null;
    vehicle_registration?: string | null;
    color?: string | null;
    manufacturing_year?: number | null;
    countryOfRegistration?: string | null;
    model?: {
      model: string | null;
      year?: number | null;
      make?: {
        name: string | null;
      };
    };
  };
  owner?: {
    first_name: string | null;
    last_name: string | null;
  };
  // Database field mappings for compatibility
  partyId?: number; // maps to party_id  
  listingType?: string; // maps to listing_type
}

export interface ListingMediaRead {
  id: number;
  listingId: number;
  mediaPath: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ListingFormData {
  // Vehicle details
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  location: string;
  description: string;
  vinNumber: string;
  vehicleRegistration?: string;
  countryId?: number;
  
  // Vehicle selection by IDs (for dropdown-based selection)
  makeId?: number;
  modelId?: number;
  variantId?: number;
  
  // Listing details
  fraction: number;
  pricePerFraction: number;
  monthlyRentalPrice: number;
  listingType: string;
  audience: string;
  effectiveFrom: string;
  effectiveTo: string;
  
  // Images
  vehicleImages?: string[];
  
 
}

export interface CreateListingRequest {
  vehicle_id: number;
  listing_type: ListingTypeEnum;
  audience: AudienceEnum;
  condition: ConditionEnum;
  asking_price: number;
  fraction: number;
  effective_from: string;
  effective_to: string;
  mileage?: number;
  company_id?: number; // For private group sharing
}

export interface VehicleWithCompanyFormData {
  // Vehicle details
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  description: string;
  vinNumber: string;
  vehicleRegistration?: string;
  countryId?: number;
  vehicleImages?: string[];
  
  // Company details
  companyId?: number | null;
  newCompany?: {
    name: string;
    description: string;
  } | null;
}

// Enhanced vehicle creation options
export interface VehicleCreateOptions {
  createCompany?: boolean; // Whether to create a company for the vehicle
  companyName?: string; // Custom company name
  companyDescription?: string; // Custom company description
  initialOwnershipFraction?: number; // What fraction the creator owns (0-1)
}

// ==================== VEHICLE CRUD OPERATIONS ====================

// Create a new vehicle
export async function createVehicleDrizzle(
  vehicleData: VehicleCreate
): Promise<VehicleCreate & { id: number }> {
  // Input validation
  if (!vehicleData.party_id || vehicleData.party_id <= 0) {
    throw new Error("Valid party ID is required to create a vehicle");
  }

  if (!vehicleData.model_id || vehicleData.model_id <= 0) {
    throw new Error("Valid model ID is required to create a vehicle");
  }

  if (!vehicleData.vin_number) {
    throw new Error("VIN number is required");
  }

  // Wrap in transaction for data consistency
  return await db.transaction(async (tx) => {
    try {
      // Verify party exists
      const partyExists = await tx
        .select({ id: party.id })
        .from(party)
        .where(eq(party.id, vehicleData.party_id!))
        .limit(1);

      if (partyExists.length === 0) {
        throw new Error(`Party with ID ${vehicleData.party_id} does not exist`);
      }

      // Verify model exists
      const modelExists = await tx
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .where(eq(vehicleModel.id, vehicleData.model_id))
        .limit(1);

      if (modelExists.length === 0) {
        throw new Error(`Vehicle model with ID ${vehicleData.model_id} does not exist`);
      }

      console.log(`Creating vehicle with partyId: ${vehicleData.party_id}, modelId: ${vehicleData.model_id}`);

      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: vehicleData.party_id!,
          modelId: vehicleData.model_id!,
          vinNumber: vehicleData.vin_number,
          vehicleRegistration: vehicleData.vehicle_registration || null,
          countryId: vehicleData.country_id || 1,
          manufacturingYear: vehicleData.manufacturing_year || null,
          purchaseDate: vehicleData.purchase_date || new Date().toISOString(),
          color: vehicleData.color || null,
          isActive: vehicleData.is_active ?? true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      console.log(`Created vehicle with ID: ${newVehicle[0].id} for partyId: ${newVehicle[0].partyId}`);

      // Map database result to domain type
      const vehicleResult = {
        id: newVehicle[0].id,
        party_id: newVehicle[0].partyId,
        model_id: newVehicle[0].modelId,
        vin_number: newVehicle[0].vinNumber,
        vehicle_registration: newVehicle[0].vehicleRegistration || undefined,
        countryId: newVehicle[0].countryId || undefined,
        manufacturing_year: newVehicle[0].manufacturingYear || undefined,
        purchase_date: newVehicle[0].purchaseDate || "",
        color: newVehicle[0].color || "",
        is_active: newVehicle[0].isActive,
      };

      // Create initial possession after vehicle creation
      try {
        await createInitialVehiclePossession(
          vehicleResult.id,
          vehicleResult.party_id,
          vehicleResult.party_id,
          'OWNER' as any,
          'OWNERSHIP_TRANSFER',
          'Initial possession when vehicle was created'
        );
        console.log(`✅ Created initial possession for vehicle ${vehicleResult.id}`);
      } catch (possessionError) {
        console.error("❌ Failed to create initial vehicle possession:", possessionError);
        // Don't fail vehicle creation if possession creation fails
      }

      return vehicleResult;
    } catch (error) {
      console.error("Error in createVehicleDrizzle transaction:", error);
      throw error;
    }
  });
}

// ==================== LISTING CRUD OPERATIONS ====================

// Update listing
export async function updateListingDrizzle(
  listingId: number,
  updateData: Partial<ListingCreate>
): Promise<ListingRead> {
  try {
    // Map snake_case fields from ListingCreate to camelCase fields for Drizzle
    const drizzleUpdateData: any = {};
    
    if (updateData.asking_price !== undefined) {
      drizzleUpdateData.askingPrice = updateData.asking_price;
    }
    if (updateData.fraction !== undefined) {
      drizzleUpdateData.fraction = updateData.fraction;
    }
    if (updateData.effective_from !== undefined) {
      drizzleUpdateData.effectiveFrom = updateData.effective_from;
    }
    if (updateData.effective_to !== undefined) {
      drizzleUpdateData.effectiveTo = updateData.effective_to;
    }
    if (updateData.condition !== undefined) {
      drizzleUpdateData.condition = updateData.condition;
    }
    if (updateData.mileage !== undefined) {
      drizzleUpdateData.mileage = updateData.mileage;
    }
    if (updateData.audience !== undefined) {
      drizzleUpdateData.audience = updateData.audience;
    }
    if (updateData.listing_type !== undefined) {
      drizzleUpdateData.listingType = updateData.listing_type;
    }

    console.log("Updating listing with data:", drizzleUpdateData);

    const updatedListing = await db
      .update(listings)
      .set({
        ...drizzleUpdateData,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(listings.id, listingId))
      .returning();

    console.log("Updated listing result:", updatedListing);

    if (updatedListing.length === 0) {
      throw new Error("Listing not found");
    }

    // Get the full listing data with related information
    const fullListingData = await getListingByIdDrizzle(listingId);
    if (!fullListingData) {
      throw new Error("Failed to retrieve updated listing");
    }

    return fullListingData;
  } catch (error) {
    console.error("Error updating listing:", error);
    throw error;
  }
}

// End listing (set effective_to to current date)
export async function endListingDrizzle(listingId: number): Promise<void> {
  try {
    const result = await db
      .update(listings)
      .set({
        effectiveTo: new Date().toISOString().split('T')[0], // Today's date
        updatedAt: new Date().toISOString(),
      })
      .where(eq(listings.id, listingId))
      .returning();

    if (result.length === 0) {
      throw new Error("Listing not found");
    }
  } catch (error) {
    console.error("Error ending listing:", error);
    throw error;
  }
}

// Get listing by ID with related data
export async function getListingByIdDrizzle(
  listingId: number
): Promise<ListingRead | null> {
  try {
    // First get the listing with vehicle details
    const listing = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fraction: listings.fraction,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        vehiclecountryId: vehicles.countryId,
        vehiclePurchaseDate: vehicles.purchaseDate,
        vehicleModelId: vehicles.modelId,
        vehiclePartyId: vehicles.partyId,
        vehicleIsActive: vehicles.isActive,
        vehicleCreatedAt: vehicles.createdAt,
        vehicleUpdatedAt: vehicles.updatedAt,
        // Model details
        modelName: vehicleModel.model,
        modelFirstYear: vehicleModel.firstYear,
        modelLastYear: vehicleModel.lastYear,
        modelMakeId: vehicleModel.makeId,
        // Make details
        makeName: vehicleMake.name,
        makeId: vehicleMake.id,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(listings.id, listingId))
      .limit(1);

    if (listing.length === 0) return null;

    const record = listing[0];

    // Get listing media separately
    const mediaRecords = await db
      .select({
        id: listingMedia.id,
        listingId: listingMedia.listingId,
        mediaPath: listingMedia.mediaPath,
        createdAt: listingMedia.createdAt,
        updatedAt: listingMedia.updatedAt,
      })
      .from(listingMedia)
      .where(eq(listingMedia.listingId, listingId));
    
    return {
      id: record.id,
      party_id: record.partyId,
      vehicle_id: record.vehicleId,
      effective_from: record.effectiveFrom,
      effective_to: record.effectiveTo,
      fraction: record.fraction,
      asking_price: record.askingPrice,
      condition: record.condition as ConditionEnum,
      mileage: record.mileage || undefined,
      listing_type: record.listingType as ListingTypeEnum,
      audience: record.audience as AudienceEnum,
      created_at: record.createdAt || "",
      updated_at: record.updatedAt || "",
      // Include media data
      media: mediaRecords.map(media => ({
        id: media.id,
        listingId: media.listingId,
        mediaPath: media.mediaPath,
        createdAt: media.createdAt || undefined,
        updatedAt: media.updatedAt || undefined,
      })),
      // Include vehicle details  
      vehicle: {
        vin_number: record.vehicleVin,
        vehicle_registration: record.vehicleRegistration,
        color: record.vehicleColor,
        manufacturing_year: record.vehicleYear,
        countryOfRegistration: undefined, // Not fetched in this query
        model: {
          model: record.modelName,
          year: record.modelFirstYear,
          make: {
            name: record.makeName
          }
        }
      },
      // Include owner details
      owner: {
        first_name: record.ownerFirstName,
        last_name: record.ownerLastName
      }
    };
  } catch (error) {
    console.error("Error fetching listing:", error);
    throw error;
  }
}

// Get all listings with filters
export async function getAllListingsDrizzle(options: {
  page?: number;
  limit?: number;
  audience?: "BUSINESS" | "E_HAILING" | "CONSUMER";
  listingType?: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  condition?: "new" | "used";
  maxPrice?: number;
  minPrice?: number;
  sortBy?: "createdAt" | "askingPrice" | "fraction";
  sortOrder?: "asc" | "desc";
} = {}): Promise<{
  records: ListingRead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const {
      page = 1,
      limit = 20,
      audience,
      listingType,
      condition,
      maxPrice,
      minPrice,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const offset = (page - 1) * limit;

    // Build where clause
    let whereConditions = [];
    
    if (audience) {
      whereConditions.push(eq(listings.audience, audience as AudienceEnum));
    }
    if (listingType) {
      whereConditions.push(eq(listings.listingType, listingType as ListingTypeEnum));
    }
    if (condition) {
      whereConditions.push(eq(listings.condition, condition));
    }
    if (maxPrice) {
      whereConditions.push(lte(listings.askingPrice, maxPrice));
    }
    if (minPrice) {
      whereConditions.push(gte(listings.askingPrice, minPrice));
    }
    
    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Build sort order
    const orderByClause = (() => {
      const column = sortBy === "askingPrice" ? listings.askingPrice
                   : sortBy === "fraction" ? listings.fraction
                   : listings.createdAt;
      
      return sortOrder === "desc" ? desc(column) : asc(column);
    })();

    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(listings)
      .where(whereClause);
    
    const total = totalResult[0].count;

    // Get records with related data
    const records = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fraction: listings.fraction,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicles.manufacturingYear,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Get listing media for all listings
    const listingIds = records.map(record => record.id);
    const mediaRecords = listingIds.length > 0 ? await db
      .select({
        id: listingMedia.id,
        listingId: listingMedia.listingId,
        mediaPath: listingMedia.mediaPath,
        createdAt: listingMedia.createdAt,
        updatedAt: listingMedia.updatedAt,
      })
      .from(listingMedia)
      .where(inArray(listingMedia.listingId, listingIds)) : [];

    // Group media by listing ID
    const mediaByListingId = mediaRecords.reduce((acc, media) => {
      if (!acc[media.listingId]) {
        acc[media.listingId] = [];
      }
      acc[media.listingId].push({
        id: media.id,
        listingId: media.listingId,
        mediaPath: media.mediaPath,
        createdAt: media.createdAt || undefined,
        updatedAt: media.updatedAt || undefined,
      });
      return acc;
    }, {} as Record<number, ListingMediaRead[]>);

    return {
      records: records.map(record => ({
        id: record.id,
        party_id: record.partyId,
        vehicle_id: record.vehicleId,
        effective_from: record.effectiveFrom,
        effective_to: record.effectiveTo,
        fraction: record.fraction,
        asking_price: record.askingPrice,
        condition: record.condition as ConditionEnum,
        mileage: record.mileage || undefined,
        listing_type: record.listingType as ListingTypeEnum,
        audience: record.audience as AudienceEnum,
        created_at: record.createdAt || "",
        updated_at: record.updatedAt || "",
        // Include media data
        media: mediaByListingId[record.id] || [],
        // Include vehicle details
        vehicle: {
          vin_number: record.vehicleVin,
          vehicle_registration: record.vehicleRegistration,
          color: record.vehicleColor,
          manufacturing_year: record.vehicleYear,
          model: {
            model: record.modelName,
            year: record.modelYear,
            make: {
              name: record.makeName
            }
          }
        },
        // Include owner details
        owner: {
          first_name: record.ownerFirstName,
          last_name: record.ownerLastName
        }
      })),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  } catch (error) {
    console.error("Error fetching listings:", error);
    throw error;
  }
}

// ==================== COMBINED OPERATIONS ====================



// Create vehicle with company ownership for group/fleet scenarios
export async function createVehicleWithCompanyOwnership(
  formData: VehicleWithCompanyFormData,
  partyId: number
): Promise<any> {
  // Input validation
  if (!partyId || partyId <= 0) {
    throw new Error("Valid party ID is required to create a vehicle");
  }

  if (!formData.make || !formData.model || !formData.year) {
    throw new Error("Vehicle make, model, and year are required");
  }

  if (!formData.vinNumber) {
    throw new Error("VIN number is required");
  }

  // Wrap the entire operation in a transaction for data consistency
  return await db.transaction(async (tx) => {
    try {
      let companyId = formData.companyId;

      // Create new company if needed
      if (formData.newCompany && !companyId) {
        // Verify party exists before creating company
        const partyExists = await tx
          .select({ id: party.id })
          .from(party)
          .where(eq(party.id, partyId))
          .limit(1);

        if (partyExists.length === 0) {
          throw new Error(`Party with ID ${partyId} does not exist`);
        }

        // First create party for the company (assuming party type 2 is for companies)
        const newParty = await tx
          .insert(party)
          .values({
            partyTypeId: 2, // Assuming 2 is for companies
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
          .returning();

        // Create company (removed companyType field)
        const newCompany = await tx
          .insert(company)
          .values({
            partyId: newParty[0].id,
            name: formData.newCompany.name,
            description: formData.newCompany.description,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
          .returning();

        companyId = newCompany[0].id;

        // Create ownership relationship
        await tx
          .insert(companyOwnership)
          .values({
            partyId: partyId,
            companyId: companyId,
            fraction: "1.0",
            effectiveFrom: new Date().toISOString(),
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
      }

      // Find vehicle make and model
      const makeResult = await tx
        .select()
        .from(vehicleMake)
        .where(eq(vehicleMake.name, formData.make))
        .limit(1);

      if (makeResult.length === 0) {
        throw new Error(`Vehicle make '${formData.make}' not found`);
      }

      const modelResult = await tx
        .select()
        .from(vehicleModel)
        .where(and(
          eq(vehicleModel.makeId, makeResult[0].id),
          eq(vehicleModel.model, formData.model)
        ))
        .limit(1);

      if (modelResult.length === 0) {
        throw new Error(`Vehicle model '${formData.model}' not found for make '${formData.make}'`);
      }

      // Get company's party ID for vehicle ownership
      const companyResult = await tx
        .select()
        .from(company)
        .where(eq(company.id, companyId!))
        .limit(1);

      if (companyResult.length === 0) {
        throw new Error("Company not found");
      }

      console.log(`Creating vehicle with company partyId: ${companyResult[0].partyId}, modelId: ${modelResult[0].id}`);

      // Create vehicle owned by the company
      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: companyResult[0].partyId,
          modelId: modelResult[0].id,
          vinNumber: formData.vinNumber,
          vehicleRegistration: formData.vehicleRegistration,
          countryId: formData.countryId,  
          manufacturingYear: parseInt(formData.year),
          purchaseDate: new Date().toISOString(),
          color: formData.color,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      console.log(`Created company vehicle with ID: ${newVehicle[0].id} for partyId: ${newVehicle[0].partyId}`);

      // Create initial possession for the actual user (who owns the company)
      try {
        await createInitialVehiclePossession(
          newVehicle[0].id,
          partyId, // The user who created it gets possession, not the company
          partyId,
          'OWNER' as any,
          'OWNERSHIP_TRANSFER',
          'Initial possession when company vehicle was created'
        );
        console.log(`✅ Created initial possession for company vehicle ${newVehicle[0].id} → user party ${partyId}`);
      } catch (possessionError) {
        console.error("❌ Failed to create initial vehicle possession:", possessionError);
        // Don't fail vehicle creation if possession creation fails
      }

      // Add vehicle images if provided
      if (formData.vehicleImages && formData.vehicleImages.length > 0) {
        const mediaInserts = formData.vehicleImages.map(imagePath => ({
          vehicleId: newVehicle[0].id,
          mediaPath: imagePath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(mediaInserts);
        console.log(`Inserted ${mediaInserts.length} vehicle media records for company vehicle`);
      }

      return {
        vehicle: newVehicle[0],
        company: { id: companyId },
      };
    } catch (error) {
      console.error("Error in createVehicleWithCompanyOwnership transaction:", error);
      throw error;
    }
  });
}

// Get user's companies
export async function getUserCompanies(): Promise<any[]> {
  try {
    const userAttributes = await getUserAttributes();
    if (!userAttributes?.party_id) {
      throw new Error("User not authenticated");
    }

    const companies = await db
      .select({
        id: company.id,
        party_id: company.partyId,
        name: company.name,
        description: company.description,
        user_fraction: companyOwnership.fraction,
      })
      .from(companyOwnership)
      .leftJoin(company, eq(companyOwnership.companyId, company.id))
      .where(and(
        eq(companyOwnership.partyId, parseInt(userAttributes.party_id)),
        eq(companyOwnership.isActive, true)
      ));

    return companies.filter(c => c.id !== null);
  } catch (error) {
    console.error("Error getting user companies:", error);
    throw error;
  }
}

// Create a vehicle with flexible ownership options
export async function createVehicleWithOwnershipDrizzle(
  vehicleData: VehicleCreate,
  options: VehicleCreateOptions = {}
): Promise<any> {
  // Input validation
  if (!vehicleData.party_id || vehicleData.party_id <= 0) {
    throw new Error("Valid party ID is required to create a vehicle");
  }

  if (!vehicleData.model_id || vehicleData.model_id <= 0) {
    throw new Error("Valid model ID is required to create a vehicle");
  }

  if (!vehicleData.vin_number) {
    throw new Error("VIN number is required");
  }

  // Wrap in transaction for data consistency
  return await db.transaction(async (tx) => {
    try {
      // Verify party exists
      const partyExists = await tx
        .select({ id: party.id })
        .from(party)
        .where(eq(party.id, vehicleData.party_id!))
        .limit(1);

      if (partyExists.length === 0) {
        throw new Error(`Party with ID ${vehicleData.party_id} does not exist`);
      }

      // Verify model exists
      const modelExists = await tx
        .select({ id: vehicleModel.id })
        .from(vehicleModel)
        .where(eq(vehicleModel.id, vehicleData.model_id))
        .limit(1);

      if (modelExists.length === 0) {
        throw new Error(`Vehicle model with ID ${vehicleData.model_id} does not exist`);
      }

      let vehicleOwnerPartyId = vehicleData.party_id;
      let createdCompany = null;
      let ownershipRecord = null;

      // Create company if requested
      if (options.createCompany) {
        console.log("Creating company for vehicle ownership");

        // Create party for the company
        const companyParty = await tx
          .insert(party)
          .values({
            partyTypeId: 2, // Company party type
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
          .returning();

        // Create company
        createdCompany = await tx
          .insert(company)
          .values({
            partyId: companyParty[0].id,
            name: options.companyName || `Vehicle Ownership Company - VIN: ${vehicleData.vin_number}`,
            description: options.companyDescription || `Company created for vehicle ownership - VIN: ${vehicleData.vin_number}`,
            countryId: vehicleData.country_id, // Default to 1 if not provided
            purpose: "Vehicle Ownership",
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
          .returning();

        console.log(`Created company with ID: ${createdCompany[0].id}`);

        // Create ownership record for the creating party
        const ownershipFraction = options.initialOwnershipFraction || 100; // Default to 100% ownership
        
        ownershipRecord = await tx
          .insert(companyOwnership)
          .values({
            partyId: vehicleData.party_id!,
            companyId: createdCompany[0].id,
            fraction: ownershipFraction.toString(),
            effectiveFrom: new Date().toISOString(),
            effectiveTo: new Date().toISOString(),
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          })
          .returning();

        console.log(`Created company ownership: party ${vehicleData.party_id} owns ${ownershipFraction * 100}% of company ${createdCompany[0].id}`);

        // Vehicle will be owned by the company
        vehicleOwnerPartyId = companyParty[0].id;
      }

      console.log(`Creating vehicle with ownerPartyId: ${vehicleOwnerPartyId}, modelId: ${vehicleData.model_id}`);

      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: vehicleOwnerPartyId!,
          modelId: vehicleData.model_id,
          vinNumber: vehicleData.vin_number,
          vehicleRegistration: vehicleData.vehicle_registration || null,
          countryId: vehicleData.country_id || 1, 
          manufacturingYear: vehicleData.manufacturing_year || null,
          purchaseDate: vehicleData.purchase_date || new Date().toISOString(),
          color: vehicleData.color || null,
          isActive: vehicleData.is_active ?? true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      console.log(`Created vehicle with ID: ${newVehicle[0].id} for ownerPartyId: ${newVehicle[0].partyId}`);

      // Create initial possession - the actual user gets possession even if company owns vehicle
      const possessorPartyId = vehicleData.party_id!; // The user who created it gets possession
      try {
        await createInitialVehiclePossession(
          newVehicle[0].id,
          possessorPartyId,
          possessorPartyId,
          'OWNER' as any,
          'OWNERSHIP_TRANSFER',
          options.createCompany 
            ? `Initial possession - user owns vehicle through company ownership`
            : 'Initial possession when vehicle was created'
        );
        console.log(`✅ Created initial possession for vehicle ${newVehicle[0].id} → party ${possessorPartyId}`);
      } catch (possessionError) {
        console.error("❌ Failed to create initial vehicle possession:", possessionError);
        // Don't fail vehicle creation if possession creation fails
      }

      // Return comprehensive result
      return {
        vehicle: {
          id: newVehicle[0].id,
          party_id: newVehicle[0].partyId,
          model_id: newVehicle[0].modelId,
          vin_number: newVehicle[0].vinNumber,
          vehicle_registration: newVehicle[0].vehicleRegistration || undefined,
          countryId: newVehicle[0].countryId || undefined,
          manufacturing_year: newVehicle[0].manufacturingYear || undefined,
          purchase_date: newVehicle[0].purchaseDate || "",
          color: newVehicle[0].color || "",
          is_active: newVehicle[0].isActive,
        },
        company: createdCompany ? {
          id: createdCompany[0].id,
          party_id: createdCompany[0].partyId,
          name: createdCompany[0].name,
          description: createdCompany[0].description,
        } : null,
        companyOwnership: ownershipRecord ? {
          id: ownershipRecord[0].id,
          party_id: ownershipRecord[0].partyId,
          company_id: ownershipRecord[0].companyId,
          fraction: parseFloat(ownershipRecord[0].fraction?.toString() || "0"),
        } : null,
        ownershipType: options.createCompany ? "company" : "direct"
      };
    } catch (error) {
      console.error("Error in createVehicleWithOwnershipDrizzle transaction:", error);
      throw error;
    }
  });
}

// Create vehicle and listing for rental marketplace (direct ownership, listing media)
export async function createVehicleAndRentalOrCoOwnershipListingDrizzle(
  formData: ListingFormData,
  partyId: number // get from user attributes 
): Promise<any> {
  // Input validation
  if (!partyId || partyId <= 0) {
    throw new Error("Valid party ID is required to create a vehicle");
  }

  if (!formData.make || !formData.model || !formData.year) {
    throw new Error("Vehicle make, model, and year are required");
  }

  if (!formData.vinNumber) {
    throw new Error("VIN number is required");
  }

  // Wrap the entire operation in a transaction for data consistency
  return await db.transaction(async (tx) => {
    try {
      // Find vehicle model - use modelI if provided, otherwise lookup by name
    
        
      

      // Verify party exists before creating vehicle
      const partyExists = await tx
        .select({ id: party.id })
        .from(party)
        .where(eq(party.id, partyId))
        .limit(1);

      if (partyExists.length === 0) {
        throw new Error(`Party with ID ${partyId} does not exist`);
      }

      console.log(`Creating rental vehicle with partyId: ${partyId}, modelId: ${formData.modelId}`);

      // Create vehicle owned directly by the party (no company ownership for rentals)

      if (!formData.modelId || !partyId) {
        throw new Error("Model ID and party ID are required");
      }
      const newVehicle = await tx 
        .insert(vehicles)
        .values({
          partyId: partyId,
          modelId: formData.modelId,
          vinNumber: formData.vinNumber,
          vehicleRegistration: formData.vehicleRegistration || null,
          countryId: formData.countryId,
          manufacturingYear: parseInt(formData.year),
          color: formData.color,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          purchaseDate: new Date().toISOString(),
        })
        .returning();

      console.log(`Created rental vehicle with ID: ${newVehicle[0].id} for partyId: ${newVehicle[0].partyId}`);

      // Create initial possession for the vehicle owner
      try {
        await createInitialVehiclePossession(
          newVehicle[0].id,
          partyId,
          partyId,
          'OWNER' as any,
          'OWNERSHIP_TRANSFER',
          'Initial possession when rental vehicle was created'
        );
        console.log(`✅ Created initial possession for rental vehicle ${newVehicle[0].id}`);
      } catch (possessionError) {
        console.error("❌ Failed to create initial vehicle possession:", possessionError);
        // Don't fail vehicle creation if possession creation fails
      }

      // Create listing for rental
      const newListing = await tx
        .insert(listings)
        .values({
          partyId: partyId,
          vehicleId: newVehicle[0].id,
          effectiveFrom: formData.effectiveFrom,
          effectiveTo: formData.effectiveTo,
          fraction: formData.fraction,  // Percentage of vehicle ownership
          askingPrice: formData.listingType == "SHORT_TERM_LEASE_OUT" ? formData?.monthlyRentalPrice   : formData?.pricePerFraction,
          condition: formData.condition as ConditionEnum,
          mileage: formData.mileage ? parseFloat(formData.mileage.replace(/[^\d.]/g, '')) : null,
          listingType: formData.listingType as ListingTypeEnum,
          audience: formData.audience as AudienceEnum,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      console.log(`Created rental listing with ID: ${newListing[0].id}`);

      // Add media to both vehicle_media and listing_media tables for consistency
      if (formData.vehicleImages && formData.vehicleImages.length > 0) {
        // Insert into vehicle_media table (for vehicle dashboard)
        const vehicleMediaInserts = formData.vehicleImages.map(imagePath => ({
          vehicleId: newVehicle[0].id,
          mediaPath: imagePath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(vehicleMediaInserts);
        console.log(`Inserted ${vehicleMediaInserts.length} vehicle media records`);

        // Insert into listing_media table (for opportunities/listings)
        const listingMediaInserts = formData.vehicleImages.map(imagePath => ({
          listingId: newListing[0].id,
          mediaPath: imagePath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(listingMedia).values(listingMediaInserts);
        console.log(`Inserted ${listingMediaInserts.length} listing media records`);
      }

      return {
        vehicle: newVehicle[0],
        listing: newListing[0],
        ownershipType: "direct" // No company ownership for rentals
      };
    } catch (error) {
      console.error("Error in createVehicleAndRentalListingDrizzle transaction:", error);
      throw error;
    }
  });
}
