"use server";

/**
 * DRIZZLE ACTIONS - PERSONAL PROFILE
 * 
 * This file contains all direct database operations for personal profile management using Drizzle ORM.
 * Replaces API/axios calls with direct database queries.
 */

import { db } from "../db";
import { eq, and, desc, sql, inArray } from "drizzle-orm";
import {
  addressType,
  contactPointType,
  contactPoint,
  individual,
  party,
  partyIdentification,
} from "../drizzle/schema";
import type { AddressTypeRead } from "@/types/address-type";
import type { ContactPointTypeRead } from "@/types/contact-point-types";
import type { ContactPointRead, ContactPointBase, ContactPointCreate } from "@/types/contact-points";
import type { IndividualRead, IndividualFullUpdate } from "@/types/individuals";

// ==================== ADDRESS TYPES ====================

export async function getAddressTypesDrizzle(): Promise<AddressTypeRead[]> {
  const result = await db
    .select({
      id: addressType.id,
      name: addressType.name,
      description: addressType.description,
      is_active: addressType.isActive,
    })
    .from(addressType)
    .where(eq(addressType.isActive, true))
    .orderBy(addressType.name);

  return result.map(row => ({
    id: row.id,
    name: row.name,
    description: row.description ?? undefined,
    is_active: row.is_active,
  }));
}

// ==================== CONTACT POINT TYPES ====================

export async function getContactPointTypesDrizzle(): Promise<ContactPointTypeRead[]> {
  const result = await db
    .select({
      id: contactPointType.id,
      name: contactPointType.name,
      description: contactPointType.description,
      validation_pattern: contactPointType.validationPattern,
      is_active: contactPointType.isActive,
    })
    .from(contactPointType)
    .where(eq(contactPointType.isActive, true))
    .orderBy(contactPointType.name);

  return result.map(row => ({
    id: row.id,
    name: row.name,
    description: row.description ?? undefined,
    validation_pattern: row.validation_pattern ?? undefined,
    is_active: row.is_active,
  }));
}

// ==================== CONTACT POINTS ====================

export async function getContactPointsByPartyIdDrizzle(
  partyId: number
): Promise<ContactPointRead[]> {
  const result = await db
    .select({
      id: contactPoint.id,
      party_id: contactPoint.partyId,
      contact_point_type_id: contactPoint.contactPointTypeId,
      value: contactPoint.value,
      address_type_id: contactPoint.addressTypeId,
      is_primary: contactPoint.isPrimary,
      is_verified: contactPoint.isVerified,
      verification_date: contactPoint.verificationDate,
      verification_method: contactPoint.verificationMethod,
      mtadata: contactPoint.mtadata,
      created_at: contactPoint.createdAt,
      updated_at: contactPoint.updatedAt,
      // Contact point type details
      type_id: contactPointType.id,
      type_name: contactPointType.name,
      type_description: contactPointType.description,
      type_validation_pattern: contactPointType.validationPattern,
      type_is_active: contactPointType.isActive,
    })
    .from(contactPoint)
    .leftJoin(contactPointType, eq(contactPoint.contactPointTypeId, contactPointType.id))
    .where(eq(contactPoint.partyId, partyId))
    .orderBy(desc(contactPoint.isPrimary), contactPoint.createdAt);

  return result.map(row => ({
    id: row.id,
    party_id: row.party_id,
    contact_point_type_id: row.contact_point_type_id,
    value: row.value,
    address_type_id: row.address_type_id ?? undefined,
    is_primary: row.is_primary,
    is_verified: row.is_verified,
    verification_date: row.verification_date ?? undefined,
    verification_method: row.verification_method ?? undefined,
    mtadata: row.mtadata,
    created_at: row.created_at ?? "",
    updated_at: row.updated_at ?? "",
    contact_point_type: {
      id: row.type_id || 0,
      name: row.type_name || "unknown",
      description: row.type_description ?? undefined,
      validation_pattern: row.type_validation_pattern ?? undefined,
      is_active: row.type_is_active ?? true,
    },
  }));
}

export async function createContactPointDrizzle(
  data: ContactPointCreate
): Promise<ContactPointRead> {
  const [result] = await db
    .insert(contactPoint)
    .values({
      partyId: data.party_id,
      contactPointTypeId: data.contact_point_type_id,
      value: data.value,
      addressTypeId: data.address_type_id,
      isPrimary: data.is_primary,
      isVerified: data.is_verified ?? false,
      verificationDate: data.verification_date,
      verificationMethod: data.verification_method,
      mtadata: data.mtadata,
    })
    .returning();

  // Get the type info
  const [typeInfo] = await db
    .select()
    .from(contactPointType)
    .where(eq(contactPointType.id, data.contact_point_type_id))
    .limit(1);

  return {
    id: result.id,
    party_id: result.partyId,
    contact_point_type_id: result.contactPointTypeId,
    value: result.value,
    address_type_id: result.addressTypeId ?? undefined,
    is_primary: result.isPrimary,
    is_verified: result.isVerified,
    verification_date: result.verificationDate ?? undefined,
    verification_method: result.verificationMethod ?? undefined,
    mtadata: result.mtadata,
    created_at: result.createdAt ?? "",
    updated_at: result.updatedAt ?? "",
    contact_point_type: {
      id: typeInfo?.id || 0,
      name: typeInfo?.name || "unknown",
      description: typeInfo?.description ?? undefined,
      validation_pattern: typeInfo?.validationPattern ?? undefined,
      is_active: typeInfo?.isActive ?? true,
    },
  };
}

export async function updateContactPointDrizzle(
  id: number,
  data: ContactPointBase
): Promise<ContactPointRead> {
  const [result] = await db
    .update(contactPoint)
    .set({
      contactPointTypeId: data.contact_point_type_id,
      value: data.value,
      addressTypeId: data.address_type_id,
      isPrimary: data.is_primary,
      isVerified: data.is_verified,
      verificationDate: data.verification_date,
      verificationMethod: data.verification_method,
      mtadata: data.mtadata,
      updatedAt: new Date().toISOString(),
    })
    .where(eq(contactPoint.id, id))
    .returning();

  // Get the type info
  const [typeInfo] = await db
    .select()
    .from(contactPointType)
    .where(eq(contactPointType.id, data.contact_point_type_id))
    .limit(1);

  return {
    id: result.id,
    party_id: result.partyId,
    contact_point_type_id: result.contactPointTypeId,
    value: result.value,
    address_type_id: result.addressTypeId ?? undefined,
    is_primary: result.isPrimary,
    is_verified: result.isVerified,
    verification_date: result.verificationDate ?? undefined,
    verification_method: result.verificationMethod ?? undefined,
    mtadata: result.mtadata,
    created_at: result.createdAt ?? "",
    updated_at: result.updatedAt ?? "",
    contact_point_type: {
      id: typeInfo?.id || 0,
      name: typeInfo?.name || "unknown",
      description: typeInfo?.description ?? undefined,
      validation_pattern: typeInfo?.validationPattern ?? undefined,
      is_active: typeInfo?.isActive ?? true,
    },
  };
}

// ==================== INDIVIDUALS ====================

export async function getIndividualByPartyIdDrizzle(
  partyId: number
): Promise<IndividualRead | null> {
  const [result] = await db
    .select({
      id: individual.id,
      party_id: individual.partyId,
      first_name: individual.firstName,
      last_name: individual.lastName,
      middle_name: individual.middleName,
      salutation: individual.salutation,
      suffix: individual.suffix,
      gender: individual.gender,
      birth_date: individual.birthDate,
      marital_status: individual.maritalStatus,
      nationality: individual.nationality,
      preferred_language: individual.preferredLanguage,
      created_at: individual.createdAt,
      updated_at: individual.updatedAt,
    })
    .from(individual)
    .where(eq(individual.partyId, partyId))
    .limit(1);

  if (!result) {
    return null;
  }

  return {
    id: result.id,
    party_id: result.party_id,
    first_name: result.first_name,
    last_name: result.last_name,
    middle_name: result.middle_name ?? undefined,
    birth_date: result.birth_date ?? "",
    created_at: result.created_at ?? "",
    username: "",
    email: "",
  };
}

export async function updateIndividualDrizzle(
  id: number,
  data: Partial<IndividualRead>
): Promise<IndividualRead> {
  const [result] = await db
    .update(individual)
    .set({
      firstName: data.first_name,
      lastName: data.last_name,
      middleName: data.middle_name,
    
      birthDate: data.birth_date,

      updatedAt: new Date().toISOString(),
    })
    .where(eq(individual.id, id))
    .returning();

  return {
    //TODO Fix this
    id: result.id,
    party_id: result.partyId,
    first_name: result.firstName,
    last_name: result.lastName,
    middle_name: result.middleName ?? undefined,
    birth_date: result.birthDate ?? "",
    created_at: result.createdAt ?? "",
    username: "",
    email: "",
  };
}

// ==================== PROFILE UPDATE (FULL) ====================

export async function updateProfileDrizzle(_: any, formData: FormData) {
  try {
    const dateOfBirth = (formData.get("dateOfBirth") as string)?.trim();
    const address = (formData.get("address") as string)?.trim();
    const email = (formData.get("email") as string)?.trim();
    const phone = (formData.get("phone") as string)?.trim();
    const firstName = (formData.get("firstName") as string)?.trim();
    const lastName = (formData.get("lastName") as string)?.trim();
    const emailId = Number(formData.get("emailId"));
    const phoneId = Number(formData.get("phoneId"));
    const addressId = Number(formData.get("addressId"));
    const addressTypeId = Number(formData.get("addressTypeId"));
    const contactPointEmailId = Number(formData.get("contactPointEmailId"));
    const contactPointPhoneId = Number(formData.get("contactPointPhoneId"));
    const contactPointAddressId = Number(formData.get("contactPointAddressId"));
    const id = Number(formData.get("id"));

    // Update individual details
    await updateIndividualDrizzle(id, {
      first_name: firstName,
      last_name: lastName,
      birth_date: dateOfBirth,
    });

    // Update contact points
    if (email && emailId) {
      await updateContactPointDrizzle(emailId, {
        party_id: 0, // Will be ignored in update
        contact_point_type_id: contactPointEmailId,
        value: email,
        is_primary: true,
        is_verified: false,
      });
    }

    if (phone && phoneId) {
      await updateContactPointDrizzle(phoneId, {
        party_id: 0, // Will be ignored in update
        contact_point_type_id: contactPointPhoneId,
        value: phone,
        is_primary: true,
        is_verified: false,
      });
    }

    if (address && addressId) {
      await updateContactPointDrizzle(addressId, {
        party_id: 0, // Will be ignored in update
        contact_point_type_id: contactPointAddressId,
        value: address,
        address_type_id: addressTypeId,
        is_primary: true,
        is_verified: false,
      });
    }

    return { success: true };
  } catch (error: any) {
    return {
      errors: {
        form: [
          error?.detail || "Could not update profile. Please try again later.",
        ],
      },
    };
  }
} 