"use server";

/**
 * VEHICLE DOMAIN OPERATIONS
 * 
 * Pure model CRUD operations for vehicle-related entities.
 * This file handles the mapping between database schema and domain types.
 * 
 * Architecture:
 * - Domain types use snake_case (REST API conventions)
 * - Database schema uses camelCase (Drizzle conventions)
 * - Functions handle the mapping between the two
 */

import { db } from "../db";
import { eq, and, desc, asc } from "drizzle-orm";
import {
  vehicleMake,
  vehicleModel,
  vehicleVariant,
} from "../drizzle/schema";

// Domain type imports
import type { 
  VehicleMakeCreate, 
  VehicleMakeRead, 
  VehicleMakeUpdate 
} from "../types/vehicle-makes";
import type { 
  VehicleModelCreate, 
  VehicleModelRead, 
  VehicleModelUpdate,
  VehicleModelReadWithMake 
} from "../types/vehicle-model";
import type { 
  VehicleVariantCreate, 
  VehicleVariantRead, 
  VehicleVariantUpdate,
  VehicleVariantReadWithModel 
} from "../types/vehicle-variant";

// ==================== MAPPER FUNCTIONS ====================

function mapDbToVehicleMake(dbMake: any): VehicleMakeRead {
  return {
    id: dbMake.id,
    name: dbMake.name,
    description: dbMake.description,
    is_active: dbMake.isActive,
    created_at: dbMake.createdAt,
    updated_at: dbMake.updatedAt,
  };
}

function mapDbToVehicleModel(dbModel: any): VehicleModelRead {
  return {
    id: dbModel.id,
    make_id: dbModel.makeId,
    model: dbModel.model,
    slug: dbModel.slug,
    first_year: dbModel.firstYear,
    last_year: dbModel.lastYear,
    body_type: dbModel.bodyType,
    description: dbModel.description,
    is_active: dbModel.isActive,
    created_at: dbModel.createdAt,
    updated_at: dbModel.updatedAt,
  };
}

function mapDbToVehicleVariant(dbVariant: any): VehicleVariantRead {
  return {
    id: dbVariant.id,
    model_id: dbVariant.modelId,
    name: dbVariant.name,
    trim_name: dbVariant.trimName,
    year: dbVariant.year,
    engine: dbVariant.engine,
    drivetrain: dbVariant.drivetrain,
    body_type: dbVariant.bodyType,
    seats: dbVariant.seats,
    doors: dbVariant.doors,
    msrp: dbVariant.msrp,
    features: dbVariant.features,
    specs: dbVariant.specs,
    fuel_type: dbVariant.fuelType,
    transmission: dbVariant.transmission,
    description: dbVariant.description,
    is_active: dbVariant.isActive,
    created_at: dbVariant.createdAt,
    updated_at: dbVariant.updatedAt,
  };
}

// ==================== VEHICLE MAKE OPERATIONS ====================

export async function createVehicleMake(
  makeData: VehicleMakeCreate
): Promise<VehicleMakeRead> {
  try {
    const newMake = await db
      .insert(vehicleMake)
      .values({
        name: makeData.name,
        description: makeData.description,
        isActive: makeData.is_active,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return mapDbToVehicleMake(newMake[0]);
  } catch (error) {
    console.error("Error creating vehicle make:", error);
    throw error;
  }
}

export async function getAllVehicleMakes(): Promise<VehicleMakeRead[]> {
  try {
    const makes = await db
      .select()
      .from(vehicleMake)
      .where(eq(vehicleMake.isActive, true))
      .orderBy(asc(vehicleMake.name));

    return makes.map(mapDbToVehicleMake);
  } catch (error) {
    console.error("Error getting vehicle makes:", error);
    throw error;
  }
}

// ==================== VEHICLE MODEL OPERATIONS ====================

export async function createVehicleModel(
  modelData: VehicleModelCreate
): Promise<VehicleModelRead> {
  try {
    const newModel = await db
      .insert(vehicleModel)
      .values({
        makeId: modelData.make_id,
        model: modelData.model,
        slug: modelData.slug,
        firstYear: modelData.first_year,
        lastYear: modelData.last_year,
        bodyType: modelData.body_type as any, // Handle enum
        description: modelData.description,
        isActive: modelData.is_active,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return mapDbToVehicleModel(newModel[0]);
  } catch (error) {
    console.error("Error creating vehicle model:", error);
    throw error;
  }
}

export async function getVehicleModelsByMakeId(makeId: number): Promise<VehicleModelRead[]> {
  try {
    const models = await db
      .select()
      .from(vehicleModel)
      .where(
        and(
          eq(vehicleModel.makeId, makeId),
          eq(vehicleModel.isActive, true)
        )
      )
      .orderBy(asc(vehicleModel.model));

    return models.map(mapDbToVehicleModel);
  } catch (error) {
    console.error("Error getting vehicle models by make:", error);
    throw error;
  }
}

// ==================== VEHICLE VARIANT OPERATIONS ====================

export async function createVehicleVariant(
  variantData: VehicleVariantCreate
): Promise<VehicleVariantRead> {
  try {
    const newVariant = await db
      .insert(vehicleVariant)
      .values({
        modelId: variantData.model_id,
        name: variantData.name,
        trimName: variantData.trim_name,
        year: variantData.year,
        engine: variantData.engine,
        drivetrain: variantData.drivetrain as any, // Handle enum
        bodyType: variantData.body_type as any, // Handle enum
        seats: variantData.seats,
        doors: variantData.doors,
        msrp: variantData.msrp,
        features: variantData.features,
        specs: variantData.specs,
        fuelType: variantData.fuel_type as any, // Handle enum
        transmission: variantData.transmission as any, // Handle enum
        description: variantData.description,
        isActive: variantData.is_active,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return mapDbToVehicleVariant(newVariant[0]);
  } catch (error) {
    console.error("Error creating vehicle variant:", error);
    throw error;
  }
}

export async function getVehicleVariantsByModelId(modelId: number): Promise<VehicleVariantRead[]> {
  try {
    const variants = await db
      .select()
      .from(vehicleVariant)
      .where(
        and(
          eq(vehicleVariant.modelId, modelId),
          eq(vehicleVariant.isActive, true)
        )
      )
      .orderBy(asc(vehicleVariant.year));

    return variants.map(mapDbToVehicleVariant);
  } catch (error) {
    console.error("Error getting vehicle variants by model:", error);
    throw error;
  }
}

export async function getVehicleVariantsByYear(year: number): Promise<VehicleVariantRead[]> {
  try {
    const variants = await db
      .select()
      .from(vehicleVariant)
      .where(
        and(
          eq(vehicleVariant.year, year),
          eq(vehicleVariant.isActive, true)
        )
      )
      .orderBy(asc(vehicleVariant.name));

    return variants.map(mapDbToVehicleVariant);
  } catch (error) {
    console.error("Error getting vehicle variants by year:", error);
    throw error;
  }
}

// ==================== CONVENIENCE FUNCTIONS ====================

/**
 * Get vehicle model with make information
 */
export async function getVehicleModelWithMake(modelId: number): Promise<VehicleModelReadWithMake | null> {
  try {
    const result = await db
      .select({
        // Model fields
        id: vehicleModel.id,
        makeId: vehicleModel.makeId,
        model: vehicleModel.model,
        slug: vehicleModel.slug,
        firstYear: vehicleModel.firstYear,
        lastYear: vehicleModel.lastYear,
        bodyType: vehicleModel.bodyType,
        modelDescription: vehicleModel.description,
        modelIsActive: vehicleModel.isActive,
        modelCreatedAt: vehicleModel.createdAt,
        modelUpdatedAt: vehicleModel.updatedAt,
        // Make fields
        makeName: vehicleMake.name,
        makeDescription: vehicleMake.description,
        makeIsActive: vehicleMake.isActive,
        makeCreatedAt: vehicleMake.createdAt,
        makeUpdatedAt: vehicleMake.updatedAt,
      })
      .from(vehicleModel)
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(eq(vehicleModel.id, modelId))
      .limit(1);

    if (result.length === 0) return null;

    const row = result[0];
    
    return {
      id: row.id,
      make_id: row.makeId,
      model: row.model,
      slug: row.slug,
      first_year: row.firstYear,
      last_year: row.lastYear,
      body_type: row.bodyType,
      description: row.modelDescription,
      is_active: row.modelIsActive,
      created_at: row.modelCreatedAt,
      updated_at: row.modelUpdatedAt,
              make: row.makeName ? {
          id: row.makeId,
          name: row.makeName,
          description: row.makeDescription,
          is_active: row.makeIsActive || false,
          created_at: row.makeCreatedAt,
          updated_at: row.makeUpdatedAt,
        } : null,
    };
  } catch (error) {
    console.error("Error getting vehicle model with make:", error);
    throw error;
  }
}

/**
  model_id?: number | null;
  name: string;
  trim_name?: string | null;
  year: number;
  engine?: string | null;
  drivetrain?: string | null; // drivetrainEnum from schema
  body_type?: string | null; // bodyTypeEnum from schema
  seats?: number | null;
  doors?: number | null;
  msrp?: string | null; // decimal field from schema
  features?: any; // jsonb field
  specs?: any; // jsonb field
  fuel_type: string; // fuelTypeEnum from schema
  transmission: string; // transmissionEnum from schema
  description?: string | null;
  is_active: boolean;
 */
export async function getVehicleVariantWithModel(variantId: number): Promise<VehicleVariantReadWithModel | null> {
  try {
    const result = await db
      .select({
        // Variant fields
        id: vehicleVariant.id,
        name: vehicleVariant.name,
        fuelType: vehicleVariant.fuelType,
        modelId: vehicleVariant.modelId,
        year: vehicleVariant.year,
        engine: vehicleVariant.engine,
        drivetrain: vehicleVariant.drivetrain,
        body_type: vehicleVariant.bodyType,
        seats: vehicleVariant.seats,
        doors: vehicleVariant.doors,
        msrp: vehicleVariant.msrp,
        features: vehicleVariant.features,
        specs: vehicleVariant.specs,
        transmission: vehicleVariant.transmission,
        variantBodyType: vehicleVariant.bodyType,
        variantDescription: vehicleVariant.description,
        variantIsActive: vehicleVariant.isActive,
        variantCreatedAt: vehicleVariant.createdAt,
        variantUpdatedAt: vehicleVariant.updatedAt,
        model: vehicleModel.model,
        makeId: vehicleModel.makeId,
        firstYear: vehicleModel.firstYear,
        lastYear: vehicleModel.lastYear,
        modelBodyType: vehicleModel.bodyType,
        modelDescription: vehicleModel.description,
        modelIsActive: vehicleModel.isActive,
        modelCreatedAt: vehicleModel.createdAt,
        modelUpdatedAt: vehicleModel.updatedAt,

      
      })
      .from(vehicleVariant)
      .leftJoin(vehicleModel, eq(vehicleVariant.modelId, vehicleModel.id))
      .where(eq(vehicleVariant.id, variantId))
      .limit(1);

    if (result.length === 0) return null;

    const row = result[0];
    
    return {
      id: row.id,
      name: row.name,
      engine: row.engine,
      fuel_type: row.fuelType,
      model_id: row.modelId,
      year: row.year,
      seats: row.seats,
      doors: row.doors,
      msrp: row.msrp,
      features: row.features,
      specs: row.specs,
      
      transmission: row.transmission,
      body_type: row.variantBodyType,
      drivetrain: row.drivetrain,
      description: row.variantDescription,
      is_active: row.variantIsActive,

      created_at: row.variantCreatedAt,
      updated_at: row.variantUpdatedAt,
      model: {
        id: row.modelId || 0,
        make_id: row.makeId || 0,
        is_active: row.modelIsActive || false,
        slug: "",
        model: row.model || "",
        first_year: row.firstYear || 0,
        last_year: row.lastYear || 0,
        body_type: row.modelBodyType,
        description: row.modelDescription,
        created_at: row.modelCreatedAt,
        updated_at: row.modelUpdatedAt,

      }
    };
  } catch (error) {
    console.error("Error getting vehicle variant with model:", error);
    throw error;
  }
} 