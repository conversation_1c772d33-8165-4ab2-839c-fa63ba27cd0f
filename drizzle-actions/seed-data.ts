"use server";

/**
 * DRIZZLE ACTIONS - SEED DATA
 * 
 * This file contains seed data functions for populating vehicle-related tables
 * and South African geographical data with essential data for the platform.
 * 
 * SOUTH AFRICAN GEOGRAPHICAL DATA:
 * - All 9 provinces of South Africa
 * - Top cities/municipal areas for each province (45 cities total)
 * 
 * VEHICLE DATA:
 * - Focus on Uber X and Uber Go compatible vehicles - economy cars from popular makes
 * 
 * Tables seeded:
 * - countries: International countries (via seed-countries.ts)
 * - province_state: South African provinces
 * - cities: South African cities linked to provinces
 * - party_type: Individual, Company, Group types
 * - vehicle_make: Popular automotive manufacturers for ride-hailing
 * - vehicle_model: Popular economy vehicle models suitable for Uber
 * - vehicle_variant: Specific variants with transmission and fuel type combinations
 * 
 * USAGE:
 * - seedAllVehicleData(): Seeds everything including SA geographical data
 * - seedSouthAfricanData(): Seeds only provinces and cities
 * - seedSouthAfricanProvinces(): Seeds only provinces
 * - seedSouthAfricanCities(): Seeds only cities
 */

import { db } from "../db";
import { eq, and } from "drizzle-orm";
import { vehicleMake, vehicleModel, vehicleVariant, partyType, countries, provinceState, cities, contactPointType } from "../drizzle/schema";
import { seedCountries } from "./seed-countries";

// ==================== SEED DATA DEFINITIONS ====================

// Party types for the platform
const PARTY_TYPES = [
  { name: "Individual", description: "Individual person or user", isActive: true },
  { name: "Company", description: "Business entity or corporation", isActive: true },
  { name: "Group", description: "Vehicle sharing group", isActive: true }
];

// Contact point types for the platform
const CONTACT_POINT_TYPES = [
  { 
    name: "Email", 
    description: "Email address contact point", 
    validationPattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
    isActive: true 
  },
  { 
    name: "Phone", 
    description: "Phone number contact point", 
    validationPattern: "^[+]?[0-9\\s\\-\\(\\)]{7,15}$",
    isActive: true 
  }
];

// South African Provinces
const SOUTH_AFRICAN_PROVINCES = [
  { name: "Gauteng", country: "South Africa" },
  { name: "Western Cape", country: "South Africa" },
  { name: "KwaZulu-Natal", country: "South Africa" },
  { name: "Eastern Cape", country: "South Africa" },
  { name: "Limpopo", country: "South Africa" },
  { name: "Mpumalanga", country: "South Africa" },
  { name: "North West", country: "South Africa" },
  { name: "Northern Cape", country: "South Africa" },
  { name: "Free State", country: "South Africa" }
];

// South African Cities by Province
const SOUTH_AFRICAN_CITIES_BY_PROVINCE = {
  "Gauteng": [
    "Johannesburg",
    "Pretoria (Tshwane)",
    "Ekurhuleni",
    "Sedibeng (Vaal)",
    "West Rand"
  ],
  "Western Cape": [
    "Cape Town",
    "Stellenbosch",
    "George",
    "Worcester",
    "Paarl"
  ],
  "KwaZulu-Natal": [
    "Durban (eThekwini)",
    "Pietermaritzburg",
    "Newcastle",
    "Richards Bay",
    "Ladysmith"
  ],
  "Eastern Cape": [
    "Port Elizabeth (Gqeberha)",
    "East London (Buffalo City)",
    "Mthatha",
    "Grahamstown (Makhanda)",
    "Uitenhage"
  ],
  "Limpopo": [
    "Polokwane",
    "Tzaneen",
    "Mokopane",
    "Thohoyandou",
    "Giyani"
  ],
  "Mpumalanga": [
    "Nelspruit (Mbombela)",
    "Witbank (Emalahleni)",
    "Secunda",
    "Middelburg",
    "Ermelo"
  ],
  "North West": [
    "Rustenburg",
    "Mahikeng",
    "Potchefstroom",
    "Klerksdorp",
    "Brits"
  ],
  "Northern Cape": [
    "Kimberley",
    "Upington",
    "Springbok",
    "De Aar",
    "Kuruman"
  ],
  "Free State": [
    "Bloemfontein",
    "Welkom",
    "Kroonstad",
    "Bethlehem",
    "Sasolburg"
  ]
};

// Focus on makes popular in ride-hailing services
const VEHICLE_MAKES = [
  { name: "Toyota", description: "Japanese multinational automotive manufacturer" },
  { name: "Suzuki", description: "Japanese multinational corporation" },
  { name: "Honda", description: "Japanese public multinational conglomerate manufacturer" },
  { name: "Nissan", description: "Japanese multinational automobile manufacturer" },
  { name: "Hyundai", description: "South Korean multinational automotive manufacturer" },
  { name: "Kia", description: "South Korean multinational automotive manufacturer" },
  { name: "Ford", description: "American multinational automaker" },
  { name: "Volkswagen", description: "German automotive manufacturer" },
  { name: "Chevrolet", description: "American automobile division of General Motors" },
  { name: "Maruti", description: "Indian automobile manufacturer" },
  { name: "Datsun", description: "Japanese automobile brand owned by Nissan" },
  { name: "Tata", description: "Indian multinational automotive manufacturing company" },
  { name: "Mitsubishi", description: "Japanese multinational automotive manufacturer" },
  { name: "Mazda", description: "Japanese automotive manufacturer" },
  { name: "Subaru", description: "Japanese automobile manufacturer" }
];

// Popular economy models suitable for Uber X and Uber Go
const VEHICLE_MODELS_BY_MAKE = {
  "Toyota": [
    { model: "Corolla", firstYear: 2010, lastYear: 2024, bodyType: "sedan" },
    { model: "Yaris", firstYear: 2012, lastYear: 2024, bodyType: "hatchback" },
    { model: "Vitz", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" },
    { model: "Camry", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Etios", firstYear: 2012, lastYear: 2022, bodyType: "sedan" },
    { model: "Avanza", firstYear: 2012, lastYear: 2024, bodyType: "van" },
    { model: "Passo", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" }
  ],
  "Suzuki": [
    { model: "Swift", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Dzire", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Celerio", firstYear: 2014, lastYear: 2024, bodyType: "hatchback" },
    { model: "Baleno", firstYear: 2015, lastYear: 2024, bodyType: "hatchback" },
    { model: "Ciaz", firstYear: 2014, lastYear: 2024, bodyType: "sedan" },
    { model: "Alto", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Wagon R", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" }
  ],
  "Honda": [
    { model: "City", firstYear: 2010, lastYear: 2024, bodyType: "sedan" },
    { model: "Civic", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Jazz", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Amaze", firstYear: 2013, lastYear: 2024, bodyType: "sedan" },
    { model: "Brio", firstYear: 2011, lastYear: 2020, bodyType: "hatchback" },
    { model: "Fit", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" }
  ],
  "Nissan": [
    { model: "Sentra", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Almera", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Micra", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" },
    { model: "Note", firstYear: 2013, lastYear: 2024, bodyType: "hatchback" },
    { model: "Tiida", firstYear: 2010, lastYear: 2018, bodyType: "hatchback" },
    { model: "Sunny", firstYear: 2011, lastYear: 2020, bodyType: "sedan" }
  ],
  "Hyundai": [
    { model: "Accent", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Elantra", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "i10", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "i20", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Verna", firstYear: 2011, lastYear: 2024, bodyType: "sedan" },
    { model: "Grand i10", firstYear: 2013, lastYear: 2024, bodyType: "hatchback" }
  ],
  "Kia": [
    { model: "Rio", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Picanto", firstYear: 2012, lastYear: 2024, bodyType: "hatchback" },
    { model: "Cerato", firstYear: 2013, lastYear: 2024, bodyType: "sedan" },
    { model: "Soluto", firstYear: 2019, lastYear: 2024, bodyType: "sedan" }
  ],
  "Ford": [
    { model: "Fiesta", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" },
    { model: "Focus", firstYear: 2012, lastYear: 2022, bodyType: "sedan" },
    { model: "Figo", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" },
    { model: "Aspire", firstYear: 2015, lastYear: 2022, bodyType: "sedan" },
    { model: "EcoSport", firstYear: 2013, lastYear: 2024, bodyType: "suv" }
  ],
  "Volkswagen": [
    { model: "Polo", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Vento", firstYear: 2010, lastYear: 2020, bodyType: "sedan" },
    { model: "Ameo", firstYear: 2016, lastYear: 2020, bodyType: "sedan" },
    { model: "Golf", firstYear: 2012, lastYear: 2024, bodyType: "hatchback" }
  ],
  "Chevrolet": [
    { model: "Sail", firstYear: 2012, lastYear: 2018, bodyType: "sedan" },
    { model: "Beat", firstYear: 2010, lastYear: 2018, bodyType: "hatchback" },
    { model: "Spark", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" },
    { model: "Aveo", firstYear: 2010, lastYear: 2018, bodyType: "sedan" }
  ],
  "Maruti": [
    { model: "Swift", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Dzire", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Alto", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Baleno", firstYear: 2015, lastYear: 2024, bodyType: "hatchback" },
    { model: "Celerio", firstYear: 2014, lastYear: 2024, bodyType: "hatchback" },
    { model: "Wagon R", firstYear: 2010, lastYear: 2024, bodyType: "hatchback" },
    { model: "Ciaz", firstYear: 2014, lastYear: 2024, bodyType: "sedan" }
  ],
  "Datsun": [
    { model: "Go", firstYear: 2014, lastYear: 2022, bodyType: "hatchback" },
    { model: "Go+", firstYear: 2014, lastYear: 2022, bodyType: "van" },
    { model: "Redi-Go", firstYear: 2016, lastYear: 2022, bodyType: "hatchback" }
  ],
  "Tata": [
    { model: "Tiago", firstYear: 2016, lastYear: 2024, bodyType: "hatchback" },
    { model: "Tigor", firstYear: 2017, lastYear: 2024, bodyType: "sedan" },
    { model: "Zest", firstYear: 2014, lastYear: 2020, bodyType: "sedan" },
    { model: "Bolt", firstYear: 2015, lastYear: 2020, bodyType: "hatchback" }
  ],
  "Mitsubishi": [
    { model: "Mirage", firstYear: 2012, lastYear: 2024, bodyType: "hatchback" },
    { model: "Attrage", firstYear: 2013, lastYear: 2024, bodyType: "sedan" },
    { model: "Lancer", firstYear: 2010, lastYear: 2018, bodyType: "sedan" }
  ],
  "Mazda": [
    { model: "Mazda2", firstYear: 2014, lastYear: 2024, bodyType: "hatchback" },
    { model: "Mazda3", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "Demio", firstYear: 2010, lastYear: 2020, bodyType: "hatchback" }
  ],
  "Subaru": [
    { model: "Impreza", firstYear: 2012, lastYear: 2024, bodyType: "sedan" },
    { model: "XV", firstYear: 2012, lastYear: 2024, bodyType: "suv" }
  ]
};

// Common variants for economy cars - transmission and fuel combinations
const COMMON_VARIANTS = [
  { fuelType: "petrol", transmission: "manual", trimName: "Manual" },
  { fuelType: "petrol", transmission: "automatic", trimName: "Auto" },
  { fuelType: "petrol", transmission: "cvt", trimName: "CVT" },
  { fuelType: "diesel", transmission: "manual", trimName: "Diesel Manual" },
  { fuelType: "diesel", transmission: "automatic", trimName: "Diesel Auto" }
];

// ==================== SEED FUNCTIONS ====================

export async function seedPartyTypes(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    for (const partyTypeData of PARTY_TYPES) {
      // Check if party type already exists
      const existing = await db
        .select({ id: partyType.id })
        .from(partyType)
        .where(eq(partyType.name, partyTypeData.name))
        .limit(1);

      if (existing.length > 0) {
        skipped++;
        continue;
      }

      // Create the party type
      await db.insert(partyType).values({
        name: partyTypeData.name,
        description: partyTypeData.description,
        isActive: partyTypeData.isActive,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      created++;
    }

    return { created, skipped };
  } catch (error) {
    console.error("Error seeding party types:", error);
    throw new Error("Failed to seed party types");
  }
}

export async function seedContactPointTypes(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    console.log("Seeding contact point types...");

    for (const contactPointTypeData of CONTACT_POINT_TYPES) {
      // Check if contact point type already exists
      const existing = await db
        .select({ id: contactPointType.id })
        .from(contactPointType)
        .where(eq(contactPointType.name, contactPointTypeData.name))
        .limit(1);

      if (existing.length > 0) {
        skipped++;
        console.log(`  ⏭️  Skipped: ${contactPointTypeData.name} (already exists)`);
        continue;
      }

      // Create the contact point type
      await db.insert(contactPointType).values({
        name: contactPointTypeData.name,
        description: contactPointTypeData.description,
        validationPattern: contactPointTypeData.validationPattern,
        isActive: contactPointTypeData.isActive,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      created++;
      console.log(`  ✅ Created: ${contactPointTypeData.name}`);
    }

    console.log(`✨ Contact point types seeding complete: ${created} created, ${skipped} skipped`);
    return { created, skipped };
  } catch (error) {
    console.error("Error seeding contact point types:", error);
    throw new Error("Failed to seed contact point types");
  }
}

export async function seedSouthAfricanProvinces(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    console.log("Seeding South African provinces...");
    
    // Get South Africa country ID
    const southAfricaCountry = await db
      .select({ id: countries.id })
      .from(countries)
      .where(eq(countries.name, "South Africa"))
      .limit(1);

    if (southAfricaCountry.length === 0) {
      throw new Error("South Africa country not found. Please seed countries first.");
    }

    const southAfricaCountryId = southAfricaCountry[0].id;
    console.log(`Found South Africa with country ID: ${southAfricaCountryId}`);

    for (const provinceData of SOUTH_AFRICAN_PROVINCES) {
      // Check if province already exists
      const existing = await db
        .select({ id: provinceState.id })
        .from(provinceState)
        .where(
          and(
            eq(provinceState.name, provinceData.name),
            eq(provinceState.countryId, southAfricaCountryId)
          )
        )
        .limit(1);

      if (existing.length > 0) {
        skipped++;
        console.log(`  ⏭️  Skipped: ${provinceData.name} (already exists)`);
        continue;
      }

      // Create the province
      await db.insert(provinceState).values({
        name: provinceData.name,
        countryId: southAfricaCountryId,
      });

      created++;
      console.log(`  ✅ Created: ${provinceData.name}`);
    }

    console.log(`✨ Provinces seeding complete: ${created} created, ${skipped} skipped`);
    return { created, skipped };
  } catch (error) {
    console.error("Error seeding South African provinces:", error);
    throw new Error("Failed to seed South African provinces");
  }
}

export async function seedSouthAfricanCities(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    console.log("Seeding South African cities...");
    
    // Get South Africa country ID
    const southAfricaCountry = await db
      .select({ id: countries.id })
      .from(countries)
      .where(eq(countries.name, "South Africa"))
      .limit(1);

    if (southAfricaCountry.length === 0) {
      throw new Error("South Africa country not found. Please seed countries first.");
    }

    const southAfricaCountryId = southAfricaCountry[0].id;

    // Get all South African provinces
    const provinces = await db
      .select({ id: provinceState.id, name: provinceState.name })
      .from(provinceState)
      .where(eq(provinceState.countryId, southAfricaCountryId));

    const provinceMap = new Map(provinces.map(province => [province.name, province.id]));
    console.log(`Found ${provinces.length} provinces in South Africa`);

    for (const [provinceName, cityNames] of Object.entries(SOUTH_AFRICAN_CITIES_BY_PROVINCE)) {
      const provinceId = provinceMap.get(provinceName);
      
      if (!provinceId) {
        console.warn(`⚠️  Province ${provinceName} not found, skipping cities`);
        continue;
      }

      console.log(`\n📍 Processing cities for ${provinceName}:`);

      for (const cityName of cityNames) {
        // Check if city already exists in this province
        const existing = await db
          .select({ id: cities.id })
          .from(cities)
          .where(
            and(
              eq(cities.name, cityName),
              eq(cities.provinceStateId, provinceId),
              eq(cities.countryId, southAfricaCountryId)
            )
          )
          .limit(1);

        if (existing.length > 0) {
          skipped++;
          console.log(`    ⏭️  Skipped: ${cityName} (already exists)`);
          continue;
        }

        // Create the city
        await db.insert(cities).values({
          name: cityName,
          countryId: southAfricaCountryId,
          provinceStateId: provinceId,
        });

        created++;
        console.log(`    ✅ Created: ${cityName}`);
      }
    }

    console.log(`\n✨ Cities seeding complete: ${created} created, ${skipped} skipped`);
    return { created, skipped };
  } catch (error) {
    console.error("Error seeding South African cities:", error);
    throw new Error("Failed to seed South African cities");
  }
}

export async function seedVehicleMakes(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    for (const make of VEHICLE_MAKES) {
      // Check if make already exists
      const existing = await db
        .select({ id: vehicleMake.id })
        .from(vehicleMake)
        .where(eq(vehicleMake.name, make.name))
        .limit(1);

      if (existing.length > 0) {
        skipped++;
        continue;
      }

      // Create the make
      await db.insert(vehicleMake).values({
        name: make.name,
        description: make.description,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      created++;
    }

    return { created, skipped };
  } catch (error) {
    console.error("Error seeding vehicle makes:", error);
    throw new Error("Failed to seed vehicle makes");
  }
}

export async function seedVehicleModels(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    // Get all makes first
    const makes = await db
      .select({ id: vehicleMake.id, name: vehicleMake.name })
      .from(vehicleMake)
      .where(eq(vehicleMake.isActive, true));

    const makeMap = new Map(makes.map(make => [make.name, make.id]));

    for (const [makeName, models] of Object.entries(VEHICLE_MODELS_BY_MAKE)) {
      const makeId = makeMap.get(makeName);
      
      if (!makeId) {
        console.warn(`Make ${makeName} not found, skipping models`);
        continue;
      }

      for (const modelData of models) {
        // Check if model already exists for this make
        const existing = await db
          .select({ id: vehicleModel.id })
          .from(vehicleModel)
          .where(
            and(
              eq(vehicleModel.makeId, makeId),
              eq(vehicleModel.model, modelData.model)
            )
          )
          .limit(1);

        if (existing.length > 0) {
          skipped++;
          continue;
        }

        // Create the model
        await db.insert(vehicleModel).values({
          makeId: makeId,
          model: modelData.model,
          firstYear: modelData.firstYear,
          lastYear: modelData.lastYear,
          bodyType: modelData.bodyType as "sedan" | "hatchback" | "suv" | "van" | "other",
          description: `${makeName} ${modelData.model} - Popular choice for ride-hailing services`,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });

        created++;
      }
    }

    return { created, skipped };
  } catch (error) {
    console.error("Error seeding vehicle models:", error);
    throw new Error("Failed to seed vehicle models");
  }
}

export async function seedVehicleVariants(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    // Get all models with their makes
    const modelsWithMakes = await db
      .select({
        modelId: vehicleModel.id,
        model: vehicleModel.model,
        firstYear: vehicleModel.firstYear,
        lastYear: vehicleModel.lastYear,
        makeName: vehicleMake.name,
      })
      .from(vehicleModel)
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(eq(vehicleModel.isActive, true));

    console.log(`Found ${modelsWithMakes.length} models to create variants for`);

    for (const modelData of modelsWithMakes) {
      // Create variants for recent years (focus on 2018-2024 for ride-hailing)
      const startYear = Math.max(modelData.firstYear || 2010, 2018);
      const endYear = modelData.lastYear || 2024;

      for (let year = startYear; year <= endYear; year++) {
        // Create common variants for this model year
        for (const variant of COMMON_VARIANTS) {
          // Skip diesel variants for very small cars (under 1.0L typically)
          if (variant.fuelType === "diesel" && 
              (modelData.model.includes("Alto") || modelData.model.includes("Picanto") || 
               modelData.model.includes("Beat") || modelData.model.includes("Spark"))) {
            continue;
          }

          const variantName = `${modelData.makeName} ${modelData.model} ${year} ${variant.trimName}`;
          
          // Check if variant already exists
          const existing = await db
            .select({ id: vehicleVariant.id })
            .from(vehicleVariant)
            .where(
              and(
                eq(vehicleVariant.modelId, modelData.modelId),
                eq(vehicleVariant.year, year),
                eq(vehicleVariant.fuelType, variant.fuelType as "petrol" | "diesel" | "electric" | "hybrid" | "gas" | "other"),
                eq(vehicleVariant.transmission, variant.transmission as "automatic" | "manual" | "cvt" | "dual_clutch" | "other")
              )
            )
            .limit(1);

          if (existing.length > 0) {
            skipped++;
            continue;
          }

          // Create the variant
          await db.insert(vehicleVariant).values({
            modelId: modelData.modelId,
            name: variantName,
            trimName: variant.trimName,
            year: year,
            fuelType: variant.fuelType as "petrol" | "diesel" | "electric" | "hybrid" | "gas" | "other",
            transmission: variant.transmission as "automatic" | "manual" | "cvt" | "dual_clutch" | "other",
            description: `${variantName} - Ideal for ride-hailing services`,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });

          created++;

          // Add a small delay to prevent overwhelming the database
          if (created % 100 === 0) {
            console.log(`Created ${created} variants so far...`);
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        }
      }
    }

    console.log(`Finished creating variants. Created: ${created}, Skipped: ${skipped}`);
    return { created, skipped };
  } catch (error) {
    console.error("Error seeding vehicle variants:", error);
    throw new Error("Failed to seed vehicle variants");
  }
}

export async function seedAllVehicleData(): Promise<{
  makes: { created: number; skipped: number };
  models: { created: number; skipped: number };
  variants: { created: number; skipped: number };
  countries: { created: number; skipped: number };
  partyTypes: { created: number; skipped: number };
  contactPointTypes: { created: number; skipped: number };
  provinces: { created: number; skipped: number };
  cities: { created: number; skipped: number };
}> {
  console.log("Starting vehicle data seeding...");
  
  const countries = await seedCountries();
  console.log(`Countries: Created ${countries.created}, Skipped ${countries.skipped}`);
  
  const partyTypes = await seedPartyTypes();
  console.log(`Party Types: Created ${partyTypes.created}, Skipped ${partyTypes.skipped}`);
  
  const contactPointTypes = await seedContactPointTypes();
  console.log(`Contact Point Types: Created ${contactPointTypes.created}, Skipped ${contactPointTypes.skipped}`);
  
  const provinces = await seedSouthAfricanProvinces();
  console.log(`SA Provinces: Created ${provinces.created}, Skipped ${provinces.skipped}`);
  
  const cities = await seedSouthAfricanCities();
  console.log(`SA Cities: Created ${cities.created}, Skipped ${cities.skipped}`);
  
  const makes = await seedVehicleMakes();
  console.log(`Makes: Created ${makes.created}, Skipped ${makes.skipped}`);
  
  const models = await seedVehicleModels();
  console.log(`Models: Created ${models.created}, Skipped ${models.skipped}`);
  
  const variants = await seedVehicleVariants();
  console.log(`Variants: Created ${variants.created}, Skipped ${variants.skipped}`);
  
  return { makes, models, variants, countries, partyTypes, contactPointTypes, provinces, cities };
}

// Individual seed functions that can be called separately
export async function seedSouthAfricanData(): Promise<{
  provinces: { created: number; skipped: number };
  cities: { created: number; skipped: number };
}> {
  console.log("Starting South African geographical data seeding...");
  
  const provinces = await seedSouthAfricanProvinces();
  console.log(`SA Provinces: Created ${provinces.created}, Skipped ${provinces.skipped}`);
  
  const cities = await seedSouthAfricanCities();
  console.log(`SA Cities: Created ${cities.created}, Skipped ${cities.skipped}`);
  
  return { provinces, cities };
} 