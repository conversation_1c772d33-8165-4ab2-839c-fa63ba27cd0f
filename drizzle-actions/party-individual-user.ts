"use server";

import { db } from "../db";
import { eq, and, or, sql, desc, asc, gte, lte, between } from "drizzle-orm";
import {
  party,
  individual,
  contactPoint,
  users,
} from "../drizzle/schema";
import { PartyRead } from "@/types/party";
import { ContactPointRead } from "@/types/contact-points";
import { IndividualRead } from "@/types/individuals";
import { UserRead } from "@/types/users";
import { clearUserAttributesCache } from "@/lib/userAttributes";


export async function createPartyIndividualUser({externalId, firstName, lastName, birthDate, email, phoneNumber, testFailurePoint}: {externalId: string, firstName: string, lastName: string, birthDate: string, email: string, phoneNumber: string, testFailurePoint?: 'after-individual' | 'after-user' | 'after-email-contact' | 'after-phone-contact'}) {

   console.log('🚀 Starting transaction...', externalId, firstName, lastName, birthDate, email, phoneNumber, testFailurePoint);

    const transaction: {party: PartyRead, individual: IndividualRead, user: UserRead, emailContactPoint: ContactPointRead, phoneContactPoint: ContactPointRead} = await db.transaction(async (tx) => {
    const partyResult = await tx.insert(party).values({
        partyTypeId: 1,
        statusId: 1,
        externalId: externalId,
    }).returning();
    
    console.log(`✅ PARTY CREATED: ID = ${partyResult[0].id}, externalId = ${partyResult[0].externalId}`);

    const individualResult = await tx.insert(individual).values({
        firstName: firstName,
        lastName: lastName,
        birthDate: birthDate,
        partyId: partyResult[0].id,
    }).returning();
    
    console.log(`✅ INDIVIDUAL CREATED: ID = ${individualResult[0].id}, name = ${individualResult[0].firstName} ${individualResult[0].lastName}`);

    // Test failure point: after individual creation
    if (testFailurePoint === 'after-individual') {
        console.log('💥 THROWING ERROR AFTER INDIVIDUAL - Party and Individual should rollback');
        throw new Error('TEST FAILURE: After individual creation');
    }

    const userResult = await tx.insert(users).values({
        username: externalId,
        email: email,
        familyName: lastName,
        givenName: firstName,
        phoneNumber: phoneNumber,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),

    }).returning();
    
    console.log(`✅ USER CREATED: ID = ${userResult[0].id}, username = ${userResult[0].username}`);

    // Test failure point: after user creation
    if (testFailurePoint === 'after-user') {
        console.log('💥 THROWING ERROR AFTER USER - Party, Individual, and User should rollback');
        throw new Error('TEST FAILURE: After user creation');
    }

    const emailContactPointResult = await tx.insert(contactPoint).values({
        contactPointTypeId: 1,
        value: email,
        isPrimary: true,
        partyId: partyResult[0].id,
        isVerified: true,
    }).returning();
    
    console.log(`✅ EMAIL CONTACT CREATED: ID = ${emailContactPointResult[0].id}, email = ${emailContactPointResult[0].value}`);

    // Test failure point: after email contact point creation
    if (testFailurePoint === 'after-email-contact') {
        console.log('💥 THROWING ERROR AFTER EMAIL CONTACT - All previous records should rollback');
        throw new Error('TEST FAILURE: After email contact creation');
    }

    const phoneContactPointResult = await tx.insert(contactPoint).values({
        contactPointTypeId: 2,
        value: phoneNumber,
        isPrimary: true,
        partyId: partyResult[0].id,
        isVerified: true,
    }).returning();
    
    console.log(`✅ PHONE CONTACT CREATED: ID = ${phoneContactPointResult[0].id}, phone = ${phoneContactPointResult[0].value}`);

    // Test failure point: after phone contact point creation
    if (testFailurePoint === 'after-phone-contact') {
        console.log('💥 THROWING ERROR AFTER PHONE CONTACT - All records should rollback');
        throw new Error('TEST FAILURE: After phone contact creation');
    }


    

    console.log('🎉 ALL RECORDS CREATED SUCCESSFULLY!');


    return {
        party: {
            id: partyResult[0].id,
            created_at: partyResult[0].createdAt ?? new Date().toISOString(),
            updated_at: partyResult[0].updatedAt ?? new Date().toISOString(),
            status_id: partyResult[0].statusId,
            external_id: partyResult[0].externalId ?? '',
            party_type_id: partyResult[0].partyTypeId,
        },
        individual: {
            username: externalId,
            email: email,
            id: individualResult[0].id,
            party_id: partyResult[0].id,
            first_name: individualResult[0].firstName,
            last_name: individualResult[0].lastName,
            birth_date: individualResult[0].birthDate ?? '',
            created_at: individualResult[0].createdAt ?? new Date().toISOString(),
        },
        user: {
            id: userResult[0].id,
            username: userResult[0].username ?? '',
            email: userResult[0].email ?? '',
            familyName: userResult[0].familyName ?? '',
            givenName: userResult[0].givenName ?? '',
            phoneNumber: userResult[0].phoneNumber ?? '',
            createdAt: userResult[0].createdAt ?? new Date().toISOString(),
            updatedAt: userResult[0].updatedAt ?? new Date().toISOString(),
            orgId: '',
        },
        emailContactPoint: {
            id: emailContactPointResult[0].id,
            party_id: emailContactPointResult[0].partyId,
            contact_point_type_id: emailContactPointResult[0].contactPointTypeId,
            value: emailContactPointResult[0].value,
            is_primary: emailContactPointResult[0].isPrimary,
            is_verified: emailContactPointResult[0].isVerified,
            created_at: emailContactPointResult[0].createdAt ?? new Date().toISOString(),
            updated_at: emailContactPointResult[0].updatedAt ?? new Date().toISOString(),
            contact_point_type: { id: 1, name: 'Email', description: 'Email contact point' },
        },
        phoneContactPoint: {
            id: phoneContactPointResult[0].id,
            party_id: phoneContactPointResult[0].partyId,
            contact_point_type_id: phoneContactPointResult[0].contactPointTypeId,
            value: phoneContactPointResult[0].value,
            is_primary: phoneContactPointResult[0].isPrimary,
            is_verified: phoneContactPointResult[0].isVerified,
            created_at: phoneContactPointResult[0].createdAt ?? new Date().toISOString(),
            updated_at: phoneContactPointResult[0].updatedAt ?? new Date().toISOString(),
            contact_point_type: { id: 2, name: 'Phone', description: 'Phone contact point' },
        },
    };
   });

    console.log('🎉 ALL RECORDS CREATED SUCCESSFULLY!', transaction);

    return transaction;

}