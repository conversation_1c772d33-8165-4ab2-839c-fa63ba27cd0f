"use server";

import { db } from "../db";
import { eq } from "drizzle-orm";
import { countries } from "../drizzle/schema";

// Country data organized by regions
const COUNTRIES_DATA = [
  // South Africa first
  { name: "South Africa", code: "ZA" },

  // SADC Countries (excluding South Africa)
  { name: "Angola", code: "AO" },
  { name: "Botswana", code: "BW" },
  { name: "Comoros", code: "KM" },
  { name: "Democratic Republic of the Congo", code: "CD" },
  { name: "Eswati<PERSON>", code: "SZ" },
  { name: "Lesotho", code: "LS" },
  { name: "Madagascar", code: "MG" },
  { name: "Malawi", code: "MW" },
  { name: "Mauritius", code: "MU" },
  { name: "Mozambique", code: "MZ" },
  { name: "Namibia", code: "NA" },
  { name: "Seychelles", code: "SC" },
  { name: "Tanzania", code: "TZ" },
  { name: "Zambia", code: "ZM" },
  { name: "Zimbabwe", code: "ZW" },

  // Rest of Africa (alphabetically)
  { name: "Algeria", code: "DZ" },
  { name: "Benin", code: "BJ" },
  { name: "Burkina Faso", code: "BF" },
  { name: "Burundi", code: "BI" },
  { name: "Cameroon", code: "CM" },
  { name: "Cape Verde", code: "CV" },
  { name: "Central African Republic", code: "CF" },
  { name: "Chad", code: "TD" },
  { name: "Congo", code: "CG" },
  { name: "Djibouti", code: "DJ" },
  { name: "Egypt", code: "EG" },
  { name: "Equatorial Guinea", code: "GQ" },
  { name: "Eritrea", code: "ER" },
  { name: "Ethiopia", code: "ET" },
  { name: "Gabon", code: "GA" },
  { name: "Gambia", code: "GM" },
  { name: "Ghana", code: "GH" },
  { name: "Guinea", code: "GN" },
  { name: "Guinea-Bissau", code: "GW" },
  { name: "Ivory Coast", code: "CI" },
  { name: "Kenya", code: "KE" },
  { name: "Liberia", code: "LR" },
  { name: "Libya", code: "LY" },
  { name: "Mali", code: "ML" },
  { name: "Mauritania", code: "MR" },
  { name: "Morocco", code: "MA" },
  { name: "Niger", code: "NE" },
  { name: "Nigeria", code: "NG" },
  { name: "Rwanda", code: "RW" },
  { name: "Sao Tome and Principe", code: "ST" },
  { name: "Senegal", code: "SN" },
  { name: "Sierra Leone", code: "SL" },
  { name: "Somalia", code: "SO" },
  { name: "South Sudan", code: "SS" },
  { name: "Sudan", code: "SD" },
  { name: "Togo", code: "TG" },
  { name: "Tunisia", code: "TN" },
  { name: "Uganda", code: "UG" },

  // Rest of World (major countries, alphabetically)
  { name: "Afghanistan", code: "AF" },
  { name: "Argentina", code: "AR" },
  { name: "Australia", code: "AU" },
  { name: "Austria", code: "AT" },
  { name: "Bangladesh", code: "BD" },
  { name: "Belgium", code: "BE" },
  { name: "Brazil", code: "BR" },
  { name: "Canada", code: "CA" },
  { name: "China", code: "CN" },
  { name: "Colombia", code: "CO" },
  { name: "Denmark", code: "DK" },
  { name: "Finland", code: "FI" },
  { name: "France", code: "FR" },
  { name: "Germany", code: "DE" },
  { name: "Greece", code: "GR" },
  { name: "India", code: "IN" },
  { name: "Indonesia", code: "ID" },
  { name: "Iran", code: "IR" },
  { name: "Iraq", code: "IQ" },
  { name: "Ireland", code: "IE" },
  { name: "Israel", code: "IL" },
  { name: "Italy", code: "IT" },
  { name: "Japan", code: "JP" },
  { name: "Malaysia", code: "MY" },
  { name: "Mexico", code: "MX" },
  { name: "Netherlands", code: "NL" },
  { name: "New Zealand", code: "NZ" },
  { name: "Norway", code: "NO" },
  { name: "Pakistan", code: "PK" },
  { name: "Philippines", code: "PH" },
  { name: "Poland", code: "PL" },
  { name: "Portugal", code: "PT" },
  { name: "Russia", code: "RU" },
  { name: "Saudi Arabia", code: "SA" },
  { name: "Singapore", code: "SG" },
  { name: "South Korea", code: "KR" },
  { name: "Spain", code: "ES" },
  { name: "Sweden", code: "SE" },
  { name: "Switzerland", code: "CH" },
  { name: "Thailand", code: "TH" },
  { name: "Turkey", code: "TR" },
  { name: "Ukraine", code: "UA" },
  { name: "United Arab Emirates", code: "AE" },
  { name: "United Kingdom", code: "GB" },
  { name: "United States", code: "US" },
  { name: "Vietnam", code: "VN" }
];

export async function seedCountries(): Promise<{ created: number; skipped: number }> {
  let created = 0;
  let skipped = 0;

  try {
    for (const country of COUNTRIES_DATA) {
      // Check if country already exists
      const existing = await db
        .select({ id: countries.id })
        .from(countries)
        .where(eq(countries.code, country.code))
        .limit(1);

      if (existing.length > 0) {
        skipped++;
        continue;
      }

      // Create the country
      await db.insert(countries).values({
        name: country.name,
        code: country.code,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      created++;
    }

    return { created, skipped };
  } catch (error) {
    console.error("Error seeding countries:", error);
    throw new Error("Failed to seed countries");
  }
} 