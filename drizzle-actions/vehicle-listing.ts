"use server";

/**
 * DRIZZLE ACTIONS - VEHICLE LISTING CREATION
 *
 * This file contains database operations for creating vehicles directly for listing purposes.
 * Used by the VehicleListingDrawer component.
 */

import { db } from "../db";
import { eq, and } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  vehicleMedia,
  party,
  vehicleVariant,
} from "../drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Interface for vehicle creation with media
export interface VehicleListingData {
  // Required fields
  vinNumber: string;
  modelId: number;

  // Optional vehicle fields
  vehicleRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  color?: string;
  countryId?: number;

  // Media paths (S3 paths)
  images: string[];
}

// Interface for vehicle creation result
export interface VehicleCreationResult {
  success: boolean;
  vehicleId?: number;
  error?: string;
}

/**
 * Create a vehicle with media for listing purposes
 * This function creates a vehicle directly owned by the user (no company)
 */
export async function createVehicleForListing(
  vehicleData: VehicleListingData
): Promise<VehicleCreationResult> {
  try {
    // Get user attributes
    const userAttributes = await getUserAttributes();
    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    // Validate required fields
    if (!vehicleData.vinNumber || !vehicleData.modelId) {
      return { success: false, error: "VIN number and model ID are required" };
    }

    // Verify party exists
    const partyExists = await db
      .select({ id: party.id })
      .from(party)
      .where(eq(party.id, parseInt(partyId)))
      .limit(1);

    if (partyExists.length === 0) {
      return {
        success: false,
        error: `Party with ID ${partyId} does not exist`,
      };
    }

    // Verify model exists
    const modelExists = await db
      .select({ id: vehicleModel.id })
      .from(vehicleModel)
      .where(eq(vehicleModel.id, vehicleData.modelId))
      .limit(1);

    if (modelExists.length === 0) {
      return {
        success: false,
        error: `Vehicle model with ID ${vehicleData.modelId} does not exist`,
      };
    }

    // Check if VIN already exists
    const existingVehicle = await db
      .select({ id: vehicles.id })
      .from(vehicles)
      .where(eq(vehicles.vinNumber, vehicleData.vinNumber))
      .limit(1);

    if (existingVehicle.length > 0) {
      return {
        success: false,
        error: "A vehicle with this VIN number already exists",
      };
    }

    // Create vehicle in transaction
    const result = await db.transaction(async (tx) => {
      // Create the vehicle
      const newVehicle = await tx
        .insert(vehicles)
        .values({
          partyId: parseInt(partyId),
          modelId: vehicleData.modelId,
          vinNumber: vehicleData.vinNumber,
          vehicleRegistration: vehicleData.vehicleRegistration || null,
          countryId: vehicleData.countryId || 1,
          manufacturingYear: vehicleData.manufacturingYear || null,
          purchaseDate: vehicleData.purchaseDate || new Date().toISOString(),
          color: vehicleData.color || null,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();

      const vehicleId = newVehicle[0].id;

      // Add vehicle media if provided
      if (vehicleData.images && vehicleData.images.length > 0) {
        const mediaInserts = vehicleData.images.map((imagePath) => ({
          vehicleId: vehicleId,
          mediaPath: imagePath,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }));

        await tx.insert(vehicleMedia).values(mediaInserts);
        console.log(
          `Inserted ${mediaInserts.length} vehicle media records for vehicle ${vehicleId}`
        );
      }

      return { vehicleId };
    });

    return { success: true, vehicleId: result.vehicleId };
  } catch (error) {
    console.error("Error creating vehicle for listing:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create vehicle",
    };
  }
}

/**
 * Get vehicle makes for dropdown selection
 */
export async function getVehicleMakesForListing() {
  try {
    const makes = await db
      .select({
        id: vehicleMake.id,
        name: vehicleMake.name,
        description: vehicleMake.description,
      })
      .from(vehicleMake)
      .where(eq(vehicleMake.isActive, true))
      .orderBy(vehicleMake.name);

    return makes;
  } catch (error) {
    console.error("Error fetching vehicle makes:", error);
    return [];
  }
}

/**
 * Get vehicle models by make for dropdown selection
 */
export async function getVehicleModelsByMakeForListing(makeId: number) {
  try {
    const models = await db
      .select({
        id: vehicleModel.id,
        model: vehicleModel.model,
        firstYear: vehicleModel.firstYear,
        lastYear: vehicleModel.lastYear,
        bodyType: vehicleModel.bodyType,
      })
      .from(vehicleModel)
      .where(
        and(eq(vehicleModel.makeId, makeId), eq(vehicleModel.isActive, true))
      )
      .orderBy(vehicleModel.model);

    return models;
  } catch (error) {
    console.error("Error fetching vehicle models:", error);
    return [];
  }
}

/**
 * Get vehicle variants by model for dropdown selection
 */
export async function getVehicleVariantsByModelForListing(modelId: number) {
  try {
    const variants = await db
      .select({
        id: vehicleVariant.id,
        name: vehicleVariant.name,
        trimName: vehicleVariant.trimName,
        year: vehicleVariant.year,
        fuelType: vehicleVariant.fuelType,
        transmission: vehicleVariant.transmission,
      })
      .from(vehicleVariant)
      .where(
        and(
          eq(vehicleVariant.modelId, modelId),
          eq(vehicleVariant.isActive, true)
        )
      )
      .orderBy(vehicleVariant.year, vehicleVariant.name);

    return variants;
  } catch (error) {
    console.error("Error fetching vehicle variants:", error);
    return [];
  }
}
