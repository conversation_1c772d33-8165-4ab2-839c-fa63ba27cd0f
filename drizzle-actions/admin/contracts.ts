"use server";

import { db } from "../../db";
import { eq, desc, and, isNull } from "drizzle-orm";
import { getCurrentUserNoCache } from "../../lib/amplifyServerUtils";
import {
  h_assignment_contracts,
  h_payment_audit_log,
} from "../../drizzle/h_schema/payment-contract";
import { party } from "../../drizzle/schema";
import {
  ContractRecord,
  ContractUploadInput,
  PaymentError,
} from "../../types/payment-contract";
import {
  DocumentUpload,
  DocumentDelete,
  generateDocumentUrl,
} from "../../lib/utils";

// =====================================================
// CONTRACT UPLOAD AND MANAGEMENT
// =====================================================

/**
 * Upload a contract document for a vehicle assignment
 */
export async function uploadAssignmentContract(
  assignmentId: number,
  contractFile: File,
  notes?: string
): Promise<{ success: boolean; data?: ContractRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Validate file
    const validationResult = validateContractFile(contractFile);
    if (!validationResult.isValid) {
      return {
        success: false,
        error: validationResult.error,
      };
    }

    // Upload file to S3
    let uploadResult;
    try {
      uploadResult = await DocumentUpload(contractFile, "contracts");
    } catch (uploadError) {
      console.error(
        "❌ [uploadAssignmentContract] S3 Upload Error:",
        uploadError
      );
      return {
        success: false,
        error: {
          code: "UPLOAD_FAILED",
          message:
            uploadError instanceof Error
              ? uploadError.message
              : "Failed to upload contract file",
        },
      };
    }

    const contractFilePath = uploadResult?.path || uploadResult;
    if (!contractFilePath) {
      return {
        success: false,
        error: {
          code: "UPLOAD_FAILED",
          message: "No file path returned from upload",
        },
      };
    }

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Check if there's an existing active contract for this assignment
      const existingContracts = await tx
        .select()
        .from(h_assignment_contracts)
        .where(
          and(
            eq(h_assignment_contracts.assignmentId, assignmentId),
            isNull(h_assignment_contracts.replacedBy)
          )
        );

      // Mark existing contract as replaced if exists
      if (existingContracts.length > 0) {
        const existingContract = existingContracts[0];

        // Insert new contract first
        const [newContract] = await tx
          .insert(h_assignment_contracts)
          .values({
            assignmentId,
            contractFilePath:
              typeof contractFilePath === "string" ? contractFilePath : "",
            originalFilename: contractFile.name,
            fileSize: contractFile.size,
            mimeType: contractFile.type,
            uploadedBy: adminPartyId,
            notes,
          })
          .returning();

        // Update existing contract to mark it as replaced
        await tx
          .update(h_assignment_contracts)
          .set({ replacedBy: newContract.id })
          .where(eq(h_assignment_contracts.id, existingContract.id));

        // Create audit log for replacement
        await tx.insert(h_payment_audit_log).values({
          assignmentId,
          actionType: "REPLACE_CONTRACT",
          tableName: "h_assignment_contracts",
          recordId: newContract.id,
          oldValues: {
            contractId: existingContract.id,
            originalFilename: existingContract.originalFilename,
          },
          newValues: {
            contractId: newContract.id,
            originalFilename: contractFile.name,
            fileSize: contractFile.size,
            mimeType: contractFile.type,
          },
          performedBy: adminPartyId,
        });

        return newContract;
      } else {
        // Insert new contract (first contract for this assignment)
        const [newContract] = await tx
          .insert(h_assignment_contracts)
          .values({
            assignmentId,
            contractFilePath:
              typeof contractFilePath === "string" ? contractFilePath : "",
            originalFilename: contractFile.name,
            fileSize: contractFile.size,
            mimeType: contractFile.type,
            uploadedBy: adminPartyId,
            notes,
          })
          .returning();

        // Create audit log entry
        await tx.insert(h_payment_audit_log).values({
          assignmentId,
          actionType: "UPLOAD_CONTRACT",
          tableName: "h_assignment_contracts",
          recordId: newContract.id,
          newValues: {
            contractId: newContract.id,
            originalFilename: contractFile.name,
            fileSize: contractFile.size,
            mimeType: contractFile.type,
          },
          performedBy: adminPartyId,
        });

        return newContract;
      }
    });

    // Format response
    const contractRecord: ContractRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      contractFilePath: result.contractFilePath,
      originalFilename: result.originalFilename,
      fileSize: result.fileSize,
      mimeType: result.mimeType,
      uploadedAt: result.uploadedAt.toISOString(),
      uploadedBy: result.uploadedBy,
      isActive: !result.replacedBy,
      replacedBy: result.replacedBy || undefined,
      notes: result.notes || undefined,
    };

    return {
      success: true,
      data: contractRecord,
    };
  } catch (error) {
    console.error("❌ [uploadAssignmentContract] Error:", error);
    return {
      success: false,
      error: {
        code: "CONTRACT_UPLOAD_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to upload assignment contract",
      },
    };
  }
}

/**
 * Get the active contract for an assignment
 */
export async function getAssignmentContract(assignmentId: number): Promise<{
  success: boolean;
  data?: ContractRecord | null;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get the active contract (not replaced)
    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(
        and(
          eq(h_assignment_contracts.assignmentId, assignmentId),
          isNull(h_assignment_contracts.replacedBy)
        )
      )
      .orderBy(desc(h_assignment_contracts.uploadedAt))
      .limit(1);

    if (contracts.length === 0) {
      return {
        success: true,
        data: null,
      };
    }

    const contract = contracts[0];
    const contractRecord: ContractRecord = {
      id: contract.id,
      assignmentId: contract.assignmentId,
      contractFilePath: contract.contractFilePath,
      originalFilename: contract.originalFilename,
      fileSize: contract.fileSize,
      mimeType: contract.mimeType,
      uploadedAt: contract.uploadedAt.toISOString(),
      uploadedBy: contract.uploadedBy,
      isActive: true,
      replacedBy: contract.replacedBy || undefined,
      notes: contract.notes || undefined,
    };

    return {
      success: true,
      data: contractRecord,
    };
  } catch (error) {
    console.error("❌ [getAssignmentContract] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_CONTRACT_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch assignment contract",
      },
    };
  }
}

/**
 * Get all contracts for an assignment (including replaced ones)
 */
export async function getAssignmentContractHistory(
  assignmentId: number
): Promise<{
  success: boolean;
  data?: ContractRecord[];
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.assignmentId, assignmentId))
      .orderBy(desc(h_assignment_contracts.uploadedAt));

    const contractRecords: ContractRecord[] = contracts.map((contract) => ({
      id: contract.id,
      assignmentId: contract.assignmentId,
      contractFilePath: contract.contractFilePath,
      originalFilename: contract.originalFilename,
      fileSize: contract.fileSize,
      mimeType: contract.mimeType,
      uploadedAt: contract.uploadedAt.toISOString(),
      uploadedBy: contract.uploadedBy,
      isActive: !contract.replacedBy,
      replacedBy: contract.replacedBy || undefined,
      notes: contract.notes || undefined,
    }));

    return {
      success: true,
      data: contractRecords,
    };
  } catch (error) {
    console.error("❌ [getAssignmentContractHistory] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_CONTRACT_HISTORY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch contract history",
      },
    };
  }
}

/**
 * Generate a signed URL for contract download
 */
export async function generateContractDownloadUrl(
  contractId: number
): Promise<{ success: boolean; data?: string; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get contract
    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.id, contractId))
      .limit(1);

    if (contracts.length === 0) {
      return {
        success: false,
        error: {
          code: "CONTRACT_NOT_FOUND",
          message: "Contract not found",
        },
      };
    }

    const contract = contracts[0];

    // Generate signed URL
    try {
      const signedUrl = await generateDocumentUrl(contract.contractFilePath);

      return {
        success: true,
        data: signedUrl,
      };
    } catch (urlError) {
      console.error("❌ [generateContractDownloadUrl] URL Error:", urlError);
      return {
        success: false,
        error: {
          code: "URL_GENERATION_FAILED",
          message: "Failed to generate download URL",
        },
      };
    }
  } catch (error) {
    console.error("❌ [generateContractDownloadUrl] Error:", error);
    return {
      success: false,
      error: {
        code: "DOWNLOAD_URL_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to generate contract download URL",
      },
    };
  }
}

/**
 * Delete a contract (soft delete by marking as replaced)
 */
export async function deleteAssignmentContract(
  contractId: number,
  reason?: string
): Promise<{ success: boolean; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get contract to delete
    const contracts = await db
      .select()
      .from(h_assignment_contracts)
      .where(eq(h_assignment_contracts.id, contractId))
      .limit(1);

    if (contracts.length === 0) {
      return {
        success: false,
        error: {
          code: "CONTRACT_NOT_FOUND",
          message: "Contract not found",
        },
      };
    }

    const contract = contracts[0];

    // Soft delete by marking as replaced (with special ID -1 for deleted)
    await db.transaction(async (tx) => {
      // Update contract as "deleted"
      await tx
        .update(h_assignment_contracts)
        .set({ replacedBy: -1 }) // Special marker for deleted contracts
        .where(eq(h_assignment_contracts.id, contractId));

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: contract.assignmentId,
        actionType: "DELETE_CONTRACT",
        tableName: "h_assignment_contracts",
        recordId: contractId,
        oldValues: {
          contractId,
          originalFilename: contract.originalFilename,
          isActive: true,
        },
        newValues: {
          contractId,
          isActive: false,
          deletionReason: reason,
        },
        performedBy: adminPartyId,
      });
    });

    // Optionally delete from S3 (commented out for safety - keep files for audit)
    // try {
    //   await DocumentDelete(contract.contractFilePath);
    // } catch (deleteError) {
    //   console.warn("Failed to delete file from S3:", deleteError);
    //   // Don't fail the operation if S3 delete fails
    // }

    return {
      success: true,
    };
  } catch (error) {
    console.error("❌ [deleteAssignmentContract] Error:", error);
    return {
      success: false,
      error: {
        code: "CONTRACT_DELETE_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to delete assignment contract",
      },
    };
  }
}

// =====================================================
// VALIDATION HELPERS
// =====================================================

/**
 * Validate contract file before upload
 */
export function validateContractFile(file: File): {
  isValid: boolean;
  error?: PaymentError;
} {
  // File size validation (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: {
        code: "FILE_TOO_LARGE",
        message: `File size must be less than 10MB. Current size: ${(file.size / (1024 * 1024)).toFixed(2)}MB`,
        field: "file",
      },
    };
  }

  // File type validation
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/jpg",
    "image/png",
  ];

  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: {
        code: "INVALID_FILE_TYPE",
        message: `File type not allowed. Allowed types: PDF, JPG, PNG. Current type: ${file.type}`,
        field: "file",
      },
    };
  }

  // File name validation
  if (!file.name || file.name.trim().length === 0) {
    return {
      isValid: false,
      error: {
        code: "INVALID_FILE_NAME",
        message: "File must have a valid name",
        field: "file",
      },
    };
  }

  return {
    isValid: true,
  };
}

/**
 * Get contracts summary for multiple assignments
 */
export async function getContractsSummary(assignmentIds: number[]): Promise<{
  success: boolean;
  data?: Array<{
    assignmentId: number;
    hasActiveContract: boolean;
    contractCount: number;
    latestContractDate?: string;
    latestContractFilename?: string;
  }>;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get contract summaries for all assignments
    const summaries = await Promise.all(
      assignmentIds.map(async (assignmentId) => {
        const allContracts = await db
          .select()
          .from(h_assignment_contracts)
          .where(eq(h_assignment_contracts.assignmentId, assignmentId))
          .orderBy(desc(h_assignment_contracts.uploadedAt));

        const activeContract = allContracts.find(
          (contract) => !contract.replacedBy
        );
        const latestContract = allContracts[0];

        return {
          assignmentId,
          hasActiveContract: !!activeContract,
          contractCount: allContracts.length,
          latestContractDate: latestContract?.uploadedAt.toISOString(),
          latestContractFilename: latestContract?.originalFilename,
        };
      })
    );

    return {
      success: true,
      data: summaries,
    };
  } catch (error) {
    console.error("❌ [getContractsSummary] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_CONTRACTS_SUMMARY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch contracts summary",
      },
    };
  }
}
