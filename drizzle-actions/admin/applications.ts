import { db } from "../../db";
import { and, desc, eq, inArray, ne, sql, isNull, or } from "drizzle-orm";
import {
  h_applications,
  h_applicationDocuments,
  h_applicationDecisions,
  h_applicationDocumentsStatus,
} from "@/drizzle/h_schema/applications";
import { h_listings } from "@/drizzle/h_schema/listings";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";
import { party, individual, vehicles, contactPoint } from "@/drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import { vehicleMake, vehicleModel } from "@/drizzle/schema";

// ==================== TYPES ====================

export interface AdminApplicationWithDetails {
  id: number;
  applicantId: number;
  listingId: number;
  applicationDetails: any;
  createdAt: Date;

  // Listing information
  listing: {
    id: number;
    listingType: string;
    listingDetails: any;
    sourceType: string;
    sourceId: number;
  };

  // Applicant information
  applicant: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
  };

  // Vehicle/Catalog information
  vehicle?: {
    make: string;
    model: string;
    year: number;
    registration?: string;
  };

  catalog?: {
    make: string;
    model: string;
    year: number;
    category: string;
    weeklyFeeTarget?: number;
  };

  // Documents
  documents: Array<{
    id: number;
    documentType: string;
    documentUrl: string;
    uploadedAt: Date;
    status?: string;
    statusAt?: Date;
  }>;

  // Latest decision
  latestDecision?: {
    decision: string;
    reason?: string;
    decisionAt: Date;
    reviewerName?: string;
  };
}

export interface AdminApplicationFilters {
  status?: "all" | "pending" | "under_review" | "approved" | "rejected";
  applicationType?: "all" | "ehailing-platform" | "rental" | "fractional";
  searchQuery?: string;
  dateFrom?: Date;
  dateTo?: Date;
}

// ==================== ADMIN APPLICATION FUNCTIONS ====================

/**
 * Get current admin user's party ID from authentication
 */
async function getCurrentAdminPartyId(): Promise<number | null> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      console.error("❌ [Auth] No authenticated user found");
      return null;
    }

    const partyId = userAttributes["custom:db_id"];

    if (!partyId) {
      console.error("❌ [Auth] No party ID found in user attributes");
      return null;
    }

    const parsedPartyId = parseInt(partyId);
    return parsedPartyId;
  } catch (error) {
    console.error("❌ [Auth] Error getting current admin party ID:", error);
    return null;
  }
}

export async function getContactPointsByPartyId(partyId: number) {
  const contactPoints = await db
    .select({
      contactPointTypeId: contactPoint.contactPointTypeId,
      value: contactPoint.value,
      isPrimary: contactPoint.isPrimary,
    })
    .from(contactPoint)
    .where(eq(contactPoint.partyId, partyId));

  // Transform to more useful format
  const result = {
    email:
      contactPoints.find((cp) => cp.contactPointTypeId === 1 && cp.isPrimary)
        ?.value ||
      contactPoints.find((cp) => cp.contactPointTypeId === 1)?.value ||
      null,
    phone:
      contactPoints.find((cp) => cp.contactPointTypeId === 2 && cp.isPrimary)
        ?.value ||
      contactPoints.find((cp) => cp.contactPointTypeId === 2)?.value ||
      null,
  };

  return result;
}

/**
 * Admin function: Make a decision on an application
 */
export async function makeApplicationDecision(
  applicationId: number,
  decision: "approved" | "rejected",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  console.log("🚀 [DRIZZLE] makeApplicationDecision called");
  console.log("🚀 [DRIZZLE] Parameters:", { applicationId, decision, reason });

  try {
    console.log("🚀 [DRIZZLE] Getting user attributes...");
    const userAttributes = await getUserAttributes();
    console.log("🚀 [DRIZZLE] User attributes:", userAttributes);

    if (!userAttributes) {
      console.log("❌ [DRIZZLE] User not authenticated");
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the application exists
    console.log("🚀 [DRIZZLE] Verifying application exists...");
    const application = await db
      .select()
      .from(h_applications)
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    console.log("🚀 [DRIZZLE] Application query result:", application);

    if (application.length === 0) {
      console.log("❌ [DRIZZLE] Application not found");
      return { success: false, error: "Application not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    console.log("🚀 [DRIZZLE] Reviewer party ID:", reviewerPartyId);
    console.log(
      "🚀 [DRIZZLE] All user attributes keys:",
      Object.keys(userAttributes)
    );
    console.log("🚀 [DRIZZLE] User sub:", userAttributes.sub);
    console.log("🚀 [DRIZZLE] User email:", userAttributes.email);

    if (!reviewerPartyId) {
      console.log("❌ [DRIZZLE] Reviewer party ID not found in custom:db_id");
      console.log(
        "❌ [DRIZZLE] This admin user needs to have custom:db_id attribute set"
      );
      return {
        success: false,
        error:
          "Admin user missing custom:db_id attribute. Admin needs proper setup.",
      };
    }

    // Insert decision
    console.log("🚀 [DRIZZLE] Inserting decision into database...");
    const decisionData = {
      applicationId,
      decision,
      reason: reason || null,
      reviewerId: parseInt(reviewerPartyId),
      decisionAt: new Date(),
    };
    console.log("🚀 [DRIZZLE] Decision data to insert:", decisionData);

    await db.insert(h_applicationDecisions).values(decisionData);

    console.log("✅ [DRIZZLE] Decision inserted successfully");
    return { success: true };
  } catch (error) {
    console.error("❌ [DRIZZLE] Error making application decision:", error);
    return { success: false, error: "Failed to make decision" };
  }
}

/**
 * Update document verification status
 */
export async function updateDocumentStatus(
  documentId: number,
  status: "verified" | "rejected" | "pending"
): Promise<{ success: boolean; error?: string; status?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the document exists
    const document = await db
      .select()
      .from(h_applicationDocuments)
      .where(eq(h_applicationDocuments.id, documentId))
      .limit(1);

    if (document.length === 0) {
      return { success: false, error: "Document not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Insert document status
    await db.insert(h_applicationDocumentsStatus).values({
      applicationDocumentId: documentId,
      status,
      statusAt: new Date(),
      statusBy: parseInt(reviewerPartyId),
    });

    return { success: true, status };
  } catch (error) {
    console.error("Error updating document status:", error);
    return { success: false, error: "Failed to update document status" };
  }
}

/**
 * Update application status with timeline tracking
 */
export async function updateApplicationStatus(
  applicationId: number,
  newStatus: "pending" | "under_review" | "approved" | "rejected" | "withdrawn",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // Get reviewer party ID
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Verify the application exists
    const application = await db
      .select()
      .from(h_applications)
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    if (application.length === 0) {
      return { success: false, error: "Application not found" };
    }

    // Insert decision record for status tracking
    await db.insert(h_applicationDecisions).values({
      applicationId,
      decision: newStatus,
      reason,
      reviewerId: parseInt(reviewerPartyId),
      decisionAt: new Date(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating application status:", error);
    return { success: false, error: "Failed to update application status" };
  }
}

/**
 * Get application status timeline
 */
export async function getApplicationStatusTimeline(
  applicationId: number
): Promise<{
  success: boolean;
  timeline?: Array<{
    id: number;
    status: string;
    reason?: string | null;
    timestamp: Date;
    reviewerName?: string | null;
  }>;
  error?: string;
}> {
  try {
    const timeline = await db
      .select({
        id: h_applicationDecisions.id,
        status: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        timestamp: h_applicationDecisions.decisionAt,
        reviewerName: sql<
          string | null
        >`CONCAT(${individual.firstName}, ' ', ${individual.lastName})`,
      })
      .from(h_applicationDecisions)
      .leftJoin(party, eq(h_applicationDecisions.reviewerId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt));

    return { success: true, timeline };
  } catch (error) {
    console.error("Error fetching application timeline:", error);
    return { success: false, error: "Failed to fetch application timeline" };
  }
}

/**
 * Get current application status
 */
export async function getCurrentApplicationStatus(
  applicationId: number
): Promise<{
  success: boolean;
  status?: "pending" | "under_review" | "approved" | "rejected" | "withdrawn";
  error?: string;
}> {
  try {
    const latestDecision = await db
      .select({
        status: h_applicationDecisions.decision,
      })
      .from(h_applicationDecisions)
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt))
      .limit(1);

    if (latestDecision.length === 0) {
      // No decisions yet, default to pending
      return { success: true, status: "pending" };
    }

    return { success: true, status: latestDecision[0].status };
  } catch (error) {
    console.error("Error fetching current application status:", error);
    return { success: false, error: "Failed to fetch application status" };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStats(): Promise<{
  success: boolean;
  stats?: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    byListingType: Record<string, number>;
  };
  error?: string;
}> {
  try {
    // TODO: Add admin authorization check

    // Get total applications
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(h_applications);

    // Get applications by latest decision status
    const statusStats = await db
      .select({
        decision: h_applicationDecisions.decision,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .leftJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .groupBy(h_applicationDecisions.decision);

    // Get applications by listing type
    const typeStats = await db
      .select({
        listingType: h_listings.listingType,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .groupBy(h_listings.listingType);

    const stats = {
      total: totalResult[0]?.count || 0,
      pending:
        statusStats.find((s) => s.decision === null || s.decision === "pending")
          ?.count || 0,
      approved: statusStats.find((s) => s.decision === "approved")?.count || 0,
      rejected: statusStats.find((s) => s.decision === "rejected")?.count || 0,
      byListingType: typeStats.reduce(
        (acc, stat) => {
          acc[stat.listingType] = stat.count;
          return acc;
        },
        {} as Record<string, number>
      ),
    };

    return { success: true, stats };
  } catch (error) {
    console.error("Error fetching application stats:", error);
    return { success: false, error: "Failed to fetch application statistics" };
  }
}

/**
 * ADMIN: Get all applications for admin review (WORKING VERSION)
 */
export async function getAllApplicationsForAdmin(): Promise<{
  success: boolean;
  applications?: AdminApplicationWithDetails[];
  error?: string;
}> {
  try {
    // TODO: Add admin authentication check here if needed

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,

        // Listing fields
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,

        // Applicant fields (these are the people who submitted applications)
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id IN (1, 4)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("email"),
        phone: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id IN (2, 3)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("phone"),
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id)) // Join to applicant, not admin
      .leftJoin(individual, eq(party.id, individual.partyId))
      .orderBy(desc(h_applications.id));

    if (applications.length === 0) {
      return { success: true, applications: [] };
    }

    const applicationIds = applications.map((app) => app.id);

    // Step 2: Get vehicle data for vehicle-based listings
    const vehicleListings = applications.filter(
      (app) => app.sourceType === "vehicle"
    );
    const vehicleData =
      vehicleListings.length > 0
        ? await db
            .select({
              sourceId: vehicles.id,
              make: vehicleMake.name,
              model: vehicleModel.model,
              year: vehicles.manufacturingYear,
              registration: vehicles.vehicleRegistration,
            })
            .from(vehicles)
            .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
            .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
            .where(
              inArray(
                vehicles.id,
                vehicleListings.map((app) => app.sourceId)
              )
            )
        : [];

    // Step 3: Get catalog data for catalog-based listings
    const catalogListings = applications.filter(
      (app) => app.sourceType === "catalog"
    );
    const catalogData =
      catalogListings.length > 0
        ? await db
            .select({
              sourceId: h_vehicleCatalog.id,
              make: h_vehicleCatalog.make,
              model: h_vehicleCatalog.model,
              year: h_vehicleCatalog.year,
              category: h_vehicleCatalog.category,
              weeklyFeeTarget: h_vehicleCatalog.weeklyFeeTarget,
            })
            .from(h_vehicleCatalog)
            .where(
              inArray(
                h_vehicleCatalog.id,
                catalogListings.map((app) => app.sourceId)
              )
            )
        : [];

    // Step 4: Get all documents for these applications with their latest status
    const documentsResult = await db
      .select({
        applicationId: h_applicationDocuments.applicationId,
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
        documentUrl: h_applicationDocuments.documentUrl,
        uploadedAt: h_applicationDocuments.uploadedAt,
        status: h_applicationDocumentsStatus.status,
        statusAt: h_applicationDocumentsStatus.statusAt,
      })
      .from(h_applicationDocuments)
      .leftJoin(
        h_applicationDocumentsStatus,
        eq(
          h_applicationDocuments.id,
          h_applicationDocumentsStatus.applicationDocumentId
        )
      )
      .where(
        and(
          inArray(h_applicationDocuments.applicationId, applicationIds),
          // Only exclude superseded documents if they have a status
          or(
            isNull(h_applicationDocumentsStatus.status),
            ne(h_applicationDocumentsStatus.status, "superseded")
          )
        )
      )
      .orderBy(
        desc(h_applicationDocuments.uploadedAt),
        desc(h_applicationDocumentsStatus.statusAt)
      );

    // Step 5: Get latest decisions for these applications
    const latestDecisionsResult = await db
      .select({
        applicationId: h_applicationDecisions.applicationId,
        decision: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        decisionAt: h_applicationDecisions.decisionAt,
        reviewerId: h_applicationDecisions.reviewerId,
        // Get reviewer name
        reviewerFirstName: sql<string>`(
          SELECT i.first_name 
          FROM individual i 
          WHERE i.party_id = ${h_applicationDecisions.reviewerId}
          LIMIT 1
        )`.as("reviewerFirstName"),
        reviewerLastName: sql<string>`(
          SELECT i.last_name 
          FROM individual i 
          WHERE i.party_id = ${h_applicationDecisions.reviewerId}
          LIMIT 1
        )`.as("reviewerLastName"),
      })
      .from(h_applicationDecisions)
      .where(inArray(h_applicationDecisions.applicationId, applicationIds))
      .orderBy(desc(h_applicationDecisions.decisionAt));

    // Step 6: Create lookup maps for efficient data access
    const vehicleMap = vehicleData.reduce(
      (acc, vehicle) => {
        acc[vehicle.sourceId] = vehicle;
        return acc;
      },
      {} as Record<number, (typeof vehicleData)[0]>
    );

    const catalogMap = catalogData.reduce(
      (acc, catalog) => {
        acc[catalog.sourceId] = catalog;
        return acc;
      },
      {} as Record<number, (typeof catalogData)[0]>
    );

    // Create documents lookup map with latest status only
    const documentsLookup = new Map<number, typeof documentsResult>();

    // Group documents by document ID, keeping only the latest status for each document
    const documentsByDocId = new Map<number, (typeof documentsResult)[0]>();

    for (const doc of documentsResult) {
      const currentDocStatusAt = documentsByDocId.has(doc.id)
        ? documentsByDocId.get(doc.id)!.statusAt
        : null;

      if (
        !documentsByDocId.has(doc.id) ||
        (doc.statusAt &&
          currentDocStatusAt &&
          new Date(doc.statusAt) > new Date(currentDocStatusAt)) ||
        (doc.statusAt && !currentDocStatusAt)
      ) {
        documentsByDocId.set(doc.id, doc);
      }
    }

    // Now group by application and document type, keeping only non-superseded documents
    const latestDocumentsByAppAndType = new Map<
      string,
      (typeof documentsResult)[0]
    >();

    for (const doc of documentsByDocId.values()) {
      // Skip superseded documents
      if (doc.status === "superseded") continue;

      const key = `${doc.applicationId}-${doc.documentType}`;
      if (
        !latestDocumentsByAppAndType.has(key) ||
        new Date(doc.uploadedAt) >
          new Date(latestDocumentsByAppAndType.get(key)!.uploadedAt)
      ) {
        latestDocumentsByAppAndType.set(key, doc);
      }
    }

    // Group by application ID
    for (const doc of latestDocumentsByAppAndType.values()) {
      if (!documentsLookup.has(doc.applicationId)) {
        documentsLookup.set(doc.applicationId, []);
      }
      documentsLookup.get(doc.applicationId)!.push(doc);
    }

    // Create latest decision lookup map (one decision per application)
    const decisionsLookup = latestDecisionsResult.reduce((acc, decision) => {
      if (!acc.has(decision.applicationId)) {
        acc.set(decision.applicationId, decision);
      }
      return acc;
    }, new Map<number, (typeof latestDecisionsResult)[0]>());

    // Step 7: Transform to AdminApplicationWithDetails format
    const result: AdminApplicationWithDetails[] = applications.map((app) => {
      const vehicle =
        app.sourceType === "vehicle" ? vehicleMap[app.sourceId] : undefined;
      const catalog =
        app.sourceType === "catalog" ? catalogMap[app.sourceId] : undefined;
      const appDocuments = documentsLookup.get(app.id) || [];
      const latestDecision = decisionsLookup.get(app.id);

      return {
        id: app.id,
        applicantId: app.applicantId,
        listingId: app.listingId,
        applicationDetails: app.applicationDetails
          ? JSON.parse(app.applicationDetails)
          : {},
        createdAt: new Date(),

        applicant: {
          id: app.applicantId,
          firstName: app.firstName || "",
          lastName: app.lastName || "",
          email: app.email || "",
        },
        listing: {
          id: app.listingId,
          listingType: app.listingType,
          listingDetails: app.listingDetails
            ? JSON.parse(app.listingDetails)
            : {},
          sourceType: app.sourceType,
          sourceId: app.sourceId,
        },

        // Add vehicle details if available
        ...(vehicle && {
          vehicle: {
            make: vehicle.make || "Unknown Make",
            model: vehicle.model || "Unknown Model",
            year: vehicle.year || 0,
            registration: vehicle.registration || undefined,
          },
        }),

        // Add catalog details if available
        ...(catalog && {
          catalog: {
            make: catalog.make || "Unknown Make",
            model: catalog.model || "Unknown Model",
            year: catalog.year || 0,
            category: catalog.category || "Unknown",
            weeklyFeeTarget: catalog.weeklyFeeTarget || undefined,
          },
        }),

        // Add documents with status
        documents: appDocuments.map((doc) => {
          return {
            id: doc.id,
            documentType: doc.documentType,
            documentUrl: doc.documentUrl,
            uploadedAt: doc.uploadedAt,
            status: doc.status || undefined, // Convert null to undefined for type compatibility
            statusAt: doc.statusAt || undefined, // Convert null to undefined for type compatibility
          };
        }),

        // Add latest decision
        latestDecision: latestDecision
          ? {
              decision: latestDecision.decision,
              reason: latestDecision.reason || undefined,
              decisionAt: latestDecision.decisionAt,
              reviewerName:
                latestDecision.reviewerFirstName &&
                latestDecision.reviewerLastName
                  ? `${latestDecision.reviewerFirstName} ${latestDecision.reviewerLastName}`
                  : undefined,
            }
          : undefined,
      };
    });

    return { success: true, applications: result };
  } catch (error) {
    console.error("Error fetching applications for admin:", error);
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStatisticsForAdmin(): Promise<{
  total: number;
  pending: number;
  underReview: number;
  approved: number;
  rejected: number;
  byType: Record<string, number>;
}> {
  try {
    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(h_applications);

    const total = totalResult[0]?.count || 0;

    // Get counts by latest decision status
    const statusCounts = await db
      .select({
        decision: h_applicationDecisions.decision,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .leftJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .groupBy(h_applicationDecisions.decision);

    // Get counts by listing type
    const typeCounts = await db
      .select({
        listingType: h_listings.listingType,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .groupBy(h_listings.listingType);

    // Process status counts
    const statusMap = statusCounts.reduce(
      (acc, item) => {
        acc[item.decision || "pending"] = item.count;
        return acc;
      },
      {} as Record<string, number>
    );

    // Process type counts
    const typeMap = typeCounts.reduce(
      (acc, item) => {
        acc[item.listingType] = item.count;
        return acc;
      },
      {} as Record<string, number>
    );

    return {
      total,
      pending: statusMap.pending || 0,
      underReview: statusMap.under_review || 0,
      approved: statusMap.approved || 0,
      rejected: statusMap.rejected || 0,
      byType: typeMap,
    };
  } catch (error) {
    console.error("Error fetching application statistics:", error);
    throw new Error("Failed to fetch application statistics");
  }
}
