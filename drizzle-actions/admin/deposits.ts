"use server";

import { db } from "../../db";
import { eq, desc, sum, and, sql } from "drizzle-orm";
import { getCurrentUserNoCache } from "../../lib/amplifyServerUtils";
import {
  h_assignment_deposits,
  h_payment_audit_log,
} from "../../drizzle/h_schema/payment-contract";
import { party } from "../../drizzle/schema";
import {
  DepositRecord,
  DepositPaymentInput,
  PaymentError,
} from "../../types/payment-contract";

// =====================================================
// DEPOSIT RECORD CREATION
// =====================================================

/**
 * Record a deposit payment for a vehicle assignment
 */
export async function recordDepositPayment(
  input: DepositPaymentInput
): Promise<{ success: boolean; data?: DepositRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Validate assignment exists (you may want to add this check based on your assignment system)
    // TODO: Add assignment existence validation once assignment tables are defined

    // Calculate balance remaining
    const balanceRemaining = input.depositAmount - input.amountPaid;

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Insert deposit record
      const [depositRecord] = await tx
        .insert(h_assignment_deposits)
        .values({
          assignmentId: input.assignmentId,
          depositAmount: input.depositAmount.toString(),
          amountPaid: input.amountPaid.toString(),
          balanceRemaining: balanceRemaining.toString(),
          paymentMethod: input.paymentMethod,
          paymentReference: input.paymentReference,
          paymentDate: input.amountPaid > 0 ? new Date() : null,
          notes: input.notes,
        })
        .returning();

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: input.assignmentId,
        actionType: "CREATE_DEPOSIT",
        tableName: "h_assignment_deposits",
        recordId: depositRecord.id,
        newValues: {
          depositAmount: input.depositAmount,
          amountPaid: input.amountPaid,
          balanceRemaining,
          paymentMethod: input.paymentMethod,
          paymentReference: input.paymentReference,
        },
        performedBy: adminPartyId,
      });

      return depositRecord;
    });

    // Format response
    const depositRecord: DepositRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      depositAmount: parseFloat(result.depositAmount),
      amountPaid: parseFloat(result.amountPaid),
      balanceRemaining: parseFloat(result.balanceRemaining),
      paymentMethod: result.paymentMethod || undefined,
      paymentReference: result.paymentReference || undefined,
      paymentDate: result.paymentDate?.toISOString(),
      notes: result.notes || undefined,
      createdAt: new Date().toISOString(),
      createdBy: adminPartyId,
    };

    return {
      success: true,
      data: depositRecord,
    };
  } catch (error) {
    console.error("❌ [recordDepositPayment] Error:", error);
    return {
      success: false,
      error: {
        code: "DEPOSIT_RECORD_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to record deposit payment",
      },
    };
  }
}

/**
 * Add additional payment to existing deposit
 */
export async function addDepositPayment(
  depositId: number,
  additionalPayment: number,
  paymentMethod?: string,
  paymentReference?: string,
  notes?: string
): Promise<{ success: boolean; data?: DepositRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing deposit
    const existingDeposit = await db
      .select()
      .from(h_assignment_deposits)
      .where(eq(h_assignment_deposits.id, depositId))
      .limit(1);

    if (existingDeposit.length === 0) {
      return {
        success: false,
        error: {
          code: "DEPOSIT_NOT_FOUND",
          message: "Deposit record not found",
        },
      };
    }

    const deposit = existingDeposit[0];
    const currentAmountPaid = parseFloat(deposit.amountPaid);
    const depositAmount = parseFloat(deposit.depositAmount);
    const newAmountPaid = currentAmountPaid + additionalPayment;
    const newBalanceRemaining = depositAmount - newAmountPaid;

    // Validate payment doesn't exceed deposit
    if (newAmountPaid > depositAmount) {
      return {
        success: false,
        error: {
          code: "PAYMENT_EXCEEDS_DEPOSIT",
          message: "Additional payment would exceed deposit amount",
          details: {
            depositAmount,
            currentAmountPaid,
            additionalPayment,
            excessAmount: newAmountPaid - depositAmount,
          },
        },
      };
    }

    // Update deposit record in transaction
    const result = await db.transaction(async (tx) => {
      const [updatedDeposit] = await tx
        .update(h_assignment_deposits)
        .set({
          amountPaid: newAmountPaid.toString(),
          balanceRemaining: newBalanceRemaining.toString(),
          paymentMethod:
            (paymentMethod as
              | "bank_transfer"
              | "cash"
              | "eft"
              | "card"
              | "other") || deposit.paymentMethod,
          paymentReference: paymentReference || deposit.paymentReference,
          paymentDate: new Date(),
        })
        .where(eq(h_assignment_deposits.id, depositId))
        .returning();

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: deposit.assignmentId,
        actionType: "UPDATE_DEPOSIT_PAYMENT",
        tableName: "h_assignment_deposits",
        recordId: depositId,
        oldValues: {
          amountPaid: currentAmountPaid,
          balanceRemaining: parseFloat(deposit.balanceRemaining),
        },
        newValues: {
          amountPaid: newAmountPaid,
          balanceRemaining: newBalanceRemaining,
          additionalPayment,
        },
        performedBy: adminPartyId,
      });

      return updatedDeposit;
    });

    // Format response
    const depositRecord: DepositRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      depositAmount: parseFloat(result.depositAmount),
      amountPaid: parseFloat(result.amountPaid),
      balanceRemaining: parseFloat(result.balanceRemaining),
      paymentMethod: result.paymentMethod || undefined,
      paymentReference: result.paymentReference || undefined,
      paymentDate: result.paymentDate?.toISOString(),
      notes: result.notes || undefined,
      createdAt: new Date().toISOString(),
      createdBy: adminPartyId,
    };

    return {
      success: true,
      data: depositRecord,
    };
  } catch (error) {
    console.error("❌ [addDepositPayment] Error:", error);
    return {
      success: false,
      error: {
        code: "DEPOSIT_PAYMENT_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to add deposit payment",
      },
    };
  }
}

// =====================================================
// DEPOSIT RETRIEVAL
// =====================================================

/**
 * Get all deposits for a specific assignment
 */
export async function getAssignmentDeposits(
  assignmentId: number
): Promise<{ success: boolean; data?: DepositRecord[]; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const deposits = await db
      .select()
      .from(h_assignment_deposits)
      .where(eq(h_assignment_deposits.assignmentId, assignmentId))
      .orderBy(desc(h_assignment_deposits.id));

    const depositRecords: DepositRecord[] = deposits.map((deposit) => ({
      id: deposit.id,
      assignmentId: deposit.assignmentId,
      depositAmount: parseFloat(deposit.depositAmount),
      amountPaid: parseFloat(deposit.amountPaid),
      balanceRemaining: parseFloat(deposit.balanceRemaining),
      paymentMethod: deposit.paymentMethod || undefined,
      paymentReference: deposit.paymentReference || undefined,
      paymentDate: deposit.paymentDate?.toISOString(),
      notes: deposit.notes || undefined,
      createdAt: new Date().toISOString(), // You may want to add createdAt to schema
      createdBy: adminPartyId,
    }));

    return {
      success: true,
      data: depositRecords,
    };
  } catch (error) {
    console.error("❌ [getAssignmentDeposits] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_DEPOSITS_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch assignment deposits",
      },
    };
  }
}

/**
 * Get deposit summary for multiple assignments
 */
export async function getDepositsSummary(assignmentIds: number[]): Promise<{
  success: boolean;
  data?: Array<{
    assignmentId: number;
    totalDepositAmount: number;
    totalAmountPaid: number;
    totalBalanceRemaining: number;
    paymentCount: number;
    lastPaymentDate?: string;
  }>;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const depositSummaries = await db
      .select({
        assignmentId: h_assignment_deposits.assignmentId,
        totalDepositAmount: sum(h_assignment_deposits.depositAmount),
        totalAmountPaid: sum(h_assignment_deposits.amountPaid),
        totalBalanceRemaining: sum(h_assignment_deposits.balanceRemaining),
        paymentCount: sql<number>`count(*)`,
        lastPaymentDate: sql<Date>`max(${h_assignment_deposits.paymentDate})`,
      })
      .from(h_assignment_deposits)
      .where(sql`${h_assignment_deposits.assignmentId} = ANY(${assignmentIds})`)
      .groupBy(h_assignment_deposits.assignmentId);

    const summaryData = depositSummaries.map((summary) => ({
      assignmentId: summary.assignmentId,
      totalDepositAmount: parseFloat(summary.totalDepositAmount || "0"),
      totalAmountPaid: parseFloat(summary.totalAmountPaid || "0"),
      totalBalanceRemaining: parseFloat(summary.totalBalanceRemaining || "0"),
      paymentCount: summary.paymentCount,
      lastPaymentDate: summary.lastPaymentDate?.toISOString(),
    }));

    return {
      success: true,
      data: summaryData,
    };
  } catch (error) {
    console.error("❌ [getDepositsSummary] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_SUMMARY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch deposits summary",
      },
    };
  }
}

// =====================================================
// BALANCE CALCULATIONS
// =====================================================

/**
 * Calculate total outstanding deposit balance for an assignment
 */
export async function getAssignmentDepositBalance(
  assignmentId: number
): Promise<{ success: boolean; data?: number; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const result = await db
      .select({
        totalBalance: sum(h_assignment_deposits.balanceRemaining),
      })
      .from(h_assignment_deposits)
      .where(eq(h_assignment_deposits.assignmentId, assignmentId));

    const totalBalance = parseFloat(result[0]?.totalBalance || "0");

    return {
      success: true,
      data: totalBalance,
    };
  } catch (error) {
    console.error("❌ [getAssignmentDepositBalance] Error:", error);
    return {
      success: false,
      error: {
        code: "BALANCE_CALCULATION_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to calculate deposit balance",
      },
    };
  }
}

/**
 * Validate deposit payment business rules
 */
export function validateDepositPayment(input: DepositPaymentInput): {
  isValid: boolean;
  errors: PaymentError[];
} {
  const errors: PaymentError[] = [];

  // Amount paid cannot exceed deposit amount
  if (input.amountPaid > input.depositAmount) {
    errors.push({
      code: "PAYMENT_EXCEEDS_DEPOSIT",
      message: "Amount paid cannot exceed deposit amount",
      field: "amountPaid",
      details: {
        depositAmount: input.depositAmount,
        amountPaid: input.amountPaid,
        excess: input.amountPaid - input.depositAmount,
      },
    });
  }

  // Payment reference required when amount paid > 0
  if (input.amountPaid > 0 && !input.paymentReference?.trim()) {
    errors.push({
      code: "PAYMENT_REFERENCE_REQUIRED",
      message: "Payment reference is required when recording a payment",
      field: "paymentReference",
    });
  }

  // Deposit amount must be positive
  if (input.depositAmount <= 0) {
    errors.push({
      code: "INVALID_DEPOSIT_AMOUNT",
      message: "Deposit amount must be greater than zero",
      field: "depositAmount",
    });
  }

  // Amount paid cannot be negative
  if (input.amountPaid < 0) {
    errors.push({
      code: "INVALID_PAYMENT_AMOUNT",
      message: "Payment amount cannot be negative",
      field: "amountPaid",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
