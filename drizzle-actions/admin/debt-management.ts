"use server";

import { db } from "@/db";
import {
  eq,
  desc,
  and,
  gte,
  lte,
  sum,
  sql,
  ne,
  isNull,
  or,
  inArray,
} from "drizzle-orm";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import {
  h_driver_debt_tracking,
  h_payment_audit_log,
} from "@/drizzle/h_schema/payment-contract";
import { party } from "@/drizzle/schema";
import { PaymentError, DebtCreationInput, DebtAdjustmentInput, DebtSummary, DebtReportData } from "@/types/payment-contract";

// Define types locally that match the actual database schema
interface DebtRecord {
  id: number;
  assignmentId: number;
  debtSourceId: number;
  debtSourceType: "earnings_shortfall" | "debt_recovery" | "adjustment";
  debtAmount: number;
  runningBalance: number;
  transactionDate: string;
  createdBy: number;
  notes?: string;
}

// Remove local DebtCreationInput - use the one from @/types/payment-contract instead

interface DebtSummary {
  assignmentId: number;
  totalOutstandingDebt: string;
  totalResolvedDebt: string;
  totalDebtCount: number;
  outstandingDebtCount: number;
  resolvedDebtCount: number;
  averageDebtAmount: string;
  earningsShortfallCount: number;
  adjustmentCount: number;
  debtRecoveryCount: number;
  latestDebtDate?: string;
  latestDebtAmount?: string;
}

interface DebtReportData {
  reportPeriod: {
    startDate: string;
    endDate: string;
    assignmentCount: number;
  };
  overallStatistics: {
    totalOutstandingDebt: string;
    totalResolvedDebt: string;
    totalDebtCount: number;
    outstandingDebtCount: number;
    resolvedDebtCount: number;
    averageDebtAmount: string;
    resolutionRate: string;
  };
  debtBreakdown: {
    earningsShortfall: {
      count: number;
      percentage: string;
    };
    adjustment: {
      count: number;
      percentage: string;
    };
    debtRecovery: {
      count: number;
      percentage: string;
    };
  };
  assignmentSummaries: DebtSummary[];
  generatedAt: string;
  generatedBy: number;
}

// =====================================================
// MANUAL DEBT CREATION AND MANAGEMENT
// =====================================================

/**
 * Create a manual debt record (non-earnings related)
 * NOTE: This function is disabled due to schema mismatch and needs to be updated
 */
export async function createManualDebt(
  input: DebtCreationInput
): Promise<{ success: boolean; data?: DebtRecord; error?: PaymentError }> {
  return {
    success: false,
    error: {
      code: "NOT_IMPLEMENTED",
      message:
        "Function disabled due to schema mismatch - needs to be updated to match h_driver_debt_tracking table structure",
    },
  };
}

/**
 * Adjust an existing debt amount
 * NOTE: This function is disabled due to schema mismatch and needs to be updated
 */
export async function adjustDebtAmount(
  debtId: number,
  input: DebtAdjustmentInput
): Promise<{ success: boolean; data?: DebtRecord; error?: PaymentError }> {
  return {
    success: false,
    error: {
      code: "NOT_IMPLEMENTED", 
      message: "Function disabled due to schema mismatch - needs to be updated to match h_driver_debt_tracking table structure",
    },
  };
}
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing debt
    const existingDebts = await db
      .select()
      .from(h_driver_debt_tracking)
      .where(eq(h_driver_debt_tracking.id, debtId))
      .limit(1);

    if (existingDebts.length === 0) {
      return {
        success: false,
        error: {
          code: "DEBT_NOT_FOUND",
          message: "Debt record not found",
        },
      };
    }

    const existingDebt = existingDebts[0];

    if (existingDebt.isResolved) {
      return {
        success: false,
        error: {
          code: "DEBT_ALREADY_RESOLVED",
          message: "Cannot adjust a resolved debt",
        },
      };
    }

    // Validate new amount
    if (input.newAmount <= 0) {
      return {
        success: false,
        error: {
          code: "INVALID_DEBT_AMOUNT",
          message: "New debt amount must be positive",
          field: "newAmount",
        },
      };
    }

    if (!input.adjustmentReason?.trim()) {
      return {
        success: false,
        error: {
          code: "MISSING_ADJUSTMENT_REASON",
          message: "Adjustment reason is required",
          field: "adjustmentReason",
        },
      };
    }

    const oldAmount = Number(existingDebt.amount);
    const adjustmentAmount = input.newAmount - oldAmount;

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Update debt amount
      const [updatedDebt] = await tx
        .update(h_driver_debt_tracking)
        .set({
          amount: input.newAmount,
          notes: input.notes
            ? existingDebt.notes
              ? `${existingDebt.notes}\n\nAdjustment: ${input.adjustmentReason}`
              : `Adjustment: ${input.adjustmentReason}`
            : existingDebt.notes,
        })
        .where(eq(h_driver_debt_tracking.id, debtId))
        .returning();

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingDebt.assignmentId,
        actionType: "ADJUST_DEBT",
        tableName: "h_driver_debt_tracking",
        recordId: debtId,
        oldValues: {
          amount: oldAmount,
        },
        newValues: {
          amount: input.newAmount,
          adjustmentAmount,
          adjustmentReason: input.adjustmentReason,
          notes: input.notes,
        },
        performedBy: adminPartyId,
      });

      return updatedDebt;
    });

    const formattedDebt: DebtRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      sourceType: result.sourceType,
      amount: result.amount.toString(),
      description: result.description,
      sourceRecordId: result.sourceRecordId || undefined,
      sourceWeek: result.sourceWeek?.toISOString() || undefined,
      isResolved: result.isResolved,
      resolvedAt: result.resolvedAt?.toISOString() || undefined,
      resolvedBy: result.resolvedBy || undefined,
      resolutionNotes: result.resolutionNotes || undefined,
      notes: result.notes || undefined,
      createdAt: result.createdAt.toISOString(),
      createdBy: result.createdBy,
    };

    return {
      success: true,
      data: formattedDebt,
    };
  } catch (error) {
    console.error("❌ [adjustDebtAmount] Error:", error);
    return {
      success: false,
      error: {
        code: "DEBT_ADJUSTMENT_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to adjust debt amount",
      },
    };
  }
}

/**
 * Forgive a debt (mark as resolved without payment)
 */
export async function forgiveDebt(
  debtId: number,
  forgivenessReason: string,
  notes?: string
): Promise<{ success: boolean; data?: DebtRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing debt
    const existingDebts = await db
      .select()
      .from(h_driver_debt_tracking)
      .where(eq(h_driver_debt_tracking.id, debtId))
      .limit(1);

    if (existingDebts.length === 0) {
      return {
        success: false,
        error: {
          code: "DEBT_NOT_FOUND",
          message: "Debt record not found",
        },
      };
    }

    const existingDebt = existingDebts[0];

    if (existingDebt.isResolved) {
      return {
        success: false,
        error: {
          code: "DEBT_ALREADY_RESOLVED",
          message: "Debt is already resolved",
        },
      };
    }

    if (!forgivenessReason?.trim()) {
      return {
        success: false,
        error: {
          code: "MISSING_FORGIVENESS_REASON",
          message: "Forgiveness reason is required",
        },
      };
    }

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Mark debt as resolved (forgiven)
      const [forgivenDebt] = await tx
        .update(h_driver_debt_tracking)
        .set({
          isResolved: true,
          resolvedAt: new Date(),
          resolvedBy: adminPartyId,
          resolutionNotes: `Debt forgiven: ${forgivenessReason}`,
          notes: notes
            ? existingDebt.notes
              ? `${existingDebt.notes}\n\nForgiveness Notes: ${notes}`
              : `Forgiveness Notes: ${notes}`
            : existingDebt.notes,
        })
        .where(eq(h_driver_debt_tracking.id, debtId))
        .returning();

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingDebt.assignmentId,
        actionType: "FORGIVE_DEBT",
        tableName: "h_driver_debt_tracking",
        recordId: debtId,
        oldValues: {
          isResolved: false,
          amount: Number(existingDebt.amount),
        },
        newValues: {
          isResolved: true,
          forgivenessReason,
          forgivenAmount: Number(existingDebt.amount),
          notes,
        },
        performedBy: adminPartyId,
      });

      return forgivenDebt;
    });

    const formattedDebt: DebtRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      sourceType: result.sourceType,
      amount: result.amount.toString(),
      description: result.description,
      sourceRecordId: result.sourceRecordId || undefined,
      sourceWeek: result.sourceWeek?.toISOString() || undefined,
      isResolved: result.isResolved,
      resolvedAt: result.resolvedAt?.toISOString() || undefined,
      resolvedBy: result.resolvedBy || undefined,
      resolutionNotes: result.resolutionNotes || undefined,
      notes: result.notes || undefined,
      createdAt: result.createdAt.toISOString(),
      createdBy: result.createdBy,
    };

    return {
      success: true,
      data: formattedDebt,
    };
  } catch (error) {
    console.error("❌ [forgiveDebt] Error:", error);
    return {
      success: false,
      error: {
        code: "DEBT_FORGIVENESS_FAILED",
        message:
          error instanceof Error ? error.message : "Failed to forgive debt",
      },
    };
  }
}

/**
 * Restore a resolved debt back to outstanding
 */
export async function restoreDebt(
  debtId: number,
  restorationReason: string,
  notes?: string
): Promise<{ success: boolean; data?: DebtRecord; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get existing debt
    const existingDebts = await db
      .select()
      .from(h_driver_debt_tracking)
      .where(eq(h_driver_debt_tracking.id, debtId))
      .limit(1);

    if (existingDebts.length === 0) {
      return {
        success: false,
        error: {
          code: "DEBT_NOT_FOUND",
          message: "Debt record not found",
        },
      };
    }

    const existingDebt = existingDebts[0];

    if (!existingDebt.isResolved) {
      return {
        success: false,
        error: {
          code: "DEBT_NOT_RESOLVED",
          message: "Cannot restore an unresolved debt",
        },
      };
    }

    if (!restorationReason?.trim()) {
      return {
        success: false,
        error: {
          code: "MISSING_RESTORATION_REASON",
          message: "Restoration reason is required",
        },
      };
    }

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Restore debt to outstanding
      const [restoredDebt] = await tx
        .update(h_driver_debt_tracking)
        .set({
          isResolved: false,
          resolvedAt: null,
          resolvedBy: null,
          resolutionNotes: null,
          notes: notes
            ? existingDebt.notes
              ? `${existingDebt.notes}\n\nRestoration: ${restorationReason}`
              : `Restoration: ${restorationReason}`
            : existingDebt.notes,
        })
        .where(eq(h_driver_debt_tracking.id, debtId))
        .returning();

      // Create audit log entry
      await tx.insert(h_payment_audit_log).values({
        assignmentId: existingDebt.assignmentId,
        actionType: "RESTORE_DEBT",
        tableName: "h_driver_debt_tracking",
        recordId: debtId,
        oldValues: {
          isResolved: true,
          resolvedAt: existingDebt.resolvedAt?.toISOString(),
          resolutionNotes: existingDebt.resolutionNotes,
        },
        newValues: {
          isResolved: false,
          restorationReason,
          notes,
        },
        performedBy: adminPartyId,
      });

      return restoredDebt;
    });

    const formattedDebt: DebtRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      sourceType: result.sourceType,
      amount: result.amount.toString(),
      description: result.description,
      sourceRecordId: result.sourceRecordId || undefined,
      sourceWeek: result.sourceWeek?.toISOString() || undefined,
      isResolved: result.isResolved,
      resolvedAt: result.resolvedAt?.toISOString() || undefined,
      resolvedBy: result.resolvedBy || undefined,
      resolutionNotes: result.resolutionNotes || undefined,
      notes: result.notes || undefined,
      createdAt: result.createdAt.toISOString(),
      createdBy: result.createdBy,
    };

    return {
      success: true,
      data: formattedDebt,
    };
  } catch (error) {
    console.error("❌ [restoreDebt] Error:", error);
    return {
      success: false,
      error: {
        code: "DEBT_RESTORATION_FAILED",
        message:
          error instanceof Error ? error.message : "Failed to restore debt",
      },
    };
  }
}

// =====================================================
// DEBT CONSOLIDATION AND BULK OPERATIONS
// =====================================================

/**
 * Consolidate multiple debts into a single debt record
 */
export async function consolidateDebts(
  assignmentId: number,
  debtIds: number[],
  consolidationNotes: string
): Promise<{
  success: boolean;
  data?: DebtRecord;
  consolidatedCount?: number;
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    if (!Array.isArray(debtIds) || debtIds.length < 2) {
      return {
        success: false,
        error: {
          code: "INSUFFICIENT_DEBTS",
          message: "At least 2 debts are required for consolidation",
        },
      };
    }

    // Get debts to consolidate
    const debtsToConsolidate = await db
      .select()
      .from(h_driver_debt_tracking)
      .where(
        and(
          eq(h_driver_debt_tracking.assignmentId, assignmentId),
          inArray(h_driver_debt_tracking.id, debtIds),
          eq(h_driver_debt_tracking.isResolved, false)
        )
      );

    if (debtsToConsolidate.length !== debtIds.length) {
      return {
        success: false,
        error: {
          code: "INVALID_DEBTS_FOR_CONSOLIDATION",
          message: "Some debts are not found or already resolved",
        },
      };
    }

    const totalAmount = debtsToConsolidate.reduce(
      (sum, debt) => sum + Number(debt.amount),
      0
    );
    const debtDescriptions = debtsToConsolidate
      .map((debt) => debt.description)
      .join("; ");

    // Start transaction
    const result = await db.transaction(async (tx) => {
      // Create consolidated debt
      const [consolidatedDebt] = await tx
        .insert(h_driver_debt_tracking)
        .values({
          assignmentId,
          sourceType: "debt_recovery",
          amount: totalAmount,
          description: `Consolidated debt from ${debtsToConsolidate.length} debts: ${debtDescriptions}`,
          notes: consolidationNotes,
          createdBy: adminPartyId,
        })
        .returning();

      // Mark original debts as resolved (consolidated)
      await tx
        .update(h_driver_debt_tracking)
        .set({
          isResolved: true,
          resolvedAt: new Date(),
          resolvedBy: adminPartyId,
          resolutionNotes: `Consolidated into debt ID ${consolidatedDebt.id}`,
        })
        .where(inArray(h_driver_debt_tracking.id, debtIds));

      // Create audit log entry for consolidation
      await tx.insert(h_payment_audit_log).values({
        assignmentId,
        actionType: "CONSOLIDATE_DEBTS",
        tableName: "h_driver_debt_tracking",
        recordId: consolidatedDebt.id,
        oldValues: {
          originalDebtIds: debtIds,
          originalDebtsCount: debtsToConsolidate.length,
          individualAmounts: debtsToConsolidate.map((debt) =>
            Number(debt.amount)
          ),
        },
        newValues: {
          consolidatedDebtId: consolidatedDebt.id,
          totalAmount,
          consolidationNotes,
        },
        performedBy: adminPartyId,
      });

      return consolidatedDebt;
    });

    const formattedDebt: DebtRecord = {
      id: result.id,
      assignmentId: result.assignmentId,
      sourceType: result.sourceType,
      amount: result.amount.toString(),
      description: result.description,
      sourceRecordId: result.sourceRecordId || undefined,
      sourceWeek: result.sourceWeek?.toISOString() || undefined,
      isResolved: result.isResolved,
      resolvedAt: result.resolvedAt?.toISOString() || undefined,
      resolvedBy: result.resolvedBy || undefined,
      resolutionNotes: result.resolutionNotes || undefined,
      notes: result.notes || undefined,
      createdAt: result.createdAt.toISOString(),
      createdBy: result.createdBy,
    };

    return {
      success: true,
      data: formattedDebt,
      consolidatedCount: debtsToConsolidate.length,
    };
  } catch (error) {
    console.error("❌ [consolidateDebts] Error:", error);
    return {
      success: false,
      error: {
        code: "DEBT_CONSOLIDATION_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to consolidate debts",
      },
    };
  }
}

/**
 * Bulk forgive multiple debts
 */
export async function bulkForgiveDebts(
  debtIds: number[],
  forgivenessReason: string,
  notes?: string
): Promise<{
  success: boolean;
  data?: {
    successful: DebtRecord[];
    failed: Array<{ debtId: number; error: string }>;
    totalForgivenAmount: number;
  };
  error?: PaymentError;
}> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    if (!Array.isArray(debtIds) || debtIds.length === 0) {
      return {
        success: false,
        error: {
          code: "NO_DEBTS_PROVIDED",
          message: "No debt IDs provided for forgiveness",
        },
      };
    }

    // Process each debt forgiveness
    const results = {
      successful: [] as DebtRecord[],
      failed: [] as Array<{ debtId: number; error: string }>,
      totalForgivenAmount: 0,
    };

    for (const debtId of debtIds) {
      try {
        const result = await forgiveDebt(debtId, forgivenessReason, notes);

        if (result.success && result.data) {
          results.successful.push(result.data);
          results.totalForgivenAmount += Number(result.data.amount);
        } else {
          results.failed.push({
            debtId,
            error: result.error?.message || "Unknown error",
          });
        }
      } catch (forgivenessError) {
        results.failed.push({
          debtId,
          error:
            forgivenessError instanceof Error
              ? forgivenessError.message
              : "Processing error",
        });
      }
    }

    return {
      success: true,
      data: results,
    };
  } catch (error) {
    console.error("❌ [bulkForgiveDebts] Error:", error);
    return {
      success: false,
      error: {
        code: "BULK_FORGIVENESS_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to bulk forgive debts",
      },
    };
  }
}

// =====================================================
// DEBT RETRIEVAL AND REPORTING
// =====================================================

/**
 * Get debts for a specific assignment
 */
export async function getAssignmentDebts(
  assignmentId: number,
  includeResolved: boolean = false,
  startDate?: string,
  endDate?: string
): Promise<{ success: boolean; data?: DebtRecord[]; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Build query conditions
    const conditions = [eq(h_driver_debt_tracking.assignmentId, assignmentId)];

    if (!includeResolved) {
      conditions.push(eq(h_driver_debt_tracking.isResolved, false));
    }

    if (startDate) {
      conditions.push(
        gte(h_driver_debt_tracking.createdAt, new Date(startDate))
      );
    }

    if (endDate) {
      conditions.push(lte(h_driver_debt_tracking.createdAt, new Date(endDate)));
    }

    const debts = await db
      .select()
      .from(h_driver_debt_tracking)
      .where(and(...conditions))
      .orderBy(desc(h_driver_debt_tracking.createdAt));

    const formattedDebts: DebtRecord[] = debts.map((debt) => ({
      id: debt.id,
      assignmentId: debt.assignmentId,
      sourceType: debt.sourceType,
      amount: debt.amount.toString(),
      description: debt.description,
      sourceRecordId: debt.sourceRecordId || undefined,
      sourceWeek: debt.sourceWeek?.toISOString() || undefined,
      isResolved: debt.isResolved,
      resolvedAt: debt.resolvedAt?.toISOString() || undefined,
      resolvedBy: debt.resolvedBy || undefined,
      resolutionNotes: debt.resolutionNotes || undefined,
      notes: debt.notes || undefined,
      createdAt: debt.createdAt.toISOString(),
      createdBy: debt.createdBy,
    }));

    return {
      success: true,
      data: formattedDebts,
    };
  } catch (error) {
    console.error("❌ [getAssignmentDebts] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_DEBTS_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch assignment debts",
      },
    };
  }
}

/**
 * Get debt summaries for multiple assignments
 */
export async function getDebtsSummary(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{ success: boolean; data?: DebtSummary[]; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    const summaries = await Promise.all(
      assignmentIds.map(async (assignmentId) => {
        // Build query conditions
        const conditions = [
          eq(h_driver_debt_tracking.assignmentId, assignmentId),
        ];

        if (startDate) {
          conditions.push(
            gte(h_driver_debt_tracking.createdAt, new Date(startDate))
          );
        }

        if (endDate) {
          conditions.push(
            lte(h_driver_debt_tracking.createdAt, new Date(endDate))
          );
        }

        // Get debt summary
        const summaryQuery = await db
          .select({
            totalOutstandingDebt: sum(
              sql`CASE WHEN ${h_driver_debt_tracking.isResolved} = false THEN ${h_driver_debt_tracking.amount} ELSE 0 END`
            ),
            totalResolvedDebt: sum(
              sql`CASE WHEN ${h_driver_debt_tracking.isResolved} = true THEN ${h_driver_debt_tracking.amount} ELSE 0 END`
            ),
            totalDebtCount: sql<number>`COUNT(*)`,
            outstandingDebtCount: sql<number>`COUNT(*) FILTER (WHERE ${h_driver_debt_tracking.isResolved} = false)`,
            resolvedDebtCount: sql<number>`COUNT(*) FILTER (WHERE ${h_driver_debt_tracking.isResolved} = true)`,
            earningsShortfallCount: sql<number>`COUNT(*) FILTER (WHERE ${h_driver_debt_tracking.sourceType} = 'earnings_shortfall')`,
            adjustmentCount: sql<number>`COUNT(*) FILTER (WHERE ${h_driver_debt_tracking.sourceType} = 'adjustment')`,
            debtRecoveryCount: sql<number>`COUNT(*) FILTER (WHERE ${h_driver_debt_tracking.sourceType} = 'debt_recovery')`,
          })
          .from(h_driver_debt_tracking)
          .where(and(...conditions));

        const summary = summaryQuery[0];

        // Get latest debt
        const latestDebt = await db
          .select()
          .from(h_driver_debt_tracking)
          .where(eq(h_driver_debt_tracking.assignmentId, assignmentId))
          .orderBy(desc(h_driver_debt_tracking.createdAt))
          .limit(1);

        // Get oldest outstanding debt
        const oldestOutstandingDebt = await db
          .select()
          .from(h_driver_debt_tracking)
          .where(
            and(
              eq(h_driver_debt_tracking.assignmentId, assignmentId),
              sql`${h_driver_debt_tracking.runningBalance} > 0`
            )
          )
          .orderBy(h_driver_debt_tracking.createdAt)
          .limit(1);

        // Calculate resolution rate
        const totalDebtCount = Number(summary.totalDebtCount) || 0;
        const resolvedDebtCount = Number(summary.resolvedDebtCount) || 0;
        const resolutionRate = totalDebtCount > 0 
          ? ((resolvedDebtCount / totalDebtCount) * 100).toFixed(2)
          : "0";

        return {
          assignmentId,
          totalOutstandingDebt: summary.totalOutstandingDebt?.toString() || "0",
          totalResolvedDebt: summary.totalResolvedDebt?.toString() || "0",
          totalDebtCount,
          outstandingDebtCount: Number(summary.outstandingDebtCount) || 0,
          resolvedDebtCount,
          averageDebtAmount:
            totalDebtCount > 0
              ? (
                  (Number(summary.totalOutstandingDebt) +
                    Number(summary.totalResolvedDebt)) /
                  totalDebtCount
                ).toString()
              : "0",
          resolutionRate,
          earningsShortfallCount: Number(summary.earningsShortfallCount) || 0,
          adjustmentCount: Number(summary.adjustmentCount) || 0,
          debtRecoveryCount: Number(summary.debtRecoveryCount) || 0,
          latestDebtDate: latestDebt[0]?.createdAt.toISOString() || undefined,
          oldestOutstandingDate: oldestOutstandingDebt[0]?.createdAt.toISOString() || undefined,
        };
      })
    );

    return {
      success: true,
      data: summaries,
    };
  } catch (error) {
    console.error("❌ [getDebtsSummary] Error:", error);
    return {
      success: false,
      error: {
        code: "FETCH_DEBTS_SUMMARY_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to fetch debts summary",
      },
    };
  }
}

/**
 * Generate comprehensive debt report
 */
export async function generateDebtReport(
  assignmentIds: number[],
  startDate?: string,
  endDate?: string
): Promise<{ success: boolean; data?: DebtReportData; error?: PaymentError }> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const adminPartyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!adminPartyId) {
      return {
        success: false,
        error: {
          code: "UNAUTHORIZED",
          message: "Admin authentication required",
        },
      };
    }

    // Get debt summaries
    const summariesResult = await getDebtsSummary(
      assignmentIds,
      startDate,
      endDate
    );

    if (!summariesResult.success || !summariesResult.data) {
      return {
        success: false,
        error: summariesResult.error || {
          code: "REPORT_GENERATION_FAILED",
          message: "Failed to generate debt report",
        },
      };
    }

    const summaries = summariesResult.data;

    // Calculate overall statistics
    const overallStats = summaries.reduce(
      (acc, summary) => ({
        totalOutstandingDebt:
          acc.totalOutstandingDebt + Number(summary.totalOutstandingDebt),
        totalResolvedDebt:
          acc.totalResolvedDebt + Number(summary.totalResolvedDebt),
        totalDebtCount: acc.totalDebtCount + summary.totalDebtCount,
        outstandingDebtCount:
          acc.outstandingDebtCount + summary.outstandingDebtCount,
        resolvedDebtCount: acc.resolvedDebtCount + summary.resolvedDebtCount,
        earningsShortfallCount:
          acc.earningsShortfallCount + summary.earningsShortfallCount,
        adjustmentCount: acc.adjustmentCount + summary.adjustmentCount,
        debtRecoveryCount: acc.debtRecoveryCount + summary.debtRecoveryCount,
      }),
      {
        totalOutstandingDebt: 0,
        totalResolvedDebt: 0,
        totalDebtCount: 0,
        outstandingDebtCount: 0,
        resolvedDebtCount: 0,
        earningsShortfallCount: 0,
        adjustmentCount: 0,
        debtRecoveryCount: 0,
      }
    );

    const reportData: DebtReportData = {
      reportPeriod: {
        startDate: startDate || "All time",
        endDate: endDate || new Date().toISOString().split("T")[0],
        assignmentCount: assignmentIds.length,
      },
      overallStatistics: {
        totalOutstandingDebt: overallStats.totalOutstandingDebt.toString(),
        totalResolvedDebt: overallStats.totalResolvedDebt.toString(),
        totalDebtCount: overallStats.totalDebtCount,
        outstandingDebtCount: overallStats.outstandingDebtCount,
        resolvedDebtCount: overallStats.resolvedDebtCount,
        averageDebtAmount:
          overallStats.totalDebtCount > 0
            ? (
                (overallStats.totalOutstandingDebt +
                  overallStats.totalResolvedDebt) /
                overallStats.totalDebtCount
              ).toString()
            : "0",
        resolutionRate:
          overallStats.totalDebtCount > 0
            ? (
                (overallStats.resolvedDebtCount / overallStats.totalDebtCount) *
                100
              ).toFixed(2)
            : "0",
      },
      debtBreakdown: {
        earningsShortfall: {
          count: overallStats.earningsShortfallCount,
          percentage:
            overallStats.totalDebtCount > 0
              ? (
                  (overallStats.earningsShortfallCount /
                    overallStats.totalDebtCount) *
                  100
                ).toFixed(2)
              : "0",
        },
        adjustment: {
          count: overallStats.adjustmentCount,
          percentage:
            overallStats.totalDebtCount > 0
              ? (
                  (overallStats.adjustmentCount / overallStats.totalDebtCount) *
                  100
                ).toFixed(2)
              : "0",
        },
        debtRecovery: {
          count: overallStats.debtRecoveryCount,
          percentage:
            overallStats.totalDebtCount > 0
              ? (
                  (overallStats.debtRecoveryCount /
                    overallStats.totalDebtCount) *
                  100
                ).toFixed(2)
              : "0",
        },
      },
      assignmentSummaries: summaries,
      generatedAt: new Date().toISOString(),
      generatedBy: adminPartyId,
    };

    return {
      success: true,
      data: reportData,
    };
  } catch (error) {
    console.error("❌ [generateDebtReport] Error:", error);
    return {
      success: false,
      error: {
        code: "DEBT_REPORT_GENERATION_FAILED",
        message:
          error instanceof Error
            ? error.message
            : "Failed to generate debt report",
      },
    };
  }
}

// =====================================================
// VALIDATION HELPERS
// =====================================================

/**
 * Validate debt creation input
 */
export function validateDebtCreation(input: DebtCreationInput): {
  isValid: boolean;
  errors: PaymentError[];
} {
  const errors: PaymentError[] = [];

  // Validate assignment ID
  if (!input.assignmentId || input.assignmentId <= 0) {
    errors.push({
      code: "INVALID_ASSIGNMENT_ID",
      message: "Assignment ID is required and must be positive",
      field: "assignmentId",
    });
  }

  // Validate amount
  if (!input.amount || input.amount <= 0) {
    errors.push({
      code: "INVALID_DEBT_AMOUNT",
      message: "Debt amount must be positive",
      field: "amount",
    });
  }

  // Validate description
  if (!input.description?.trim()) {
    errors.push({
      code: "MISSING_DESCRIPTION",
      message: "Debt description is required",
      field: "description",
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
