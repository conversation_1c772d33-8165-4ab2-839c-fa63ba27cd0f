"use server";

import { db } from "../db";
import { eq, inArray, and } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  companyOwnership,
  company,
  individual,
  vehicleMedia,
  listings,
} from "../drizzle/schema";
import type { VehicleReadWithModelAndParty, VehicleReadWithListings } from "@/types/vehicles";
import type { CompanyOwnershipReadWithRelations, CompanyMembershipRead } from "@/types/company-ownerships";

// Get single vehicle by ID
export async function getVehicleByIdDrizzle(
  vehicleId: number
): Promise<VehicleReadWithModelAndParty | null> {
  try {
    const vehicleResult = await db
      .select({
        // Vehicle fields
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryId,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        
        // Model fields
        modelName: vehicleModel.model,
        modelFirstYear: vehicleModel.firstYear,
        modelLastYear: vehicleModel.lastYear,
        modelBodyType: vehicleModel.bodyType,
        modelDescription: vehicleModel.description,
        
        // Make fields
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        makeDescription: vehicleMake.description,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicleResult.length === 0) {
      return null;
    }

    const vehicle = vehicleResult[0];

    // Get vehicle media
    const mediaResults = await db
      .select({
        id: vehicleMedia.id,
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .where(eq(vehicleMedia.vehicleId, vehicleId))
      .orderBy(vehicleMedia.createdAt);

    return {
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_id: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      purchase_date: vehicle.purchaseDate,
      color: vehicle.color,
      is_active: vehicle.isActive,
      created_at: vehicle.createdAt?.toString(),
      updated_at: vehicle.updatedAt?.toString(),
      
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: vehicle.modelFirstYear,
        description: vehicle.modelDescription || undefined,
        transmission: undefined,
        fuel: undefined,
        is_active: true,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName,
          description: vehicle.makeDescription || undefined,
          logo_url: undefined,
          is_active: true,
        }
      },
      
      // These arrays would need additional queries in a real implementation
      bookings: [],
      maintenance_items: [],
      inspections: [],
      media: mediaResults.map(media => ({
        id: media.id,
        vehicle_id: media.vehicleId,
        media_path: media.mediaPath,
        created_at: media.createdAt?.toString() || "",
      })),
      vehicle_documents: [],
    } as VehicleReadWithModelAndParty;
    
  } catch (error) {
    console.error("Error fetching vehicle by ID:", error);
    return null;
  }
}

// Get single vehicle by ID with listings data
export async function getVehicleByIdWithListings(
  vehicleId: number
): Promise<VehicleReadWithListings | null> {
  try {
    const vehicleResult = await db
      .select({
        // Vehicle fields
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryId,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        
        // Model fields
        modelName: vehicleModel.model,
        modelFirstYear: vehicleModel.firstYear,
        modelLastYear: vehicleModel.lastYear,
        modelBodyType: vehicleModel.bodyType,
        modelDescription: vehicleModel.description,
        
        // Make fields
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        makeDescription: vehicleMake.description,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicleResult.length === 0) {
      return null;
    }

    const vehicle = vehicleResult[0];

    // Get vehicle media
    const mediaResults = await db
      .select({
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        id: vehicleMedia.id,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .where(eq(vehicleMedia.vehicleId, vehicleId))
      .orderBy(vehicleMedia.createdAt);

    // Get listings for this vehicle
    const listingsResults = await db
      .select({
        id: listings.id,
        vehicleId: listings.vehicleId,
        partyId: listings.partyId,
        listingType: listings.listingType,
        askingPrice: listings.askingPrice,
        fraction: listings.fraction,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        condition: listings.condition,
        audience: listings.audience,
        createdAt: listings.createdAt,
      })
      .from(listings)
      .where(eq(listings.vehicleId, vehicleId))
      .orderBy(listings.createdAt);

    // Format media
    const formattedMedia = mediaResults.map(media => ({
      id: media.id,
      vehicle_id: media.vehicleId,
      media_path: media.mediaPath,
      created_at: media.createdAt?.toString() || "",
    }));

    // Format listings
    const formattedListings = listingsResults.map(listing => ({
      id: listing.id,
      party_id: listing.partyId,
      listing_type: listing.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      asking_price: listing.askingPrice,
      fraction: listing.fraction,
      effective_from: listing.effectiveFrom,
      effective_to: listing.effectiveTo,
      condition: listing.condition as "new" | "used",
      audience: listing.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      created_at: listing.createdAt?.toString() || "",
    }));

    return {
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_of_registration: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      purchase_date: vehicle.purchaseDate,
      color: vehicle.color,
      is_active: vehicle.isActive,
      created_at: vehicle.createdAt?.toString(),
      updated_at: vehicle.updatedAt?.toString(),
      
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: vehicle.modelFirstYear,
        description: (vehicle.modelDescription || undefined) as string | undefined,
        transmission: undefined,
        fuel: undefined,
        is_active: true,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName,
          description: (vehicle.makeDescription || undefined) as string | undefined,
          logo_url: undefined,
          is_active: true,
        }
      },
      
      // These would need additional queries for full implementation
      bookings: [],
      maintenance_items: [],
      inspections: [],
      media: formattedMedia,
      vehicle_documents: [],
      
      // Include listings data
      listings: formattedListings,
    } as VehicleReadWithListings;
    
  } catch (error) {
    console.error("Error fetching vehicle by ID with listings:", error);
    return null;
  }
}

export async function getVehiclesByParties(
  partyIds: number[]
): Promise<VehicleReadWithListings[]> {
  if (partyIds.length === 0) {
    return [];
  }

  try {
    const vehicleResults = await db
      .select({
        // Vehicle fields
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryId,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        
        // Model fields
        modelName: vehicleModel.model,
        modelFirstYear: vehicleModel.firstYear,
        modelLastYear: vehicleModel.lastYear,
        modelBodyType: vehicleModel.bodyType,
        modelDescription: vehicleModel.description,
        
        // Make fields
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        makeDescription: vehicleMake.description,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(inArray(vehicles.partyId, partyIds))
      .orderBy(vehicles.id);

    console.log(`Found ${vehicleResults.length} vehicles for party IDs:`, partyIds);

    // Get vehicle media for all vehicles
    const vehicleIds = vehicleResults.map(v => v.id);
    const mediaResults = vehicleIds.length > 0 ? await db
      .select({
        vehicleId: vehicleMedia.vehicleId,
        mediaPath: vehicleMedia.mediaPath,
        id: vehicleMedia.id,
        createdAt: vehicleMedia.createdAt,
      })
      .from(vehicleMedia)
      .where(inArray(vehicleMedia.vehicleId, vehicleIds))
      .orderBy(vehicleMedia.vehicleId, vehicleMedia.createdAt) : [];

    console.log(`Found ${mediaResults.length} media records for vehicles`);

    // Group media by vehicle ID - with proper null checking
    const mediaByVehicle = mediaResults && mediaResults.length > 0 
      ? mediaResults.reduce((acc, media) => {
          // Ensure media and acc are defined before proceeding
          if (!media || !media.vehicleId || !acc) {
            console.warn("Skipping media record due to null/undefined values:", media);
            return acc || {};
          }

          if (!acc[media.vehicleId]) {
            acc[media.vehicleId] = [];
          }
          
          acc[media.vehicleId].push({
            id: media.id,
            vehicle_id: media.vehicleId,
            media_path: media.mediaPath,
            media_type: "image", // Default to image since schema doesn't have media_type
            created_at: media.createdAt?.toString() || "",
            updated_at: media.createdAt?.toString() || "",
          });
          
          return acc;
        }, {} as Record<number, any[]>)
      : {};

    console.log(`Grouped media for ${Object.keys(mediaByVehicle).length} vehicles`);

    // Get listings for all vehicles
    const listingsResults = vehicleIds.length > 0 ? await db
      .select({
        id: listings.id,
        vehicleId: listings.vehicleId,
        partyId: listings.partyId,
        listingType: listings.listingType,
        askingPrice: listings.askingPrice,
        fraction: listings.fraction,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        condition: listings.condition,
        audience: listings.audience,
        createdAt: listings.createdAt,
      })
      .from(listings)
      .where(inArray(listings.vehicleId, vehicleIds))
      .orderBy(listings.vehicleId, listings.createdAt) : [];

    console.log(`Found ${listingsResults.length} listings for vehicles`);

    // Group listings by vehicle ID
    const listingsByVehicle = listingsResults.reduce((acc, listing) => {
      if (!acc[listing.vehicleId]) {
        acc[listing.vehicleId] = [];
      }
      
      acc[listing.vehicleId].push({
        id: listing.id,
        party_id: listing.partyId,
        listing_type: listing.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
        asking_price: listing.askingPrice,
        fraction: listing.fraction,
        effective_from: listing.effectiveFrom,
        effective_to: listing.effectiveTo,
        condition: listing.condition as "new" | "used",
        audience: listing.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
        created_at: listing.createdAt?.toString() || "",
      });
      
      return acc;
    }, {} as Record<number, any[]>);

    console.log(`Grouped listings for ${Object.keys(listingsByVehicle).length} vehicles`);

    return vehicleResults.map(vehicle => ({
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_of_registration: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      purchase_date: vehicle.purchaseDate,
      color: vehicle.color,
      is_active: vehicle.isActive,
      created_at: vehicle.createdAt?.toString(),
      updated_at: vehicle.updatedAt?.toString(),
      
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: vehicle.modelFirstYear,
        description: (vehicle.modelDescription || undefined) as string | undefined,
        transmission: undefined,
        fuel: undefined,
        is_active: true,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName,
          description: (vehicle.makeDescription || undefined) as string | undefined,
          logo_url: undefined,
          is_active: true,
        }
      },
      
      // These arrays would need additional queries in a real implementation
      // For now, we'll return empty arrays to match the expected type
      bookings: [],
      maintenance_items: [],
      inspections: [],
      media: mediaByVehicle[vehicle.id] || [],
      vehicle_documents: [],
      
      // Add listings data
      listings: listingsByVehicle[vehicle.id] || [],
    })) as VehicleReadWithListings[];
    
  } catch (error) {
    console.error("Error fetching vehicles by parties:", error);
    return [];
  }
}

export async function getCompanyOwnershipByParty(
  partyId: number
): Promise<CompanyOwnershipReadWithRelations[]> {
  try {
    const ownershipResults = await db
      .select({
        // CompanyOwnership fields
        id: companyOwnership.id,
        partyId: companyOwnership.partyId,
        companyId: companyOwnership.companyId,
        fraction: companyOwnership.fraction,
        effectiveFrom: companyOwnership.effectiveFrom,
        effectiveTo: companyOwnership.effectiveTo,
        isActive: companyOwnership.isActive,
        createdAt: companyOwnership.createdAt,
        updatedAt: companyOwnership.updatedAt,
        
        // Company fields
        companyname: company.name,
        companyDescription: company.description,
        companyRegistrationNumber: company.registrationNumber,
        companyRegistrationCountry: company.countryId,
        companyRegistrationDate: company.registrationDate,
        companyPurpose: company.purpose,
        companyPartyId: company.partyId,
        companyCreatedAt: company.createdAt,
        companyUpdatedAt: company.updatedAt,
      })
      .from(companyOwnership)
      .leftJoin(company, eq(companyOwnership.companyId, company.id))
      .where(
        and(
          eq(companyOwnership.partyId, partyId),
          eq(companyOwnership.isActive, true)
        )
      )
      .orderBy(companyOwnership.id);

    return ownershipResults.map(ownership => ({
      id: ownership.id,
      party_id: ownership.partyId,
      company_id: ownership.companyId,
      fraction: parseFloat(ownership.fraction?.toString() || "0"),
      effective_from: ownership.effectiveFrom ? new Date(ownership.effectiveFrom) : new Date(),
      effective_to: ownership.effectiveTo ? new Date(ownership.effectiveTo) : null,
      is_active: ownership.isActive,
      created_at: ownership.createdAt ? new Date(ownership.createdAt) : new Date(),
      updated_at: ownership.updatedAt ? new Date(ownership.updatedAt) : new Date(),
      
      party: null,
      
      company: ownership.companyId ? {
        id: ownership.companyId,
        name: ownership.companyname,
        description: ownership.companyDescription,
        registration_number: ownership.companyRegistrationNumber,
        registration_country: ownership.companyRegistrationCountry,
        registration_date: ownership.companyRegistrationDate?.toString() || "",
        purpose: ownership.companyPurpose,
        party_id: ownership.companyPartyId || 0, // Use 0 as fallback for required field
        created_at: ownership.companyCreatedAt?.toString() || "",
        updated_at: ownership.companyUpdatedAt?.toString() || "",
      } : null,
    })) as CompanyOwnershipReadWithRelations[];
    
  } catch (error) {
    console.error("Error fetching company ownership by party:", error);
    return [];
  }
}

export async function getCompanyOwnershipsByCompanies(
  companyIds: number[]
): Promise<CompanyMembershipRead> {
  if (companyIds.length === 0) {
    return { individuals: [], companies: [] };
  }

  try {
    // Get individuals who are members of these companies
    const individualResults = await db
      .select({
        // Individual fields
        individualId: individual.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        birthDate: individual.birthDate,
        partyId: individual.partyId,
        
        // CompanyOwnership fields
        fraction: companyOwnership.fraction,
        companyId: companyOwnership.companyId,
      })
      .from(companyOwnership)
      .innerJoin(individual, eq(companyOwnership.partyId, individual.partyId))
      .where(
        and(
          inArray(companyOwnership.companyId, companyIds),
          eq(companyOwnership.isActive, true)
        )
      )
      .orderBy(companyOwnership.companyId, companyOwnership.fraction);

    // Get companies
    const companyResults = await db
      .select({
        id: company.id,
        name: company.name,
        description: company.description,
        registrationNumber: company.registrationNumber,
        registrationCountry: company.countryId,
        registrationDate: company.registrationDate,
        purpose: company.purpose,
        partyId: company.partyId,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
      })
      .from(company)
      .where(inArray(company.id, companyIds))
      .orderBy(company.id);

    return {
      individuals: individualResults.map(result => ({
        individual: {
          id: result.individualId,
          first_name: result.firstName,
          last_name: result.lastName,
          date_of_birth: result.birthDate,
          party_id: result.partyId,
          username: "", // Required by type but not in schema
          email: "", // Required by type but not in schema
          birth_date: result.birthDate,
          created_at: "",
          updated_at: "",
        } as any,
        fraction: parseFloat(result.fraction?.toString() || "0"),
        company_id: result.companyId,
      })),
      companies: companyResults.map(comp => ({
        id: comp.id,
        name: comp.name || "",
        description: comp.description,
        registration_number: comp.registrationNumber,
        registration_country: comp.registrationCountry,
        registration_date: comp.registrationDate?.toString() || "",
        purpose: comp.purpose,
        party_id: comp.partyId || 0,
        created_at: comp.createdAt?.toString() || "",
        updated_at: comp.updatedAt?.toString() || "",
      } as any)),
    } as CompanyMembershipRead;
    
  } catch (error) {
    console.error("Error fetching company ownerships by companies:", error);
    return { individuals: [], companies: [] };
  }
} 