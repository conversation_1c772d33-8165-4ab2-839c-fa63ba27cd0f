"use server";

/**
 * DRIZZLE ACTIONS - APPLICATIONS (USER FUNCTIONS ONLY)
 *
 * This file contains direct database operations for USER applications using Drizzle ORM.
 * Admin functions have been moved to drizzle-actions/admin/applications.ts
 *
 * Architecture:
 * - drizzle-actions/applications.ts = User application operations
 * - drizzle-actions/admin/applications.ts = Admin application operations
 * - actions/* = Server actions for form handling that call these functions
 *
 * Schema Tables Used:
 * - h_applications: Main application records
 * - h_applicationDocuments: Document uploads for applications
 * - h_applicationDecisions: Admin decisions on applications (read-only for users)
 * - h_listings: Referenced listings
 * - party: Applicants
 */

import { db } from "../db";
import { eq, desc, and, sql, inArray, ne, isNull, or } from "drizzle-orm";
import {
  h_applications,
  h_applicationDocuments,
  h_applicationDecisions,
  h_applicationDocumentsStatus,
} from "@/drizzle/h_schema/applications";
import {
  h_listings,
  h_listing_publish_status,
} from "@/drizzle/h_schema/listings";
import { h_vehicleCatalog } from "@/drizzle/h_schema/vehicle-catalog";
import {
  party,
  individual,
  contactPoint,
  vehicles,
  vehicleMake,
  vehicleModel,
} from "@/drizzle/schema";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// Types for application data
export interface ApplicationData {
  listingId: number;
  applicationDetails: {
    // E-hailing specific
    hasEhailingExperience?: boolean;
    ehailingCompany?: string;
    ehailingProfileNumber?: string;
    ehailingWorkType?: string;
    drivingExperienceYears?: string;
    arrangementRequested?: boolean;

    // Rental/Fractional specific
    purpose?: string;
    applicantPreferences?: {
      minAge?: number;
      drivingExperienceYears?: string;
      gender?: string;
    };
  };
}

export interface DocumentUpload {
  documentType: string;
  documentUrl: string;
}

export interface ApplicationWithDetails {
  id: number;
  applicantId: number;
  listingId: number;
  applicationDetails: any;
  createdAt: string;

  // Related data
  applicant: {
    id: number;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  listing: {
    id: number;
    listingType: string;
    listingDetails: any;
    sourceType?: string;
    sourceId?: number;
  };

  // Vehicle details (when listing is vehicle-based)
  vehicle?: {
    make: string;
    model: string;
    year: number;
    registration?: string;
  };

  // Catalog details (when listing is catalog-based)
  catalog?: {
    make: string;
    model: string;
    year: number;
    category: string;
    weeklyFeeTarget?: number;
  };

  documents: {
    id: number;
    documentType: string;
    documentUrl: string;
    uploadedAt: string;
    status?: string;
  }[];
  latestDecision?: {
    decision: string;
    reason?: string;
    decisionAt: string;
    reviewerName?: string;
  };
}

/**
 * Create a new application
 */
export async function createApplication(
  applicationData: ApplicationData
): Promise<{ success: boolean; applicationId?: number; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    // Verify the listing exists and is published
    const listing = await db
      .select()
      .from(h_listings)
      .where(eq(h_listings.id, applicationData.listingId))
      .limit(1);

    if (listing.length === 0) {
      return { success: false, error: "Listing not found" };
    }

    // Check if user already has an application for this listing
    const existingApplication = await db
      .select()
      .from(h_applications)
      .where(
        and(
          eq(h_applications.applicantId, parseInt(partyId)),
          eq(h_applications.listingId, applicationData.listingId)
        )
      )
      .limit(1);

    if (existingApplication.length > 0) {
      return {
        success: false,
        error: "You have already applied for this listing",
      };
    }

    // Create the application
    const result = await db
      .insert(h_applications)
      .values({
        applicantId: parseInt(partyId),
        listingId: applicationData.listingId,
        applicationDetails: JSON.stringify(applicationData.applicationDetails),
      })
      .returning({ id: h_applications.id });

    const applicationId = result[0].id;

    // Create initial "pending" decision in the decisions table using the applicant's party ID
    await db.insert(h_applicationDecisions).values({
      applicationId,
      decision: "pending",
      reason: "Application submitted and awaiting review",
      reviewerId: parseInt(partyId), // Use applicant's party ID instead of hardcoded 7
      decisionAt: new Date(),
    });

    return { success: true, applicationId };
  } catch (error) {
    console.error("Error creating application:", error);
    return { success: false, error: "Failed to create application" };
  }
}

/**
 * Upload documents for an application
 */
export async function uploadApplicationDocuments(
  applicationId: number,
  documents: DocumentUpload[]
): Promise<{ success: boolean; error?: string; documentIds?: number[] }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    if (!partyId) {
      return { success: false, error: "User party ID not found" };
    }

    // Verify the application belongs to the user
    const application = await db
      .select()
      .from(h_applications)
      .where(
        and(
          eq(h_applications.id, applicationId),
          eq(h_applications.applicantId, parseInt(partyId))
        )
      )
      .limit(1);

    if (application.length === 0) {
      return {
        success: false,
        error: "Application not found or access denied",
      };
    }

    // Insert documents and get the inserted IDs
    const documentInserts = documents.map((doc) => ({
      applicationId,
      documentType: doc.documentType,
      documentUrl: doc.documentUrl,
    }));

    const insertedDocuments = await db
      .insert(h_applicationDocuments)
      .values(documentInserts)
      .returning({
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
      });

    // Create initial status entries for each document (uploaded status)
    const statusInserts = insertedDocuments.map((doc) => ({
      applicationDocumentId: doc.id,
      status: "uploaded" as const,
      statusAt: new Date(),
      statusBy: parseInt(partyId), // User who uploaded the document
    }));

    await db.insert(h_applicationDocumentsStatus).values(statusInserts);

    return {
      success: true,
      documentIds: insertedDocuments.map((doc) => doc.id),
    };
  } catch (error) {
    console.error("Error uploading application documents:", error);
    return { success: false, error: "Failed to upload documents" };
  }
}

/**
 * Get all application documents for a user (only active/non-superseded)
 */
export async function getUserApplicationDocuments(partyId: string) {
  try {
    const documents = await db
      .select({
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
        documentUrl: h_applicationDocuments.documentUrl,
        uploadedAt: h_applicationDocuments.uploadedAt,
        applicationId: h_applicationDocuments.applicationId,

        // Get the latest status
        status: h_applicationDocumentsStatus.status,
        statusAt: h_applicationDocumentsStatus.statusAt,
        // Get application details
        listingId: h_applications.listingId,
        applicationCreatedAt: h_applications.createdAt,
      })
      .from(h_applicationDocuments)
      .innerJoin(
        h_applications,
        eq(h_applicationDocuments.applicationId, h_applications.id)
      )
      .innerJoin(
        h_applicationDocumentsStatus,
        eq(
          h_applicationDocuments.id,
          h_applicationDocumentsStatus.applicationDocumentId
        )
      )
      .where(
        and(
          eq(h_applications.applicantId, parseInt(partyId)),
          ne(h_applicationDocumentsStatus.status, "superseded")
        )
      )
      .orderBy(
        desc(h_applicationDocuments.uploadedAt),
        desc(h_applicationDocumentsStatus.statusAt)
      );

    // Group by document to get only the latest status for each document
    const documentsMap = new Map();
    for (const doc of documents) {
      const key = `${doc.applicationId}-${doc.documentType}`;
      if (
        !documentsMap.has(key) ||
        new Date(doc.statusAt) > new Date(documentsMap.get(key).statusAt)
      ) {
        documentsMap.set(key, doc);
      }
    }
    const latestDocuments = Array.from(documentsMap.values());

    return { success: true, documents: latestDocuments };
  } catch (error) {
    console.error("Error fetching user application documents:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application documents",
      documents: [],
    };
  }
}

/**
 * Document requirements by application type
 */
const DOCUMENT_REQUIREMENTS = {
  ehailing: [
    {
      type: "drivers_license",
      name: "Driver's License",
      description: "Valid driver's license",
      required: true,
    },
    {
      type: "id_document",
      name: "ID Document",
      description: "National ID or passport",
      required: true,
    },
    {
      type: "proof_of_residence",
      name: "Proof of Residence",
      description: "Utility bill or bank statement",
      required: true,
    },
    {
      type: "selfie",
      name: "Selfie",
      description: "Clear photo of yourself",
      required: true,
    },
    {
      type: "bank_statement",
      name: "Bank Statement",
      description: "Recent bank statement",
      required: false,
    },
  ],
  rental: [
    {
      type: "drivers_license",
      name: "Driver's License",
      description: "Valid driver's license",
      required: true,
    },
    {
      type: "bank_statement",
      name: "Bank Statement",
      description: "Recent bank statement",
      required: true,
    },
    {
      type: "proof_of_residence",
      name: "Proof of Residence",
      description: "Utility bill or lease agreement",
      required: true,
    },
    {
      type: "proof_of_income",
      name: "Proof of Income",
      description: "Salary slip or employment letter",
      required: false,
    },
  ],
  fractional: [
    {
      type: "drivers_license",
      name: "Driver's License",
      description: "Valid driver's license",
      required: true,
    },
    {
      type: "bank_statement",
      name: "Bank Statement",
      description: "Recent bank statement",
      required: true,
    },
    {
      type: "proof_of_income",
      name: "Proof of Income",
      description: "Salary slip or employment letter",
      required: true,
    },
    {
      type: "proof_of_residence",
      name: "Proof of Residence",
      description: "Utility bill or lease agreement",
      required: false,
    },
  ],
} as const;

/**
 * Get document requirements for an application type (internal utility)
 */
function getDocumentRequirements(applicationType: string) {
  return (
    DOCUMENT_REQUIREMENTS[
      applicationType as keyof typeof DOCUMENT_REQUIREMENTS
    ] || []
  );
}

/**
 * Determine application type from listing or application details
 */
function determineApplicationType(
  listingId: number,
  applicationDetails: any
): string {
  // For now, default to ehailing - in the future, this could query the listing type
  // or parse the applicationDetails JSON to determine the type
  return "ehailing";
}

/**
 * Get user's application documents with missing document detection
 */
export async function getUserApplicationDocumentsWithRequirements(
  partyId: string
) {
  try {
    // Get all user applications
    const applications = await db
      .select({
        id: h_applications.id,
        listingId: h_applications.listingId,
        createdAt: h_applications.createdAt,
        applicationDetails: h_applications.applicationDetails,
      })
      .from(h_applications)
      .where(eq(h_applications.applicantId, parseInt(partyId)))
      .orderBy(desc(h_applications.createdAt));

    // Get all uploaded documents with their latest status (non-superseded)
    const uploadedDocuments = await db
      .select({
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
        documentUrl: h_applicationDocuments.documentUrl,
        uploadedAt: h_applicationDocuments.uploadedAt,
        applicationId: h_applicationDocuments.applicationId,
        status: h_applicationDocumentsStatus.status,
        statusAt: h_applicationDocumentsStatus.statusAt,
      })
      .from(h_applicationDocuments)
      .innerJoin(
        h_applications,
        eq(h_applicationDocuments.applicationId, h_applications.id)
      )
      .innerJoin(
        h_applicationDocumentsStatus,
        eq(
          h_applicationDocuments.id,
          h_applicationDocumentsStatus.applicationDocumentId
        )
      )
      .where(
        and(
          eq(h_applications.applicantId, parseInt(partyId)),
          ne(h_applicationDocumentsStatus.status, "superseded")
        )
      )
      .orderBy(
        desc(h_applicationDocuments.uploadedAt),
        desc(h_applicationDocumentsStatus.statusAt)
      );

    // Group by document to get only the latest status for each document
    const documentsMap = new Map();
    for (const doc of uploadedDocuments) {
      const key = `${doc.applicationId}-${doc.documentType}`;
      if (
        !documentsMap.has(key) ||
        new Date(doc.statusAt) > new Date(documentsMap.get(key).statusAt)
      ) {
        documentsMap.set(key, doc);
      }
    }
    const latestDocuments = Array.from(documentsMap.values());

    // Build comprehensive document list with missing documents
    const allDocuments = [];

    for (const application of applications) {
      // Determine application type
      const applicationType = determineApplicationType(
        application.listingId,
        application.applicationDetails
      );
      const requiredDocs = getDocumentRequirements(applicationType);

      // Get uploaded docs for this application (using latest status only)
      const appUploadedDocs = latestDocuments.filter(
        (doc) => doc.applicationId === application.id
      );

      // Add uploaded documents
      for (const doc of appUploadedDocs) {
        // Find the requirement info for this document type
        const requirementInfo = requiredDocs.find(
          (req) => req.type === doc.documentType
        );

        allDocuments.push({
          ...doc,
          listingId: application.listingId,
          applicationCreatedAt: application.createdAt,
          documentState:
            doc.status === "rejected"
              ? "rejected"
              : doc.status === "verified"
                ? "approved"
                : "uploaded",
          isMissing: false,
          // Add requirement info
          documentName: requirementInfo?.name || doc.documentType,
          documentDescription: requirementInfo?.description || "",
          isRequired: requirementInfo?.required || false,
        });
      }

      // Add missing documents (only required ones for now)
      const uploadedTypes = appUploadedDocs.map((doc) => doc.documentType);
      const missingRequirements = requiredDocs.filter(
        (req) => req.required && !uploadedTypes.includes(req.type)
      );

      for (const missingReq of missingRequirements) {
        allDocuments.push({
          id: null,
          documentType: missingReq.type,
          documentUrl: null,
          uploadedAt: null,
          applicationId: application.id,
          status: null,
          statusAt: null,
          listingId: application.listingId,
          applicationCreatedAt: application.createdAt,
          documentState: "missing",
          isMissing: true,
          // Add requirement info
          documentName: missingReq.name,
          documentDescription: missingReq.description,
          isRequired: missingReq.required,
        });
      }
    }

    return { success: true, documents: allDocuments };
  } catch (error) {
    console.error(
      "Error fetching user application documents with requirements:",
      error
    );
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch application documents",
      documents: [],
    };
  }
}

/**
 * Upload a single document for an application (marks old documents as superseded)
 */
export async function uploadSingleApplicationDocument(
  applicationId: number,
  documentType: string,
  documentUrl: string,
  partyId: string
) {
  try {
    // Start a transaction to ensure consistency
    await db.transaction(async (tx) => {
      // Find existing documents of this type and mark them as superseded
      const existingDocuments = await tx
        .select({ id: h_applicationDocuments.id })
        .from(h_applicationDocuments)
        .innerJoin(
          h_applicationDocumentsStatus,
          eq(
            h_applicationDocuments.id,
            h_applicationDocumentsStatus.applicationDocumentId
          )
        )
        .where(
          and(
            eq(h_applicationDocuments.applicationId, applicationId),
            eq(h_applicationDocuments.documentType, documentType),
            ne(h_applicationDocumentsStatus.status, "superseded")
          )
        );

      // Mark existing documents as superseded
      for (const doc of existingDocuments) {
        await tx.insert(h_applicationDocumentsStatus).values({
          applicationDocumentId: doc.id,
          status: "superseded",
          statusAt: new Date(),
          statusBy: parseInt(partyId),
        });
      }

      // Insert new document
      const [newDocument] = await tx
        .insert(h_applicationDocuments)
        .values({
          applicationId,
          documentType,
          documentUrl,
        })
        .returning({ id: h_applicationDocuments.id });

      // Create initial status entry
      await tx.insert(h_applicationDocumentsStatus).values({
        applicationDocumentId: newDocument.id,
        status: "uploaded",
        statusAt: new Date(),
        statusBy: parseInt(partyId),
      });
    });

    return { success: true };
  } catch (error) {
    console.error("Error uploading single application document:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to upload document",
    };
  }
}

/**
 * Get user's applications
 */
export async function getUserApplications(): Promise<{
  success: boolean;
  applications?: ApplicationWithDetails[];
  error?: string;
}> {
  console.log("🚀 [getUserApplications] Function called");

  try {
    const userAttributes = await getUserAttributes();
    console.log("🚀 [getUserApplications] User attributes:", userAttributes);

    if (!userAttributes) {
      console.log("❌ [getUserApplications] User not authenticated");
      return { success: false, error: "User not authenticated" };
    }

    const partyId = userAttributes["custom:db_id"];
    console.log("🚀 [getUserApplications] Party ID:", partyId);

    if (!partyId) {
      console.log("❌ [getUserApplications] Party ID not found");
      return { success: false, error: "User party ID not found" };
    }

    console.log(
      "🚀 [getUserApplications] Querying applications for party ID:",
      parseInt(partyId)
    );

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,
        createdAt: h_applications.createdAt,

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id = 1 
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("email"),
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applications.applicantId, parseInt(partyId)))
      .orderBy(desc(h_applications.id));

    console.log(
      "🚀 [getUserApplications] Found applications:",
      applications.length
    );
    console.log(
      "🚀 [getUserApplications] Applications data:",
      JSON.stringify(applications, null, 2)
    );

    // Get documents for each application
    const applicationIds = applications.map((app) => app.id);
    console.log(
      "🚀 [getUserApplications] Application IDs for document lookup:",
      applicationIds
    );

    const documents =
      applicationIds.length > 0
        ? await db
            .select()
            .from(h_applicationDocuments)
            .where(
              inArray(h_applicationDocuments.applicationId, applicationIds)
            )
        : [];

    console.log("🚀 [getUserApplications] Found documents:", documents.length);
    console.log(
      "🚀 [getUserApplications] Documents data:",
      JSON.stringify(documents, null, 2)
    );

    // Get latest decisions for each application
    const decisions =
      applicationIds.length > 0
        ? await db
            .select({
              applicationId: h_applicationDecisions.applicationId,
              decision: h_applicationDecisions.decision,
              reason: h_applicationDecisions.reason,
              decisionAt: h_applicationDecisions.decisionAt,
            })
            .from(h_applicationDecisions)
            .where(
              inArray(h_applicationDecisions.applicationId, applicationIds)
            )
            .orderBy(desc(h_applicationDecisions.decisionAt))
        : [];

    console.log("🚀 [getUserApplications] Found decisions:", decisions.length);
    console.log(
      "🚀 [getUserApplications] Decisions data:",
      JSON.stringify(decisions, null, 2)
    );

    // Group decisions by application ID to see if we have multiple decisions
    const decisionsByAppId = decisions.reduce(
      (acc, decision) => {
        const appId = decision.applicationId;
        if (!acc[appId]) {
          acc[appId] = [];
        }
        acc[appId].push(decision);
        return acc;
      },
      {} as Record<number, typeof decisions>
    );

    console.log(
      "🚀 [getUserApplications] Decisions grouped by application ID:",
      JSON.stringify(decisionsByAppId, null, 2)
    );

    // Combine data
    console.log("🚀 [getUserApplications] Starting data combination...");
    const result: ApplicationWithDetails[] = applications.map((app) => {
      console.log(
        `🚀 [getUserApplications] Processing application ID: ${app.id}`
      );

      const appDocuments = documents.filter(
        (doc) => doc.applicationId === app.id
      );
      console.log(
        `🚀 [getUserApplications] App ${app.id} has ${appDocuments.length} documents`
      );

      const appDecision = decisions.find((dec) => dec.applicationId === app.id);
      console.log(
        `🚀 [getUserApplications] App ${app.id} latest decision:`,
        appDecision
      );

      const appDecisions = decisionsByAppId[app.id] || [];
      console.log(
        `🚀 [getUserApplications] App ${app.id} has ${appDecisions.length} total decisions`
      );

      const applicationResult = {
        id: app.id,
        applicantId: app.applicantId,
        listingId: app.listingId,
        applicationDetails: app.applicationDetails
          ? JSON.parse(app.applicationDetails)
          : {},
        createdAt: app.createdAt.toISOString(),

        applicant: {
          id: app.applicantId,
          firstName: app.firstName || "",
          lastName: app.lastName || "",
          email: app.email || "",
        },
        listing: {
          id: app.listingId,
          listingType: app.listingType,
          listingDetails: app.listingDetails
            ? JSON.parse(app.listingDetails)
            : {},
        },
        documents: appDocuments.map((doc) => {
          const docWithStatus = doc as any; // Type assertion for status fields
          return {
            id: doc.id,
            documentType: doc.documentType,
            documentUrl: doc.documentUrl,
            uploadedAt: doc.uploadedAt.toISOString(),
            status: docWithStatus.status || undefined, // Include status with fallback
            statusAt: docWithStatus.statusAt
              ? docWithStatus.statusAt.toISOString()
              : undefined, // Include statusAt
          };
        }),
        latestDecision: appDecision
          ? {
              decision: appDecision.decision,
              reason: appDecision.reason || undefined,
              decisionAt: appDecision.decisionAt.toISOString(),
            }
          : undefined,
      };

      console.log(
        `🚀 [getUserApplications] App ${app.id} final result:`,
        JSON.stringify(applicationResult, null, 2)
      );
      return applicationResult;
    });

    console.log("🚀 [getUserApplications] Final result count:", result.length);
    console.log(
      "🚀 [getUserApplications] Final result:",
      JSON.stringify(result, null, 2)
    );

    return { success: true, applications: result };
  } catch (error) {
    console.error(
      "❌ [getUserApplications] Error fetching user applications:",
      error
    );
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Get a specific application with full details (including vehicle/catalog data)
 */
export async function getApplicationById(applicationId: number): Promise<{
  success: boolean;
  application?: ApplicationWithDetails;
  error?: string;
}> {
  try {
    // For admin access, we'll allow getting any application by ID
    // TODO: Add proper admin authorization check

    const application = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,
        createdAt: h_applications.createdAt,

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        listingSourceType: h_listings.sourceType,
        listingSourceId: h_listings.sourceId,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${h_applications.applicantId} 
          AND cp.contact_point_type_id IN (1, 4)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("email"),
        phone: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${h_applications.applicantId} 
          AND cp.contact_point_type_id IN (2, 3)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("phone"),
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    if (application.length === 0) {
      return {
        success: false,
        error: "Application not found",
      };
    }

    const app = application[0];

    // Get documents with their latest status
    const documentsWithStatus = await db
      .select({
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
        documentUrl: h_applicationDocuments.documentUrl,
        uploadedAt: h_applicationDocuments.uploadedAt,
        status: h_applicationDocumentsStatus.status,
        statusAt: h_applicationDocumentsStatus.statusAt,
      })
      .from(h_applicationDocuments)
      .leftJoin(
        h_applicationDocumentsStatus,
        eq(
          h_applicationDocuments.id,
          h_applicationDocumentsStatus.applicationDocumentId
        )
      )
      .where(
        and(
          eq(h_applicationDocuments.applicationId, applicationId),
          // Only exclude superseded documents if they have a status
          or(
            isNull(h_applicationDocumentsStatus.status),
            ne(h_applicationDocumentsStatus.status, "superseded")
          )
        )
      )
      .orderBy(
        desc(h_applicationDocuments.uploadedAt),
        desc(h_applicationDocumentsStatus.statusAt)
      );

    // Group by document ID to get only the latest status for each document
    const documentsByDocId = new Map();
    for (const doc of documentsWithStatus) {
      const docWithStatus = doc as any; // Type assertion for debugging
      const currentDoc = documentsByDocId.get(doc.id);
      if (
        !currentDoc ||
        (docWithStatus.statusAt &&
          currentDoc.statusAt &&
          new Date(docWithStatus.statusAt) > new Date(currentDoc.statusAt)) ||
        (docWithStatus.statusAt && !currentDoc.statusAt)
      ) {
        documentsByDocId.set(doc.id, docWithStatus);
      }
    }

    const documents = Array.from(documentsByDocId.values());

    // Get latest decision
    const decisions = await db
      .select({
        decision: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        decisionAt: h_applicationDecisions.decisionAt,
      })
      .from(h_applicationDecisions)
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt))
      .limit(1);

    // Fetch vehicle data if listing is vehicle-based
    let vehicleData = null;
    if (app.listingSourceType === "vehicle" && app.listingSourceId) {
      const vehicleResult = await db
        .select({
          make: vehicleMake.name,
          model: vehicleModel.model,
          year: vehicles.manufacturingYear,
          registration: vehicles.vehicleRegistration,
        })
        .from(vehicles)
        .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(eq(vehicles.id, app.listingSourceId))
        .limit(1);

      if (vehicleResult.length > 0) {
        vehicleData = vehicleResult[0];
      }
    }

    // Fetch catalog data if listing is catalog-based
    let catalogData = null;
    if (app.listingSourceType === "catalog" && app.listingSourceId) {
      const catalogResult = await db
        .select({
          make: h_vehicleCatalog.make,
          model: h_vehicleCatalog.model,
          year: h_vehicleCatalog.year,
          category: h_vehicleCatalog.category,
          weeklyFeeTarget: h_vehicleCatalog.weeklyFeeTarget,
        })
        .from(h_vehicleCatalog)
        .where(eq(h_vehicleCatalog.id, app.listingSourceId))
        .limit(1);

      if (catalogResult.length > 0) {
        catalogData = catalogResult[0];
      }
    }

    const result: ApplicationWithDetails = {
      id: app.id,
      applicantId: app.applicantId,
      listingId: app.listingId,
      applicationDetails: app.applicationDetails
        ? JSON.parse(app.applicationDetails)
        : {},
      createdAt: app.createdAt.toISOString(),

      applicant: {
        id: app.applicantId,
        firstName: app.firstName || "",
        lastName: app.lastName || "",
        email: app.email || "",
        phone: app.phone || undefined,
      },
      listing: {
        id: app.listingId,
        listingType: app.listingType,
        listingDetails: app.listingDetails
          ? JSON.parse(app.listingDetails)
          : {},
        sourceType: app.listingSourceType || undefined,
        sourceId: app.listingSourceId || undefined,
      },

      // Add vehicle data if available
      ...(vehicleData && {
        vehicle: {
          make: vehicleData.make || "",
          model: vehicleData.model || "",
          year: vehicleData.year || 0,
          registration: vehicleData.registration || undefined,
        },
      }),

      // Add catalog data if available
      ...(catalogData && {
        catalog: {
          make: catalogData.make || "",
          model: catalogData.model || "",
          year: catalogData.year || 0,
          category: catalogData.category || "",
          weeklyFeeTarget: catalogData.weeklyFeeTarget || undefined,
        },
      }),

      documents: documents.map((doc) => {
        const docWithStatus = doc as any; // Type assertion for status fields
        return {
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt.toISOString(),
          status: docWithStatus.status || undefined, // Include status with fallback
          statusAt: docWithStatus.statusAt
            ? docWithStatus.statusAt.toISOString()
            : undefined, // Include statusAt
        };
      }),
      latestDecision:
        decisions.length > 0
          ? {
              decision: decisions[0].decision,
              reason: decisions[0].reason || undefined,
              decisionAt: decisions[0].decisionAt.toISOString(),
            }
          : undefined,
    };

    return { success: true, application: result };
  } catch (error) {
    console.error("Error fetching application:", error);
    return { success: false, error: "Failed to fetch application" };
  }
}

/**
 * Admin function: Get all applications for a listing
 */
export async function getApplicationsForListing(listingId: number): Promise<{
  success: boolean;
  applications?: ApplicationWithDetails[];
  error?: string;
}> {
  try {
    // TODO: Add admin authorization check

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,
        createdAt: h_applications.createdAt,

        // Listing details
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,

        // Applicant details
        firstName: individual.firstName,
        lastName: individual.lastName,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applications.listingId, listingId))
      .orderBy(desc(h_applications.id));

    // Get documents for each application
    const applicationIds = applications.map((app) => app.id);
    const documents =
      applicationIds.length > 0
        ? await db
            .select()
            .from(h_applicationDocuments)
            .where(
              inArray(h_applicationDocuments.applicationId, applicationIds)
            )
        : [];

    // Get latest decisions for each application
    const decisions =
      applicationIds.length > 0
        ? await db
            .select({
              applicationId: h_applicationDecisions.applicationId,
              decision: h_applicationDecisions.decision,
              reason: h_applicationDecisions.reason,
              decisionAt: h_applicationDecisions.decisionAt,
            })
            .from(h_applicationDecisions)
            .where(
              inArray(h_applicationDecisions.applicationId, applicationIds)
            )
            .orderBy(desc(h_applicationDecisions.decisionAt))
        : [];

    // Combine data
    const result: ApplicationWithDetails[] = applications.map((app) => ({
      id: app.id,
      applicantId: app.applicantId,
      listingId: app.listingId,
      applicationDetails: app.applicationDetails
        ? JSON.parse(app.applicationDetails)
        : {},
      createdAt: app.createdAt.toISOString(),

      applicant: {
        id: app.applicantId,
        firstName: app.firstName || "",
        lastName: app.lastName || "",
        email: "", // TODO: Get from contact points
      },
      listing: {
        id: app.listingId,
        listingType: app.listingType,
        listingDetails: app.listingDetails
          ? JSON.parse(app.listingDetails)
          : {},
      },
      documents: documents
        .filter((doc) => doc.applicationId === app.id)
        .map((doc) => ({
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt.toISOString(),
        })),
      latestDecision: decisions.find((dec) => dec.applicationId === app.id)
        ? {
            decision: decisions.find((dec) => dec.applicationId === app.id)!
              .decision,
            reason:
              decisions.find((dec) => dec.applicationId === app.id)!.reason ||
              undefined,
            decisionAt: decisions
              .find((dec) => dec.applicationId === app.id)!
              .decisionAt.toISOString(),
          }
        : undefined,
    }));

    return { success: true, applications: result };
  } catch (error) {
    console.error("Error fetching applications for listing:", error);
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Admin function: Make a decision on an application
 */
export async function makeApplicationDecision(
  applicationId: number,
  decision: "approved" | "rejected",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  console.log("🚀 [DRIZZLE] makeApplicationDecision called");
  console.log("🚀 [DRIZZLE] Parameters:", { applicationId, decision, reason });

  try {
    console.log("🚀 [DRIZZLE] Getting user attributes...");
    const userAttributes = await getUserAttributes();
    console.log("🚀 [DRIZZLE] User attributes:", userAttributes);

    if (!userAttributes) {
      console.log("❌ [DRIZZLE] User not authenticated");
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the application exists
    console.log("🚀 [DRIZZLE] Verifying application exists...");
    const application = await db
      .select()
      .from(h_applications)
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    console.log("🚀 [DRIZZLE] Application query result:", application);

    if (application.length === 0) {
      console.log("❌ [DRIZZLE] Application not found");
      return { success: false, error: "Application not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    console.log("🚀 [DRIZZLE] Reviewer party ID:", reviewerPartyId);

    if (!reviewerPartyId) {
      console.log("❌ [DRIZZLE] Reviewer party ID not found");
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Insert decision
    console.log("🚀 [DRIZZLE] Inserting decision into database...");
    const decisionData = {
      applicationId,
      decision,
      reason: reason || null,
      reviewerId: parseInt(reviewerPartyId),
      decisionAt: new Date(),
    };
    console.log("🚀 [DRIZZLE] Decision data to insert:", decisionData);

    await db.insert(h_applicationDecisions).values(decisionData);

    console.log("✅ [DRIZZLE] Decision inserted successfully");
    return { success: true };
  } catch (error) {
    console.error("❌ [DRIZZLE] Error making application decision:", error);
    return { success: false, error: "Failed to make decision" };
  }
}

/**
 * Update document verification status
 */
export async function updateDocumentStatus(
  documentId: number,
  status: "verified" | "rejected"
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // TODO: Add admin authorization check

    // Verify the document exists
    const document = await db
      .select()
      .from(h_applicationDocuments)
      .where(eq(h_applicationDocuments.id, documentId))
      .limit(1);

    if (document.length === 0) {
      return { success: false, error: "Document not found" };
    }

    // Get reviewer party ID (admin user making the decision)
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Insert document status
    await db.insert(h_applicationDocumentsStatus).values({
      applicationDocumentId: documentId,
      status,
      statusAt: new Date(),
      statusBy: parseInt(reviewerPartyId),
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating document status:", error);
    return { success: false, error: "Failed to update document status" };
  }
}

/**
 * Update application status with timeline tracking
 */
export async function updateApplicationStatus(
  applicationId: number,
  newStatus: "pending" | "under_review" | "approved" | "rejected" | "withdrawn",
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const userAttributes = await getUserAttributes();

    if (!userAttributes) {
      return { success: false, error: "User not authenticated" };
    }

    // Get reviewer party ID
    const reviewerPartyId = userAttributes["custom:db_id"];
    if (!reviewerPartyId) {
      return { success: false, error: "Reviewer party ID not found" };
    }

    // Verify the application exists
    const application = await db
      .select()
      .from(h_applications)
      .where(eq(h_applications.id, applicationId))
      .limit(1);

    if (application.length === 0) {
      return { success: false, error: "Application not found" };
    }

    // Insert decision record for status tracking
    await db.insert(h_applicationDecisions).values({
      applicationId,
      decision: newStatus,
      reason,
      reviewerId: parseInt(reviewerPartyId),
      decisionAt: new Date(),
    });

    return { success: true };
  } catch (error) {
    console.error("Error updating application status:", error);
    return { success: false, error: "Failed to update application status" };
  }
}

/**
 * Get application status timeline
 */
export async function getApplicationStatusTimeline(
  applicationId: number
): Promise<{
  success: boolean;
  timeline?: Array<{
    id: number;
    status: string;
    reason?: string | null;
    timestamp: Date;
    reviewerName?: string | null;
  }>;
  error?: string;
}> {
  try {
    const timeline = await db
      .select({
        id: h_applicationDecisions.id,
        status: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        timestamp: h_applicationDecisions.decisionAt,
        reviewerName: sql<
          string | null
        >`CONCAT(${individual.firstName}, ' ', ${individual.lastName})`,
      })
      .from(h_applicationDecisions)
      .leftJoin(party, eq(h_applicationDecisions.reviewerId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt));

    return { success: true, timeline };
  } catch (error) {
    console.error("Error fetching application timeline:", error);
    return { success: false, error: "Failed to fetch application timeline" };
  }
}

/**
 * Get current application status
 */
export async function getCurrentApplicationStatus(
  applicationId: number
): Promise<{
  success: boolean;
  status?: "pending" | "under_review" | "approved" | "rejected" | "withdrawn";
  error?: string;
}> {
  try {
    const latestDecision = await db
      .select({
        status: h_applicationDecisions.decision,
      })
      .from(h_applicationDecisions)
      .where(eq(h_applicationDecisions.applicationId, applicationId))
      .orderBy(desc(h_applicationDecisions.decisionAt))
      .limit(1);

    if (latestDecision.length === 0) {
      // No decisions yet, default to pending
      return { success: true, status: "pending" };
    }

    return { success: true, status: latestDecision[0].status };
  } catch (error) {
    console.error("Error fetching current application status:", error);
    return { success: false, error: "Failed to fetch application status" };
  }
}

/**
 * Get application statistics for admin dashboard
 */
export async function getApplicationStats(): Promise<{
  success: boolean;
  stats?: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    byListingType: Record<string, number>;
  };
  error?: string;
}> {
  try {
    // TODO: Add admin authorization check

    // Get total applications
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(h_applications);

    // Get applications by latest decision status
    const statusStats = await db
      .select({
        decision: h_applicationDecisions.decision,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .leftJoin(
        h_applicationDecisions,
        eq(h_applications.id, h_applicationDecisions.applicationId)
      )
      .groupBy(h_applicationDecisions.decision);

    // Get applications by listing type
    const typeStats = await db
      .select({
        listingType: h_listings.listingType,
        count: sql<number>`count(*)`,
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .groupBy(h_listings.listingType);

    const stats = {
      total: totalResult[0]?.count || 0,
      pending:
        statusStats.find((s) => s.decision === null || s.decision === "pending")
          ?.count || 0,
      approved: statusStats.find((s) => s.decision === "approved")?.count || 0,
      rejected: statusStats.find((s) => s.decision === "rejected")?.count || 0,
      byListingType: typeStats.reduce(
        (acc, stat) => {
          acc[stat.listingType] = stat.count;
          return acc;
        },
        {} as Record<string, number>
      ),
    };

    return { success: true, stats };
  } catch (error) {
    console.error("Error fetching application stats:", error);
    return { success: false, error: "Failed to fetch application statistics" };
  }
}

/**
 * ADMIN: Get all applications for admin review (WORKING VERSION)
 */
export async function getAllApplicationsForAdmin(): Promise<{
  success: boolean;
  applications?: ApplicationWithDetails[];
  error?: string;
}> {
  try {
    // TODO: Add admin authentication check here if needed

    const applications = await db
      .select({
        id: h_applications.id,
        applicantId: h_applications.applicantId,
        listingId: h_applications.listingId,
        applicationDetails: h_applications.applicationDetails,

        // Listing fields
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,

        // Applicant fields (these are the people who submitted applications)
        firstName: individual.firstName,
        lastName: individual.lastName,
        email: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id IN (1, 4)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("email"),
        phone: sql<string>`(
          SELECT cp.value 
          FROM contact_point cp 
          WHERE cp.party_id = ${party.id} 
          AND cp.contact_point_type_id IN (2, 3)
          AND cp.is_primary = true 
          LIMIT 1
        )`.as("phone"),
      })
      .from(h_applications)
      .innerJoin(h_listings, eq(h_applications.listingId, h_listings.id))
      .innerJoin(party, eq(h_applications.applicantId, party.id)) // Join to applicant, not admin
      .leftJoin(individual, eq(party.id, individual.partyId))
      .orderBy(desc(h_applications.id));

    if (applications.length === 0) {
      return { success: true, applications: [] };
    }

    const applicationIds = applications.map((app) => app.id);

    // Step 2: Get vehicle data for vehicle-based listings
    const vehicleListings = applications.filter(
      (app) => app.sourceType === "vehicle"
    );
    const vehicleData =
      vehicleListings.length > 0
        ? await db
            .select({
              sourceId: vehicles.id,
              make: vehicleMake.name,
              model: vehicleModel.model,
              year: vehicles.manufacturingYear,
              registration: vehicles.vehicleRegistration,
            })
            .from(vehicles)
            .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
            .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
            .where(
              inArray(
                vehicles.id,
                vehicleListings.map((app) => app.sourceId)
              )
            )
        : [];

    // Step 3: Get catalog data for catalog-based listings
    const catalogListings = applications.filter(
      (app) => app.sourceType === "catalog"
    );
    const catalogData =
      catalogListings.length > 0
        ? await db
            .select({
              sourceId: h_vehicleCatalog.id,
              make: h_vehicleCatalog.make,
              model: h_vehicleCatalog.model,
              year: h_vehicleCatalog.year,
              category: h_vehicleCatalog.category,
              weeklyFeeTarget: h_vehicleCatalog.weeklyFeeTarget,
            })
            .from(h_vehicleCatalog)
            .where(
              inArray(
                h_vehicleCatalog.id,
                catalogListings.map((app) => app.sourceId)
              )
            )
        : [];

    // Step 4: Get all documents for these applications
    const documentsResult = await db
      .select({
        applicationId: h_applicationDocuments.applicationId,
        id: h_applicationDocuments.id,
        documentType: h_applicationDocuments.documentType,
        documentUrl: h_applicationDocuments.documentUrl,
        uploadedAt: h_applicationDocuments.uploadedAt,
      })
      .from(h_applicationDocuments)
      .where(inArray(h_applicationDocuments.applicationId, applicationIds));

    // Step 5: Get latest decisions for these applications
    const latestDecisionsResult = await db
      .select({
        applicationId: h_applicationDecisions.applicationId,
        decision: h_applicationDecisions.decision,
        reason: h_applicationDecisions.reason,
        decisionAt: h_applicationDecisions.decisionAt,
        reviewerId: h_applicationDecisions.reviewerId,
        // Get reviewer name
        reviewerFirstName: sql<string>`(
          SELECT i.first_name 
          FROM individual i 
          WHERE i.party_id = ${h_applicationDecisions.reviewerId}
          LIMIT 1
        )`.as("reviewerFirstName"),
        reviewerLastName: sql<string>`(
          SELECT i.last_name 
          FROM individual i 
          WHERE i.party_id = ${h_applicationDecisions.reviewerId}
          LIMIT 1
        )`.as("reviewerLastName"),
      })
      .from(h_applicationDecisions)
      .where(inArray(h_applicationDecisions.applicationId, applicationIds))
      .orderBy(desc(h_applicationDecisions.decisionAt));

    // Step 6: Create lookup maps for efficient data access
    const vehicleMap = vehicleData.reduce(
      (acc, vehicle) => {
        acc[vehicle.sourceId] = vehicle;
        return acc;
      },
      {} as Record<number, (typeof vehicleData)[0]>
    );

    const catalogMap = catalogData.reduce(
      (acc, catalog) => {
        acc[catalog.sourceId] = catalog;
        return acc;
      },
      {} as Record<number, (typeof catalogData)[0]>
    );

    // Create documents lookup map
    const documentsLookup = documentsResult.reduce((acc, doc) => {
      if (!acc.has(doc.applicationId)) {
        acc.set(doc.applicationId, []);
      }
      acc.get(doc.applicationId)!.push(doc);
      return acc;
    }, new Map<number, typeof documentsResult>());

    // Create latest decision lookup map (one decision per application)
    const decisionsLookup = latestDecisionsResult.reduce((acc, decision) => {
      if (!acc.has(decision.applicationId)) {
        acc.set(decision.applicationId, decision);
      }
      return acc;
    }, new Map<number, (typeof latestDecisionsResult)[0]>());

    // Step 7: Transform to ApplicationWithDetails format
    const result: ApplicationWithDetails[] = applications.map((app) => {
      const vehicle =
        app.sourceType === "vehicle" ? vehicleMap[app.sourceId] : undefined;
      const catalog =
        app.sourceType === "catalog" ? catalogMap[app.sourceId] : undefined;
      const appDocuments = documentsLookup.get(app.id) || [];
      const latestDecision = decisionsLookup.get(app.id);

      return {
        id: app.id,
        applicantId: app.applicantId,
        listingId: app.listingId,
        applicationDetails: app.applicationDetails
          ? JSON.parse(app.applicationDetails)
          : {},
        createdAt: new Date().toISOString(),

        applicant: {
          id: app.applicantId,
          firstName: app.firstName || "",
          lastName: app.lastName || "",
          email: app.email || "",
          phone: app.phone || undefined,
        },
        listing: {
          id: app.listingId,
          listingType: app.listingType,
          listingDetails: app.listingDetails
            ? JSON.parse(app.listingDetails)
            : {},
          sourceType: app.sourceType,
          sourceId: app.sourceId,
        },

        // Add vehicle details if available
        ...(vehicle && {
          vehicle: {
            make: vehicle.make || "Unknown Make",
            model: vehicle.model || "Unknown Model",
            year: vehicle.year || 0,
            registration: vehicle.registration || undefined,
          },
        }),

        // Add catalog details if available
        ...(catalog && {
          catalog: {
            make: catalog.make || "Unknown Make",
            model: catalog.model || "Unknown Model",
            year: catalog.year || 0,
            category: catalog.category || "Unknown",
            weeklyFeeTarget: catalog.weeklyFeeTarget || undefined,
          },
        }),

        // Add documents
        documents: appDocuments.map((doc) => ({
          id: doc.id,
          documentType: doc.documentType,
          documentUrl: doc.documentUrl,
          uploadedAt: doc.uploadedAt.toISOString(),
        })),

        // Add latest decision
        latestDecision: latestDecision
          ? {
              decision: latestDecision.decision,
              reason: latestDecision.reason || undefined,
              decisionAt: latestDecision.decisionAt.toISOString(),
              reviewerName:
                latestDecision.reviewerFirstName &&
                latestDecision.reviewerLastName
                  ? `${latestDecision.reviewerFirstName} ${latestDecision.reviewerLastName}`
                  : undefined,
            }
          : undefined,
      };
    });

    return { success: true, applications: result };
  } catch (error) {
    console.error("Error fetching applications for admin:", error);
    return { success: false, error: "Failed to fetch applications" };
  }
}

/**
 * Get published catalog-based listings
 */
export async function getPublishedCatalogListings(): Promise<{
  success: boolean;
  listings?: Array<{
    id: number;
    listingType: string;
    listingDetails: any;
    sourceType: string;
    sourceId: number;
    publishedAt?: string;
    catalog: {
      id: number;
      make: string;
      model: string;
      year: number;
      category: string;
      weeklyFeeTarget?: number;
      description?: string;
      ehailingEligible: boolean;
      estimatedPrice?: number;
      weeklyRate?: number;
      initiationFee?: number;
      variant?: string;
      images: Array<{
        id: number;
        imageUrl: string;
        isPrimary: boolean;
      }>;
    };
  }>;
  error?: string;
}> {
  try {
    // Get published listings with catalog source type
    const listings = await db
      .select({
        // Listing fields
        id: h_listings.id,
        listingType: h_listings.listingType,
        listingDetails: h_listings.listingDetails,
        sourceType: h_listings.sourceType,
        sourceId: h_listings.sourceId,

        // Published status timestamp
        publishedAt: h_listing_publish_status.statusAt,

        // Catalog fields
        catalogId: h_vehicleCatalog.id,
        catalogMake: h_vehicleCatalog.make,
        catalogModel: h_vehicleCatalog.model,
        catalogYear: h_vehicleCatalog.year,
        catalogCategory: h_vehicleCatalog.category,
        catalogWeeklyFeeTarget: h_vehicleCatalog.weeklyFeeTarget,
        catalogDescription: h_vehicleCatalog.description,
        catalogEhailingEligible: h_vehicleCatalog.ehailingEligible,
        catalogEstimatedPrice: h_vehicleCatalog.estimatedPrice,
        catalogVariant: h_vehicleCatalog.variant,
        // Catalog amounts
        catalogWeeklyRate: h_vehicleCatalog.weeklyRate,
        catalogInitiationFee: h_vehicleCatalog.initiationFee,
      })
      .from(h_listings)
      .innerJoin(h_vehicleCatalog, eq(h_listings.sourceId, h_vehicleCatalog.id))
      .leftJoin(
        h_listing_publish_status,
        sql`${h_listings.id} = ${h_listing_publish_status.listingId} AND ${h_listing_publish_status.id} = (
          SELECT id FROM h_listing_publish_status AS lps 
          WHERE lps.listing_id = ${h_listings.id} 
          ORDER BY lps.status_at DESC 
          LIMIT 1
        )`
      )
      .where(
        and(
          eq(h_listings.sourceType, "catalog"),
          eq(h_listing_publish_status.status, "published")
        )
      )
      .orderBy(desc(h_listing_publish_status.statusAt));

    // Get catalog IDs for fetching images
    const catalogIds = listings.map((listing) => listing.catalogId);

    // Fetch catalog images for all catalog items
    const { h_vehicleCatalogImages } = await import(
      "@/drizzle/h_schema/vehicle-catalog"
    );
    const catalogImages =
      catalogIds.length > 0
        ? await db
            .select({
              catalogId: h_vehicleCatalogImages.catalogId,
              id: h_vehicleCatalogImages.id,
              imageUrl: h_vehicleCatalogImages.imageUrl,
              isPrimary: h_vehicleCatalogImages.isPrimary,
            })
            .from(h_vehicleCatalogImages)
            .where(inArray(h_vehicleCatalogImages.catalogId, catalogIds))
            .orderBy(
              desc(h_vehicleCatalogImages.isPrimary),
              h_vehicleCatalogImages.id
            )
        : [];

    // Group images by catalog ID
    const imagesByCatalogId = new Map<
      number,
      Array<{
        id: number;
        imageUrl: string;
        isPrimary: boolean;
      }>
    >();

    catalogImages.forEach((image) => {
      if (!imagesByCatalogId.has(image.catalogId)) {
        imagesByCatalogId.set(image.catalogId, []);
      }
      imagesByCatalogId.get(image.catalogId)!.push({
        id: image.id,
        imageUrl: image.imageUrl,
        isPrimary: image.isPrimary,
      });
    });

    // Transform the data
    const result = listings.map((listing) => ({
      id: listing.id,
      listingType: listing.listingType,
      listingDetails: listing.listingDetails
        ? JSON.parse(listing.listingDetails)
        : {},
      sourceType: listing.sourceType,
      sourceId: listing.sourceId,
      publishedAt: listing.publishedAt?.toString(),
      catalog: {
        id: listing.catalogId,
        make: listing.catalogMake || "Unknown Make",
        model: listing.catalogModel || "Unknown Model",
        year: listing.catalogYear || 0,
        variant: listing.catalogVariant ?? undefined,
        category: listing.catalogCategory || "Unknown Category",
        weeklyFeeTarget: listing.catalogWeeklyFeeTarget || undefined, // Keep for backward compatibility
        weeklyRate: listing.catalogWeeklyRate || undefined,
        initiationFee: listing.catalogInitiationFee || undefined,
        description: listing.catalogDescription || undefined,
        ehailingEligible: listing.catalogEhailingEligible || false,
        estimatedPrice: listing.catalogEstimatedPrice || undefined,
        images: imagesByCatalogId.get(listing.catalogId) || [],
      },
    }));

    return { success: true, listings: result };
  } catch (error) {
    console.error("Error fetching published catalog listings:", error);
    return {
      success: false,
      error: "Failed to fetch published catalog listings",
    };
  }
}
