"use server";

/**
 * DRIZZLE ACTIONS - BOOKINGS
 * 
 * This file contains all direct database operations for bookings using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Use these functions when you need direct database access for better performance
 * and type safety. For API compatibility, use the functions in actions/bookings.ts
 */

import { db } from "../db";
import { eq, and, or, sql, desc, asc, gte, lte, between } from "drizzle-orm";
import {
  bookings,
  vehicles,
  vehiclePossessions,
  party,
  individual,
  contactPoint,
  contactPointType,
  vehicleModel,
  vehicleMake,

  // New immutable booking system tables
  bookingEvents,
  vehiclePossessionEvents,
  vehicleAccessEvents,
  vehicleHandovers,
  handoverStatusEvents,
  vehicleInspectionsImmutable,
  inspectionPhotos,
  handoverIssues,
  handoverIssueResolutions,
  currentBookingStatusView,
  currentVehiclePossessionView,
  currentVehicleAccessView,
  currentHandoverStatusView,
} from "../drizzle/schema";
import { createVehicleHandoverTasks } from "@/actions/tasks";

// Helper function to create initial vehicle possession when a vehicle is created
export async function createInitialVehiclePossession(
  vehicleId: number,
  possessorPartyId: number,
  recordedBy: number,
  possessionType: PossessionType = PossessionType.OWNER,
  triggerType: string = 'OWNERSHIP_TRANSFER',
  notes?: string
): Promise<VehiclePossessionEventRead | null> {
  try {
    console.log(`🏠 === createInitialVehiclePossession START ===`);
    console.log(`🏠 Parameters:`, {
      vehicleId,
      possessorPartyId,
      recordedBy,
      possessionType,
      triggerType,
      notes
    });

    const possessionEventData: VehiclePossessionEventCreate = {
      vehicleId,
      possessorPartyId,
      possessionStart: new Date().toISOString(),
      possessionEnd: null, // Current possession - no end date
      possessionType,
      triggerType,
      triggerReference: `INITIAL_${vehicleId}`,
      recordedBy,
      notes: notes || `Initial possession for vehicle ${vehicleId}`
    };

    console.log(`🏠 Possession event data to insert:`, possessionEventData);

    const result = await db
      .insert(vehiclePossessionEvents)
      .values(possessionEventData)
      .returning();

    console.log(`🏠 Successfully created initial possession:`, result[0]);
    console.log(`✅ Created initial possession for vehicle ${vehicleId} → party ${possessorPartyId}`);
    
    return result[0] as VehiclePossessionEventRead;
  } catch (error) {
    console.error(`❌ Failed to create initial vehicle possession:`, error);
    return null;
  }
}
import type {
  deprecated_BookingCreate,
  deprecated_BookingRead,
  deprecated_BookingUpdate,
  // New immutable booking types
  BookingEventCreate,
  BookingEventRead,
  VehiclePossessionEventCreate,
  VehiclePossessionEventRead,
  VehicleAccessEventCreate,
  VehicleAccessEventRead,
  VehicleHandoverCreate,
  VehicleHandoverRead,
  HandoverStatusEventCreate,
  HandoverStatusEventRead,
  VehicleInspectionCreate,
  VehicleInspectionRead,
  InspectionPhotoCreate,
  InspectionPhotoRead,
  HandoverIssueCreate,
  HandoverIssueRead,
  HandoverIssueResolutionCreate,
  HandoverIssueResolutionRead,
  CurrentBookingStatus,
  CurrentVehiclePossession,
  CurrentVehicleAccess,
  CurrentHandoverStatus,
  CreateBookingRequest,
  UpdateBookingStatusRequest,
  ScheduleHandoverRequest,
  CompleteInspectionRequest,
  BookingAvailabilityRequest,
  BookingAvailabilityResponse,
  ConditionLevel,
  ConditionLevelBasic,
  GeneralCondition,
  LightsCondition,
  CleanlinessLevel,
  DashboardCondition,
  OdorLevel,
  ConditionEnhanced,
} from "@/types/bookings";
import {
  BookingStatus,
  HandoverStatus,
  HandoverType,
  InspectionType,
  PossessionType,
  PermissionType,
  IssueType,
} from "@/types/bookings";


// ==================== BOOKING CRUD OPERATIONS ====================

// Create a new booking
export async function deprecated_createBookingDrizzle(
  bookingData: deprecated_BookingCreate
): Promise<deprecated_BookingRead> {
  try {
    // Map status to correct enum value
    let statusEnum: "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";

    if (typeof bookingData.status === 'string') {
      statusEnum = bookingData.status.toUpperCase() as "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
    } else {
      // Handle numeric enum values from BookingStatus
      switch (bookingData.status) {
        case 0:
          statusEnum = "PENDING";
          break;
        case 1:
          statusEnum = "CONFIRMED";
          break;
        case 2:
          statusEnum = "CANCELLED";
          break;
        case 3:
          statusEnum = "COMPLETED";
          break;
        default:
          statusEnum = "PENDING";
      }
    }

    const newBooking = await db
      .insert(bookings)
      .values({
        vehicleId: bookingData.vehicle_id,
        reference: bookingData.reference,
        startDatetime: bookingData.start_datetime,
        endDatetime: bookingData.end_datetime,
        status: statusEnum,
        totalPrice: bookingData.total_price,
        notes: bookingData.notes,
        partyId: bookingData.party_id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newBooking[0].id,
      vehicle_id: newBooking[0].vehicleId,
      reference: newBooking[0].reference,
      start_datetime: newBooking[0].startDatetime,
      end_datetime: newBooking[0].endDatetime,
      status: newBooking[0].status as any,
      total_price: newBooking[0].totalPrice,
      notes: newBooking[0].notes,
      party_id: newBooking[0].partyId,
      created_at: newBooking[0].createdAt || new Date().toISOString(),
      updated_at: newBooking[0].updatedAt || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
}

// Get a single booking by ID with all related data
export async function deprecated_getBookingByIdDrizzle(bookingId: number): Promise<deprecated_BookingRead | null> {
  try {
    const booking = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.id, bookingId))
      .limit(1);

    if (booking.length === 0) return null;

    return {
      ...booking[0],
      status: booking[0].status as any,
      created_at: booking[0].created_at || new Date().toISOString(),
      updated_at: booking[0].updated_at || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error fetching booking:", error);
    throw error;
  }
}

// Update a booking
export async function deprecated_updateBookingDrizzle(
  bookingId: number,
  updateData: Partial<deprecated_BookingUpdate>
): Promise<deprecated_BookingRead | null> {
  try {
    // Prepare update object with proper enum handling
    const updateValues: any = {
      updatedAt: new Date().toISOString(),
    };

    // Handle each field individually
    if (updateData.vehicle_id) updateValues.vehicleId = updateData.vehicle_id;
    if (updateData.reference) updateValues.reference = updateData.reference;
    if (updateData.start_datetime) updateValues.startDatetime = updateData.start_datetime;
    if (updateData.end_datetime) updateValues.endDatetime = updateData.end_datetime;
    if (updateData.total_price !== undefined) updateValues.totalPrice = updateData.total_price;
    if (updateData.notes !== undefined) updateValues.notes = updateData.notes;
    if (updateData.party_id) updateValues.partyId = updateData.party_id;

    // Handle status enum properly
    if (updateData.status) {
      if (typeof updateData.status === 'string') {
        updateValues.status = updateData.status.toUpperCase() as "PENDING" | "CONFIRMED" | "CANCELLED" | "COMPLETED";
      } else {
        // Handle numeric enum values
        switch (updateData.status) {
          case 0:
            updateValues.status = "PENDING";
            break;
          case 1:
            updateValues.status = "CONFIRMED";
            break;
          case 2:
            updateValues.status = "CANCELLED";
            break;
          case 3:
            updateValues.status = "COMPLETED";
            break;
          default:
            updateValues.status = "PENDING";
        }
      }
    }

    const updated = await db
      .update(bookings)
      .set(updateValues)
      .where(eq(bookings.id, bookingId))
      .returning();

    if (updated.length === 0) return null;

    return {
      id: updated[0].id,
      vehicle_id: updated[0].vehicleId,
      reference: updated[0].reference,
      start_datetime: updated[0].startDatetime,
      end_datetime: updated[0].endDatetime,
      status: updated[0].status as any,
      total_price: updated[0].totalPrice,
      notes: updated[0].notes,
      party_id: updated[0].partyId,
      created_at: updated[0].createdAt || new Date().toISOString(),
      updated_at: updated[0].updatedAt || new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error updating booking:", error);
    throw error;
  }
}

// Delete a booking
export async function deprecated_deleteBookingDrizzle(bookingId: number): Promise<boolean> {
  try {
    const deleted = await db
      .delete(bookings)
      .where(eq(bookings.id, bookingId))
      .returning({ id: bookings.id });

    return deleted.length > 0;
  } catch (error) {
    console.error("Error deleting booking:", error);
    throw error;
  }
}

// ==================== BOOKING QUERIES ====================

// Check for booking overlaps using Drizzle
export async function checkBookingOverlap(
  vehicleId: number,
  startDatetime: string,
  endDatetime: string,
  excludeBookingId?: number
): Promise<boolean> {
  try {
    const conditions = [
      eq(bookings.vehicleId, vehicleId),
      or(
        eq(bookings.status, "CONFIRMED"),
        eq(bookings.status, "PENDING")
      ),
      // Check for overlap: new booking overlaps if it starts before existing ends and ends after existing starts
      and(
        sql`${bookings.startDatetime} < ${endDatetime}`,
        sql`${bookings.endDatetime} > ${startDatetime}`
      )
    ];

    // Exclude specific booking if updating
    if (excludeBookingId) {
      conditions.push(sql`${bookings.id} != ${excludeBookingId}`);
    }

    const overlappingBookings = await db
      .select({ id: bookings.id })
      .from(bookings)
      .where(and(...conditions))
      .limit(1);

    return overlappingBookings.length > 0;
  } catch (error) {
    console.error("Error checking booking overlap:", error);
    throw error;
  }
}

// Get bookings for a vehicle with related data
export async function deprecated_getVehicleBookingsWithDetails(vehicleId: number): Promise<deprecated_BookingRead[]> {
  try {
    const vehicleBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.vehicleId, vehicleId))
      .orderBy(desc(bookings.startDatetime));

    return vehicleBookings.map(booking => ({
      ...booking,
      status: booking.status as any // Type assertion to handle enum mapping
    })) as deprecated_BookingRead[];
  } catch (error) {
    console.error("Error fetching vehicle bookings:", error);
    throw error;
  }
}

// Get bookings for a party (user)
export async function deprecated_getPartyBookingsWithDetails(partyId: number): Promise<deprecated_BookingRead[]> {
  try {
    const partyBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(eq(bookings.partyId, partyId))
      .orderBy(desc(bookings.startDatetime));

    return partyBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as deprecated_BookingRead[];
  } catch (error) {
    console.error("Error fetching party bookings:", error);
    throw error;
  }
}

// Get active bookings for a vehicle (confirmed or pending)
export async function deprecated_getActiveVehicleBookings(vehicleId: number): Promise<deprecated_BookingRead[]> {
  try {
    const activeBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          )
        )
      )
      .orderBy(asc(bookings.startDatetime));

    return activeBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as deprecated_BookingRead[];
  } catch (error) {
    console.error("Error fetching active vehicle bookings:", error);
    throw error;
  }
}

// Get bookings within a date range
export async function deprecated_getBookingsInDateRange(
  startDate: string,
  endDate: string,
  vehicleId?: number
): Promise<deprecated_BookingRead[]> {
  try {
    const conditions = [
      and(
        gte(bookings.startDatetime, startDate),
        lte(bookings.endDatetime, endDate)
      )
    ];

    if (vehicleId) {
      conditions.push(eq(bookings.vehicleId, vehicleId));
    }

    const rangeBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(and(...conditions))
      .orderBy(asc(bookings.startDatetime));

    return rangeBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as deprecated_BookingRead[];
  } catch (error) {
    console.error("Error fetching bookings in date range:", error);
    throw error;
  }
}

// ==================== VEHICLE POSSESSION INTEGRATION ====================

// Get current vehicle possession status - who actually has the car
export async function deprecated_getCurrentVehiclePossession(vehicleId: number) {
  try {
    const currentPossession = await db
      .select({
        id: vehiclePossessions.id,
        fromPartyId: vehiclePossessions.fromPartyId,
        toPartyId: vehiclePossessions.toPartyId,
        vehicleId: vehiclePossessions.vehicleId,
        handoverExpectedDatetime: vehiclePossessions.handoverExpectedDatetime,
        handoverActualDatetime: vehiclePossessions.handoverActualDatetime,
        status: vehiclePossessions.status,
      })
      .from(vehiclePossessions)
      .where(
        and(
          eq(vehiclePossessions.vehicleId, vehicleId),
          eq(vehiclePossessions.status, "COMPLETED")
        )
      )
      .orderBy(desc(vehiclePossessions.handoverActualDatetime))
      .limit(1);

    return currentPossession[0] || null;
  } catch (error) {
    console.error("Error fetching current vehicle possession:", error);
    throw error;
  }
}

// Get the party that currently has possession of the vehicle
export async function deprecated_getCurrentVehicleHolder(vehicleId: number): Promise<number | null> {
  try {
    const latestPossession = await deprecated_getCurrentVehiclePossession(vehicleId);

    // If there's a completed possession, the 'toPartyId' is who currently has the car
    // If no possession records exist, the vehicle owner (from vehicles table) has it
    if (latestPossession) {
      return latestPossession.toPartyId;
    }

    // Get the vehicle owner as fallback
    const vehicle = await db
      .select({ partyId: vehicles.partyId })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    return vehicle[0]?.partyId || null;
  } catch (error) {
    console.error("Error getting current vehicle holder:", error);
    throw error;
  }
}

// ==================== VEHICLE AVAILABILITY ====================

// Get vehicle availability status considering both bookings and current possession
export async function deprecated_getVehicleAvailabilityStatus(vehicleId: number) {
  try {
    const now = new Date().toISOString();

    // Check for active bookings
    const activeBooking = await db
      .select({
        id: bookings.id,
        status: bookings.status,
        partyId: bookings.partyId,
        startDatetime: bookings.startDatetime,
        endDatetime: bookings.endDatetime
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          ),
          sql`${bookings.startDatetime} <= ${now}`,
          sql`${bookings.endDatetime} >= ${now}`
        )
      )
      .limit(1);

    if (activeBooking.length > 0) {
      return {
        status: "in-use",
        reason: "Active booking",
        bookingId: activeBooking[0].id,
        currentHolder: activeBooking[0].partyId,
      };
    }

    // Check who currently has the vehicle (based on completed possessions)
    const currentHolder = await deprecated_getCurrentVehicleHolder(vehicleId);

    // Check vehicle maintenance status
    const vehicle = await db
      .select({
        isActive: vehicles.isActive,
        partyId: vehicles.partyId
      })
      .from(vehicles)
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicle.length === 0) {
      return {
        status: "not-found",
        reason: "Vehicle not found",
      };
    }

    if (!vehicle[0].isActive) {
      return {
        status: "maintenance",
        reason: "Vehicle under maintenance",
        currentHolder,
      };
    }

    return {
      status: "available",
      reason: "Vehicle is available for booking",
      currentHolder,
      vehicleOwner: vehicle[0].partyId,
    };
  } catch (error) {
    console.error("Error checking vehicle availability:", error);
    throw error;
  }
}

// ==================== BOOKING ANALYTICS ====================

// Get booking statistics for a vehicle
export async function deprecated_getVehicleBookingStats(vehicleId: number) {
  try {
    const stats = await db
      .select({
        total_bookings: sql<number>`count(*)`,
        confirmed_bookings: sql<number>`sum(case when status = 'CONFIRMED' then 1 else 0 end)`,
        pending_bookings: sql<number>`sum(case when status = 'PENDING' then 1 else 0 end)`,
        completed_bookings: sql<number>`sum(case when status = 'COMPLETED' then 1 else 0 end)`,
        cancelled_bookings: sql<number>`sum(case when status = 'CANCELLED' then 1 else 0 end)`,
        total_revenue: sql<number>`sum(total_price)`,
      })
      .from(bookings)
      .where(eq(bookings.vehicleId, vehicleId));

    return stats[0];
  } catch (error) {
    console.error("Error fetching vehicle booking stats:", error);
    throw error;
  }
}

// Get upcoming bookings for a vehicle
export async function deprecated_getUpcomingVehicleBookings(vehicleId: number, limit: number = 10): Promise<deprecated_BookingRead[]> {
  try {
    const now = new Date().toISOString();

    const upcomingBookings = await db
      .select({
        id: bookings.id,
        vehicle_id: bookings.vehicleId,
        reference: bookings.reference,
        start_datetime: bookings.startDatetime,
        end_datetime: bookings.endDatetime,
        status: bookings.status,
        total_price: bookings.totalPrice,
        notes: bookings.notes,
        party_id: bookings.partyId,
        created_at: bookings.createdAt,
        updated_at: bookings.updatedAt,
      })
      .from(bookings)
      .where(
        and(
          eq(bookings.vehicleId, vehicleId),
          or(
            eq(bookings.status, "CONFIRMED"),
            eq(bookings.status, "PENDING")
          ),
          gte(bookings.startDatetime, now)
        )
      )
      .orderBy(asc(bookings.startDatetime))
      .limit(limit);

    return upcomingBookings.map(booking => ({
      ...booking,
      status: booking.status as any
    })) as deprecated_BookingRead[];
  } catch (error) {
    console.error("Error fetching upcoming vehicle bookings:", error);
    throw error;
  }
}

// ==================== FORM SUBMISSION ACTIONS ====================

// Server action for form-based booking creation (replaces legacy API action)
export async function deprecated_addBookingDrizzle(_: any, formData: FormData) {
  try {
    console.log("🚀 Starting booking creation process");
    const start_datetime = (formData.get("start_datetime") as string)?.trim();
    const end_datetime = (formData.get("end_datetime") as string)?.trim();
    const status = (formData.get("status") as string)?.trim();
    const reference = (formData.get("reference") as string)?.trim();
    const vehicle_id = Number(formData.get("vehicle_id"));
    const party_id = Number(formData.get("party_id"));

    console.log("📋 Booking data:", { start_datetime, end_datetime, status, reference, vehicle_id, party_id });

    // Validation
    if (!start_datetime || !end_datetime || !vehicle_id || !party_id) {
      console.error("❌ Missing required fields");
      return {
        success: false,
        errors: {
          general: ["Missing required fields"]
        }
      };
    }

    // Enhanced availability and permission check
    console.log("🔍 Checking booking availability...");
    const bookingCheck = await deprecated_canPartyBookVehicle(vehicle_id, party_id, start_datetime, end_datetime);
    if (!bookingCheck.canBook) {
      console.error("❌ Booking check failed:", bookingCheck.reason);
      return {
        success: false,
        errors: {
          dates: [bookingCheck.reason]
        }
      };
    }
    console.log("✅ Booking availability confirmed");

    const bookingData: deprecated_BookingCreate = {
      vehicle_id,
      reference,
      start_datetime,
      end_datetime,
      party_id,
      status: status as any, // Will be cast to proper enum in createBookingDrizzle
      total_price: null,
      notes: null,
    };

    // Create booking using direct DB access
    console.log("💾 Creating booking in database...");
    const booking = await deprecated_createBookingDrizzle(bookingData);
    console.log("✅ Booking created successfully:", { id: booking.id, reference: booking.reference });

    // After creating the booking, create the handover tasks
    try {
      console.log("🎯 Starting handover task creation process...");
      
      // Get borrower information
      console.log("👤 Fetching borrower information for party_id:", party_id);
      const borrowerInfo = await db
        .select({
          partyId: party.id,
          firstName: individual.firstName,
          lastName: individual.lastName,
          email: contactPoint.value,
        })
        .from(party)
        .leftJoin(individual, eq(party.id, individual.partyId))
        .leftJoin(contactPoint, and(
          eq(party.id, contactPoint.partyId),
          or(
            eq(contactPoint.contactPointTypeId, 1), // Email (capitalized)
            eq(contactPoint.contactPointTypeId, 4)  // email (lowercase) - duplicate
          )
        ))
        .where(eq(party.id, party_id))
        .limit(1);

      console.log("👤 Borrower info result:", borrowerInfo);

      // Get vehicle information
      console.log("🚗 Fetching vehicle information for vehicle_id:", vehicle_id);
      const vehicleInfo = await db
        .select({
          vehicleName: sql<string>`${vehicleMake.name} || ' ' || ${vehicleModel.model}`.as('vehicle_name'),
        })
        .from(vehicles)
        .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(eq(vehicles.id, vehicle_id))
        .limit(1);

      console.log("🚗 Vehicle info result:", vehicleInfo);

      // Get current vehicle holder information
      console.log("🔍 Checking current vehicle possession...");
      let currentPossession = await getCurrentVehiclePossession(vehicle_id);
      let currentHolderInfo = null;
      
      // If no current possession exists, create one for the vehicle owner
      if (!currentPossession) {
        console.log(`⚠️  No current possession found for vehicle ${vehicle_id}. Creating initial possession for vehicle owner.`);
        
        // Get vehicle owner as fallback
        const vehicleOwner = await db
          .select({ partyId: vehicles.partyId })
          .from(vehicles)
          .where(eq(vehicles.id, vehicle_id))
          .limit(1);
          
        console.log("🏠 Vehicle owner result:", vehicleOwner);
          
        if (vehicleOwner.length > 0) {
          // Create initial possession for the vehicle owner
          console.log("📝 Creating initial possession...");
          await createInitialVehiclePossession(
            vehicle_id,
            vehicleOwner[0].partyId,
            party_id, // Use the person creating the booking as the recorder
            PossessionType.OWNER,
            'OWNERSHIP_TRANSFER',
            'Initial possession created during booking - vehicle had no current possessor'
          );
          
          // Re-fetch the possession
          console.log("🔄 Re-fetching possession after creation...");
          currentPossession = await getCurrentVehiclePossession(vehicle_id);
          console.log("🔄 New possession result:", currentPossession);
        }
      } else {
        console.log("✅ Found existing possession:", currentPossession);
      }
      
      if (currentPossession) {
        console.log("👤 Fetching current holder info for party_id:", currentPossession.possessorPartyId);
        
        // Debug: Check what contact point types exist
        console.log("🔍 Checking contact point types...");
        const contactPointTypes = await db
          .select()
          .from(contactPointType)
          .orderBy(contactPointType.id);
        console.log("📧 Available contact point types:", contactPointTypes);
        
        // Debug: Check all contact points for this party
        console.log("🔍 Checking all contact points for party:", currentPossession.possessorPartyId);
        const allContactPoints = await db
          .select()
          .from(contactPoint)
          .where(eq(contactPoint.partyId, currentPossession.possessorPartyId));
        console.log("📧 All contact points for party:", allContactPoints);
        
        const holderInfo = await db
          .select({
            partyId: party.id,
            firstName: individual.firstName,
            lastName: individual.lastName,
            email: contactPoint.value,
          })
          .from(party)
          .leftJoin(individual, eq(party.id, individual.partyId))
          .leftJoin(contactPoint, and(
            eq(party.id, contactPoint.partyId),
            or(
              eq(contactPoint.contactPointTypeId, 1), // Email (capitalized)
              eq(contactPoint.contactPointTypeId, 4)  // email (lowercase) - duplicate
            )
          ))
          .where(eq(party.id, currentPossession.possessorPartyId))
          .limit(1);
        
        currentHolderInfo = holderInfo[0] || null;
        console.log("👤 Current holder info result:", currentHolderInfo);
      }

      // Create tasks if we have the necessary information
      if (borrowerInfo[0] && vehicleInfo[0]) {
        console.log("✅ Have required info for task creation");
        const borrower = borrowerInfo[0];
        const vehicle = vehicleInfo[0];
        const borrowerName = `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim();
        const borrowerEmail = borrower.email;
        
        let currentHolderName: string | undefined;
        let currentHolderEmail: string | undefined;
        let currentHolderPartyId: number | undefined;
        
        if (currentHolderInfo) {
          currentHolderName = `${currentHolderInfo.firstName || ''} ${currentHolderInfo.lastName || ''}`.trim();
          currentHolderEmail = currentHolderInfo.email || undefined;
          currentHolderPartyId = currentHolderInfo.partyId;
        }

        console.log("📊 Task creation parameters:", {
          bookingReference: booking.reference,
          vehicleId: vehicle_id,
          vehicleName: vehicle.vehicleName,
          borrowerEmail,
          borrowerName,
          borrowerPartyId: party_id,
          currentHolderEmail,
          currentHolderName,
          currentHolderPartyId
        });

        // Create all handover tasks
        console.log("🎯 Calling createVehicleHandoverTasks...");
        const taskResult = await createVehicleHandoverTasks(
          booking.reference,
          vehicle_id,
          vehicle.vehicleName || 'Unknown Vehicle',
          borrowerEmail || '',
          borrowerName || 'Unknown User',
          party_id,
          start_datetime,
          end_datetime,
          currentHolderEmail,
          currentHolderName,
          currentHolderPartyId
        );

        console.log("🎯 Task creation result:", taskResult);

        if (taskResult.success) {
          console.log(`✅ Successfully created ${taskResult.tasksCreated} handover tasks for booking ${booking.reference}`);
        } else {
          console.error(`❌ Failed to create handover tasks for booking ${booking.reference}:`, taskResult.error);
        }
      } else {
        console.warn(`⚠️ Missing required information to create handover tasks for booking ${booking.reference}`);
        console.warn("Missing info details:", {
          hasBorrowerInfo: !!borrowerInfo[0],
          hasVehicleInfo: !!vehicleInfo[0],
          borrowerInfo: borrowerInfo[0],
          vehicleInfo: vehicleInfo[0]
        });
      }
    } catch (taskError) {
      // Don't fail the booking creation if task creation fails
      console.error("❌ Error creating handover tasks for booking:", taskError);
    }

    return {
      success: true,
      message: "Booking created successfully.",
      bookingID: booking.id,
    };
  } catch (error: any) {
    console.error("❌ Error creating booking:", error);
    return {
      success: false,
      errors: {
        general: [error.message || "Failed to create booking"]
      }
    };
  }
}

// Check if a party can book a vehicle for specific dates
export async function deprecated_canPartyBookVehicle(
  vehicleId: number,
  partyId: number,
  startDatetime: string,
  endDatetime: string
): Promise<{ canBook: boolean; reason: string; currentHolder?: number }> {
  try {
    // Check basic availability
    const availability = await deprecated_getVehicleAvailabilityStatus(vehicleId);

    if (availability.status === "not-found") {
      return { canBook: false, reason: "Vehicle not found" };
    }

    if (availability.status === "maintenance") {
      return {
        canBook: false,
        reason: "Vehicle is under maintenance",
        currentHolder: availability.currentHolder || undefined
      };
    }

    // Check for booking conflicts
    const hasConflict = await checkBookingOverlap(vehicleId, startDatetime, endDatetime);
    if (hasConflict) {
      return { canBook: false, reason: "Date conflict with existing booking" };
    }

    // All checks passed
    return {
      canBook: true,
      reason: "Vehicle is available for booking",
      currentHolder: availability.currentHolder || undefined
    };
  } catch (error) {
    console.error("Error checking if party can book vehicle:", error);
    return { canBook: false, reason: "Error checking availability" };
  }
}

// =====================================================
// NEW IMMUTABLE BOOKING SYSTEM ACTIONS
// =====================================================

// Generate unique booking reference
function generateBookingReference(): string {
  const prefix = 'BK';
  const year = new Date().getFullYear();
  const timestamp = Date.now().toString().slice(-6);
  return `${prefix}-${year}-${timestamp}`;
}

// ==================== BOOKING EVENT OPERATIONS ====================

// Create a new booking (immutable event-based)
export async function createBooking(request: CreateBookingRequest, createdBy: number): Promise<BookingEventRead> {
  try {
    // Generate unique reference
    const bookingReference = generateBookingReference();

    // Check availability first
    const availability = await checkBookingAvailability({
      vehicleId: request.vehicleId,
      requestedStart: request.requestedStart,
      requestedEnd: request.requestedEnd,
    });

    if (!availability.isAvailable) {
      throw new Error(`Booking not available: ${availability.conflictingBookings?.[0]?.currentStatus || 'Unknown conflict'}`);
    }

    const bookingEventData: BookingEventCreate = {
      bookingReference,
      vehicleId: request.vehicleId,
      borrowerPartyId: request.borrowerPartyId,
      eventType: 'CREATED',
      status: BookingStatus.DRAFT,
      requestedStart: request.requestedStart,
      requestedEnd: request.requestedEnd,
      changedBy: createdBy,
      purpose: request.purpose,
      specialRequirements: request.specialRequirements,
      notes: request.notes,
      currency: 'USD',
    };

    const newEvent = await db
      .insert(bookingEvents)
      .values(bookingEventData)
      .returning();

    // After creating the booking, create the handover tasks
    try {
      // Get borrower information
      const borrowerInfo = await db
        .select({
          partyId: party.id,
          firstName: individual.firstName,
          lastName: individual.lastName,
          email: contactPoint.value,
        })
        .from(party)
        .leftJoin(individual, eq(party.id, individual.partyId))
        .leftJoin(contactPoint, and(
          eq(party.id, contactPoint.partyId),
          or(
            eq(contactPoint.contactPointTypeId, 1), // Email (capitalized)
            eq(contactPoint.contactPointTypeId, 4)  // email (lowercase) - duplicate
          )
        ))
        .where(eq(party.id, request.borrowerPartyId))
        .limit(1);

      // Get vehicle information
      const vehicleInfo = await db
        .select({
          vehicleName: sql<string>`${vehicleMake.name} || ' ' || ${vehicleModel.model}`.as('vehicle_name'),
        })
        .from(vehicles)
        .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
        .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
        .where(eq(vehicles.id, request.vehicleId))
        .limit(1);

      // Get current vehicle holder information
      let currentPossession = await getCurrentVehiclePossession(request.vehicleId);
      let currentHolderInfo = null;
      
      // If no current possession exists, create one for the vehicle owner
      if (!currentPossession) {
        console.log(`⚠️  No current possession found for vehicle ${request.vehicleId}. Creating initial possession for vehicle owner.`);
        
        // Get vehicle owner as fallback
        const vehicleOwner = await db
          .select({ partyId: vehicles.partyId })
          .from(vehicles)
          .where(eq(vehicles.id, request.vehicleId))
          .limit(1);
          
        if (vehicleOwner.length > 0) {
          // Create initial possession for the vehicle owner
          await createInitialVehiclePossession(
            request.vehicleId,
            vehicleOwner[0].partyId,
            createdBy, // Use the person creating the booking as the recorder
            PossessionType.OWNER,
            'OWNERSHIP_TRANSFER',
            'Initial possession created during booking - vehicle had no current possessor'
          );
          
          // Re-fetch the possession
          currentPossession = await getCurrentVehiclePossession(request.vehicleId);
        }
      }
      
      if (currentPossession) {
        console.log("👤 Fetching current holder info for party_id:", currentPossession.possessorPartyId);
        
        // Debug: Check what contact point types exist
        console.log("🔍 Checking contact point types...");
        const contactPointTypes = await db
          .select()
          .from(contactPointType)
          .orderBy(contactPointType.id);
        console.log("📧 Available contact point types:", contactPointTypes);
        
        // Debug: Check all contact points for this party
        console.log("🔍 Checking all contact points for party:", currentPossession.possessorPartyId);
        const allContactPoints = await db
          .select()
          .from(contactPoint)
          .where(eq(contactPoint.partyId, currentPossession.possessorPartyId));
        console.log("📧 All contact points for party:", allContactPoints);
        
        const holderInfo = await db
          .select({
            partyId: party.id,
            firstName: individual.firstName,
            lastName: individual.lastName,
            email: contactPoint.value,
          })
          .from(party)
          .leftJoin(individual, eq(party.id, individual.partyId))
          .leftJoin(contactPoint, and(
            eq(party.id, contactPoint.partyId),
            or(
              eq(contactPoint.contactPointTypeId, 1), // Email (capitalized)
              eq(contactPoint.contactPointTypeId, 4)  // email (lowercase) - duplicate
            )
          ))
          .where(eq(party.id, currentPossession.possessorPartyId))
          .limit(1);
        
        currentHolderInfo = holderInfo[0] || null;
        console.log("👤 Current holder info result:", currentHolderInfo);
      }

      // Create tasks if we have the necessary information
      if (borrowerInfo[0] && vehicleInfo[0]) {
        const borrower = borrowerInfo[0];
        const vehicle = vehicleInfo[0];
        const borrowerName = `${borrower.firstName || ''} ${borrower.lastName || ''}`.trim();
        const borrowerEmail = borrower.email;
        
        let currentHolderName: string | undefined;
        let currentHolderEmail: string | undefined;
        let currentHolderPartyId: number | undefined;
        
        if (currentHolderInfo) {
          currentHolderName = `${currentHolderInfo.firstName || ''} ${currentHolderInfo.lastName || ''}`.trim();
          currentHolderEmail = currentHolderInfo.email || undefined;
          currentHolderPartyId = currentHolderInfo.partyId;
        }

        // Create all handover tasks
        const taskResult = await createVehicleHandoverTasks(
          bookingReference,
          request.vehicleId,
          vehicle.vehicleName || 'Unknown Vehicle',
          borrowerEmail || '',
          borrowerName || 'Unknown User',
          request.borrowerPartyId,
          request.requestedStart,
          request.requestedEnd,
          currentHolderEmail,
          currentHolderName,
          currentHolderPartyId
        );

        if (taskResult.success) {
          console.log(`✅ Successfully created ${taskResult.tasksCreated} handover tasks for booking ${bookingReference}`);
        } else {
          console.error(`❌ Failed to create handover tasks for booking ${bookingReference}:`, taskResult.error);
        }
      } else {
        console.warn(`⚠️ Missing required information to create handover tasks for booking ${bookingReference}`);
      }
    } catch (taskError) {
      // Don't fail the booking creation if task creation fails
      console.error("❌ Error creating handover tasks for booking:", taskError);
    }

    return newEvent[0] as BookingEventRead;
  } catch (error) {
    console.error("Error creating booking:", error);
    throw error;
  }
}

// Update booking status (creates new event)
export async function updateBookingStatus(request: UpdateBookingStatusRequest): Promise<BookingEventRead> {
  try {
    // Get current booking state
    const currentBooking = await getCurrentBookingStatus(request.bookingReference);
    if (!currentBooking) {
      throw new Error('Booking not found');
    }

    const bookingEventData: BookingEventCreate = {
      ...currentBooking,
      eventType: 'STATUS_CHANGED',
      status: request.newStatus,
      changedBy: request.changedBy,
      approvedBy: request.approvedBy,
      changeReason: request.changeReason,
    };

    const newEvent = await db
      .insert(bookingEvents)
      .values(bookingEventData)
      .returning();

    return newEvent[0] as BookingEventRead;
  } catch (error) {
    console.error("Error updating booking status:", error);
    throw error;
  }
}

// Get current booking status from view
export async function getCurrentBookingStatus(bookingReference: string): Promise<CurrentBookingStatus | null> {
  try {
    const current = await db
      .select()
      .from(currentBookingStatusView)
      .where(eq(currentBookingStatusView.bookingReference, bookingReference))
      .limit(1);

    return current[0] as CurrentBookingStatus || null;
  } catch (error) {
    console.error("Error getting current booking status:", error);
    throw error;
  }
}

// Get all booking events for a booking (history)
export async function getBookingHistory(bookingReference: string): Promise<BookingEventRead[]> {
  try {
    const events = await db
      .select()
      .from(bookingEvents)
      .where(eq(bookingEvents.bookingReference, bookingReference))
      .orderBy(asc(bookingEvents.eventTimestamp));

    return events as BookingEventRead[];
  } catch (error) {
    console.error("Error getting booking history:", error);
    throw error;
  }
}

// Get current bookings for a vehicle
export async function getVehicleCurrentBookings(vehicleId: number): Promise<CurrentBookingStatus[]> {
  try {
    const bookings = await db
      .select()
      .from(currentBookingStatusView)
      .where(eq(currentBookingStatusView.vehicleId, vehicleId))
      .orderBy(asc(currentBookingStatusView.requestedStart));

    return bookings as CurrentBookingStatus[];
  } catch (error) {
    console.error("Error getting vehicle current bookings:", error);
    throw error;
  }
}

// Get current bookings for a party
export async function getPartyCurrentBookings(partyId: number): Promise<CurrentBookingStatus[]> {
  try {
    const bookings = await db
      .select()
      .from(currentBookingStatusView)
      .where(eq(currentBookingStatusView.borrowerPartyId, partyId))
      .orderBy(desc(currentBookingStatusView.requestedStart));

    return bookings as CurrentBookingStatus[];
  } catch (error) {
    console.error("Error getting party current bookings:", error);
    throw error;
  }
}

// Check booking availability
export async function checkBookingAvailability(request: BookingAvailabilityRequest): Promise<BookingAvailabilityResponse> {
  try {
    // Check for overlapping confirmed bookings
    const conflictingBookings = await db
      .select()
      .from(currentBookingStatusView)
      .where(
        and(
          eq(currentBookingStatusView.vehicleId, request.vehicleId),
          or(
            eq(currentBookingStatusView.currentStatus, 'CONFIRMED'),
            eq(currentBookingStatusView.currentStatus, 'IN_PROGRESS')
          ),
          // Check for time overlap
          sql`${currentBookingStatusView.confirmedStart} < ${request.requestedEnd}`,
          sql`${currentBookingStatusView.confirmedEnd} > ${request.requestedStart}`
        )
      );

    // Get current possession
    const currentPossessor = await getCurrentVehiclePossession(request.vehicleId);

    return {
      isAvailable: conflictingBookings.length === 0,
      conflictingBookings: conflictingBookings.length > 0 ? conflictingBookings : undefined,
      currentPossessor: currentPossessor || undefined,
    };
  } catch (error) {
    console.error("Error checking booking availability:", error);
    throw error;
  }
}

// ==================== VEHICLE POSSESSION OPERATIONS ====================

// Get current vehicle possession
export async function getCurrentVehiclePossession(vehicleId: number): Promise<CurrentVehiclePossession | null> {
  try {
    const current = await db
      .select()
      .from(currentVehiclePossessionView)
      .where(eq(currentVehiclePossessionView.vehicleId, vehicleId))
      .limit(1);

    return current[0] || null;
  } catch (error) {
    console.error("Error getting current vehicle possession:", error);
    throw error;
  }
}

// Record possession event (vehicle handover)
export async function recordPossessionEvent(data: VehiclePossessionEventCreate): Promise<VehiclePossessionEventRead> {
  try {
    const newEvent = await db
      .insert(vehiclePossessionEvents)
      .values(data)
      .returning();

    return newEvent[0] as VehiclePossessionEventRead;
  } catch (error) {
    console.error("Error recording possession event:", error);
    throw error;
  }
}

// Get possession history for a vehicle
export async function getVehiclePossessionHistory(vehicleId: number): Promise<VehiclePossessionEventRead[]> {
  try {
    const history = await db
      .select()
      .from(vehiclePossessionEvents)
      .where(eq(vehiclePossessionEvents.vehicleId, vehicleId))
      .orderBy(desc(vehiclePossessionEvents.eventTimestamp));

    return history as VehiclePossessionEventRead[];
  } catch (error) {
    console.error("Error getting vehicle possession history:", error);
    throw error;
  }
}

// ==================== HANDOVER OPERATIONS ====================

// Schedule a handover
export async function scheduleHandover(request: ScheduleHandoverRequest): Promise<VehicleHandoverRead> {
  try {
    const handoverData: VehicleHandoverCreate = {
      vehicleId: request.vehicleId,
      handoverType: request.handoverType,
      bookingReference: request.bookingReference,
      fromPartyId: request.fromPartyId,
      toPartyId: request.toPartyId,
      scheduledTime: request.scheduledTime,
      handoverLocation: request.handoverLocation,
      notes: request.notes,
      createdBy: request.createdBy,
    };

    const newHandover = await db
      .insert(vehicleHandovers)
      .values(handoverData)
      .returning();

    // Create initial status event
    await db
      .insert(handoverStatusEvents)
      .values({
        handoverId: newHandover[0].id,
        status: 'SCHEDULED' as const,
        statusTimestamp: request.scheduledTime,
        changedBy: request.createdBy,
        notes: 'Handover scheduled',
      });

    return newHandover[0] as VehicleHandoverRead;
  } catch (error) {
    console.error("Error scheduling handover:", error);
    throw error;
  }
}

// Update handover status
export async function updateHandoverStatus(
  handoverId: number,
  status: HandoverStatus,
  changedBy: number,
  notes?: string
): Promise<HandoverStatusEventRead> {
  try {
    const statusEvent: HandoverStatusEventCreate = {
      handoverId,
      status,
      statusTimestamp: new Date().toISOString(),
      changedBy,
      notes,
    };

    const newStatusEvent = await db
      .insert(handoverStatusEvents)
      .values(statusEvent)
      .returning();

    return newStatusEvent[0] as HandoverStatusEventRead;
  } catch (error) {
    console.error("Error updating handover status:", error);
    throw error;
  }
}

// Get current handover status
export async function getCurrentHandoverStatus(handoverId: number): Promise<CurrentHandoverStatus | null> {
  try {
    const current = await db
      .select()
      .from(currentHandoverStatusView)
      .where(eq(currentHandoverStatusView.handoverId, handoverId))
      .limit(1);

    return current[0] || null;
  } catch (error) {
    console.error("Error getting current handover status:", error);
    throw error;
  }
}

// ==================== INSPECTION OPERATIONS ====================

// Complete an inspection
export async function completeInspection(request: CompleteInspectionRequest): Promise<VehicleInspectionRead> {
  try {
    const inspectionData: VehicleInspectionCreate = {
      vehicleId: request.vehicleId,
      handoverId: request.handoverId,
      inspectorPartyId: request.inspectorPartyId,
      inspectionType: request.inspectionType,
      odometer: request.odometer,
      fuelLevel: request.fuelLevel,
      scratches: request.conditionAssessment.scratches,
      dents: request.conditionAssessment.dents,
      tires: request.conditionAssessment.tires,
      lights: request.conditionAssessment.lights,
      cleanliness: request.conditionAssessment.cleanliness,
      seats: request.conditionAssessment.seats,
      dashboardControls: request.conditionAssessment.dashboardControls,
      odors: request.conditionAssessment.odors,
      overallCondition: request.conditionAssessment.overallCondition,
      knownIssues: request.knownIssues,
      newDamage: request.newDamage,
      itemsInVehicle: request.itemsInVehicle,
      inspectorSignature: request.inspectorSignature,
      inspectionCompletedAt: new Date().toISOString(),
      relatedInspectionId: request.relatedInspectionId,
    };

    const newInspection = await db
      .insert(vehicleInspectionsImmutable)
      .values(inspectionData)
      .returning();

    return newInspection[0] as VehicleInspectionRead;
  } catch (error) {
    console.error("Error completing inspection:", error);
    throw error;
  }
}

// Get inspections for a handover
export async function getHandoverInspections(handoverId: number): Promise<VehicleInspectionRead[]> {
  try {
    const inspections = await db
      .select()
      .from(vehicleInspectionsImmutable)
      .where(eq(vehicleInspectionsImmutable.handoverId, handoverId))
      .orderBy(asc(vehicleInspectionsImmutable.createdAt));

    return inspections as VehicleInspectionRead[];
  } catch (error) {
    console.error("Error getting handover inspections:", error);
    throw error;
  }
}

// Add inspection photo
export async function addInspectionPhoto(data: InspectionPhotoCreate): Promise<InspectionPhotoRead> {
  try {
    const newPhoto = await db
      .insert(inspectionPhotos)
      .values(data)
      .returning();

    return newPhoto[0] as InspectionPhotoRead;
  } catch (error) {
    console.error("Error adding inspection photo:", error);
    throw error;
  }
}

// ==================== ISSUE MANAGEMENT ====================

// Report a handover issue
export async function reportHandoverIssue(data: HandoverIssueCreate): Promise<HandoverIssueRead> {
  try {
    const newIssue = await db
      .insert(handoverIssues)
      .values(data)
      .returning();

    return newIssue[0] as HandoverIssueRead;
  } catch (error) {
    console.error("Error reporting handover issue:", error);
    throw error;
  }
}

// Resolve a handover issue
export async function resolveHandoverIssue(data: HandoverIssueResolutionCreate): Promise<HandoverIssueResolutionRead> {
  try {
    const newResolution = await db
      .insert(handoverIssueResolutions)
      .values(data)
      .returning();

    return newResolution[0] as HandoverIssueResolutionRead;
  } catch (error) {
    console.error("Error resolving handover issue:", error);
    throw error;
  }
}

// ==================== FORM SUBMISSION ACTIONS (NEW SYSTEM) ====================

// Server action for creating new bookings with immutable system
export async function addBooking(_: any, formData: FormData) {
  try {
    const vehicleId = Number(formData.get("vehicle_id"));
    const borrowerPartyId = Number(formData.get("party_id"));
    const requestedStart = (formData.get("start_datetime") as string)?.trim();
    const requestedEnd = (formData.get("end_datetime") as string)?.trim();
    const purpose = (formData.get("purpose") as string)?.trim();
    const notes = (formData.get("notes") as string)?.trim();
    const createdBy = Number(formData.get("created_by") || borrowerPartyId);

    // Validation
    if (!vehicleId || !borrowerPartyId || !requestedStart || !requestedEnd) {
      return {
        success: false,
        errors: {
          general: ["Missing required fields"]
        }
      };
    }

    // Create booking using immutable system
    const booking = await createBooking(
      {
        vehicleId,
        borrowerPartyId,
        requestedStart,
        requestedEnd,
        purpose: purpose || undefined,
        notes: notes || undefined,
      },
      createdBy
    );

    return {
      success: true,
      message: "Booking created successfully.",
      bookingReference: booking.bookingReference,
      bookingId: booking.id,
    };
  } catch (error: any) {
    console.error("Error creating booking:", error);
    return {
      success: false,
      errors: {
        general: [error.message || "Failed to create booking"]
      }
    };
  }
}




