"use server";

import { db } from "@/db";
import { eq, sql, and, desc, like, or, gte, lte } from "drizzle-orm";
import {
  groups,
  groupMemberships,
  groupMemberRoles,
  groupSharedVehicles,
  vehicles,
  vehicleModel,
  vehicleMake,
  cities,
  countries,
  party,
  individual,
  bookings,
  partyStatus
} from "@/drizzle/schema";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { CommunityGroup, GroupDetails, GroupSearchFilters, GroupStats } from "@/types/community";
import { GroupRoleEnum } from "@/types/groups";
import { PartyStatusEnum } from "@/types/party";
import { CompanyPurposeEnum } from "@/types/company";

/**
 * Get groups where the current user is a member
 */
export async function getUserGroups(): Promise<CommunityGroup[]> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      console.log("No user party ID found, returning empty groups");
      return [];
    }

    const userGroups = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberRole: groupMemberRoles.role,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .innerJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          eq(groupMemberships.partyId, partyId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupMemberRoles,
        and(
          eq(groups.id, groupMemberRoles.groupId),
          eq(groupMemberRoles.partyId, partyId),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupMemberships as typeof groupMemberships & { alias: "all_members" },
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      )
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose,
        groupMemberRoles.role
      )
      .orderBy(desc(groups.createdAt));

    return userGroups.map((group: any) => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER",
      memberRole: group.memberRole || GroupRoleEnum.MEMBER
    }));
  } catch (error) {
    console.error("Error fetching user groups:", error);
    return [];
  }
}

/**
 * Get all public groups with optional filtering
 */
export async function getPublicGroups(filters?: GroupSearchFilters): Promise<CommunityGroup[]> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    let query = db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      );

    // Apply filters
    const conditions = [];
    
    if (filters?.location) {
      conditions.push(
        or(
          like(cities.name, `%${filters.location}%`),
          like(countries.name, `%${filters.location}%`)
        )
      );
    }

    if (filters?.purpose) {
      conditions.push(eq(groups.initialPurpose, filters.purpose as CompanyPurposeEnum));
    }

    if (filters?.isManaged !== undefined) {
      conditions.push(eq(groups.isManaged, filters.isManaged));
    }

    if (conditions.length > 0) {
      query = (query as any).where(and(...conditions));
    }

    const publicGroups = await query
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose
      )
      .having(
        and(
          filters?.memberCount?.min ? gte(sql`count(distinct ${groupMemberships.partyId})`, filters.memberCount.min) : sql`true`,
          filters?.memberCount?.max ? lte(sql`count(distinct ${groupMemberships.partyId})`, filters.memberCount.max) : sql`true`,
          filters?.vehicleCount?.min ? gte(sql`count(distinct ${groupSharedVehicles.vehicleId})`, filters.vehicleCount.min) : sql`true`,
          filters?.vehicleCount?.max ? lte(sql`count(distinct ${groupSharedVehicles.vehicleId})`, filters.vehicleCount.max) : sql`true`
        )
      )
      .orderBy(desc(groups.createdAt));

    return publicGroups.map(group => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER"
    }));
  } catch (error) {
    console.error("Error fetching public groups:", error);
    return [];
  }
}

/**
 * Get detailed information about a specific group
 */
export async function getGroupDetails(groupId: number): Promise<GroupDetails | null> {
  try {
    // Get group basic information
    const groupInfo = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        createdBy: groups.createdBy,
        creatorFirstName: individual.firstName,
        creatorLastName: individual.lastName,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(party, eq(groups.creator, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(groups.id, groupId))
      .limit(1);

    if (groupInfo.length === 0) {
      return null;
    }

    const group = groupInfo[0];

    // Get group members
    const members = await db
      .select({
        id: individual.id,
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        role: groupMemberRoles.role,
        joinedAt: groupMemberships.effectiveFrom,
      })
      .from(groupMemberships)
      .innerJoin(party, eq(groupMemberships.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(
        groupMemberRoles,
        and(
          eq(groupMemberRoles.groupId, groupMemberships.groupId),
          eq(groupMemberRoles.partyId, groupMemberships.partyId),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      )
      .where(
        and(
          eq(groupMemberships.groupId, groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      );

    // Get group vehicles
    const groupVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        color: vehicles.color,
        isActive: vehicles.isActive,
        manufacturingYear: vehicles.manufacturingYear,
        make: vehicleMake.name,
        model: vehicleModel.model,
      })
      .from(groupSharedVehicles)
      .innerJoin(vehicles, eq(groupSharedVehicles.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(groupSharedVehicles.groupId, groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      );

    // Get upcoming bookings
    const upcomingBookings = await db
      .select({
        id: bookings.id,
        reference: bookings.reference,
        startDatetime: bookings.startDatetime,
        endDatetime: bookings.endDatetime,
        status: bookings.status,
        vehicleId: vehicles.id,
        make: vehicleMake.name,
        model: vehicleModel.model,
        memberFirstName: individual.firstName,
        memberLastName: individual.lastName,
      })
      .from(bookings)
      .innerJoin(vehicles, eq(bookings.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(party, eq(bookings.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .innerJoin(groupSharedVehicles, eq(vehicles.id, groupSharedVehicles.vehicleId))
      .where(
        and(
          eq(groupSharedVehicles.groupId, groupId),
          sql`${bookings.startDatetime} >= NOW()`,
          sql`${bookings.status} IN ('CONFIRMED', 'PENDING')`
        )
      )
      .orderBy(bookings.startDatetime)
      .limit(10);

    return {
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER",
      createdBy: {
        id: group.createdBy,
        name: group.creatorFirstName && group.creatorLastName 
          ? `${group.creatorFirstName} ${group.creatorLastName}` 
          : "Unknown"
      },
      members: members.map(member => ({
        id: member.id,
        partyId: member.partyId,
        name: `${member.firstName} ${member.lastName}`,
        role: member.role || GroupRoleEnum.MEMBER,
        avatar: "/placeholder.svg?height=40&width=40",
        joinedAt: member.joinedAt || new Date().toISOString()
      })),
      vehicles: groupVehicles.map(vehicle => ({
        id: vehicle.id,
        name: `${vehicle.make} ${vehicle.model}`,
        registration: vehicle.vehicleRegistration || "Unknown",
        status: !vehicle.isActive ? "maintenance" : "available" as const,
        image: "/placeholder.svg?height=60&width=100",
        make: vehicle.make || "Unknown",
        model: vehicle.model || "Unknown",
        year: vehicle.manufacturingYear || undefined,
        color: vehicle.color || undefined
      })),
      upcomingBookings: upcomingBookings.map(booking => ({
        id: booking.id,
        vehicle: `${booking.make} ${booking.model}`,
        member: `${booking.memberFirstName} ${booking.memberLastName}`,
        startDate: booking.startDatetime || new Date().toISOString(),
        endDate: booking.endDatetime || new Date().toISOString(),
        status: booking.status || "PENDING"
      }))
    };
  } catch (error) {
    console.error("Error fetching group details:", error);
    return null;
  }
}

/**
 * Get community statistics
 */
export async function getCommunityStats(): Promise<GroupStats> {
  try {
    const stats = await db
      .select({
        totalGroups: sql<number>`cast(count(distinct ${groups.id}) as integer)`,
        totalMembers: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        totalVehicles: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
        activeBookings: sql<number>`cast(count(distinct ${bookings.id}) as integer)`,
      })
      .from(groups)
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .leftJoin(bookings, 
        and(
          eq(groupSharedVehicles.vehicleId, bookings.vehicleId),
          sql`${bookings.status} IN ('CONFIRMED', 'PENDING')`,
          sql`${bookings.endDatetime} >= NOW()`
        )
      );

    return stats[0] || {
      totalGroups: 0,
      totalMembers: 0,
      totalVehicles: 0,
      activeBookings: 0
    };
  } catch (error) {
    console.error("Error fetching community stats:", error);
    return {
      totalGroups: 0,
      totalMembers: 0,
      totalVehicles: 0,
      activeBookings: 0
    };
  }
}

/**
 * Search groups by name or description
 */
export async function searchGroups(searchTerm: string): Promise<CommunityGroup[]> {
  try {
    const searchResults = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .where(
        or(
          like(groups.name, `%${searchTerm}%`),
          like(groups.description, `%${searchTerm}%`),
          like(cities.name, `%${searchTerm}%`),
          like(countries.name, `%${searchTerm}%`)
        )
      )
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose
      )
      .orderBy(desc(groups.createdAt))
      .limit(20);

    return searchResults.map(group => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER"
    }));
  } catch (error) {
    console.error("Error searching groups:", error);
    return [];
  }
} 