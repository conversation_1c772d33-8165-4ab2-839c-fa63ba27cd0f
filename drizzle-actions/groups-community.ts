"use server";

import { db } from "@/db";
import { eq, sql, and, desc, like, or, gte, lte, inArray } from "drizzle-orm";
import {
  groups,
  groupMemberships,
  groupMemberRoles,
  groupSharedVehicles,
  vehicles,
  vehicleModel,
  vehicleMake,
  cities,
  countries,
  party,
  individual,
  bookings,
  partyStatus,
  groupsCommunityView,
  userGroupMembershipsView,
  groupsDetailedView,
  groupMembershipInvitations
} from "@/drizzle/schema";
import { getCurrentUserNoCache } from "@/lib/amplifyServerUtils";
import { CommunityGroup, GroupDetails, GroupSearchFilters, GroupStats } from "@/types/community";
import { GroupRoleEnum } from "@/types/groups";
import { PartyStatusEnum } from "@/types/party";

/**
 * Get groups where the current user is a member
 */
export async function getUserGroups(): Promise<CommunityGroup[]> {
  try {
    const userAttributes = await getCurrentUserNoCache();
    const partyId = +(userAttributes?.["custom:db_id"] || 0);

    if (!partyId) {
      console.log("No user party ID found, returning empty groups");
      return [];
    }

    const userGroups = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberRole: groupMemberRoles.role,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .innerJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          eq(groupMemberships.partyId, partyId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupMemberRoles,
        and(
          eq(groups.id, groupMemberRoles.groupId),
          eq(groupMemberRoles.partyId, partyId),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      )
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose,
        groupMemberRoles.role
      )
      .orderBy(desc(groups.createdAt));

    return userGroups.map(group => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER",
      memberRole: group.memberRole || GroupRoleEnum.MEMBER
    }));
  } catch (error) {
    console.error("Error fetching user groups:", error);
    return [];
  }
}

/**
 * Get all public groups with optional filtering
 * 
 * Performance Note: For better performance at scale, consider using the groupsCommunityView
 * instead of direct table queries. This can be enabled by setting useView parameter to true.
 */
export async function getPublicGroups(filters?: GroupSearchFilters, useView: boolean = false): Promise<CommunityGroup[]> {
  try {
    const publicGroups = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      )
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose
      )
      .orderBy(desc(groups.createdAt));

    return publicGroups.map(group => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER"
    }));
  } catch (error) {
    console.error("Error fetching public groups:", error);
    return [];
  }
}

/**
 * Get detailed information about a specific group
 */
export async function getGroupDetails(groupId: number): Promise<GroupDetails | null> {
  try {
    // Get group basic information
    const groupInfo = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        createdBy: groups.createdBy,
        creatorFirstName: individual.firstName,
        creatorLastName: individual.lastName,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(party, eq(groups.creator, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(groups.id, groupId))
      .limit(1);

    if (groupInfo.length === 0) {
      return null;
    }

    const group = groupInfo[0];

    // Get group members
    const members = await db
      .select({
        id: individual.id,
        partyId: party.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        role: groupMemberRoles.role,
        joinedAt: groupMemberships.effectiveFrom,
      })
      .from(groupMemberships)
      .innerJoin(party, eq(groupMemberships.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .leftJoin(
        groupMemberRoles,
        and(
          eq(groupMemberRoles.groupId, groupMemberships.groupId),
          eq(groupMemberRoles.partyId, groupMemberships.partyId),
          sql`${groupMemberRoles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberRoles.effectiveTo} IS NULL`,
            sql`${groupMemberRoles.effectiveTo} > NOW()`
          )
        )
      )
      .where(
        and(
          eq(groupMemberships.groupId, groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      );

    // Get group vehicles
    const groupVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        color: vehicles.color,
        isActive: vehicles.isActive,
        manufacturingYear: vehicles.manufacturingYear,
        make: vehicleMake.name,
        model: vehicleModel.model,
      })
      .from(groupSharedVehicles)
      .innerJoin(vehicles, eq(groupSharedVehicles.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(groupSharedVehicles.groupId, groupId),
          eq(groupSharedVehicles.isActive, true),
          sql`${groupSharedVehicles.effectiveFrom} <= NOW()`,
          or(
            sql`${groupSharedVehicles.effectiveTo} IS NULL`,
            sql`${groupSharedVehicles.effectiveTo} > NOW()`
          )
        )
      );

    // Get upcoming bookings
    console.log(`Fetching upcoming bookings for group ${groupId}...`);
    const upcomingBookings = await db
      .select({
        id: bookings.id,
        reference: bookings.reference,
        startDatetime: bookings.startDatetime,
        endDatetime: bookings.endDatetime,
        status: bookings.status,
        vehicleId: vehicles.id,
        make: vehicleMake.name,
        model: vehicleModel.model,
        memberFirstName: individual.firstName,
        memberLastName: individual.lastName,
      })
      .from(bookings)
      .innerJoin(vehicles, eq(bookings.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .innerJoin(party, eq(bookings.partyId, party.id))
      .innerJoin(individual, eq(party.id, individual.partyId))
      .innerJoin(groupSharedVehicles, eq(vehicles.id, groupSharedVehicles.vehicleId))
      .where(
        and(
          eq(groupSharedVehicles.groupId, groupId),
          eq(groupSharedVehicles.isActive, true),
          gte(bookings.endDatetime, new Date().toISOString()),
          inArray(bookings.status, ['CONFIRMED', 'PENDING'])
        )
      )
      .orderBy(bookings.startDatetime)
      .limit(10);
    
    console.log(`Found ${upcomingBookings.length} upcoming bookings for group ${groupId}:`, upcomingBookings);

    return {
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER",
      createdBy: {
        id: group.createdBy,
        name: group.creatorFirstName && group.creatorLastName 
          ? `${group.creatorFirstName} ${group.creatorLastName}` 
          : "Unknown"
      },
      members: members.map(member => ({
        id: member.id,
        partyId: member.partyId,
        name: `${member.firstName} ${member.lastName}`,
        role: member.role || GroupRoleEnum.MEMBER,
        avatar: "/placeholder.svg?height=40&width=40",
        joinedAt: member.joinedAt || new Date().toISOString()
      })),
      vehicles: groupVehicles.map(vehicle => ({
        id: vehicle.id,
        name: `${vehicle.make} ${vehicle.model}`,
        registration: vehicle.vehicleRegistration || "Unknown",
        status: !vehicle.isActive ? "maintenance" : "available" as const,
        image: "/placeholder.svg?height=60&width=100",
        make: vehicle.make || "Unknown",
        model: vehicle.model || "Unknown",
        year: vehicle.manufacturingYear || undefined,
        color: vehicle.color || undefined
      })),
      upcomingBookings: upcomingBookings.map(booking => ({
        id: booking.id,
        vehicle: `${booking.make} ${booking.model}`,
        member: `${booking.memberFirstName} ${booking.memberLastName}`,
        startDate: booking.startDatetime || new Date().toISOString(),
        endDate: booking.endDatetime || new Date().toISOString(),
        status: booking.status || "PENDING"
      }))
    };
  } catch (error) {
    console.error("Error fetching group details:", error);
    return null;
  }
}

/**
 * Get community statistics
 */
export async function getCommunityStats(): Promise<GroupStats> {
  try {
    const stats = await db
      .select({
        totalGroups: sql<number>`cast(count(distinct ${groups.id}) as integer)`,
        totalMembers: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        totalVehicles: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
        activeBookings: sql<number>`cast(count(distinct ${bookings.id}) as integer)`,
      })
      .from(groups)
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .leftJoin(bookings, 
        and(
          eq(groupSharedVehicles.vehicleId, bookings.vehicleId),
          sql`${bookings.status} IN ('CONFIRMED', 'PENDING')`,
          sql`${bookings.endDatetime} >= NOW()`
        )
      );

    return stats[0] || {
      totalGroups: 0,
      totalMembers: 0,
      totalVehicles: 0,
      activeBookings: 0
    };
  } catch (error) {
    console.error("Error fetching community stats:", error);
    return {
      totalGroups: 0,
      totalMembers: 0,
      totalVehicles: 0,
      activeBookings: 0
    };
  }
}

/**
 * Search groups by name or description
 */
export async function searchGroups(searchTerm: string): Promise<CommunityGroup[]> {
  try {
    const searchResults = await db
      .select({
        id: groups.id,
        name: groups.name,
        description: groups.description,
        cityName: cities.name,
        countryName: countries.name,
        createdAt: groups.createdAt,
        isManaged: groups.isManaged,
        initialPurpose: groups.initialPurpose,
        memberCount: sql<number>`cast(count(distinct ${groupMemberships.partyId}) as integer)`,
        vehicleCount: sql<number>`cast(count(distinct ${groupSharedVehicles.vehicleId}) as integer)`,
      })
      .from(groups)
      .leftJoin(cities, eq(groups.cityId, cities.id))
      .leftJoin(countries, eq(groups.countryId, countries.id))
      .leftJoin(
        groupMemberships,
        and(
          eq(groups.id, groupMemberships.groupId),
          sql`${groupMemberships.effectiveFrom} <= NOW()`,
          or(
            sql`${groupMemberships.effectiveTo} IS NULL`,
            sql`${groupMemberships.effectiveTo} > NOW()`
          )
        )
      )
      .leftJoin(
        groupSharedVehicles,
        and(
          eq(groups.id, groupSharedVehicles.groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .where(
        or(
          like(groups.name, `%${searchTerm}%`),
          like(groups.description, `%${searchTerm}%`),
          like(cities.name, `%${searchTerm}%`),
          like(countries.name, `%${searchTerm}%`)
        )
      )
      .groupBy(
        groups.id,
        groups.name,
        groups.description,
        cities.name,
        countries.name,
        groups.createdAt,
        groups.isManaged,
        groups.initialPurpose
      )
      .orderBy(desc(groups.createdAt))
      .limit(20);

    return searchResults.map(group => ({
      id: group.id,
      name: group.name || "Unnamed Group",
      description: group.description || "No description available",
      members: group.memberCount || 0,
      vehicles: group.vehicleCount || 0,
      location: group.cityName && group.countryName 
        ? `${group.cityName}, ${group.countryName}` 
        : group.cityName || group.countryName || "Unknown",
      image: "/placeholder.svg?height=60&width=60",
      createdAt: group.createdAt || new Date().toISOString(),
      isManaged: group.isManaged || false,
      initialPurpose: group.initialPurpose || "OTHER"
    }));
  } catch (error) {
    console.error("Error searching groups:", error);
    return [];
  }
}

/**
 * Get group vehicles with their current booking status
 */
export async function getGroupVehiclesWithStatus(groupId: number) {
  try {
    // Get current time for status checking
    const now = new Date();

    const groupVehicles = await db
      .select({
        id: vehicles.id,
        name: sql<string>`concat(${vehicleMake.name}, ' ', ${vehicleModel.model})`,
        vehicleRegistration: vehicles.vehicleRegistration,
        color: vehicles.color,
        isActive: vehicles.isActive,
        make: vehicleMake.name,
        model: vehicleModel.model,
        manufacturingYear: vehicles.manufacturingYear,
        currentBookingId: bookings.id,
        bookingStatus: bookings.status,
        bookingStart: bookings.startDatetime,
        bookingEnd: bookings.endDatetime,
      })
      .from(groupSharedVehicles)
      .innerJoin(vehicles, eq(groupSharedVehicles.vehicleId, vehicles.id))
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(
        bookings,
        and(
          eq(vehicles.id, bookings.vehicleId),
          sql`${bookings.endDatetime} >= NOW()`,
          sql`${bookings.status} IN ('CONFIRMED', 'PENDING')`
        )
      )
      .where(
        and(
          eq(groupSharedVehicles.groupId, groupId),
          eq(groupSharedVehicles.isActive, true)
        )
      )
      .orderBy(vehicles.id);

    return groupVehicles.map(vehicle => {
      let status = "available";
      
      if (!vehicle.isActive) {
        status = "maintenance";
      } else if (vehicle.currentBookingId) {
        const now = new Date();
        const bookingStart = vehicle.bookingStart ? new Date(vehicle.bookingStart) : null;
        const bookingEnd = vehicle.bookingEnd ? new Date(vehicle.bookingEnd) : null;
        
        if (bookingStart && bookingEnd) {
          if (now >= bookingStart && now <= bookingEnd) {
            status = "in-use";
          } else if (now < bookingStart) {
            status = "available"; // Future booking, still available for now
          }
        } else {
          status = "in-use"; // Default to in-use if we have a booking but unclear timing
        }
      }

      return {
        id: vehicle.id,
        name: vehicle.name || `${vehicle.make} ${vehicle.model}`,
        registration: vehicle.vehicleRegistration,
        status,
        image: "/placeholder.svg?height=60&width=100",
        make: vehicle.make,
        model: vehicle.model,
        year: vehicle.manufacturingYear,
        color: vehicle.color
      };
    });
  } catch (error) {
    console.error("Error fetching group vehicles with status:", error);
    return [];
  }
}

/**
 * Get pending invitations for a group
 */
export async function getGroupInvitations(groupId: number) {
  try {
    const invitations = await db
      .select({
        id: groupMembershipInvitations.id,
        email: groupMembershipInvitations.email,
        firstName: groupMembershipInvitations.firstName,
        lastName: groupMembershipInvitations.lastName,
        status: groupMembershipInvitations.status,
        createdAt: groupMembershipInvitations.createdAt,
        invitedBy: groupMembershipInvitations.invitedBy,
        inviterFirstName: individual.firstName,
        inviterLastName: individual.lastName,
      })
      .from(groupMembershipInvitations)
      .leftJoin(party, eq(groupMembershipInvitations.invitedBy, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(groupMembershipInvitations.groupId, groupId))
      .orderBy(desc(groupMembershipInvitations.createdAt));

    return invitations.map(invitation => ({
      id: invitation.id,
      email: invitation.email,
      name: invitation.firstName && invitation.lastName 
        ? `${invitation.firstName} ${invitation.lastName}` 
        : invitation.email,
      firstName: invitation.firstName,
      lastName: invitation.lastName,
      fraction: 0.1, // Default fraction for new members
      status: invitation.status,
      sentAt: invitation.createdAt,
      invitedBy: invitation.inviterFirstName && invitation.inviterLastName
        ? `${invitation.inviterFirstName} ${invitation.inviterLastName}`
        : "Unknown"
    }));
  } catch (error) {
    console.error("Error fetching group invitations:", error);
    return [];
  }
}

/**
 * Cancel a group membership invitation
 */
export async function cancelInvitation(invitationId: number) {
  try {
    // For now, we'll delete the invitation record
    // In a more complete implementation, you might want to update status instead
    const result = await db
      .delete(groupMembershipInvitations)
      .where(eq(groupMembershipInvitations.id, invitationId));

    return {
      success: true,
      message: "Invitation cancelled successfully"
    };
  } catch (error) {
    console.error("Error cancelling invitation:", error);
    return {
      success: false,
      message: "Failed to cancel invitation"
    };
  }
} 