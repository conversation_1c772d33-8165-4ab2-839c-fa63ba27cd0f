import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getUserGroups } from "@/lib/serverUserAttributes";

const ADMIN_PATHS = ["/admin"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  try {
    const groups = (await getUserGroups()) || [];
    const isAdmin = groups.includes("ADMINS");

    // If user is an admin, restrict access to admin paths only
    if (isAdmin && !ADMIN_PATHS.some((path) => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL("/admin/dashboard", request.url));
    }

    // If user is not an admin, prevent access to admin paths
    if (!isAdmin && ADMIN_PATHS.some((path) => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL("/home", request.url));
    }

    // Allow request to proceed for valid paths
    return NextResponse.next();
  } catch (err) {
    // Log error and redirect to home on failure
    console.error("Error fetching user groups:", err);
    return NextResponse.redirect(new URL("/home", request.url));
  }
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"], // Apply to all routes except static assets and API
};
