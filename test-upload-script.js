// Test script for document upload functionality
// Run this in the browser console on your app

// Test 1: Check if DocumentUpload function is available
console.log("Testing DocumentUpload function...");

// Test 2: Create a test file
function createTestFile() {
  const canvas = document.createElement('canvas');
  canvas.width = 100;
  canvas.height = 100;
  const ctx = canvas.getContext('2d');
  ctx.fillStyle = 'red';
  ctx.fillRect(0, 0, 100, 100);
  
  return new Promise((resolve) => {
    canvas.toBlob((blob) => {
      const file = new File([blob], 'test-image.png', { type: 'image/png' });
      resolve(file);
    });
  });
}

// Test 3: Test upload function
async function testUpload() {
  try {
    const testFile = await createTestFile();
    console.log('Created test file:', testFile);
    
    // Import the DocumentUpload function
    const { DocumentUpload } = await import('/lib/utils.ts');
    
    console.log('Starting upload...');
    const result = await DocumentUpload(testFile, 'test-uploads');
    console.log('Upload result:', result);
    
    return result;
  } catch (error) {
    console.error('Upload test failed:', error);
    return null;
  }
}

// Test 4: Test application document upload action
async function testApplicationUpload() {
  try {
    const { uploadApplicationDocumentsAction } = await import('/actions/applications.ts');
    
    const testDocuments = [
      {
        documentType: 'test_document',
        documentUrl: 'test-uploads/test-file.png'
      }
    ];
    
    // Note: This will fail without a valid applicationId
    const result = await uploadApplicationDocumentsAction(1, testDocuments);
    console.log('Application upload result:', result);
    
    return result;
  } catch (error) {
    console.error('Application upload test failed:', error);
    return null;
  }
}

// Run tests
console.log('=== Document Upload Tests ===');
console.log('1. Run testUpload() to test file upload to S3');
console.log('2. Run testApplicationUpload() to test database save');
console.log('3. Check network tab for upload requests');
console.log('4. Check browser storage for authentication');

// Make functions available globally
window.testUpload = testUpload;
window.testApplicationUpload = testApplicationUpload;
window.createTestFile = createTestFile;
