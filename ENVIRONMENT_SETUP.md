# Environment Variables Setup for PWA Push Notifications

## Required Environment Variables

Add these to your `.env.local` file:

```bash
# Push Notifications VAPID Keys
VAPID_PUBLIC_KEY=BDRv-RXm2xs_IABy2XaYqct_4EZ3jYS4IVJ-DZnc34yhb9sRP2NDNN0Ox7nr5vfWzvUZHuV75u9HpFaNDtoJmAc
VAPID_PRIVATE_KEY=0cBFezeZhPbDReh_YoQv2DpsYwrWazjQ4COPll3I1Do
VAPID_EMAIL=mailto:<EMAIL>
NEXT_PUBLIC_VAPID_PUBLIC_KEY=BDRv-RXm2xs_IABy2XaYqct_4EZ3jYS4IVJ-DZnc34yhb9sRP2NDNN0Ox7nr5vfWzvUZHuV75u9HpFaNDtoJmAc

# Push Notification Settings
NEXT_PUBLIC_NOTIFICATION_TYPES=booking,group,maintenance,financial,compliance
```

## Production Environment

For production deployment (Amplify, Vercel, etc.), add these environment variables:

- `VAPID_PRIVATE_KEY` (keep secret!)
- `VAPID_EMAIL` 
- `NEXT_PUBLIC_VAPID_PUBLIC_KEY` (public)
- `NEXT_PUBLIC_NOTIFICATION_TYPES` (public)

## Generating New VAPID Keys

If you need to generate new VAPID keys:

```bash
npx web-push generate-vapid-keys
```

⚠️ **Important**: VAPID keys are tied to your domain. If you change keys, all existing subscriptions will become invalid. 