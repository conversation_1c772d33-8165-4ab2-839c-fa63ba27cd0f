name: Cypress Tests Without Cloud

on:
  push:
    branches: [dev]
  pull_request:
    branches: [dev]

jobs:
  cypress-run:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm i

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
        env:
          AWS_DEFAULT_REGION: ""  # Avoid conflicts

      - name: Debug AWS Environment
        run: |
          echo "AWS_REGION: $AWS_REGION"
          echo "AWS_PROFILE: $AWS_PROFILE"
          echo "AWS_ACCESS_KEY_ID is set: $(if [ -n \"$AWS_ACCESS_KEY_ID\" ]; then echo 'yes'; else echo 'no'; fi)"
          aws configure list

      - name: Generate custom outputs
        run: npx ampx generate outputs --branch dev --app-id ${{ secrets.AWS_APP_ID }} --format json --outputs-version 1.4 --debug
        if: always()
        env:
          AWS_PROFILE: ""  # Unset to avoid conflicts

      - name: Build Next.js app
        run: npm run build

      - name: Run Cypress tests
        uses: cypress-io/github-action@v6
        with:
          browser: chrome
          start: npm start
          wait-on: 'http://localhost:3000'
        env:
          CYPRESS_BASE_URL: 'http://localhost:3000'
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          
      - name: Upload Cypress tests report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-report
          path: cypress/mochawesome-report
          if-no-files-found: ignore

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots
          if-no-files-found: ignore

      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-videos
          path: cypress/videos
          if-no-files-found: ignore