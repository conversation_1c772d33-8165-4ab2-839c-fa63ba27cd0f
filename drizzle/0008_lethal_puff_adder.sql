ALTER TABLE "cities" ADD COLUMN "country_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "cities" ADD COLUMN "province_state_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "cities" ADD CONSTRAINT "cities_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "cities" ADD CONSTRAINT "cities_province_state_id_province_state_id_fk" FOREIGN KEY ("province_state_id") REFERENCES "public"."province_state"("id") ON DELETE no action ON UPDATE no action;