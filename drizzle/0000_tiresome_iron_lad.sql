CREATE TYPE "public"."application_status" AS ENUM('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'APPROVED', 'REJECTED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."application_type" AS ENUM('CO_OWNERSHIP', 'LEASE');--> statement-breakpoint
CREATE TYPE "public"."audienceenum" AS ENUM('BUSINESS', 'E_HAILING', 'CONSUMER');--> statement-breakpoint
CREATE TYPE "public"."body_type" AS ENUM('sedan', 'hatchback', 'suv', 'truck', 'coupe', 'convertible', 'wagon', 'van', 'minivan', 'other');--> statement-breakpoint
CREATE TYPE "public"."bookingstatus" AS ENUM('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."cleanlinesslevel" AS ENUM('clean', 'acceptable', 'dirty');--> statement-breakpoint
CREATE TYPE "public"."commentpriority" AS ENUM('LOW', 'MEDIUM', 'HIGH');--> statement-breakpoint
CREATE TYPE "public"."commentstatus" AS ENUM('OPEN', 'RESOLVED', 'DELETED');--> statement-breakpoint
CREATE TYPE "public"."company_purpose" AS ENUM('RIDE_SHARE', 'GROUP_MONETIZATION', 'FLEET', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."companyownershipinviteenum" AS ENUM('SENT', 'DECLINED', 'ACCEPTED');--> statement-breakpoint
CREATE TYPE "public"."companytypeenum" AS ENUM('PRIVATE_COMPANY', 'NON_PROFIT', 'PARTNERSHIP', 'COOPERATIVE');--> statement-breakpoint
CREATE TYPE "public"."compliance_category" AS ENUM('IDENTITY', 'DRIVING', 'FINANCIAL', 'ADDRESS', 'VEHICLE', 'BUSINESS', 'VERIFICATION', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."conditionenum" AS ENUM('new', 'used');--> statement-breakpoint
CREATE TYPE "public"."conditionlevel" AS ENUM('none', 'minor', 'major');--> statement-breakpoint
CREATE TYPE "public"."dashboardcondition" AS ENUM('working', 'partial', 'issues');--> statement-breakpoint
CREATE TYPE "public"."disputestatus" AS ENUM('OPEN', 'RESOLVED', 'DELETED');--> statement-breakpoint
CREATE TYPE "public"."disputetype" AS ENUM('BOOKING', 'vEEHICLE_DAMAGE', 'VEHICLE_MAINTENANCE', 'MAINTENANCE_COST_DISPUTE', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."document_status" AS ENUM('PENDING', 'VERIFIED', 'REJECTED', 'EXPIRED');--> statement-breakpoint
CREATE TYPE "public"."documenttype" AS ENUM('registration', 'insurance', 'inspection', 'other');--> statement-breakpoint
CREATE TYPE "public"."drivetrain" AS ENUM('fwd', 'rwd', 'awd', '4wd', 'other');--> statement-breakpoint
CREATE TYPE "public"."duration_type" AS ENUM('TERM', 'INFINITE');--> statement-breakpoint
CREATE TYPE "public"."finance_method" AS ENUM('CASH', 'FINANCE', 'CASH_AND_FINANCE');--> statement-breakpoint
CREATE TYPE "public"."fuel_type" AS ENUM('petrol', 'diesel', 'electric', 'hybrid', 'gas', 'other');--> statement-breakpoint
CREATE TYPE "public"."generalcondition" AS ENUM('good', 'fair', 'poor');--> statement-breakpoint
CREATE TYPE "public"."group_role" AS ENUM('ADMIN', 'MEMBER');--> statement-breakpoint
CREATE TYPE "public"."lightscondition" AS ENUM('working', 'partial', 'broken');--> statement-breakpoint
CREATE TYPE "public"."listingtypeenum" AS ENUM('SHORT_TERM_LEASE_OUT', 'LONG_TERM_LEASE_OUT', 'CO_OWNERSHIP_SALE');--> statement-breakpoint
CREATE TYPE "public"."odorlevel" AS ENUM('none', 'mild', 'strong');--> statement-breakpoint
CREATE TYPE "public"."offer_status" AS ENUM('DRAFT', 'ACTIVE', 'PAUSED', 'EXPIRED', 'WITHDRAWN');--> statement-breakpoint
CREATE TYPE "public"."offer_type" AS ENUM('CO_OWNERSHIP', 'LEASE', 'RENTAL', 'FULL_SALE');--> statement-breakpoint
CREATE TYPE "public"."phototype" AS ENUM('left_view', 'right_view', 'rear_view', 'front_view', 'dashboard', 'seats_view', 'interior', 'additional', 'tires', 'signature');--> statement-breakpoint
CREATE TYPE "public"."possessionstatus" AS ENUM('PENDING', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."priority" AS ENUM('LOW', 'MEDIUM', 'HIGH');--> statement-breakpoint
CREATE TYPE "public"."group_purpose" AS ENUM('RIDE_SHARE', 'FLEET_MANAGEMENT', 'CO_OWNERSHIP', 'SOCIAL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."referencetypeenum" AS ENUM('TAX_PIN', 'URL');--> statement-breakpoint
CREATE TYPE "public"."requirement_status" AS ENUM('ACTIVE', 'INACTIVE', 'DEPRECATED');--> statement-breakpoint
CREATE TYPE "public"."requirement_type" AS ENUM('DOCUMENT', 'CERTIFIED_DOCUMENT', 'IMAGE', 'VIDEO', 'AUDIO', 'CERTIFICATION', 'ATTESTATION', 'INSPECTION', 'AUDIT', 'SITE_INSPECTION', 'VEHICLE_INSPECTION', 'OTP', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."risk_level" AS ENUM('LOW', 'MEDIUM', 'HIGH');--> statement-breakpoint
CREATE TYPE "public"."servicestatus" AS ENUM('SCHEDULED', 'PENDING', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."servicetypeenum" AS ENUM('MAINTENANCE', 'INSURANCE', 'CLEANING', 'FUEL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."status" AS ENUM('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'ADDITIONAL_INFO_REQUIRED', 'VERIFIED', 'REJECTED', 'EXPIRED', 'PENDING_UPLOAD', 'UPLOADED', 'PENDING_VERIFICATION', 'VERIFICATION_FAILED', 'DOCUMENT_REJECTED', 'DOCUMENT_ACCEPTED', 'VERIFICATION_PENDING', 'VERIFICATION_IN_PROGRESS', 'VERIFICATION_COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."submission_status" AS ENUM('DRAFT', 'SUBMITTED', 'UNDER_REVIEW', 'ADDITIONAL_INFO_REQUIRED', 'VERIFIED', 'REJECTED', 'EXPIRED');--> statement-breakpoint
CREATE TYPE "public"."transmission" AS ENUM('automatic', 'manual', 'cvt', 'dual_clutch', 'other');--> statement-breakpoint
CREATE TYPE "public"."vehicle_fuel_type" AS ENUM('petrol', 'diesel', 'electric', 'hybrid', 'plug-in-hybrid');--> statement-breakpoint
CREATE TYPE "public"."vehicle_transmission" AS ENUM('manual', 'automatic', 'semi-automatic');--> statement-breakpoint
CREATE TYPE "public"."vehicleservicestatus" AS ENUM('SCHEDULED', 'PENDING', 'COMPLETED');--> statement-breakpoint
CREATE TYPE "public"."verification_method" AS ENUM('MANUAL', 'API', 'AI', 'BLOCKCHAIN');--> statement-breakpoint
CREATE TYPE "public"."verification_status" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."verificationtypeenum" AS ENUM('AI', 'MANUAL', 'API');--> statement-breakpoint
CREATE TABLE "account_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "account_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "address_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "address_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "alembic_version" (
	"version_num" varchar(32) PRIMARY KEY NOT NULL
);
--> statement-breakpoint
CREATE TABLE "application_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"application_id" integer NOT NULL,
	"documentType" varchar NOT NULL,
	"documentUrl" varchar NOT NULL,
	"uploaded_at" timestamp with time zone DEFAULT now() NOT NULL,
	"verificationStatus" varchar,
	"verified_at" timestamp with time zone,
	"verified_by" integer
);
--> statement-breakpoint
CREATE TABLE "applications" (
	"id" serial PRIMARY KEY NOT NULL,
	"type" "application_type" NOT NULL,
	"status" "application_status" NOT NULL,
	"applicant_id" integer NOT NULL,
	"asset_id" integer NOT NULL,
	"fraction" numeric,
	"amount" numeric(10, 2),
	"duration" integer,
	"start_date" timestamp with time zone,
	"end_date" timestamp with time zone,
	"notes" text,
	"reviewer_id" integer,
	"review_notes" text,
	"reviewed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "assets" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"description" text,
	"status" varchar NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone,
	"created_by" integer,
	"updated_by" integer
);
--> statement-breakpoint
CREATE TABLE "asset_ownership" (
	"id" serial PRIMARY KEY NOT NULL,
	"asset_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"fraction" numeric NOT NULL,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "bookings" (
	"vehicle_id" integer NOT NULL,
	"reference" varchar(50) NOT NULL,
	"start_datetime" timestamp NOT NULL,
	"end_datetime" timestamp NOT NULL,
	"status" "bookingstatus",
	"total_price" double precision,
	"notes" text,
	"party_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "bookings_reference_key" UNIQUE("reference")
);
--> statement-breakpoint
CREATE TABLE "cities" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"country" varchar NOT NULL,
	"province" varchar NOT NULL,
	CONSTRAINT "uq_city_name_province" UNIQUE("name","province")
);
--> statement-breakpoint
CREATE TABLE "company" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"registration_number" varchar,
	"country_id" integer DEFAULT 1,
	"registration_date" timestamp with time zone,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"name" varchar,
	"description" varchar,
	"city_id" integer,
	"purpose" varchar
);
--> statement-breakpoint
CREATE TABLE "company_notification_preferences" (
	"id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"booking_notifications" boolean,
	"payment_notifications" boolean,
	"maintenance_alerts" boolean,
	"member_activity" boolean,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "company_notification_preferences_pkey" PRIMARY KEY("id","company_id")
);
--> statement-breakpoint
CREATE TABLE "company_ownership" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"fraction" numeric NOT NULL,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone,
	"is_active" boolean NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "company_ownership_invite" (
	"id" serial PRIMARY KEY NOT NULL,
	"company_id" integer NOT NULL,
	"fraction" numeric NOT NULL,
	"status" "companyownershipinviteenum" NOT NULL,
	"email" varchar(100) NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"first_name" text,
	"last_name" text
);
--> statement-breakpoint
CREATE TABLE "compliance_audit" (
	"id" serial PRIMARY KEY NOT NULL,
	"entityType" varchar NOT NULL,
	"entityId" integer NOT NULL,
	"action" varchar NOT NULL,
	"actorId" integer NOT NULL,
	"changes" jsonb,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "compliance_document" (
	"id" serial PRIMARY KEY NOT NULL,
	"submissionId" integer NOT NULL,
	"documentNumber" varchar,
	"documentType" varchar NOT NULL,
	"issuingAuthorityId" integer,
	"issue_date" timestamp with time zone,
	"expiry_date" timestamp with time zone,
	"status" "document_status" NOT NULL,
	"documentUrl" varchar,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "compliance_requirement" (
	"id" serial PRIMARY KEY NOT NULL,
	"code" varchar NOT NULL,
	"version" integer DEFAULT 1 NOT NULL,
	"name" varchar NOT NULL,
	"description" text,
	"category" "compliance_category" NOT NULL,
	"type" "requirement_type" NOT NULL,
	"status" "requirement_status" NOT NULL,
	"validation_rules" jsonb,
	"document_schema" jsonb,
	"default_validity_days" integer,
	"riskLevel" "risk_level" DEFAULT 'LOW' NOT NULL,
	"is_recurring" boolean DEFAULT false NOT NULL,
	"recurring_period_days" integer,
	"metadata" jsonb,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "compliance_submission" (
	"id" serial PRIMARY KEY NOT NULL,
	"requirement_id" integer NOT NULL,
	"entityType" varchar NOT NULL,
	"entityId" integer NOT NULL,
	"currentStatusHistoryId" integer,
	"batchId" uuid,
	"data" jsonb,
	"validationResults" jsonb,
	"submittedBy" integer NOT NULL,
	"submitted_at" timestamp with time zone DEFAULT now() NOT NULL,
	"expires_at" timestamp with time zone,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "compliance_verification" (
	"id" serial PRIMARY KEY NOT NULL,
	"submissionId" integer NOT NULL,
	"method" "verification_method" NOT NULL,
	"status" "verification_status" NOT NULL,
	"verifiedBy" integer,
	"verificationData" jsonb,
	"result" jsonb,
	"cost" numeric(10, 2),
	"metadata" jsonb,
	"started_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "contact_point" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"contact_point_type_id" integer NOT NULL,
	"value" text NOT NULL,
	"address_type_id" integer,
	"is_primary" boolean NOT NULL,
	"is_verified" boolean NOT NULL,
	"verification_date" timestamp,
	"verification_method" text,
	"mtadata" json,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "contact_point_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"validation_pattern" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contact_point_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contact_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contact_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contract_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contract_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "contract_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "contract_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "countries" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"code" varchar NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "current_entity_status" (
	"entityType" varchar NOT NULL,
	"entityId" integer NOT NULL,
	"currentStatus" "status" NOT NULL,
	"last_transition_at" timestamp with time zone NOT NULL,
	"lastTransitionBy" integer NOT NULL,
	"statusHistoryId" integer NOT NULL,
	CONSTRAINT "current_entity_status_entityType_entityId_pk" PRIMARY KEY("entityType","entityId")
);
--> statement-breakpoint
CREATE TABLE "dispute_comments" (
	"comment" text NOT NULL,
	"dispute_id" integer NOT NULL,
	"reply_to_comment_id" integer,
	"user_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "dispute_media" (
	"dispute_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "disputes" (
	"name" varchar NOT NULL,
	"description" text,
	"vehicle_id" integer NOT NULL,
	"dispute_type" "disputetype" NOT NULL,
	"party_offending" integer NOT NULL,
	"party_logging" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	"priority" "priority",
	"company_id" integer NOT NULL,
	"dispute_status" "disputestatus"
);
--> statement-breakpoint
CREATE TABLE "document_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "document_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "emergency_contacts" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"phone_number" varchar NOT NULL,
	"contact_type" varchar NOT NULL,
	"description" varchar,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "group_member_roles" (
	"id" serial PRIMARY KEY NOT NULL,
	"group_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"role" "group_role" NOT NULL,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "group_memberships" (
	"id" serial PRIMARY KEY NOT NULL,
	"group_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "groups" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"name" varchar NOT NULL,
	"description" text,
	"city_id" integer,
	"country_id" integer,
	"initial_purpose" "company_purpose" NOT NULL,
	"is_managed" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"created_by" integer NOT NULL,
	"updated_at" timestamp with time zone,
	"updated_by" integer,
	"creator" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "identification_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"validation_pattern" text,
	"expiration_required" boolean,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "identification_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "individual" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"first_name" text NOT NULL,
	"last_name" text NOT NULL,
	"middle_name" text,
	"salutation" text,
	"suffix" text,
	"gender" text,
	"birth_date" timestamp,
	"marital_status" text,
	"nationality" text,
	"preferred_language" text,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "industry" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "industry_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "issuing_authority" (
	"party_id" integer NOT NULL,
	"name" varchar NOT NULL,
	"description" varchar,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"country" varchar,
	CONSTRAINT "issuing_authority_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "lead" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"company" text,
	"title" text,
	"status_id" integer NOT NULL,
	"source_id" integer,
	"rating" text,
	"annual_revenue" integer,
	"number_of_employees" integer,
	"industry" text,
	"description" text,
	"is_converted" boolean NOT NULL,
	"converted_date" timestamp,
	"converted_account_id" integer,
	"converted_contact_id" integer,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "lead_source" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "lead_source_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "lead_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "lead_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "listing_interest_expressions" (
	"listing_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "listing_media" (
	"listing_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "listing_offers" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"offer_type" "offer_type" NOT NULL,
	"fraction" numeric(5, 2),
	"duration_type" "duration_type" NOT NULL,
	"price" numeric(10, 2) NOT NULL,
	"finance_method" "finance_method" NOT NULL,
	"financing_terms" jsonb,
	"terms" jsonb NOT NULL,
	"status" "offer_status" NOT NULL,
	"valid_from" timestamp with time zone NOT NULL,
	"valid_to" timestamp with time zone DEFAULT 'infinity'::timestamp,
	"contract_terms" jsonb NOT NULL,
	"payment_terms" jsonb NOT NULL,
	"usage_terms" jsonb
);
--> statement-breakpoint
CREATE TABLE "listings" (
	"party_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"effective_from" date NOT NULL,
	"effective_to" date DEFAULT 'infinity'::timestamp NOT NULL,
	"fraction" double precision NOT NULL,
	"asking_price" double precision NOT NULL,
	"condition" "conditionenum" NOT NULL,
	"mileage" double precision,
	"listing_type" "listingtypeenum" NOT NULL,
	"audience" "audienceenum" NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "match_rule_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"confidence" integer,
	"is_active" boolean,
	"priority" integer,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "opportunities" (
	"opportunity_name" varchar NOT NULL,
	"description" varchar,
	"company_id" integer NOT NULL,
	"price" double precision NOT NULL,
	"fraction" double precision NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "opportunity_stage" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"probability" integer,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "opportunity_stage_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "opportunity_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "opportunity_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "organization" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"industry" text,
	"number_of_employees" integer,
	"annual_revenue" integer,
	"website_url" text,
	"logo_url" text,
	"is_deleted" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "party" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_type_id" integer NOT NULL,
	"status_id" integer NOT NULL,
	"external_id" text,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_external_id_key" UNIQUE("external_id")
);
--> statement-breakpoint
CREATE TABLE "party_identification" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"identification_type_id" integer NOT NULL,
	"document_number" text NOT NULL,
	"issuing_authority" text,
	"issue_date" timestamp,
	"expiry_date" timestamp,
	"is_verified" boolean NOT NULL,
	"verification_date" timestamp,
	"verification_method" text,
	"document_image_url" text,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "party_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "party_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "party_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "product_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "product_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "record_type" (
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "record_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "relationship_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "relationship_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "requirement_dependency" (
	"id" serial PRIMARY KEY NOT NULL,
	"requirementId" integer NOT NULL,
	"dependsOnId" integer NOT NULL,
	"condition" jsonb,
	"isRequired" boolean DEFAULT true NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "service_providers" (
	"name" varchar NOT NULL,
	"description" varchar,
	"service_type" "servicetypeenum" NOT NULL,
	"effective_from" date NOT NULL,
	"effective_to" date NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "social_media_type" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"base_url" text,
	"icon" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "social_media_type_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "social_profile" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"social_media_type_id" integer NOT NULL,
	"username" text NOT NULL,
	"url" text,
	"is_primary" boolean NOT NULL,
	"is_verified" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "status_change_notification" (
	"id" serial PRIMARY KEY NOT NULL,
	"statusHistoryId" integer NOT NULL,
	"notificationType" varchar NOT NULL,
	"recipientId" integer NOT NULL,
	"content" jsonb,
	"sent_at" timestamp with time zone,
	"error" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "status_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"entity_type" varchar NOT NULL,
	"entity_id" integer NOT NULL,
	"from_status" "status" NOT NULL,
	"to_status" "status" NOT NULL,
	"transition_rule_id" integer,
	"changed_by" integer NOT NULL,
	"reason" text,
	"notes" text,
	"metadata" jsonb,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "status_sla" (
	"id" serial PRIMARY KEY NOT NULL,
	"entityType" varchar NOT NULL,
	"status" "status" NOT NULL,
	"expectedDurationHours" integer NOT NULL,
	"escalationRules" jsonb,
	"notificationRules" jsonb,
	"isActive" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "status_sla_tracking" (
	"id" serial PRIMARY KEY NOT NULL,
	"statusHistoryId" integer NOT NULL,
	"slaId" integer NOT NULL,
	"due_at" timestamp with time zone NOT NULL,
	"completed_at" timestamp with time zone,
	"isBreached" boolean DEFAULT false NOT NULL,
	"escalationLevel" integer DEFAULT 0 NOT NULL,
	"metadata" jsonb,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "status_transition_rules" (
	"id" serial PRIMARY KEY NOT NULL,
	"entityType" varchar NOT NULL,
	"fromStatus" "status" NOT NULL,
	"toStatus" "status" NOT NULL,
	"allowedRoles" jsonb NOT NULL,
	"requiresApproval" boolean DEFAULT false NOT NULL,
	"validationRules" jsonb,
	"metadata" jsonb,
	"isActive" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "subscription_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"display_order" integer,
	"is_active" boolean NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	CONSTRAINT "subscription_status_name_key" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" serial PRIMARY KEY NOT NULL,
	"username" varchar(50) NOT NULL,
	"email" varchar(100) NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp,
	"family_name" varchar(100),
	"given_name" varchar(100),
	"phone_number" varchar(20),
	"org_id" varchar(16),
	CONSTRAINT "users_username_key" UNIQUE("username"),
	CONSTRAINT "users_email_key" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "vehicle_documents" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"document_type" "documenttype" NOT NULL,
	"expiration_date" timestamp with time zone,
	"name" varchar,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_inspections" (
	"scratches" "conditionlevel" NOT NULL,
	"dents" "conditionlevel" NOT NULL,
	"tires" "generalcondition" NOT NULL,
	"lights" "lightscondition" NOT NULL,
	"cleanliness" "cleanlinesslevel" NOT NULL,
	"seats" "generalcondition" NOT NULL,
	"dashboard_controls" "dashboardcondition" NOT NULL,
	"odors" "odorlevel" NOT NULL,
	"odometer" integer NOT NULL,
	"known_issues" text,
	"vehicle_id" integer NOT NULL,
	"possession_id" integer NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_maintenance" (
	"vehicle_id" integer NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" varchar(500),
	"due_date" timestamp NOT NULL,
	"due_odometer" double precision NOT NULL,
	"status" "vehicleservicestatus" NOT NULL,
	"expected_cost" double precision NOT NULL,
	"completed_date" timestamp,
	"completed_odometer" double precision,
	"actual_cost" double precision,
	"technician_notes" varchar(1000),
	"service_provider" varchar(1000),
	"created_at" timestamp,
	"updated_at" timestamp,
	"is_scheduled" boolean,
	"id" serial NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_make" (
	"name" varchar NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_media" (
	"vehicle_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_model" (
	"make_id" integer NOT NULL,
	"model" varchar NOT NULL,
	"slug" text,
	"first_year" integer,
	"last_year" integer,
	"body_type" "body_type",
	"description" text,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_model_media" (
	"vehicle_model_id" integer NOT NULL,
	"media_path" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_photos" (
	"inspection_id" integer NOT NULL,
	"type" "phototype" NOT NULL,
	"file_url" varchar NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_possessions" (
	"from_party_id" integer NOT NULL,
	"to_party_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"handover_expected_datetime" timestamp,
	"handover_actual_datetime" timestamp,
	"status" "possessionstatus" NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicle_variant" (
	"id" serial PRIMARY KEY NOT NULL,
	"model_id" integer,
	"name" varchar NOT NULL,
	"trim_name" text,
	"year" integer NOT NULL,
	"engine" text,
	"drivetrain" "drivetrain",
	"body_type" "body_type",
	"seats" integer,
	"doors" integer,
	"msrp" numeric(10, 2),
	"features" jsonb,
	"specs" jsonb,
	"fuel_type" "fuel_type" NOT NULL,
	"transmission" "transmission" NOT NULL,
	"description" text,
	"is_active" boolean NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "vehicles" (
	"party_id" integer NOT NULL,
	"model_id" integer NOT NULL,
	"vin_number" varchar NOT NULL,
	"vehicle_registration" varchar,
	"country_id" integer DEFAULT 1,
	"manufacturing_year" integer,
	"purchase_date" timestamp with time zone,
	"color" varchar,
	"is_active" boolean NOT NULL,
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "vehicles_vin_number_key" UNIQUE("vin_number")
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"verifying_party_id" integer NOT NULL,
	"verification_outcome" varchar,
	"cost" double precision,
	"verification_type" "verificationtypeenum",
	"id" serial PRIMARY KEY NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "voting_threshold" (
	"id" integer NOT NULL,
	"company_id" integer NOT NULL,
	"unanimous" boolean,
	"simple_majority" boolean,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone,
	CONSTRAINT "voting_threshold_pkey" PRIMARY KEY("id","company_id")
);
--> statement-breakpoint
ALTER TABLE "application_documents" ADD CONSTRAINT "application_documents_application_id_applications_id_fk" FOREIGN KEY ("application_id") REFERENCES "public"."applications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "application_documents" ADD CONSTRAINT "application_documents_verified_by_party_id_fk" FOREIGN KEY ("verified_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "applications" ADD CONSTRAINT "applications_applicant_id_party_id_fk" FOREIGN KEY ("applicant_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "applications" ADD CONSTRAINT "applications_asset_id_assets_id_fk" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "applications" ADD CONSTRAINT "applications_reviewer_id_party_id_fk" FOREIGN KEY ("reviewer_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assets" ADD CONSTRAINT "assets_created_by_party_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assets" ADD CONSTRAINT "assets_updated_by_party_id_fk" FOREIGN KEY ("updated_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_ownership" ADD CONSTRAINT "asset_ownership_asset_id_assets_id_fk" FOREIGN KEY ("asset_id") REFERENCES "public"."assets"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "asset_ownership" ADD CONSTRAINT "asset_ownership_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "bookings" ADD CONSTRAINT "bookings_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company" ADD CONSTRAINT "company_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company" ADD CONSTRAINT "company_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "public"."cities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company" ADD CONSTRAINT "company_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_notification_preferences" ADD CONSTRAINT "company_notification_preferences_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_ownership" ADD CONSTRAINT "company_ownership_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_ownership" ADD CONSTRAINT "company_ownership_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "company_ownership_invite" ADD CONSTRAINT "company_ownership_invite_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_audit" ADD CONSTRAINT "compliance_audit_actorId_party_id_fk" FOREIGN KEY ("actorId") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_document" ADD CONSTRAINT "compliance_document_submissionId_compliance_submission_id_fk" FOREIGN KEY ("submissionId") REFERENCES "public"."compliance_submission"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_document" ADD CONSTRAINT "compliance_document_issuingAuthorityId_issuing_authority_id_fk" FOREIGN KEY ("issuingAuthorityId") REFERENCES "public"."issuing_authority"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_submission" ADD CONSTRAINT "compliance_submission_requirement_id_compliance_requirement_id_fk" FOREIGN KEY ("requirement_id") REFERENCES "public"."compliance_requirement"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_submission" ADD CONSTRAINT "compliance_submission_currentStatusHistoryId_status_history_id_fk" FOREIGN KEY ("currentStatusHistoryId") REFERENCES "public"."status_history"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_submission" ADD CONSTRAINT "compliance_submission_submittedBy_party_id_fk" FOREIGN KEY ("submittedBy") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_verification" ADD CONSTRAINT "compliance_verification_submissionId_compliance_submission_id_fk" FOREIGN KEY ("submissionId") REFERENCES "public"."compliance_submission"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "compliance_verification" ADD CONSTRAINT "compliance_verification_verifiedBy_party_id_fk" FOREIGN KEY ("verifiedBy") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_address_type_id_fkey" FOREIGN KEY ("address_type_id") REFERENCES "public"."address_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_contact_point_type_id_fkey" FOREIGN KEY ("contact_point_type_id") REFERENCES "public"."contact_point_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "contact_point" ADD CONSTRAINT "contact_point_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "current_entity_status" ADD CONSTRAINT "current_entity_status_lastTransitionBy_party_id_fk" FOREIGN KEY ("lastTransitionBy") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "current_entity_status" ADD CONSTRAINT "current_entity_status_statusHistoryId_status_history_id_fk" FOREIGN KEY ("statusHistoryId") REFERENCES "public"."status_history"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_comments" ADD CONSTRAINT "dispute_comments_dispute_id_fkey" FOREIGN KEY ("dispute_id") REFERENCES "public"."disputes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_comments" ADD CONSTRAINT "dispute_comments_reply_to_comment_id_fkey" FOREIGN KEY ("reply_to_comment_id") REFERENCES "public"."dispute_comments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "dispute_media" ADD CONSTRAINT "dispute_media_dispute_id_fkey" FOREIGN KEY ("dispute_id") REFERENCES "public"."disputes"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_party_offending_fkey" FOREIGN KEY ("party_offending") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_party_logging_fkey" FOREIGN KEY ("party_logging") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "disputes" ADD CONSTRAINT "disputes_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_member_roles" ADD CONSTRAINT "group_member_roles_group_id_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_member_roles" ADD CONSTRAINT "group_member_roles_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_memberships" ADD CONSTRAINT "group_memberships_group_id_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_memberships" ADD CONSTRAINT "group_memberships_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "groups" ADD CONSTRAINT "groups_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "groups" ADD CONSTRAINT "groups_city_id_cities_id_fk" FOREIGN KEY ("city_id") REFERENCES "public"."cities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "groups" ADD CONSTRAINT "groups_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "groups" ADD CONSTRAINT "groups_creator_party_id_fk" FOREIGN KEY ("creator") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "individual" ADD CONSTRAINT "individual_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "issuing_authority" ADD CONSTRAINT "issuing_authority_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_source_id_fkey" FOREIGN KEY ("source_id") REFERENCES "public"."lead_source"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "lead" ADD CONSTRAINT "lead_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."lead_status"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_interest_expressions" ADD CONSTRAINT "listing_interest_expressions_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_interest_expressions" ADD CONSTRAINT "listing_interest_expressions_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_media" ADD CONSTRAINT "listing_media_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_offers" ADD CONSTRAINT "listing_offers_listing_id_listings_id_fk" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listings" ADD CONSTRAINT "listings_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "opportunities" ADD CONSTRAINT "opportunities_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "public"."company"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "organization" ADD CONSTRAINT "organization_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party" ADD CONSTRAINT "party_party_type_id_fkey" FOREIGN KEY ("party_type_id") REFERENCES "public"."party_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party" ADD CONSTRAINT "party_status_id_fkey" FOREIGN KEY ("status_id") REFERENCES "public"."party_status"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party_identification" ADD CONSTRAINT "party_identification_identification_type_id_fkey" FOREIGN KEY ("identification_type_id") REFERENCES "public"."identification_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party_identification" ADD CONSTRAINT "party_identification_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requirement_dependency" ADD CONSTRAINT "requirement_dependency_requirementId_compliance_requirement_id_fk" FOREIGN KEY ("requirementId") REFERENCES "public"."compliance_requirement"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "requirement_dependency" ADD CONSTRAINT "requirement_dependency_dependsOnId_compliance_requirement_id_fk" FOREIGN KEY ("dependsOnId") REFERENCES "public"."compliance_requirement"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "social_profile" ADD CONSTRAINT "social_profile_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "social_profile" ADD CONSTRAINT "social_profile_social_media_type_id_fkey" FOREIGN KEY ("social_media_type_id") REFERENCES "public"."social_media_type"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_change_notification" ADD CONSTRAINT "status_change_notification_statusHistoryId_status_history_id_fk" FOREIGN KEY ("statusHistoryId") REFERENCES "public"."status_history"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_change_notification" ADD CONSTRAINT "status_change_notification_recipientId_party_id_fk" FOREIGN KEY ("recipientId") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_history" ADD CONSTRAINT "status_history_transition_rule_id_status_transition_rules_id_fk" FOREIGN KEY ("transition_rule_id") REFERENCES "public"."status_transition_rules"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_history" ADD CONSTRAINT "status_history_changed_by_party_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_sla_tracking" ADD CONSTRAINT "status_sla_tracking_statusHistoryId_status_history_id_fk" FOREIGN KEY ("statusHistoryId") REFERENCES "public"."status_history"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "status_sla_tracking" ADD CONSTRAINT "status_sla_tracking_slaId_status_sla_id_fk" FOREIGN KEY ("slaId") REFERENCES "public"."status_sla"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_documents" ADD CONSTRAINT "vehicle_documents_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections" ADD CONSTRAINT "vehicle_inspections_possession_id_fkey" FOREIGN KEY ("possession_id") REFERENCES "public"."vehicle_possessions"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections" ADD CONSTRAINT "vehicle_inspections_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_maintenance" ADD CONSTRAINT "vehicle_maintenance_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_media" ADD CONSTRAINT "vehicle_media_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_model" ADD CONSTRAINT "vehicle_model_make_id_fkey" FOREIGN KEY ("make_id") REFERENCES "public"."vehicle_make"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_model_media" ADD CONSTRAINT "vehicle_model_media_vehicle_model_id_fkey" FOREIGN KEY ("vehicle_model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_photos" ADD CONSTRAINT "vehicle_photos_inspection_id_fkey" FOREIGN KEY ("inspection_id") REFERENCES "public"."vehicle_inspections"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_from_party_id_fkey" FOREIGN KEY ("from_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_to_party_id_fkey" FOREIGN KEY ("to_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possessions" ADD CONSTRAINT "vehicle_possessions_vehicle_id_fkey" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_variant" ADD CONSTRAINT "vehicle_variant_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicles" ADD CONSTRAINT "vehicles_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicles" ADD CONSTRAINT "vehicles_model_id_fkey" FOREIGN KEY ("model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicles" ADD CONSTRAINT "vehicles_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "verification" ADD CONSTRAINT "verification_verifying_party_id_fkey" FOREIGN KEY ("verifying_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "ix_bookings_id" ON "bookings" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_cities_id" ON "cities" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_company_notification_preferences_id" ON "company_notification_preferences" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "audit_entity_idx" ON "compliance_audit" USING btree ("entityType","entityId");--> statement-breakpoint
CREATE INDEX "audit_created_at_idx" ON "compliance_audit" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "submission_entity_idx" ON "compliance_submission" USING btree ("entityType","entityId");--> statement-breakpoint
CREATE INDEX "current_status_compliance_submission_idx" ON "compliance_submission" USING btree ("currentStatusHistoryId");--> statement-breakpoint
CREATE INDEX "ix_dispute_comments_id" ON "dispute_comments" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_dispute_media_id" ON "dispute_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_disputes_id" ON "disputes" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_emergency_contacts_id" ON "emergency_contacts" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "status_history_entity_idx" ON "status_history" USING btree ("entity_type","entity_id");--> statement-breakpoint
CREATE INDEX "current_status_history_idx" ON "status_history" USING btree ("entity_type","entity_id","effective_to");--> statement-breakpoint
CREATE INDEX "status_transition_idx" ON "status_transition_rules" USING btree ("entityType","fromStatus","toStatus");--> statement-breakpoint
CREATE INDEX "ix_vehicle_documents_id" ON "vehicle_documents" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_inspections_id" ON "vehicle_inspections" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_maintenance_id" ON "vehicle_maintenance" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_make_id" ON "vehicle_make" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_media_id" ON "vehicle_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_model_id" ON "vehicle_model" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_model_media_id" ON "vehicle_model_media" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_photos_id" ON "vehicle_photos" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_possessions_id" ON "vehicle_possessions" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_variant_id" ON "vehicle_variant" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicle_variant_name_key" ON "vehicle_variant" USING btree ("name" text_ops);--> statement-breakpoint
CREATE INDEX "ix_vehicles_id" ON "vehicles" USING btree ("id" int4_ops);--> statement-breakpoint
CREATE INDEX "ix_voting_threshold_id" ON "voting_threshold" USING btree ("id" int4_ops);