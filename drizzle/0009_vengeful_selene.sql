CREATE VIEW "public"."groups_community_view" AS (select "groups"."id", "groups"."name", "groups"."description", "groups"."created_at", "groups"."created_by", "cities"."name" as "city_name", "countries"."name" as "country_name", "groups"."is_managed", "groups"."initial_purpose", cast(count(distinct "group_memberships"."party_id") as integer) as "member_count", cast(count(distinct "group_shared_vehicles"."vehicle_id") as integer) as "vehicle_count" from "groups" left join "cities" on "groups"."city_id" = "cities"."id" left join "countries" on "groups"."country_id" = "countries"."id" left join "group_memberships" on "groups"."id" = "group_memberships"."group_id" AND "group_memberships"."effective_from" <= NOW() AND ("group_memberships"."effective_to" IS NULL OR "group_memberships"."effective_to" > NOW()) left join "group_shared_vehicles" on "groups"."id" = "group_shared_vehicles"."group_id" AND "group_shared_vehicles"."is_active" = true AND "group_shared_vehicles"."effective_from" <= NOW() AND ("group_shared_vehicles"."effective_to" IS NULL OR "group_shared_vehicles"."effective_to" > NOW()) group by "groups"."id", "groups"."name", "groups"."description", "groups"."created_at", "groups"."created_by", "cities"."name", "countries"."name", "groups"."is_managed", "groups"."initial_purpose");--> statement-breakpoint
CREATE VIEW "public"."user_group_memberships_view" AS (select "group_memberships"."group_id", "group_memberships"."party_id", "group_member_roles"."role", "groups"."name" as "group_name", "groups"."description" as "group_description", "cities"."name" as "city_name", "countries"."name" as "country_name", "groups"."created_at" as "group_created_at", "group_memberships"."effective_from" as "membership_start", "group_memberships"."effective_to" as "membership_end", "group_member_roles"."effective_from" as "role_start", "group_member_roles"."effective_to" as "role_end", "groups"."is_managed" as "is_managed", "groups"."initial_purpose" as "initial_purpose" from "group_memberships" inner join "groups" on "group_memberships"."group_id" = "groups"."id" left join "group_member_roles" on "group_memberships"."group_id" = "group_member_roles"."group_id" AND "group_memberships"."party_id" = "group_member_roles"."party_id" AND "group_member_roles"."effective_from" <= NOW() AND ("group_member_roles"."effective_to" IS NULL OR "group_member_roles"."effective_to" > NOW()) left join "cities" on "groups"."city_id" = "cities"."id" left join "countries" on "groups"."country_id" = "countries"."id" where "group_memberships"."effective_from" <= NOW() AND ("group_memberships"."effective_to" IS NULL OR "group_memberships"."effective_to" > NOW()));--> statement-breakpoint
CREATE MATERIALIZED VIEW "public"."groups_detailed_view" AS (select "groups"."id", "groups"."party_id", "groups"."name", "groups"."description", "groups"."city_id", "groups"."country_id", "groups"."initial_purpose", "groups"."is_managed", "groups"."created_at", "groups"."created_by", "groups"."creator", "cities"."name" as "city_name", "countries"."name" as "country_name", "province_state"."name" as "province_state_name", "individual"."first_name" as "creator_first_name", "individual"."last_name" as "creator_last_name", cast(count(distinct "group_memberships"."party_id") as integer) as "member_count", cast(count(distinct "group_shared_vehicles"."vehicle_id") as integer) as "vehicle_count", cast(count(distinct case when "group_member_roles"."role" = 'ADMIN' then "group_member_roles"."party_id" end) as integer) as "admin_count" from "groups" left join "cities" on "groups"."city_id" = "cities"."id" left join "countries" on "groups"."country_id" = "countries"."id" left join "province_state" on "cities"."province_state_id" = "province_state"."id" left join "party" on "groups"."creator" = "party"."id" left join "individual" on "party"."id" = "individual"."party_id" left join "group_memberships" on "groups"."id" = "group_memberships"."group_id" AND "group_memberships"."effective_from" <= NOW() AND ("group_memberships"."effective_to" IS NULL OR "group_memberships"."effective_to" > NOW()) left join "group_shared_vehicles" on "groups"."id" = "group_shared_vehicles"."group_id" AND "group_shared_vehicles"."is_active" = true AND "group_shared_vehicles"."effective_from" <= NOW() AND ("group_shared_vehicles"."effective_to" IS NULL OR "group_shared_vehicles"."effective_to" > NOW()) left join "group_member_roles" on "groups"."id" = "group_member_roles"."group_id" AND "group_member_roles"."effective_from" <= NOW() AND ("group_member_roles"."effective_to" IS NULL OR "group_member_roles"."effective_to" > NOW()) group by "groups"."id", "groups"."party_id", "groups"."name", "groups"."description", "groups"."city_id", "groups"."country_id", "groups"."initial_purpose", "groups"."is_managed", "groups"."created_at", "groups"."created_by", "groups"."creator", "cities"."name", "countries"."name", "province_state"."name", "individual"."first_name", "individual"."last_name");