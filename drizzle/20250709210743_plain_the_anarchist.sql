CREATE TABLE "inventory" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"admin_party_id" integer NOT NULL,
	"catalog_id" varchar,
	"status" varchar(20) DEFAULT 'available' NOT NULL,
	"assigned_to_party_id" integer,
	"location" varchar(255),
	"notes" text,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone DEFAULT 'infinity'::timestamp,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "inventory_vehicle_active" UNIQUE("vehicle_id","effective_to"),
	CONSTRAINT "inventory_status_check" CHECK (status IN ('available', 'assigned', 'maintenance', 'inspection'))
);
--> statement-breakpoint
ALTER TABLE "h_application_decisions" DROP CONSTRAINT "h_application_decisions_reviewer_id_users_id_fk";
--> statement-breakpoint
ALTER TABLE "h_applications" ADD COLUMN "created_at" timestamp with time zone DEFAULT now() NOT NULL;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "party_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "make_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "model_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "variant_id" integer;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "weekly_rate" double precision;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "initiation_fee" double precision;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD COLUMN "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "inventory" ADD CONSTRAINT "inventory_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory" ADD CONSTRAINT "inventory_admin_party_id_party_id_fk" FOREIGN KEY ("admin_party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inventory" ADD CONSTRAINT "inventory_assigned_to_party_id_party_id_fk" FOREIGN KEY ("assigned_to_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_inventory_admin_status" ON "inventory" USING btree ("admin_party_id","status");--> statement-breakpoint
CREATE INDEX "idx_inventory_vehicle_active" ON "inventory" USING btree ("vehicle_id","effective_to");--> statement-breakpoint
CREATE INDEX "idx_inventory_effective_period" ON "inventory" USING btree ("effective_from","effective_to");--> statement-breakpoint
ALTER TABLE "h_application_decisions" ADD CONSTRAINT "h_application_decisions_reviewer_id_party_id_fk" FOREIGN KEY ("reviewer_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD CONSTRAINT "h_vehicle_catalog_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD CONSTRAINT "h_vehicle_catalog_make_id_vehicle_make_id_fk" FOREIGN KEY ("make_id") REFERENCES "public"."vehicle_make"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD CONSTRAINT "h_vehicle_catalog_model_id_vehicle_model_id_fk" FOREIGN KEY ("model_id") REFERENCES "public"."vehicle_model"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "h_vehicle_catalog" ADD CONSTRAINT "h_vehicle_catalog_variant_id_vehicle_variant_id_fk" FOREIGN KEY ("variant_id") REFERENCES "public"."vehicle_variant"("id") ON DELETE no action ON UPDATE no action;