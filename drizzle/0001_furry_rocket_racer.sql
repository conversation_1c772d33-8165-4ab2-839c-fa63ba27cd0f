CREATE TYPE "public"."invitation_status" AS ENUM('SENT', 'ACCEPTED', 'DECLINED', 'CANCELLED', 'EXPIRED');--> statement-breakpoint
CREATE TABLE "group_membership_invitations" (
	"id" serial PRIMARY KEY NOT NULL,
	"group_id" integer NOT NULL,
	"first_name" varchar NOT NULL,
	"last_name" varchar NOT NULL,
	"email" varchar NOT NULL,
	"role" "group_role" NOT NULL,
	"status" "invitation_status" DEFAULT 'SENT' NOT NULL,
	"invited_by" integer NOT NULL,
	"invitation_token" varchar NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"accepted_at" timestamp with time zone,
	"accepted_by" integer,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "group_shared_vehicles" (
	"id" serial PRIMARY KEY NOT NULL,
	"group_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"shared_by" integer NOT NULL,
	"effective_from" timestamp with time zone DEFAULT now() NOT NULL,
	"effective_to" timestamp with time zone DEFAULT 'infinity'::timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone,
	CONSTRAINT "unique_group_vehicle" UNIQUE("group_id","vehicle_id")
);
--> statement-breakpoint
ALTER TABLE "group_member_roles" ALTER COLUMN "effective_to" SET DEFAULT 'infinity'::timestamp;--> statement-breakpoint
ALTER TABLE "group_memberships" ALTER COLUMN "effective_to" SET DEFAULT 'infinity'::timestamp;--> statement-breakpoint
ALTER TABLE "group_membership_invitations" ADD CONSTRAINT "group_membership_invitations_group_id_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_membership_invitations" ADD CONSTRAINT "group_membership_invitations_invited_by_party_id_fk" FOREIGN KEY ("invited_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_membership_invitations" ADD CONSTRAINT "group_membership_invitations_accepted_by_party_id_fk" FOREIGN KEY ("accepted_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_shared_vehicles" ADD CONSTRAINT "group_shared_vehicles_group_id_groups_id_fk" FOREIGN KEY ("group_id") REFERENCES "public"."groups"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_shared_vehicles" ADD CONSTRAINT "group_shared_vehicles_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "group_shared_vehicles" ADD CONSTRAINT "group_shared_vehicles_shared_by_party_id_fk" FOREIGN KEY ("shared_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;