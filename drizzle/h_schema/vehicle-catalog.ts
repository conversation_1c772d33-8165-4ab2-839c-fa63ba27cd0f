import {
  boolean,
  doublePrecision,
  integer,
  serial,
  text,
  pgTable,
  varchar,
  timestamp,
} from "drizzle-orm/pg-core";
import { temporalFields } from "./temporals";
import { party, vehicleMake, vehicleModel, vehicleVariant } from "../schema";

// Platform Vehicle Catalog - template/specs for what can be offered
export const h_vehicleCatalog = pgTable("h_vehicle_catalog", {
  id: serial().primaryKey().notNull(),
  partyId: integer("party_id")
    .references(() => party.id)
    .notNull(),

  // Foreign key references to vehicle tables
  makeId: integer("make_id")
    .references(() => vehicleMake.id)
    .notNull(),
  modelId: integer("model_id")
    .references(() => vehicleModel.id)
    .notNull(),
  variantId: integer("variant_id").references(() => vehicleVariant.id), // Optional variant

  // Keep text fields for backward compatibility and denormalization
  make: text("make").notNull(),
  model: text("model").notNull(),
  year: integer("year").notNull(),
  variant: text("variant"), // e.g., "1.6 GLX", "2.0 Turbo"
  category: text("category").notNull(), // sedan, suv, hatchback
  fuelType: text("fuel_type").notNull(), // petrol, diesel, electric

  // Platform specifications
  ehailingEligible: boolean("ehailing_eligible").default(false).notNull(),
  estimatedPrice: doublePrecision("estimated_price"), // What platform expects to pay
  weeklyFeeTarget: doublePrecision("weekly_fee_target"), // What they want to charge drivers

  // ADD THESE NEW FIELDS
  weeklyRate: doublePrecision("weekly_rate"), // Form field for weekly rate
  initiationFee: doublePrecision("initiation_fee"), // Form field for initiation fee
  isActive: boolean("is_active").default(true).notNull(), // Active/inactive status

  // Catalog metadata
  description: text("description"),
  specifications: text("specifications"), // JSON for detailed specs

  // Add temporal fields
  createdAt: timestamp("created_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
  updatedAt: timestamp("updated_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
  // ...temporalFields,
});

// Vehicle catalog features
export const h_vehicleCatalogFeatures = pgTable("h_vehicle_catalog_features", {
  id: serial().primaryKey().notNull(),
  catalogId: integer("catalog_id") // Integer since vehicleCatalog.id is serial
    .notNull()
    .references(() => h_vehicleCatalog.id, { onDelete: "cascade" }),
  feature: text("feature").notNull(),

  // Add temporal fields
  createdAt: timestamp("created_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
  updatedAt: timestamp("updated_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
});

// Vehicle catalog suitable platforms
export const h_vehicleCatalogPlatforms = pgTable(
  "h_vehicle_catalog_platforms",
  {
    id: serial().primaryKey().notNull(),
    catalogId: integer("catalog_id") // Integer since vehicleCatalog.id is serial
      .notNull()
      .references(() => h_vehicleCatalog.id, { onDelete: "cascade" }),
    platform: text("platform").notNull(), // e.g., "Uber", "Bolt", "InDriver"

    // Add temporal fields
    createdAt: timestamp("created_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
    updatedAt: timestamp("updated_at", {
      withTimezone: true,
      mode: "string",
    }).defaultNow(),
  }
);

// Vehicle catalog images
export const h_vehicleCatalogImages = pgTable("h_vehicle_catalog_images", {
  id: serial().primaryKey().notNull(),
  catalogId: integer("catalog_id") // Integer since vehicleCatalog.id is serial
    .notNull()
    .references(() => h_vehicleCatalog.id, { onDelete: "cascade" }),
  imageUrl: text("image_url").notNull(),
  isPrimary: boolean("is_primary").default(false).notNull(),

  // Add temporal fields
  createdAt: timestamp("created_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
  updatedAt: timestamp("updated_at", {
    withTimezone: true,
    mode: "string",
  }).defaultNow(),
});
