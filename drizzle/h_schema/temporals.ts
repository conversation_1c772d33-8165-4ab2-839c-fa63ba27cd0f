import { users } from "../schema";
import { timestamp, text, boolean } from "drizzle-orm/pg-core";

// Base temporal fields using PostgreSQL temporal tables
export const temporalFields = {
  createdAt: timestamp("created_at").defaultNow().notNull(),
  createdById: text("created_by_id").references(() => users.id),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  updatedById: text("updated_by_id").references(() => users.id),
  deletedAt: timestamp("deleted_at"), // Soft delete
  deletedById: text("deleted_by_id").references(() => users.id),
  isActive: boolean("is_active").default(true).notNull(),
  // PostgreSQL temporal columns (add these in raw SQL after table creation)
  // valid_from timestamptz GENERATED ALWAYS AS ROW START,
  // valid_to timestamptz GENERATED ALWAYS AS ROW END,
  // PERIOD FOR SYSTEM_TIME (valid_from, valid_to)
};
