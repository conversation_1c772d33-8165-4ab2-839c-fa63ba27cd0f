import { pgEnum } from "drizzle-orm/pg-core";

export const applicationStatusEnum = pgEnum("application_status", [
  "pending",
  "under_review",
  "approved",
  "rejected",
  "withdrawn",
]);

export const listingTypeEnum = pgEnum("listing_type", [
  "rental",
  "fractional",
  // "lease-to-own", This mostly applied when Poolly did not accept external parties to lease vehicles for ehailing via Poolly
  "ehailing-platform", // Poolly inventory e-hailing with lease-to-own
  //"ehailing-rent", // Non Poolly e-hailing with rental option only - we need to confirm if this is really a thing or not
]);

export const listingSourceTypeEnum = pgEnum("listing_source_type", [
  "catalog",
  "vehicle",
]);

export const documentStatusEnum = pgEnum("document_status", [
  "pending",
  "uploaded",
  "verified",
  "rejected",
  "superseded",
]);

export const listingStatusEnum = pgEnum("listing_status", [
  "draft",
  "published",
  "archived",
]);

export const listingApprovalStatusEnum = pgEnum("listing_approval_status", [
  "pending",
  "under_review",
  "approved",
  "rejected",
  "withdrawn",
]);

export const listingPublishStatusEnum = pgEnum("listing_publish_status", [
  "pending",
  "published",
  "archived",
]);

export const listingStatsTypeEnum = pgEnum("listing_stats_type", [
  "views",
  "applications",
]);

// Payment and Contract Management Enums
export const paymentStatusEnum = pgEnum("payment_status", [
  "pending",
  "paid",
  "failed",
  "cancelled",
]);

export const paymentMethodEnum = pgEnum("payment_method", [
  "bank_transfer",
  "cash",
  "eft",
  "card",
  "other",
]);

export const debtSourceTypeEnum = pgEnum("debt_source_type", [
  "earnings_shortfall",
  "debt_recovery",
  "adjustment",
]);

export const platformNameEnum = pgEnum("platform_name", [
  "uber",
  "bolt",
  "indriver",
  "other",
]);
