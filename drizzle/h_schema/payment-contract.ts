import {
  pgTable,
  serial,
  integer,
  decimal,
  text,
  timestamp,
  date,
  jsonb,
  unique,
} from "drizzle-orm/pg-core";
import { sql } from "drizzle-orm";
import { party } from "../schema";
import { temporalFields } from "./temporals";
import {
  paymentStatusEnum,
  paymentMethodEnum,
  debtSourceTypeEnum,
  platformNameEnum,
} from "./enums";

// 1. Assignment Deposits - Tracks initial deposit payments and balances
export const h_assignment_deposits = pgTable("h_assignment_deposits", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(), // References existing assignment system
  depositAmount: decimal("deposit_amount", {
    precision: 10,
    scale: 2,
  }).notNull(),
  amountPaid: decimal("amount_paid", { precision: 10, scale: 2 })
    .notNull()
    .default("0"),
  balanceRemaining: decimal("balance_remaining", {
    precision: 10,
    scale: 2,
  }).notNull(),
  paymentMethod: paymentMethodEnum("payment_method"),
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }),
  notes: text("notes"),
  // ...temporalFields,
});

// 2. Assignment Contracts - Manages contract documents for vehicle assignments
export const h_assignment_contracts = pgTable("h_assignment_contracts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  contractFilePath: text("contract_file_path").notNull(),
  originalFilename: text("original_filename").notNull(),
  fileSize: integer("file_size").notNull(),
  mimeType: text("mime_type").notNull(),
  uploadedAt: timestamp("uploaded_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  uploadedBy: integer("uploaded_by")
    .references(() => party.id)
    .notNull(),
  replacedBy: integer("replaced_by"), // Self-reference to h_assignment_contracts.id
  notes: text("notes"),
  // ...temporalFields,
});

// 3. Weekly Earnings - Records weekly e-hailing platform earnings for each driver
export const h_weekly_earnings = pgTable(
  "h_weekly_earnings",
  {
    id: serial().primaryKey().notNull(),
    assignmentId: integer("assignment_id").notNull(),
    weekStartDate: date("week_start_date").notNull(),
    weekEndDate: date("week_end_date").notNull(),
    grossEarnings: decimal("gross_earnings", {
      precision: 10,
      scale: 2,
    }).notNull(),
    platformName: platformNameEnum("platform_name").notNull().default("uber"),
    weeklyTarget: decimal("weekly_target", { precision: 10, scale: 2 })
      .notNull()
      .default("2700"),
    earningsShortfall: decimal("earnings_shortfall", {
      precision: 10,
      scale: 2,
    })
      .notNull()
      .default("0"),
    recordedBy: integer("recorded_by")
      .references(() => party.id)
      .notNull(),
    notes: text("notes"),
    // ...temporalFields,
  },
  (table) => [
    unique("unique_assignment_week").on(
      table.assignmentId,
      table.weekStartDate
    ),
  ]
);

// 4. Driver Payouts - Tracks payouts made to drivers based on their weekly performance
export const h_driver_payouts = pgTable("h_driver_payouts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  weeklyEarningsId: integer("weekly_earnings_id")
    .references(() => h_weekly_earnings.id)
    .notNull(),
  grossPayout: decimal("gross_payout", { precision: 10, scale: 2 }).notNull(),
  debtDeduction: decimal("debt_deduction", { precision: 10, scale: 2 })
    .notNull()
    .default("0"),
  netPayout: decimal("net_payout", { precision: 10, scale: 2 }).notNull(),
  paymentMethod: paymentMethodEnum("payment_method"),
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }),
  processedBy: integer("processed_by")
    .references(() => party.id)
    .notNull(),
  status: paymentStatusEnum("status").notNull().default("pending"),
  notes: text("notes"),
  // ...temporalFields,
});

// 5. Driver Debt Tracking - Maintains running debt balances for drivers who underperform weekly targets
export const h_driver_debt_tracking = pgTable("h_driver_debt_tracking", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  debtSourceId: integer("debt_source_id").notNull(), // References h_weekly_earnings.id or h_driver_payouts.id
  debtSourceType: debtSourceTypeEnum("debt_source_type").notNull(),
  debtAmount: decimal("debt_amount", { precision: 10, scale: 2 }).notNull(),
  runningBalance: decimal("running_balance", {
    precision: 10,
    scale: 2,
  }).notNull(),
  transactionDate: timestamp("transaction_date", { withTimezone: true })
    .defaultNow()
    .notNull(),
  notes: text("notes"),
  // ...temporalFields,
});

// 6. Payment Audit Log - Comprehensive audit trail for all payment-related activities
export const h_payment_audit_log = pgTable("h_payment_audit_log", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  actionType: text("action_type").notNull(),
  tableName: text("table_name").notNull(),
  recordId: integer("record_id").notNull(),
  oldValues: jsonb("old_values"),
  newValues: jsonb("new_values"),
  performedAt: timestamp("performed_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  performedBy: integer("performed_by")
    .references(() => party.id)
    .notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  // ...temporalFields,
}); 