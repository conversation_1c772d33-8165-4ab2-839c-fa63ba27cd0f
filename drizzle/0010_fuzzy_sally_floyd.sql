CREATE TYPE "public"."booking_status" AS ENUM('DRAFT', 'PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW', 'DISPUTED');--> statement-breakpoint
CREATE TYPE "public"."condition_enhanced" AS ENUM('excellent', 'good', 'fair', 'poor');--> statement-breakpoint
CREATE TYPE "public"."handover_status" AS ENUM('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'DISPUTED');--> statement-breakpoint
CREATE TYPE "public"."handover_type" AS ENUM('BOOKING_START', 'BOOKING_END', 'OWNERSHIP_TRANSFER', 'MAINTENANCE_DROP', 'MAINTENANCE_PICKUP');--> statement-breakpoint
CREATE TYPE "public"."inspection_status" AS ENUM('SCHEDULED', 'IN_PROGRESS', 'COMPLETED', 'DISPUTED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."inspection_type" AS ENUM('PRE_HANDOVER', 'POST_HANDOVER', 'PERIODIC', 'INCIDENT', 'MAINTENANCE');--> statement-breakpoint
CREATE TYPE "public"."issue_type" AS ENUM('CONDITION_DISPUTE', 'NO_SHOW', 'LATE_ARRIVAL', 'MISSING_ITEMS', 'DAMAGE_CLAIM', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."permission_type" AS ENUM('OWNER', 'AUTHORIZED_USER', 'GROUP_MEMBER');--> statement-breakpoint
CREATE TYPE "public"."photo_type_enhanced" AS ENUM('left_view', 'right_view', 'rear_view', 'front_view', 'dashboard', 'seats_view', 'interior', 'odometer', 'damage_detail', 'tires', 'inspector_signature');--> statement-breakpoint
CREATE TYPE "public"."possession_type" AS ENUM('OWNER', 'RENTER', 'BORROWER', 'MAINTENANCE');--> statement-breakpoint
CREATE TABLE "booking_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"booking_reference" varchar(50) NOT NULL,
	"vehicle_id" integer NOT NULL,
	"borrower_party_id" integer NOT NULL,
	"event_type" varchar NOT NULL,
	"status" "booking_status" NOT NULL,
	"requested_start" timestamp with time zone NOT NULL,
	"requested_end" timestamp with time zone NOT NULL,
	"confirmed_start" timestamp with time zone,
	"confirmed_end" timestamp with time zone,
	"actual_start" timestamp with time zone,
	"actual_end" timestamp with time zone,
	"changed_by" integer NOT NULL,
	"approved_by" integer,
	"quoted_price" numeric(10, 2),
	"final_price" numeric(10, 2),
	"currency" varchar(3) DEFAULT 'USD',
	"purpose" text,
	"special_requirements" text,
	"notes" text,
	"change_reason" text,
	"event_timestamp" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "handover_issue_resolutions" (
	"id" serial PRIMARY KEY NOT NULL,
	"issue_id" integer NOT NULL,
	"resolution" text NOT NULL,
	"resolved_by" integer NOT NULL,
	"resolved_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "handover_issues" (
	"id" serial PRIMARY KEY NOT NULL,
	"handover_id" integer NOT NULL,
	"reported_by" integer NOT NULL,
	"issue_type" "issue_type" NOT NULL,
	"description" text NOT NULL,
	"reported_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "handover_status_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"handover_id" integer NOT NULL,
	"status" "handover_status" NOT NULL,
	"status_timestamp" timestamp with time zone,
	"changed_by" integer NOT NULL,
	"notes" text,
	"event_timestamp" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "inspection_photos" (
	"id" serial PRIMARY KEY NOT NULL,
	"inspection_id" integer NOT NULL,
	"photo_type" "photo_type_enhanced" NOT NULL,
	"file_url" varchar NOT NULL,
	"description" text,
	"captured_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_access_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"event_type" varchar NOT NULL,
	"permission_type" "permission_type" NOT NULL,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone,
	"granted_by" integer NOT NULL,
	"reason" text,
	"event_timestamp" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_handovers" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"handover_type" "handover_type" NOT NULL,
	"booking_reference" varchar,
	"from_party_id" integer NOT NULL,
	"to_party_id" integer NOT NULL,
	"scheduled_time" timestamp with time zone NOT NULL,
	"handover_location" text,
	"handover_coordinates" text,
	"notes" text,
	"created_by" integer NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_inspections_immutable" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"handover_id" integer,
	"inspector_party_id" integer NOT NULL,
	"inspection_type" "inspection_type" NOT NULL,
	"odometer" integer NOT NULL,
	"fuel_level" integer,
	"scratches" "conditionlevel",
	"dents" "conditionlevel",
	"tires" "generalcondition",
	"lights" "lightscondition",
	"cleanliness" "cleanlinesslevel",
	"seats" "generalcondition",
	"dashboard_controls" "dashboardcondition",
	"odors" "odorlevel",
	"overall_condition" "condition_enhanced" NOT NULL,
	"known_issues" text,
	"new_damage" text,
	"items_in_vehicle" text,
	"inspector_signature" text,
	"inspection_completed_at" timestamp with time zone NOT NULL,
	"related_inspection_id" integer,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "fuel_level_range" CHECK (fuel_level >= 0 AND fuel_level <= 100)
);
--> statement-breakpoint
CREATE TABLE "vehicle_possession_events" (
	"id" serial PRIMARY KEY NOT NULL,
	"vehicle_id" integer NOT NULL,
	"possessor_party_id" integer NOT NULL,
	"possession_start" timestamp with time zone NOT NULL,
	"possession_end" timestamp with time zone,
	"possession_type" "possession_type" NOT NULL,
	"trigger_type" varchar NOT NULL,
	"trigger_reference" varchar,
	"handover_id" integer,
	"recorded_by" integer NOT NULL,
	"notes" text,
	"event_timestamp" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "booking_events" ADD CONSTRAINT "booking_events_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "booking_events" ADD CONSTRAINT "booking_events_borrower_party_id_party_id_fk" FOREIGN KEY ("borrower_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "booking_events" ADD CONSTRAINT "booking_events_changed_by_party_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "booking_events" ADD CONSTRAINT "booking_events_approved_by_party_id_fk" FOREIGN KEY ("approved_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_issue_resolutions" ADD CONSTRAINT "handover_issue_resolutions_issue_id_handover_issues_id_fk" FOREIGN KEY ("issue_id") REFERENCES "public"."handover_issues"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_issue_resolutions" ADD CONSTRAINT "handover_issue_resolutions_resolved_by_party_id_fk" FOREIGN KEY ("resolved_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_issues" ADD CONSTRAINT "handover_issues_handover_id_vehicle_handovers_id_fk" FOREIGN KEY ("handover_id") REFERENCES "public"."vehicle_handovers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_issues" ADD CONSTRAINT "handover_issues_reported_by_party_id_fk" FOREIGN KEY ("reported_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_status_events" ADD CONSTRAINT "handover_status_events_handover_id_vehicle_handovers_id_fk" FOREIGN KEY ("handover_id") REFERENCES "public"."vehicle_handovers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "handover_status_events" ADD CONSTRAINT "handover_status_events_changed_by_party_id_fk" FOREIGN KEY ("changed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "inspection_photos" ADD CONSTRAINT "inspection_photos_inspection_id_vehicle_inspections_immutable_id_fk" FOREIGN KEY ("inspection_id") REFERENCES "public"."vehicle_inspections_immutable"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_access_events" ADD CONSTRAINT "vehicle_access_events_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_access_events" ADD CONSTRAINT "vehicle_access_events_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_access_events" ADD CONSTRAINT "vehicle_access_events_granted_by_party_id_fk" FOREIGN KEY ("granted_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_handovers" ADD CONSTRAINT "vehicle_handovers_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_handovers" ADD CONSTRAINT "vehicle_handovers_from_party_id_party_id_fk" FOREIGN KEY ("from_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_handovers" ADD CONSTRAINT "vehicle_handovers_to_party_id_party_id_fk" FOREIGN KEY ("to_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_handovers" ADD CONSTRAINT "vehicle_handovers_created_by_party_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections_immutable" ADD CONSTRAINT "vehicle_inspections_immutable_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections_immutable" ADD CONSTRAINT "vehicle_inspections_immutable_handover_id_vehicle_handovers_id_fk" FOREIGN KEY ("handover_id") REFERENCES "public"."vehicle_handovers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections_immutable" ADD CONSTRAINT "vehicle_inspections_immutable_inspector_party_id_party_id_fk" FOREIGN KEY ("inspector_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_inspections_immutable" ADD CONSTRAINT "vehicle_inspections_immutable_related_inspection_fkey" FOREIGN KEY ("related_inspection_id") REFERENCES "public"."vehicle_inspections_immutable"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possession_events" ADD CONSTRAINT "vehicle_possession_events_vehicle_id_vehicles_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicles"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possession_events" ADD CONSTRAINT "vehicle_possession_events_possessor_party_id_party_id_fk" FOREIGN KEY ("possessor_party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possession_events" ADD CONSTRAINT "vehicle_possession_events_handover_id_vehicle_handovers_id_fk" FOREIGN KEY ("handover_id") REFERENCES "public"."vehicle_handovers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_possession_events" ADD CONSTRAINT "vehicle_possession_events_recorded_by_party_id_fk" FOREIGN KEY ("recorded_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "booking_events_reference_idx" ON "booking_events" USING btree ("booking_reference");--> statement-breakpoint
CREATE INDEX "booking_events_vehicle_id_idx" ON "booking_events" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "booking_events_borrower_party_id_idx" ON "booking_events" USING btree ("borrower_party_id");--> statement-breakpoint
CREATE INDEX "booking_events_status_idx" ON "booking_events" USING btree ("status");--> statement-breakpoint
CREATE INDEX "booking_events_timestamp_idx" ON "booking_events" USING btree ("event_timestamp");--> statement-breakpoint
CREATE INDEX "handover_issue_resolutions_issue_id_idx" ON "handover_issue_resolutions" USING btree ("issue_id");--> statement-breakpoint
CREATE INDEX "handover_issue_resolutions_resolved_at_idx" ON "handover_issue_resolutions" USING btree ("resolved_at");--> statement-breakpoint
CREATE INDEX "handover_issues_handover_id_idx" ON "handover_issues" USING btree ("handover_id");--> statement-breakpoint
CREATE INDEX "handover_issues_reported_at_idx" ON "handover_issues" USING btree ("reported_at");--> statement-breakpoint
CREATE INDEX "handover_status_events_handover_id_idx" ON "handover_status_events" USING btree ("handover_id");--> statement-breakpoint
CREATE INDEX "handover_status_events_status_idx" ON "handover_status_events" USING btree ("status");--> statement-breakpoint
CREATE INDEX "handover_status_events_timestamp_idx" ON "handover_status_events" USING btree ("status_timestamp");--> statement-breakpoint
CREATE INDEX "inspection_photos_inspection_id_idx" ON "inspection_photos" USING btree ("inspection_id");--> statement-breakpoint
CREATE INDEX "vehicle_access_events_vehicle_id_idx" ON "vehicle_access_events" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_access_events_party_id_idx" ON "vehicle_access_events" USING btree ("party_id");--> statement-breakpoint
CREATE INDEX "vehicle_access_events_effective_idx" ON "vehicle_access_events" USING btree ("effective_from","effective_to");--> statement-breakpoint
CREATE INDEX "vehicle_access_events_timestamp_idx" ON "vehicle_access_events" USING btree ("event_timestamp");--> statement-breakpoint
CREATE INDEX "vehicle_handovers_vehicle_id_idx" ON "vehicle_handovers" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_handovers_booking_reference_idx" ON "vehicle_handovers" USING btree ("booking_reference");--> statement-breakpoint
CREATE INDEX "vehicle_handovers_scheduled_time_idx" ON "vehicle_handovers" USING btree ("scheduled_time");--> statement-breakpoint
CREATE INDEX "vehicle_inspections_immutable_vehicle_id_idx" ON "vehicle_inspections_immutable" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_inspections_immutable_handover_id_idx" ON "vehicle_inspections_immutable" USING btree ("handover_id");--> statement-breakpoint
CREATE INDEX "vehicle_inspections_immutable_inspector_idx" ON "vehicle_inspections_immutable" USING btree ("inspector_party_id");--> statement-breakpoint
CREATE INDEX "vehicle_inspections_immutable_completed_at_idx" ON "vehicle_inspections_immutable" USING btree ("inspection_completed_at");--> statement-breakpoint
CREATE INDEX "vehicle_possession_events_vehicle_id_idx" ON "vehicle_possession_events" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_possession_events_possessor_idx" ON "vehicle_possession_events" USING btree ("possessor_party_id");--> statement-breakpoint
CREATE INDEX "vehicle_possession_events_dates_idx" ON "vehicle_possession_events" USING btree ("possession_start","possession_end");--> statement-breakpoint
CREATE INDEX "vehicle_possession_events_timestamp_idx" ON "vehicle_possession_events" USING btree ("event_timestamp");--> statement-breakpoint
CREATE VIEW "public"."current_booking_status_view" AS (select "booking_reference", "vehicle_id", "borrower_party_id", "status", "requested_start", "requested_end", "confirmed_start", "confirmed_end", "actual_start", "actual_end", "quoted_price", "final_price", "event_timestamp" from "booking_events" where "booking_events"."id" IN (
        SELECT MAX(id) 
        FROM "booking_events" be2 
        WHERE be2.booking_reference = "booking_events"."booking_reference"
      ));--> statement-breakpoint
CREATE VIEW "public"."current_handover_status_view" AS (select "handover_id", "status", "status_timestamp", "changed_by" from "handover_status_events" where "handover_status_events"."id" IN (
        SELECT MAX(id)
        FROM "handover_status_events" hse2
        WHERE hse2.handover_id = "handover_status_events"."handover_id"
      ));--> statement-breakpoint
CREATE VIEW "public"."current_vehicle_access_view" AS (select "vehicle_id", "party_id", "permission_type", "effective_from", "granted_by" from "vehicle_access_events" where "vehicle_access_events"."event_type" = 'GRANTED'
          AND "vehicle_access_events"."effective_from" <= NOW()
          AND ("vehicle_access_events"."effective_to" IS NULL OR "vehicle_access_events"."effective_to" > NOW())
          AND NOT EXISTS (
            SELECT 1 FROM "vehicle_access_events" vae2
            WHERE vae2.vehicle_id = "vehicle_access_events"."vehicle_id"
            AND vae2.party_id = "vehicle_access_events"."party_id"
            AND vae2.event_type = 'REVOKED'
            AND vae2.event_timestamp > "vehicle_access_events"."event_timestamp"
          ));--> statement-breakpoint
CREATE VIEW "public"."current_vehicle_possession_view" AS (select "vehicle_id", "possessor_party_id", "possession_type", "possession_start", "trigger_type", "trigger_reference" from "vehicle_possession_events" where "vehicle_possession_events"."possession_end" IS NULL 
          AND "vehicle_possession_events"."id" IN (
            SELECT MAX(id) 
            FROM "vehicle_possession_events" vpe2 
            WHERE vpe2.vehicle_id = "vehicle_possession_events"."vehicle_id"
            AND vpe2.possession_end IS NULL
          ));