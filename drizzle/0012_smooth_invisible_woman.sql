CREATE TABLE "listing_history" (
	"id" serial PRIMARY KEY NOT NULL,
	"listing_id" integer NOT NULL,
	"party_id" integer NOT NULL,
	"vehicle_id" integer NOT NULL,
	"effective_from" timestamp with time zone NOT NULL,
	"effective_to" timestamp with time zone,
	"fraction" double precision NOT NULL,
	"asking_price" double precision NOT NULL,
	"condition" "conditionenum" NOT NULL,
	"mileage" double precision,
	"listing_type" "listingtypeenum" NOT NULL,
	"audience" "audienceenum" NOT NULL,
	"change_type" varchar NOT NULL,
	"changed_by" integer NOT NULL,
	"change_reason" text,
	"previous_values" jsonb,
	"changed_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "listing_history" ADD CONSTRAINT "listing_history_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "public"."listings"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "listing_history" ADD CONSTRAINT "listing_history_changed_by_fkey" FOREIGN KEY ("changed_by") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "listing_history_listing_id_idx" ON "listing_history" USING btree ("listing_id");--> statement-breakpoint
CREATE INDEX "listing_history_changed_at_idx" ON "listing_history" USING btree ("changed_at");