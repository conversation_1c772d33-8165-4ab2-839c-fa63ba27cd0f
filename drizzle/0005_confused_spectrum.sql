CREATE TABLE "party_status" (
	"id" serial PRIMARY KEY NOT NULL,
	"party_id" integer NOT NULL,
	"status" "party_status_enum" NOT NULL,
	"created_at" timestamp,
	"updated_at" timestamp
);
--> statement-breakpoint
ALTER TABLE "party_status" ADD CONSTRAINT "party_status_party_id_party_id_fk" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "party_status" ADD CONSTRAINT "party_status_party_id_fkey" FOREIGN KEY ("party_id") REFERENCES "public"."party"("id") ON DELETE cascade ON UPDATE no action;