CREATE TABLE "province_state" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" varchar NOT NULL,
	"countryId" integer NOT NULL,
	CONSTRAINT "uq_province_or_state_name_country" UNIQUE("name","countryId")
);
--> statement-breakpoint
ALTER TABLE "province_state" ADD CONSTRAINT "province_state_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;