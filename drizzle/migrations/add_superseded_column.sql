-- Add superseded column to h_application_documents table
ALTER TABLE h_application_documents 
ADD COLUMN IF NOT EXISTS superseded BOOLEAN DEFAULT FALSE NOT NULL;

-- Create index for active (non-superseded) documents
CREATE INDEX IF NOT EXISTS idx_active_documents 
ON h_application_documents (application_id, document_type) 
WHERE superseded = FALSE;

-- Create index for superseded documents (for audit trail)
CREATE INDEX IF NOT EXISTS idx_superseded_documents 
ON h_application_documents (application_id, superseded, uploaded_at);
