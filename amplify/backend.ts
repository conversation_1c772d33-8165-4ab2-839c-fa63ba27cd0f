import { defineBackend } from "@aws-amplify/backend";
import { auth } from "./auth/resource.js";
import { data } from "./data/resource.js";
import { storage } from "./storage/resource";
import {
  Effect,
  PolicyStatement,
  ServicePrincipal,
  Policy,
} from "aws-cdk-lib/aws-iam";
import { postConfirmation } from "./auth/post-confirmation/resource.js";
import { CfnFunction } from "aws-cdk-lib/aws-lambda";
import * as sqs from "aws-cdk-lib/aws-sqs";
import * as aws_lambda_event_sources from "aws-cdk-lib/aws-lambda-event-sources";
import * as ses from "aws-cdk-lib/aws-ses";
import * as sns from "aws-cdk-lib/aws-sns";
import * as sns_subscriptions from "aws-cdk-lib/aws-sns-subscriptions";
import { Duration, Stack } from "aws-cdk-lib";
import { emailSender } from "./functions/email-sender/resource";
import { notifyUsers } from "./functions/notify-users/resource";
import { defineSesInviteTemplate } from "./ses-template/resource";

const backend = defineBackend({
  auth,
  data,
  storage,
  postConfirmation,
  emailSender,
  notifyUsers,
});

const postConfirmationLambda = backend.postConfirmation.resources.lambda;
const notifyUsersLambda = backend.notifyUsers.resources.lambda;
postConfirmationLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ["secretsmanager:GetSecretValue"],
    resources: [
      "arn:aws:secretsmanager:eu-west-1:097332785067:secret:poolly-database-secret-*",
    ],
  })
);
postConfirmationLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: [
      "cognito-idp:AdminUpdateUserAttributes",
      "cognito-idp:AdminAddUserToGroup",
    ],
    resources: ["arn:aws:cognito-idp:eu-west-1:097332785067:userpool/*"],
  })
);

notifyUsersLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ["secretsmanager:GetSecretValue"],
    resources: [
      "arn:aws:secretsmanager:eu-west-1:097332785067:secret:poolly-database-secret-*",
    ],
  })
);
notifyUsersLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: [
      "sqs:SendMessage", 
    ],
    resources: [
      "arn:aws:sqs:eu-west-1:097332785067:*", 
    ],
  })
);
const customResourceStack = backend.createStack("PoollyCustomResources");
const dlq = new sqs.Queue(customResourceStack, `EmailSendDeadLetter`, {
  retentionPeriod: Duration.days(14),
});
const sourceQueue = new sqs.Queue(customResourceStack, "emailSend", {
  deadLetterQueue: {
    queue: dlq,
    maxReceiveCount: 5,
  },
});

sourceQueue.grantSendMessages(backend.postConfirmation.resources.lambda);
sourceQueue.grantConsumeMessages(backend.emailSender.resources.lambda);

backend.emailSender.resources.lambda.addEventSource(
  new aws_lambda_event_sources.SqsEventSource(sourceQueue, {
    batchSize: 10,
    maxBatchingWindow: Duration.seconds(1),
    reportBatchItemFailures: true,
  })
);

const postLambda = backend.postConfirmation.resources.lambda.node
  .defaultChild as CfnFunction;
postLambda.addPropertyOverride(
  "Environment.Variables.EMAIL_QUEUE_URL",
  sourceQueue.queueUrl
);

const emailLambda = backend.emailSender.resources.lambda.node
  .defaultChild as CfnFunction;
emailLambda.addPropertyOverride(
  "Environment.Variables.EMAIL_QUEUE_URL",
  sourceQueue.queueUrl
);
const notifyLambda = backend.notifyUsers.resources.lambda.node
  .defaultChild as CfnFunction;
notifyLambda.addPropertyOverride(
  "Environment.Variables.EMAIL_QUEUE_URL",
  sourceQueue.queueUrl
);

const sqsPolicy = new Policy(backend.auth.stack, "sqsPolicy", {
  policyName: "sqsPolicy",
  statements: [
    new PolicyStatement({
      actions: ["sqs:SendMessage"],
      resources: [sourceQueue.queueArn],
    }),
  ],
});

backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(sqsPolicy);
backend.auth.resources.groups["ADMINS"].role.attachInlinePolicy(sqsPolicy);
backend.auth.resources.groups["GROUP_ADMIN"].role.attachInlinePolicy(sqsPolicy);
backend.auth.resources.groups["GROUP_USER"].role.attachInlinePolicy(sqsPolicy);

const sesStack = backend.createStack("SesEventPublishing");
const emailTopic = new sns.Topic(sesStack, "EmailEventsTopic", {});

const configurationSet = new ses.ConfigurationSet(
  sesStack,
  "EmailConfigSet",
  {}
);

emailLambda.addPropertyOverride(
  "Environment.Variables.AWS_SES_CONFIGURATION_SET",
  configurationSet.configurationSetName
);

configurationSet.addEventDestination("ToSns", {
  destination: ses.EventDestination.snsTopic(emailTopic),
  enabled: true,
  events: [
    ses.EmailSendingEvent.BOUNCE,
    ses.EmailSendingEvent.RENDERING_FAILURE,
  ],
});

emailTopic.addToResourcePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    principals: [new ServicePrincipal("ses.amazonaws.com")],
    actions: ["sns:Publish"],
    resources: [emailTopic.topicArn],
    conditions: {
      StringEquals: {
        "AWS:SourceAccount": sesStack.account,
        "AWS:SourceArn": `arn:aws:ses:${sesStack.region}:${sesStack.account}:configuration-set/*`,
      },
    },
  })
);

emailTopic.addSubscription(
  new sns_subscriptions.EmailSubscription("<EMAIL>")
);

const stack = Stack.of(backend.emailSender.resources.lambda);
const emailTemplate = defineSesInviteTemplate(stack);


notifyLambda.addPropertyOverride(
  "Environment.Variables.EMAIL_TEMPLATE_NAME",
  emailTemplate.logicalId
);

backend.addOutput({
  custom: {
    EmailQueueUrl: sourceQueue.queueUrl,
    SesConfigurationSetName: configurationSet.configurationSetName,
    inviteEmail: emailTemplate.logicalId,
  },
});
