import type { PostConfirma<PERSON>T<PERSON>gerHandler } from "aws-lambda";
import { Pool } from "pg";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import {
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
  AdminAddUserToGroupCommand,
} from "@aws-sdk/client-cognito-identity-provider";

async function updateUserAttributes(
  username: string,
  attributes: Record<string, string>
) {
  const userPoolId = process.env.COGNITO_USER_POOL_ID || "eu-west-1_UJEZ4brMP";
  if (!userPoolId) throw new Error("COGNITO_USER_POOL_ID is not set");

  const userAttributes = Object.entries(attributes).map(([Name, Value]) => ({
    Name,
    Value: String(Value),
  }));

  const command = new AdminUpdateUserAttributesCommand({
    UserPoolId: userPoolId,
    Username: username,
    UserAttributes: userAttributes,
  });

  const cognito = new CognitoIdentityProviderClient({
    region: process.env.AWS_REGION || "eu-west-1",
  });

  await cognito.send(command);
  console.log(`Successfully updated attributes for user: ${username}`);
}

async function addUserToGroup(username: string, groupName: string) {
  const userPoolId = process.env.COGNITO_USER_POOL_ID || "eu-west-1_UJEZ4brMP";
  if (!userPoolId) throw new Error("COGNITO_USER_POOL_ID is not set");

  const command = new AdminAddUserToGroupCommand({
    UserPoolId: userPoolId,
    Username: username,
    GroupName: groupName,
  });

  const cognito = new CognitoIdentityProviderClient({
    region: process.env.AWS_REGION || "eu-west-1",
  });

  await cognito.send(command);
  console.log(`User ${username} added to group ${groupName}`);
}

const secretsManager = new SecretsManagerClient({
  region: process.env.AWS_REGION || "us-east-1",
});

async function getDatabaseCredentials(secretId: string) {
  const command = new GetSecretValueCommand({ SecretId: secretId });
  const response = await secretsManager.send(command);

  if (!response.SecretString) {
    throw new Error("Secret string is empty");
  }
  return JSON.parse(response.SecretString);
}

async function createPool() {
  const secret = await getDatabaseCredentials(
    process.env.DB_SECRET_ID || "poolly-database-secret"
  );

  return new Pool({
    host: secret.POSTGRES_HOST,
    user: secret.POSTGRES_USER,
    password: secret.POSTGRES_PASSWORD,
    database: secret.POSTGRES_DB,
    port: secret.port || 5432,
    ssl: { rejectUnauthorized: false },
  });
}

// Rollback function to undo database changes
async function rollbackDatabaseChanges(client: any, partyId: string) {
  try {
    // Delete records in the reverse order of insertion to respect foreign key constraints
    await client.query("DELETE FROM public.contact_point WHERE party_id = $1", [
      partyId,
    ]);
    await client.query("DELETE FROM public.party_status WHERE party_id = $1", [
      partyId,
    ]);
    await client.query("DELETE FROM public.individual WHERE party_id = $1", [
      partyId,
    ]);
    await client.query("DELETE FROM public.party WHERE id = $1", [partyId]);
    await client.query("DELETE FROM public.users WHERE username = $1", [
      partyId,
    ]);
    console.log(`Rolled back database changes for party_id: ${partyId}`);
  } catch (error) {
    console.error("Failed to rollback database changes:", error);
    throw new Error("Rollback failed");
  }
}

export const handler: PostConfirmationTriggerHandler = async (event) => {
  let pool;
  let client;

  try {
    // Initialize the connection pool and client
    pool = await createPool();
    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
    client = await pool.connect();
    console.log("Connected to database!");

    // Start an explicit transaction
    await client.query("BEGIN");

    // SQL query with phone contact point, users table, and updated party_status
    const query = `
      WITH email_contact_type AS (
        INSERT INTO public.contact_point_type (name, description, validation_pattern, is_active, created_at, updated_at)
        VALUES ('email', 'Email', 'string', TRUE, NOW(), NOW())
        ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
        RETURNING id
      ),
      phone_contact_type AS (
        INSERT INTO public.contact_point_type (name, description, validation_pattern, is_active, created_at, updated_at)
        VALUES ('phone', 'Phone Number', 'string', TRUE, NOW(), NOW())
        ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
        RETURNING id
      ),
      individual_party_type AS (
        INSERT INTO public.party_type (name, description, is_active, created_at, updated_at)
        VALUES ('Individual', 'Individual party type', TRUE, NOW(), NOW())
        ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
        RETURNING id
      ),
      new_party AS (
        INSERT INTO public.party (party_type_id, external_id, created_at, updated_at)
        SELECT ipt.id, $1, NOW(), NOW()
        FROM individual_party_type ipt
        RETURNING id, external_id
      ),
      new_party_status AS (
        INSERT INTO public.party_status (party_id, status, created_at, updated_at)
        SELECT id, 'ACTIVE', NOW(), NOW()
        FROM new_party
        RETURNING party_id
      ),
      new_individual AS (
        INSERT INTO public.individual (party_id, first_name, last_name, created_at, updated_at)
        SELECT id, $2, $3, NOW(), NOW()
        FROM new_party
        RETURNING party_id
      ),
      new_email_contact AS (
        INSERT INTO public.contact_point (party_id, contact_point_type_id, value, is_primary, is_verified, created_at, updated_at)
        SELECT np.id, ect.id, $4, TRUE, TRUE, NOW(), NOW()
        FROM new_party np, email_contact_type ect
      ),
      new_phone_contact AS (
        INSERT INTO public.contact_point (party_id, contact_point_type_id, value, is_primary, is_verified, created_at, updated_at)
        SELECT np.id, pct.id, $5::character varying, FALSE, FALSE, NOW(), NOW()
        FROM new_party np, phone_contact_type pct
        WHERE $5::character varying IS NOT NULL
      ),
      new_user AS (
        INSERT INTO public.users (username, email, given_name, family_name, phone_number, created_at, updated_at)
        VALUES ($1, $4, $2, $3, $5::character varying, NOW(), NOW())
        RETURNING id
      )
      SELECT 
        p.id AS party_id,
        p.external_id AS sub
      FROM new_party p;
    `;

    // Extract user attributes
    const { email, sub, given_name, family_name, phone_number } =
      event.request.userAttributes;

    // Validate required attributes
    if (!email || !sub) {
      throw new Error("Missing required attributes: email or sub");
    }

    // Use default values for first_name, last_name, and phone_number if not provided
    const firstName = given_name || null;
    const lastName = family_name || null;
    const phoneNumber = phone_number || null;

    // Execute the query
    const values = [sub, firstName, lastName, email, phoneNumber];
    const result = await client.query(query, values);
    const partyId = result.rows[0]?.party_id;

    if (!partyId) {
      throw new Error("Failed to retrieve party_id from database");
    }

    // Commit the database transaction
    await client.query("COMMIT");

    // Perform Cognito operations after database transaction
    try {
      await updateUserAttributes(sub, {
        "custom:db_id": String(partyId),
      });
      await addUserToGroup(sub, "GROUP_USER");
    } catch (cognitoError) {
      // If Cognito fails, rollback database changes
      console.error(
        "Cognito operation failed, rolling back database changes:",
        cognitoError
      );
      await client.query("BEGIN");
      await rollbackDatabaseChanges(client, partyId);
      await client.query("COMMIT");
      throw new Error("Cognito operation failed, database changes rolled back");
    }

    // Return the event to complete the Lambda execution
    return event;
  } catch (error) {
    // Rollback the database transaction if it was started
    if (client) {
      await client.query("ROLLBACK");
    }
    console.error("Error in handler:", error);
    return event;
  } finally {
    // Release the client and end the pool
    if (client) client.release();
    if (pool) await pool.end();
  }
};
