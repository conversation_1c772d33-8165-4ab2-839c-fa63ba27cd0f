import { defineAuth, secret } from "@aws-amplify/backend";
import { postConfirmation } from "./post-confirmation/resource";

/**
 * Define and configure your auth resource
 * @see https://docs.amplify.aws/gen2/build-a-backend/auth
 */
export const auth = defineAuth({
  loginWith: {
    email: true,
    externalProviders: {
      google: {
        clientId: secret("GOOGLE_CLIENT_ID"),
        clientSecret: secret("GOOGLE_CLIENT_SECRET"),
        // birthdate, phone_number, given_name, family_name
        scopes: ["profile", "email", "openid"],
        attributeMapping: {
          givenName: "name",
          familyName: "family_name",
          birthdate: "birthdays",
          phoneNumber: "phoneNumbers",
          email: "email",
          gender: "genders",
          emailVerified: "email_verified",
          profilePicture: "picture",
        },
      },
      callbackUrls: ["https://hq0lvr4n-3008.inc1.devtunnels.ms/home"],
      logoutUrls: ["https://hq0lvr4n-3008.inc1.devtunnels.ms/home"],
    },
  },

  
  accountRecovery: 'EMAIL_AND_PHONE_WITHOUT_MFA',
  userAttributes: {
    email: {
      mutable: true,
      required: true,
    },
    familyName: {
      mutable: true,
      required: true,
    },

    givenName: {
      mutable: true,
      required: true,
    },
    birthdate: {
      mutable: true,
      required: true,
    },

    phoneNumber: {
      mutable: true,
      required: true,
    },
    "custom:db_id": {
      dataType: "String",
      mutable: true,
      maxLen: 16,
      minLen: 1,
    },
  },
  triggers: {
    postConfirmation,
  },
  groups: ["ADMINS", "GROUP_ADMIN", "GROUP_USER"],
});
