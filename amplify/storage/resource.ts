import { defineStorage } from "@aws-amplify/backend";

export const storage = defineStorage({
  name: "poolly",
  versioned: true,
  access: (allow) => ({
    "profile-pictures/*": [
      allow.groups(["ADMINS", "GROUP_ADMIN"]).to(["read", "write", "delete"]),
      allow.groups(["GROUP_USER"]).to(["read", "write", "delete"]),
      allow.authenticated.to(["read", "write", "delete"]),
    ],
    "vehicleMedia/*": [
      allow.groups(["ADMINS"]).to(["read", "write", "delete"]),
      allow
        .groups(["GROUP_ADMIN", "GROUP_USER"])
        .to(["read", "write", "delete"]),
      allow.authenticated.to(["read", "write", "delete"]),
    ],
    "vehicleDocuments/{entity_id}/*": [
      allow.entity("identity").to(["read", "write", "delete"]),
      allow.groups(["ADMINS"]).to(["read", "write", "delete"]),
      allow.groups(["GROUP_ADMIN", "GROUP_USER"]).to(["read", "write", "delete"]),
      allow.authenticated.to(["read"]),
    ],
    "partyDocuments/*": [
      allow.groups(["ADMINS"]).to(["read", "write", "delete"]),
      allow
        .groups(["GROUP_ADMIN", "GROUP_USER"])
        .to(["read", "write", "delete"]),
      allow.authenticated.to(["read", "write", "delete"]),
    ],
    "listingsMedia/*": [
      allow.groups(["ADMINS"]).to(["read", "write", "delete"]),
      allow
        .groups(["GROUP_ADMIN", "GROUP_USER"])
        .to(["read", "write", "delete"]),
      allow.authenticated.to(["read", "write", "delete"]),
      allow.guest.to(["read"]),
    ],
    "applications/*": [
      allow.groups(["ADMINS"]).to(["read", "write", "delete"]),
      allow
        .groups(["GROUP_ADMIN", "GROUP_USER"])
        .to(["read", "write", "delete"]),
      allow.authenticated.to(["read", "write", "delete"]),
    ],
  }),
});
