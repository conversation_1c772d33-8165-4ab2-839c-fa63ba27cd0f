import { Stack } from "aws-cdk-lib";
import { CfnTemplate } from "aws-cdk-lib/aws-ses";

export const defineSesInviteTemplate = (stack: Stack): CfnTemplate => {
  const sesTemplate = new CfnTemplate(stack, "PoollyTemplate", {
    template: {
      subjectPart: "{{subject}}",
      textPart: `{{content}}\n\nFind out more: https://dev.poollysa.com\n\nBest,\nThe Poolly Team`,
      htmlPart: `
               <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body
    style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif; background-color: #ffffff;">
    <table role="presentation" width="100%" cellspacing="0" cellpadding="0" style="padding: 20px;">
        <tr>
            <td align="center">
                <table role="presentation" width="100%"
                    style="max-width: 600px; background-color: #ffffff; border: 1px solid #e0e0e0;">
                    <!-- Header -->
                    <tr>
                        <td style="padding: 10px 20px; text-align: center;">
                            <img src="https://poollysa.com/images/poolly-logo-web.png" alt="Poolly Logo"
                                style="max-width: 150px; height: auto;">
                            <p style="font-size: 12px; color: #666666; margin: 5px 0 0;">Making it possible for everyone
                            </p>
                        </td>
                    </tr>
                    <!-- Banner -->
                    <tr>

                    </tr>
                    <!-- Body -->
                    <tr>
                        <td style="padding: 20px; text-align: center;">
                            <h2 style="color: #27ae60; font-size: 24px; margin: 0 0 10px;">Hi {{name}}</h2>
                            <p style="font-size: 16px; color: #333333; line-height: 1.5; margin: 0 0 20px;">
                                {{ content}}
                            </p>
                            <a href="https://dev.poollysa.com/home"
                                style="display: inline-block; padding: 12px 24px; background-color: #27ae60; color: #ffffff; text-decoration: none; font-size: 16px; font-weight: bold; border-radius: 5px;">
                                Find Out More
                            </a>
                        </td>
                    </tr>
                    <!-- Social -->
                    <!-- Social -->
                    <tr>
                        <td style="padding: 20px; text-align: center; background-color: #f4f4f4;">
                            <p style="font-size: 14px; color: #666666; margin: 0 0 10px;">Follow Us</p>
                            <a href="https://facebook.com"
                                style="margin: 0 10px; display: inline-block; vertical-align: middle;">
                                <img src="https://dev.poollysa.com/images/facebook-f-brands-solid.png" alt="Facebook"
                                    style="width: 24px; height: 24px; margin-bottom: 10px;">
                            </a>
                            <a href="https://instagram.com"
                                style="margin: 0 10px; display: inline-block; vertical-align: middle;">
                                <img src="https://dev.poollysa.com/images/instagram-brands-solid.png" alt="Instagram"
                                    style="width: 24px; height: 24px; margin-bottom: 10px;">
                            </a>
                            <a href="https://x.com"
                                style="margin: 0 10px; display: inline-block; vertical-align: middle;">
                                <img src="https://dev.poollysa.com/images/twitter-brands-solid.png" alt="Twitter"
                                    style="width: 24px; height: 24px; margin-bottom: 10px;">
                            </a>
                        </td>
                    </tr>

                    <!-- Footer -->
                    <tr>
                        <td
                            style="padding: 20px; text-align: center; font-size: 12px; color: #666666; background-color: #f4f4f4;">
                            <img src="https://dev.poollysa.com/images/poolly-logo.svg" alt="Poolly Logo"
                                style="max-width: 150px; height: auto; margin-bottom: 10px;">
                            <p style="margin: 0 0 10px;">Copyright © 2025 Poolly. All rights reserved.</p>
                            <p style="margin: 0 0 10px;">Want to change how you receive these emails? You can update
                                your preferences or unsubscribe</p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
      `,
    },
  });
  return sesTemplate;
};
