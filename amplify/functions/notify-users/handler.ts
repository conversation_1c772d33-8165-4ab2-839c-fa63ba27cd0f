import { <PERSON><PERSON> } from "aws-lambda";
import { Pool, PoolClient } from "pg";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import { SQSClient, SendMessageBatchCommand } from "@aws-sdk/client-sqs";

const secretsManager = new SecretsManagerClient({
  region: process.env.AWS_REGION || "eu-west-1",
});

const sqsClient = new SQSClient({
  region: process.env.AWS_REGION || "eu-west-1",
});

async function getDatabaseCredentials(secretId: string) {
  const command = new GetSecretValueCommand({ SecretId: secretId });
  const response = await secretsManager.send(command);

  if (!response.SecretString) {
    throw new Error("Secret string is empty");
  }
  return JSON.parse(response.SecretString);
}

async function createPool() {
  const secret = await getDatabaseCredentials(process.env.SECRET_NAME || "");

  return new Pool({
    host: secret.POSTGRES_HOST,
    user: secret.POSTGRES_USER,
    password: secret.POSTGRES_PASSWORD,
    database: secret.POSTGRES_DB,
    port: secret.port || 5432,
    ssl: { rejectUnauthorized: false },
  });
}

export const handler: Handler = async (event, context) => {
  let pool: Pool | undefined;
  let client: PoolClient | undefined;

  try {
    pool = await createPool();
    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
    client = await pool.connect();
    console.log("Connected to database!");

    const query = `
      SELECT * FROM tasks 
      WHERE created_at::date = CURRENT_DATE 
      OR (expires_at IS NOT NULL AND expires_at::date = CURRENT_DATE)
    `;
    const tasksResults = await client.query(query);

    const queueUrl = process.env.EMAIL_QUEUE_URL || "";
    const batchSize = 10; // SQS allows up to 10 messages per batch
    const sendMessageBatchPromises: Promise<any>[] = [];

    // Process tasks in batches
    for (let i = 0; i < tasksResults.rows.length; i += batchSize) {
      const batch = tasksResults.rows
        .slice(i, i + batchSize)
        .map((task, index) => {
          let messageBody;

          switch (task.type) {
            case "BOOKING_ACKNOWLEDGMENT":
              messageBody = {
                to: [task.email],
                templateName: process.env.EMAIL_TEMPLATE_NAME || "",
                templateData: {
                  name: task.metadata?.userName || "User",
                  subject: `Booking Confirmation for ${task.metadata?.vehicleName || "A Vehicle"}`,
                  content: `Your booking with ID ${task.metadata?.bookingReference || "Unknown Booking"} needs to be confirmed. Please review the details and contact us if you have any questions.`,
                },
              };
              break;

            case "VEHICLE_HANDOVER_GIVER":
              messageBody = {
                to: [task.email],
                templateName: process.env.EMAIL_TEMPLATE_NAME || "",
                templateData: {
                  name: task.metadata?.borrowerName || "User",
                  subject: `Vehicle Handover Reminder for ${task.metadata?.vehicleName || "A Vehicle"}`,
                  content: `Please prepare to hand over the vehicle with ID ${task.metadata?.vehicleName || "A Vehicle"} at the scheduled time. Ensure all necessary documents are ready.`,
                },
              };
              break;

            case "VEHICLE_HANDOVER_RECEIVER":
              messageBody = {
                to: [task.email],
                templateName: process.env.EMAIL_TEMPLATE_NAME || "",
                templateData: {
                  name: task.metadata?.currentHolderName || "User",
                  subject: `Vehicle Handover Confirmation for ${task.metadata?.vehicleName || "A Vehicle"}`,
                  content: `You are scheduled to receive the vehicle ${task.metadata?.vehicleName || "A Vehicle"}. Please be at the designated location on time.`,
                },
              };
              break;

            case "GROUP_INVITATION":
              messageBody = {
                to: [task.email],
                templateName: process.env.EMAIL_TEMPLATE_NAME || "",
                templateData: {
                  name: task.metadata?.inviterName || "Poolly User",
                  subject: `Reminder: Confirm Your Invitation to Join "${task.metadata?.groupName || "A Group"}"`,
                  content: `This is a reminder to confirm your invitation to join the group "${task.metadata?.groupName || "A Group"}" as a ${task.metadata?.role || "MEMBER"}.`,
                },
              };
              break;

            default:
              messageBody = {
                to: [task.email],
                templateName: process.env.EMAIL_TEMPLATE_NAME || "",
                templateData: {
                  name: task.metadata?.giverName || "User",
                  subject: `Reminder`,
                  content: `Please  attend to the task related to ${task.metadata?.vehicleId || "A Vehicle"}. Ensure all necessary actions are taken.`,
                },
              };
          }

          return {
            Id: `${task.id}_${index}`, // Unique ID for each message in the batch
            MessageBody: JSON.stringify(messageBody),
          };
        });

      const command = new SendMessageBatchCommand({
        QueueUrl: queueUrl,
        Entries: batch,
      });

      sendMessageBatchPromises.push(sqsClient.send(command));
    }

    // Wait for all batch requests to complete
    const responses = await Promise.all(sendMessageBatchPromises);

    // Check for failed messages in the batches
    const failedMessages = responses
      .flatMap((response) => response.Failed || [])
      .map((failed) => ({
        id: failed.Id,
        message: failed.Message,
        code: failed.Code,
      }));

    if (failedMessages.length > 0) {
      console.warn("Some messages failed to send:", failedMessages);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Some messages failed to send",
          failed: failedMessages,
        }),
      };
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: `Successfully sent ${tasksResults.rows.length} email(s) in ${responses.length} batch(es)`,
        responses,
      }),
    };
  } catch (error) {
    console.error("Error:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Error processing notifications",
        error: (error as Error).message,
      }),
    };
  } finally {
    if (client) client.release();
    if (pool) await pool.end();
  }
};
