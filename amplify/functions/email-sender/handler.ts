import {
  SESClient,
  SendTemplatedEmailCommand,
  ListTemplatesCommand,
} from "@aws-sdk/client-ses";
import type { Handler } from "aws-lambda";

const sesClient = new SESClient({
  region: process.env.AWS_SES_REGION || "eu-west-1",
  credentials: {
    accessKeyId: process.env.AWS_SES_ACCESS_KEY || "",
    secretAccessKey: process.env.AWS_SES_SECRET_KEY || "",
  },
});

export async function findLatestSesTemplateByLogicalId(
  logicalId: string
): Promise<string | null> {
  const command = new ListTemplatesCommand({});
  try {
    const response = await sesClient.send(command);
    const templates = (response.TemplatesMetadata || []).filter(
      (t) => t.Name && t.CreatedTimestamp
    ) as { Name: string; CreatedTimestamp: Date | string }[];
    const matchingTemplates = templates.filter((t) =>
      t.Name.includes(logicalId)
    );

    if (matchingTemplates.length === 0) {
      return null;
    }

    matchingTemplates.sort(
      (a, b) =>
        new Date(b.CreatedTimestamp).getTime() -
        new Date(a.CreatedTimestamp).getTime()
    );

    return matchingTemplates[0].Name;
  } catch (error) {
    console.error("Error finding latest SES template by logicalId:", error);
    throw error;
  }
}

export const handler: Handler = async (event, context) => {
  const batchItemFailures = [];
  for (const record of event.Records) {
    try {
      const message = JSON.parse(record.body);
      console.log(message);
      const { to, templateName, templateData } = message;
      const latestTemplateName =
        (await findLatestSesTemplateByLogicalId(templateName)) ||
        " default-template";
      if (
        !Array.isArray(to) ||
        to.length === 0 ||
        !templateName ||
        !templateData
      ) {
        throw new Error(
          `Missing or invalid fields: to=${to}, templateName=${templateName}, templateData=${JSON.stringify(templateData)}`
        );
      }
      let templateDataForSES = templateData;

      if (
        templateData &&
        typeof templateData === "object" &&
        typeof templateData.body === "string"
      ) {
        try {
          templateDataForSES = JSON.parse(templateData.body);
        } catch (e) {
          console.error("Invalid JSON in templateData.body", e);
          throw new Error("Invalid templateData.body JSON");
        }
      }
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const invalidEmails = to.filter(
        (email: string) => !emailRegex.test(email)
      );
      if (invalidEmails.length > 0) {
        throw new Error(
          `Invalid email address(es): ${invalidEmails.join(", ")}`
        );
      }

      const params = {
        Source: process.env.AWS_SES_VERIFIED_MAIL || "",
        Destination: { ToAddresses: to },
        Template: latestTemplateName,
        TemplateData: JSON.stringify(templateData),
        ConfigurationSetName: process.env.AWS_SES_CONFIGURATION_SET || "",
      };

      await sesClient.send(new SendTemplatedEmailCommand(params));
      console.log(`MessageId: ${record.messageId} sent successfully`);
    } catch (error: any) {
      console.error(
        `Failed to send email for MessageId: ${record.messageId}, Error: ${error?.message}`
      );
      batchItemFailures.push({ itemIdentifier: record.messageId });
    }
  }

  return { batchItemFailures };
};
