# Design Document

## Overview

The Payment and Contract Management system extends the existing vehicle assignment functionality to handle comprehensive financial tracking and contract management. The system manages initial deposits, contract uploads, weekly e-hailing earnings tracking, driver payouts, debt management, and provides complete audit trails for all financial transactions.

The design leverages the existing assignment infrastructure while introducing new database tables for financial transactions, contract management, and weekly payment tracking. All financial data follows immutability principles with audit trails, ensuring compliance and data integrity.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Assignments Page] --> B[Payment Tracking Page]
        A --> C[Contract Upload Dialog]
        B --> D[Weekly Payment Dialog]
        E[User Dashboard] --> F[Contract Viewer]
    end

    subgraph "API Layer"
        G[Assignment Actions] --> H[Payment Actions]
        H --> I[Contract Actions]
        I --> J[Weekly Earnings Actions]
    end

    subgraph "Database Layer"
        K[vehicle_assignments] --> L[assignment_deposits]
        K --> M[assignment_contracts]
        K --> N[weekly_earnings]
        N --> O[driver_payouts]
        N --> P[driver_debt_tracking]
    end

    subgraph "Storage Layer"
        Q[S3 Contract Storage]
    end

    A --> G
    B --> H
    C --> I
    D --> J
    M --> Q
```

### Database Schema Design

Following the established h_schema patterns with proper temporal fields, enums, and referential integrity. The system introduces six new tables that maintain consistency with existing schema conventions:

#### Enums

```typescript
// drizzle/h_schema/enums.ts additions
export const paymentStatusEnum = pgEnum("payment_status", [
  "pending",
  "paid",
  "failed",
  "cancelled",
]);

export const paymentMethodEnum = pgEnum("payment_method", [
  "bank_transfer",
  "cash",
  "eft",
  "card",
  "other",
]);

export const debtSourceTypeEnum = pgEnum("debt_source_type", [
  "earnings_shortfall",
  "debt_recovery",
  "adjustment",
]);

export const platformNameEnum = pgEnum("platform_name", [
  "uber",
  "bolt",
  "indriver",
  "other",
]);
```

#### 1. h_assignment_deposits

Tracks initial deposit payments and balances for vehicle assignments.

```typescript
// drizzle/h_schema/payment-contract.ts
export const h_assignment_deposits = pgTable("h_assignment_deposits", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(), // References existing assignment system
  depositAmount: decimal("deposit_amount", {
    precision: 10,
    scale: 2,
  }).notNull(),
  amountPaid: decimal("amount_paid", { precision: 10, scale: 2 })
    .notNull()
    .default("0"),
  balanceRemaining: decimal("balance_remaining", {
    precision: 10,
    scale: 2,
  }).notNull(),
  paymentMethod: paymentMethodEnum("payment_method"),
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }),
  notes: text("notes"),
  // ...temporalFields,
});
```

#### 2. h_assignment_contracts

Manages contract documents for vehicle assignments.

```typescript
export const h_assignment_contracts = pgTable("h_assignment_contracts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  contractFilePath: text("contract_file_path").notNull(),
  originalFilename: text("original_filename").notNull(),
  fileSize: integer("file_size").notNull(),
  mimeType: text("mime_type").notNull(),
  uploadedAt: timestamp("uploaded_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  uploadedBy: integer("uploaded_by")
    .references(() => party.id)
    .notNull(),
  replacedBy: integer("replaced_by").references(
    () => h_assignment_contracts.id
  ),
  notes: text("notes"),
  // ...temporalFields,
});
```

#### 3. h_weekly_earnings

Records weekly e-hailing platform earnings for each driver.

```typescript
export const h_weekly_earnings = pgTable(
  "h_weekly_earnings",
  {
    id: serial().primaryKey().notNull(),
    assignmentId: integer("assignment_id").notNull(),
    weekStartDate: date("week_start_date").notNull(),
    weekEndDate: date("week_end_date").notNull(),
    grossEarnings: decimal("gross_earnings", {
      precision: 10,
      scale: 2,
    }).notNull(),
    platformName: platformNameEnum("platform_name").notNull().default("uber"),
    weeklyTarget: decimal("weekly_target", { precision: 10, scale: 2 })
      .notNull()
      .default("2700"),
    earningsShortfall: decimal("earnings_shortfall", {
      precision: 10,
      scale: 2,
    }).$defaultFn(
      () =>
        sql`CASE WHEN gross_earnings < weekly_target THEN weekly_target - gross_earnings ELSE 0 END`
    ),
    recordedBy: integer("recorded_by")
      .references(() => party.id)
      .notNull(),
    notes: text("notes"),
    // ...temporalFields,
  },
  (table) => [
    unique("unique_assignment_week").on(
      table.assignmentId,
      table.weekStartDate
    ),
  ]
);
```

#### 4. h_driver_payouts

Tracks payouts made to drivers based on their weekly performance.

```typescript
export const h_driver_payouts = pgTable("h_driver_payouts", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  weeklyEarningsId: integer("weekly_earnings_id")
    .references(() => h_weekly_earnings.id)
    .notNull(),
  grossPayout: decimal("gross_payout", { precision: 10, scale: 2 }).notNull(),
  debtDeduction: decimal("debt_deduction", { precision: 10, scale: 2 })
    .notNull()
    .default("0"),
  netPayout: decimal("net_payout", { precision: 10, scale: 2 }).notNull(),
  paymentMethod: paymentMethodEnum("payment_method"),
  paymentReference: text("payment_reference"),
  paymentDate: timestamp("payment_date", { withTimezone: true }),
  processedBy: integer("processed_by")
    .references(() => party.id)
    .notNull(),
  status: paymentStatusEnum("status").notNull().default("pending"),
  notes: text("notes"),
  // ...temporalFields,
});
```

#### 5. h_driver_debt_tracking

Maintains running debt balances for drivers who underperform weekly targets.

```typescript
export const h_driver_debt_tracking = pgTable("h_driver_debt_tracking", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  debtSourceId: integer("debt_source_id").notNull(), // References h_weekly_earnings.id or h_driver_payouts.id
  debtSourceType: debtSourceTypeEnum("debt_source_type").notNull(),
  debtAmount: decimal("debt_amount", { precision: 10, scale: 2 }).notNull(),
  runningBalance: decimal("running_balance", {
    precision: 10,
    scale: 2,
  }).notNull(),
  transactionDate: timestamp("transaction_date", { withTimezone: true })
    .defaultNow()
    .notNull(),
  notes: text("notes"),
  // ...temporalFields,
});
```

#### 6. h_payment_audit_log

Comprehensive audit trail for all payment-related activities.

```typescript
export const h_payment_audit_log = pgTable("h_payment_audit_log", {
  id: serial().primaryKey().notNull(),
  assignmentId: integer("assignment_id").notNull(),
  actionType: text("action_type").notNull(),
  tableName: text("table_name").notNull(),
  recordId: integer("record_id").notNull(),
  oldValues: jsonb("old_values"),
  newValues: jsonb("new_values"),
  performedAt: timestamp("performed_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  performedBy: integer("performed_by")
    .references(() => party.id)
    .notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  // ...temporalFields,
});
```

## Components and Interfaces

### Frontend Components

#### 1. Enhanced Assignment Page Components

**DepositPaymentDialog**

- Handles initial deposit payment recording
- Calculates and displays remaining balance
- Integrates with existing payment recording patterns

**ContractUploadDialog**

- Leverages existing `ApplicationDocumentUploader` patterns
- Uploads contracts to S3 storage
- Associates contracts with specific assignments

#### 2. Enhanced Payment Page Components

**WeeklyEarningsDialog**

- Records weekly Uber/e-hailing earnings
- Calculates shortfalls against targets
- Triggers debt tracking when applicable

**DriverPayoutDialog**

- Calculates gross payouts from earnings
- Applies debt deductions automatically
- Records net payout amounts

**DebtManagementPanel**

- Displays current debt balances per driver
- Shows debt recovery history
- Provides debt adjustment capabilities

#### 3. User Dashboard Components

**ContractViewer**

- Displays uploaded contracts for assigned users
- Provides download functionality
- Shows contract history and versions

### API Actions

#### 1. Deposit Management Actions

```typescript
// actions/admin/deposits.ts
export async function recordDepositPayment(
  assignmentId: string,
  depositAmount: number,
  amountPaid: number,
  paymentMethod?: string,
  paymentReference?: string,
  notes?: string
): Promise<ActionResult<DepositRecord>>;

export async function getAssignmentDeposits(
  assignmentId: string
): Promise<ActionResult<DepositRecord[]>>;
```

#### 2. Contract Management Actions

```typescript
// actions/admin/contracts.ts
export async function uploadAssignmentContract(
  assignmentId: string,
  contractFile: File,
  uploadedBy: string,
  notes?: string
): Promise<ActionResult<ContractRecord>>;

export async function getAssignmentContract(
  assignmentId: string
): Promise<ActionResult<ContractRecord | null>>;
```

#### 3. Weekly Earnings Actions

```typescript
// actions/admin/weekly-earnings.ts
export async function recordWeeklyEarnings(
  assignmentId: string,
  weekStartDate: string,
  grossEarnings: number,
  platformName: string,
  weeklyTarget: number,
  notes?: string
): Promise<ActionResult<WeeklyEarningsRecord>>;

export async function processDriverPayout(
  weeklyEarningsId: string,
  paymentMethod?: string,
  paymentReference?: string,
  notes?: string
): Promise<ActionResult<PayoutRecord>>;
```

#### 4. Debt Management Actions

```typescript
// actions/admin/debt-management.ts
export async function getDriverDebtBalance(
  assignmentId: string
): Promise<ActionResult<number>>;

export async function getDriverDebtHistory(
  assignmentId: string
): Promise<ActionResult<DebtRecord[]>>;
```

## Data Models

### TypeScript Interfaces

```typescript
// types/payment-contract.ts
export interface DepositRecord {
  id: string;
  assignmentId: string;
  depositAmount: number;
  amountPaid: number;
  balanceRemaining: number;
  paymentMethod?: string;
  paymentReference?: string;
  paymentDate?: string;
  createdAt: string;
  createdBy: string;
  notes?: string;
}

export interface ContractRecord {
  id: string;
  assignmentId: string;
  contractFilePath: string;
  originalFilename: string;
  fileSize: number;
  mimeType: string;
  uploadedAt: string;
  uploadedBy: string;
  isActive: boolean;
  replacedBy?: string;
  notes?: string;
}

export interface WeeklyEarningsRecord {
  id: string;
  assignmentId: string;
  weekStartDate: string;
  weekEndDate: string;
  grossEarnings: number;
  platformName: string;
  weeklyTarget: number;
  earningsShortfall: number;
  recordedAt: string;
  recordedBy: string;
  notes?: string;
}

export interface PayoutRecord {
  id: string;
  assignmentId: string;
  weeklyEarningsId: string;
  grossPayout: number;
  debtDeduction: number;
  netPayout: number;
  paymentMethod?: string;
  paymentReference?: string;
  paymentDate?: string;
  processedAt: string;
  processedBy: string;
  status: "pending" | "paid" | "failed";
  notes?: string;
}

export interface DebtRecord {
  id: string;
  assignmentId: string;
  debtSourceId: string;
  debtSourceType: "earnings_shortfall" | "debt_recovery";
  debtAmount: number;
  runningBalance: number;
  transactionDate: string;
  createdBy: string;
  notes?: string;
}
```

## Error Handling

### Validation Rules

1. **Deposit Payments**
   - Amount paid cannot exceed deposit amount
   - Balance must equal deposit amount minus amount paid
   - Payment date required when amount paid > 0

2. **Contract Uploads**
   - File size limit: 10MB
   - Allowed formats: PDF, JPG, PNG
   - One active contract per assignment

3. **Weekly Earnings**
   - Cannot record duplicate weeks for same assignment
   - Earnings must be non-negative
   - Week dates must be valid Monday-Sunday periods

4. **Driver Payouts**
   - Cannot exceed gross earnings minus weekly target
   - Debt deductions cannot exceed available debt balance
   - Net payout must equal gross payout minus debt deduction

### Error Response Format

```typescript
interface PaymentError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

interface ActionResult<T> {
  success: boolean;
  data?: T;
  error?: PaymentError;
}
```

## Testing Strategy

### Unit Tests

1. **Database Operations**
   - Test all CRUD operations for new tables
   - Verify referential integrity constraints
   - Test calculated fields and triggers

2. **Business Logic**
   - Deposit balance calculations
   - Debt tracking and recovery logic
   - Payout calculations with debt deductions

3. **API Actions**
   - Input validation and sanitization
   - Error handling and response formatting
   - Authentication and authorization

### Integration Tests

1. **End-to-End Workflows**
   - Complete assignment with deposit and contract
   - Weekly earnings recording and payout processing
   - Debt accumulation and recovery scenarios

2. **File Upload Testing**
   - Contract upload to S3
   - File validation and security
   - Download and viewing functionality

### Performance Tests

1. **Database Performance**
   - Query performance with large datasets
   - Index effectiveness for common queries
   - Concurrent transaction handling

2. **File Operations**
   - Large contract file uploads
   - Multiple simultaneous uploads
   - S3 storage and retrieval performance

## Security Considerations

### Data Protection

1. **Financial Data Encryption**
   - Encrypt sensitive financial amounts at rest
   - Use secure connections for all financial transactions
   - Implement field-level encryption for payment references

2. **Contract Security**
   - Secure S3 bucket configuration with proper IAM policies
   - Signed URLs for contract downloads with expiration
   - Virus scanning for uploaded contract files

3. **Access Control**
   - Role-based access to financial data
   - Admin-only access to payment recording functions
   - Audit logging for all financial operations

### Compliance

1. **Financial Regulations**
   - Maintain immutable audit trails
   - Implement proper data retention policies
   - Ensure transaction traceability

2. **Data Privacy**
   - Anonymize financial data in logs
   - Implement right to erasure for GDPR compliance
   - Secure handling of personal financial information
