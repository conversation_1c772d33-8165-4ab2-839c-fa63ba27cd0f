# Requirements Document

## Introduction

This feature implements a comprehensive payment and contract management system for vehicle assignments. When users are assigned vehicles, they must make initial deposits and sign contracts. The system tracks ongoing weekly payments from e-hailing platforms (like Uber), calculates driver payouts based on performance targets, and maintains detailed audit trails of all financial transactions. The system handles scenarios where drivers exceed or fall short of weekly targets, with automatic debt tracking and recovery mechanisms.

## Requirements

### Requirement 1

**User Story:** As an assigned vehicle user, I want to make an initial deposit payment when I receive a vehicle assignment, so that I can secure my vehicle and begin using it for e-hailing services.

#### Acceptance Criteria

1. WHEN a user is assigned a vehicle THEN the system SHALL require an initial deposit payment
2. WHEN a user makes a partial deposit payment THEN the system SHALL record the amount paid and calculate the remaining balance
3. WHEN a deposit balance exists THEN the system SHALL track this balance and apply it to future payment calculations
4. WHEN a deposit is made THEN the system SHALL create an immutable transaction record with timestamp, amount, and payment method

### Requirement 2

**User Story:** As an admin, I want to upload a signed contract document on behalf of an assigned vehicle user, so that there is a legal agreement for their vehicle usage that they can reference later.

#### Acceptance Criteria

1. WHEN a user is assigned a vehicle THEN the system SHALL require a signed contract to be uploaded by the admin
2. WHEN an admin uploads a contract document for a user THEN the system SHALL validate the file format and store it securely
3. WHEN a contract is uploaded THEN the system SHALL make it viewable in the assigned user's dashboard
4. WHEN a user accesses their dashboard THEN the system SHALL display their current contract with download capability

### Requirement 3

**User Story:** As an admin, I want to record weekly e-hailing earnings for each driver, so that I can track their performance against targets and calculate appropriate payouts.

#### Acceptance Criteria

1. WHEN an admin accesses the payments page THEN the system SHALL display all active vehicle assignments
2. WHEN an admin records weekly earnings THEN the system SHALL capture the gross amount earned from the e-hailing platform
3. WHEN weekly earnings are recorded THEN the system SHALL create an immutable earnings record with driver ID, week period, and amount
4. WHEN earnings are below the weekly target THEN the system SHALL flag this as a shortfall and record the deficit amount

### Requirement 4

**User Story:** As an admin, I want to calculate and record driver payouts based on their weekly performance, so that drivers receive appropriate compensation while covering platform costs.

#### Acceptance Criteria

1. WHEN weekly earnings exceed the target amount THEN the system SHALL calculate the driver's payout as the excess amount
2. WHEN weekly earnings meet or exceed targets THEN the system SHALL process a payout to the driver
3. WHEN a driver has outstanding debt from previous weeks THEN the system SHALL deduct debt from current payouts before releasing funds
4. WHEN a payout is processed THEN the system SHALL create an immutable payout record with amount, debt deductions, and net payment

### Requirement 5

**User Story:** As an admin, I want to track driver debt and automatic recovery, so that platform costs are covered even when drivers underperform.

#### Acceptance Criteria

1. WHEN weekly earnings are below target THEN the system SHALL calculate and record the shortfall as debt
2. WHEN a driver has accumulated debt THEN the system SHALL display the total outstanding amount in the admin interface
3. WHEN a driver earns above target in subsequent weeks THEN the system SHALL automatically apply excess earnings to reduce outstanding debt
4. WHEN debt is partially or fully recovered THEN the system SHALL update the debt balance and create recovery transaction records

### Requirement 6

**User Story:** As a system administrator, I want all financial transactions to be immutable and auditable, so that we maintain complete financial integrity and compliance.

#### Acceptance Criteria

1. WHEN any financial transaction occurs THEN the system SHALL create immutable records that cannot be updated
2. WHEN transaction data needs correction THEN the system SHALL create new corrective entries rather than modifying existing records
3. WHEN querying transaction history THEN the system SHALL provide complete audit trails with timestamps and user attribution
4. WHEN financial reports are generated THEN the system SHALL ensure all calculations are based on the complete transaction history

### Requirement 7

**User Story:** As an admin, I want to view comprehensive payment dashboards, so that I can monitor all financial activities and driver performance across the platform.

#### Acceptance Criteria

1. WHEN an admin accesses the assignments page THEN the system SHALL display initial deposit amounts and balances for all vehicle assignments
2. WHEN an admin accesses the payments page THEN the system SHALL show weekly transaction tracking for all active drivers
3. WHEN viewing payment records THEN the system SHALL display both Uber earnings and driver payouts with clear status indicators
4. WHEN reviewing driver performance THEN the system SHALL show weekly targets, actual earnings, and debt status for each driver
