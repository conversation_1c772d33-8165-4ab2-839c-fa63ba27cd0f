# Design Document

## Overview

This design enhances the ApplicationReviewPageClient component to properly handle document grouping, status display, and re-upload scenarios. The solution groups documents by type, displays historical versions, shows current status information, and ensures verification actions only apply to the latest document version.

The system leverages the existing `h_application_documents` and `h_application_documents_status` tables to provide a comprehensive document management interface for admin users.

## Architecture

### Current System Analysis

The current system has these key components:

- `h_application_documents` table stores document uploads with `documentType`, `documentUrl`, and `uploadedAt`
- `h_application_documents_status` table tracks verification status with `status`, `statusAt`, and `statusBy`
- Document status enum includes: `pending`, `uploaded`, `verified`, `rejected`, `superseded`
- ApplicationReviewPageClient displays documents in a flat list without grouping

### Proposed Architecture

```mermaid
graph TD
    A[ApplicationReviewPageClient] --> B[Document Grouping Logic]
    B --> C[DocumentGroup Component]
    C --> D[DocumentVersion Component]
    D --> E[Document Actions]

    F[Database Layer] --> G[h_application_documents]
    F --> H[h_application_documents_status]

    B --> I[groupDocumentsByType]
    I --> J[sortDocumentsByUploadDate]
    J --> K[determineLatestDocument]
    K --> L[calculateDocumentStatus]
```

## Components and Interfaces

### 1. Document Grouping Logic

```typescript
interface DocumentGroup {
  documentType: string;
  documents: DocumentWithStatus[];
  latestDocument: DocumentWithStatus;
  hasMultipleVersions: boolean;
}

interface DocumentWithStatus {
  id: number;
  documentType: string;
  documentUrl: string;
  uploadedAt: string;
  status?: "pending" | "uploaded" | "verified" | "rejected" | "superseded";
  statusAt?: string;
  statusBy?: number;
  isLatest: boolean;
  version: number; // 1, 2, 3, etc.
}

// Core grouping function
function groupDocumentsByType(
  documents: ApplicationDocument[]
): DocumentGroup[] {
  const grouped = documents.reduce(
    (acc, doc) => {
      if (!acc[doc.documentType]) {
        acc[doc.documentType] = [];
      }
      acc[doc.documentType].push(doc);
      return acc;
    },
    {} as Record<string, DocumentWithStatus[]>
  );

  return Object.entries(grouped).map(([type, docs]) => {
    const sortedDocs = docs.sort(
      (a, b) =>
        new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
    );

    // Mark the latest document
    sortedDocs.forEach((doc, index) => {
      doc.isLatest = index === 0;
      doc.version = sortedDocs.length - index;
    });

    return {
      documentType: type,
      documents: sortedDocs,
      latestDocument: sortedDocs[0],
      hasMultipleVersions: sortedDocs.length > 1,
    };
  });
}
```

### 2. DocumentGroup Component

```typescript
interface DocumentGroupProps {
  group: DocumentGroup;
  onVerifyDocument: (documentId: number, status: 'verified' | 'rejected') => void;
  verifyingDocuments: Set<string>;
  rejectingDocuments: Set<string>;
}

function DocumentGroup({ group, onVerifyDocument, verifyingDocuments, rejectingDocuments }: DocumentGroupProps) {
  const [showHistory, setShowHistory] = useState(false);

  return (
    <div className="border border-gray-200 rounded-lg p-6 bg-white">
      {/* Group Header */}
      <DocumentGroupHeader group={group} onToggleHistory={() => setShowHistory(!showHistory)} />

      {/* Latest Document */}
      <DocumentVersion
        document={group.latestDocument}
        isLatest={true}
        showActions={true}
        onVerifyDocument={onVerifyDocument}
        verifyingDocuments={verifyingDocuments}
        rejectingDocuments={rejectingDocuments}
      />

      {/* Historical Documents */}
      {showHistory && group.hasMultipleVersions && (
        <DocumentHistory documents={group.documents.slice(1)} />
      )}
    </div>
  );
}
```

### 3. DocumentVersion Component

```typescript
interface DocumentVersionProps {
  document: DocumentWithStatus;
  isLatest: boolean;
  showActions: boolean;
  onVerifyDocument?: (documentId: number, status: 'verified' | 'rejected') => void;
  verifyingDocuments?: Set<string>;
  rejectingDocuments?: Set<string>;
}

function DocumentVersion({
  document,
  isLatest,
  showActions,
  onVerifyDocument,
  verifyingDocuments,
  rejectingDocuments
}: DocumentVersionProps) {
  return (
    <div className={`p-4 rounded-md ${isLatest ? 'bg-blue-50 border-blue-200' : 'bg-gray-50'}`}>
      {/* Version Header */}
      <DocumentVersionHeader document={document} isLatest={isLatest} />

      {/* Status Display */}
      <DocumentStatusBadge document={document} />

      {/* Document Actions */}
      <DocumentActions document={document} />

      {/* Verification Actions (only for latest) */}
      {showActions && isLatest && onVerifyDocument && (
        <DocumentVerificationActions
          document={document}
          onVerifyDocument={onVerifyDocument}
          verifyingDocuments={verifyingDocuments}
          rejectingDocuments={rejectingDocuments}
        />
      )}
    </div>
  );
}
```

### 4. Status Display Components

```typescript
function DocumentStatusBadge({ document }: { document: DocumentWithStatus }) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'verified':
        return { color: 'text-green-700 bg-green-100', icon: CheckCircle, text: 'Verified' };
      case 'rejected':
        return { color: 'text-red-700 bg-red-100', icon: XCircle, text: 'Rejected' };
      case 'superseded':
        return { color: 'text-gray-700 bg-gray-100', icon: Archive, text: 'Superseded' };
      default:
        return { color: 'text-yellow-700 bg-yellow-100', icon: Clock, text: 'Pending Review' };
    }
  };

  const config = getStatusConfig(document.status || 'pending');
  const Icon = config.icon;

  return (
    <div className={`flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium ${config.color}`}>
      <Icon size={16} />
      <span>{config.text}</span>
      {document.statusAt && (
        <span className="text-xs opacity-75">
          • {formatDateTime(document.statusAt)}
        </span>
      )}
    </div>
  );
}
```

## Data Models

### Enhanced Document Interface

```typescript
interface ApplicationDocumentEnhanced extends ApplicationDocument {
  // Existing fields
  id: number;
  documentType: string;
  documentUrl: string;
  uploadedAt: string;
  status?: "pending" | "uploaded" | "verified" | "rejected" | "superseded";
  statusAt?: string;
  statusBy?: number;

  // Enhanced fields for grouping
  isLatest: boolean;
  version: number;
  hasMultipleVersions: boolean;
  groupedDocuments?: ApplicationDocumentEnhanced[];
}
```

### Document Group State Management

```typescript
interface DocumentGroupState {
  groups: DocumentGroup[];
  expandedGroups: Set<string>;
  verifyingDocuments: Set<string>;
  rejectingDocuments: Set<string>;
}

// State management hooks
function useDocumentGroups(documents: ApplicationDocument[]) {
  const [state, setState] = useState<DocumentGroupState>({
    groups: [],
    expandedGroups: new Set(),
    verifyingDocuments: new Set(),
    rejectingDocuments: new Set(),
  });

  useEffect(() => {
    const groups = groupDocumentsByType(documents);
    setState((prev) => ({ ...prev, groups }));
  }, [documents]);

  const toggleGroupExpansion = (documentType: string) => {
    setState((prev) => {
      const newExpanded = new Set(prev.expandedGroups);
      if (newExpanded.has(documentType)) {
        newExpanded.delete(documentType);
      } else {
        newExpanded.add(documentType);
      }
      return { ...prev, expandedGroups: newExpanded };
    });
  };

  return { state, toggleGroupExpansion };
}
```

## Error Handling

### Document Status Validation

```typescript
function validateDocumentStatus(
  document: DocumentWithStatus
): ValidationResult {
  const errors: string[] = [];

  // Check if document has URL
  if (!document.documentUrl) {
    errors.push("Document URL is missing");
  }

  // Check if latest document can be verified
  if (document.isLatest && document.status === "superseded") {
    errors.push("Latest document cannot be superseded");
  }

  // Check status consistency
  if (document.status && !document.statusAt) {
    errors.push("Status timestamp is missing");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
```

### Error Boundary for Document Components

```typescript
class DocumentErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Document component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 border border-red-200 rounded-lg bg-red-50">
          <p className="text-red-700">Error loading document. Please refresh the page.</p>
        </div>
      );
    }

    return this.props.children;
  }
}
```

## Testing Strategy

### Unit Tests

1. **Document Grouping Logic**
   - Test grouping by document type
   - Test sorting by upload date
   - Test latest document identification
   - Test version numbering

2. **Status Display Logic**
   - Test status badge rendering
   - Test status color coding
   - Test timestamp formatting

3. **Verification Actions**
   - Test verify/reject button states
   - Test loading states
   - Test error handling

### Integration Tests

1. **Document Upload Flow**
   - Test initial document upload
   - Test document re-upload
   - Test status updates

2. **Admin Review Flow**
   - Test document verification
   - Test document rejection
   - Test status persistence

### Component Tests

```typescript
describe('DocumentGroup', () => {
  it('should group documents by type', () => {
    const documents = [
      { id: 1, documentType: 'license', uploadedAt: '2024-01-01' },
      { id: 2, documentType: 'license', uploadedAt: '2024-01-02' },
      { id: 3, documentType: 'insurance', uploadedAt: '2024-01-01' },
    ];

    const groups = groupDocumentsByType(documents);
    expect(groups).toHaveLength(2);
    expect(groups[0].documentType).toBe('license');
    expect(groups[0].documents).toHaveLength(2);
    expect(groups[0].latestDocument.id).toBe(2);
  });

  it('should show verification actions only for latest document', () => {
    const group = {
      documentType: 'license',
      documents: [
        { id: 2, isLatest: true, status: 'pending' },
        { id: 1, isLatest: false, status: 'superseded' },
      ],
      latestDocument: { id: 2, isLatest: true },
      hasMultipleVersions: true,
    };

    render(<DocumentGroup group={group} onVerifyDocument={jest.fn()} />);

    // Latest document should have verification buttons
    expect(screen.getByText('Verify')).toBeInTheDocument();
    expect(screen.getByText('Reject')).toBeInTheDocument();
  });
});
```

## Performance Considerations

### Optimization Strategies

1. **Memoization**
   - Memoize document grouping logic
   - Memoize status calculations
   - Use React.memo for document components

2. **Lazy Loading**
   - Load document history on demand
   - Lazy load document previews
   - Implement virtual scrolling for large document lists

3. **Caching**
   - Cache document URLs
   - Cache status calculations
   - Implement optimistic updates

```typescript
// Memoized grouping function
const groupDocumentsByType = useMemo(() => {
  return (documents: ApplicationDocument[]) => {
    // Grouping logic here
  };
}, []);

// Memoized document component
const DocumentVersion = React.memo(
  ({ document, isLatest, showActions }: DocumentVersionProps) => {
    // Component logic here
  }
);
```

## Security Considerations

### Document Access Control

1. **URL Generation**
   - Generate signed URLs for document access
   - Implement URL expiration
   - Validate user permissions before URL generation

2. **Status Updates**
   - Validate admin permissions
   - Log all status changes
   - Implement audit trail

3. **Data Validation**
   - Validate document types
   - Sanitize file names
   - Validate status transitions

```typescript
// Secure document URL generation
async function generateSecureDocumentUrl(
  documentUrl: string,
  userRole: string
): Promise<string> {
  // Validate user has permission to view document
  if (!hasDocumentViewPermission(userRole)) {
    throw new Error("Insufficient permissions");
  }

  // Generate signed URL with expiration
  return await generateSignedUrl(documentUrl, { expiresIn: "1h" });
}
```
