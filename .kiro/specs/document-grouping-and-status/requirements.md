# Requirements Document

## Introduction

This feature enhances the admin application review interface to properly handle document grouping, status display, and re-upload scenarios. Currently, when users re-upload documents, admins see duplicate document cards without clear indication of which is the latest version or proper status information. This feature will group documents by type, show historical uploads, display current status, and only allow verification actions on the latest document version.

The system works with the `h_application_documents` table for document storage and the `h_application_documents_status` table for tracking verification status and audit trail.

## Requirements

### Requirement 1

**User Story:** As an admin reviewing applications, I want to see documents grouped by type so that I can easily understand the document history and current status without confusion from duplicate cards.

#### Acceptance Criteria

1. WHEN viewing the documents tab THEN documents SHALL be grouped by documentType
2. WHEN multiple documents of the same type exist THEN they SHALL be displayed in a single grouped card
3. WHEN documents are grouped THEN the latest document SHALL be clearly identified as "Current Version"
4. WHEN documents are grouped THEN historical documents SHALL be labeled with upload timestamps
5. WHEN no documents exist for a type THEN the system SHALL show "No documents uploaded yet"

### Requirement 2

**User Story:** As an admin reviewing applications, I want to see the current status of each document so that I can understand what actions have been taken and what still needs to be done.

#### Acceptance Criteria

1. WHEN a document has a status THEN the status SHALL be prominently displayed with appropriate color coding
2. WHEN a document is verified THEN it SHALL show "Verified" status with green styling and verification timestamp
3. WHEN a document is rejected THEN it SHALL show "Rejected" status with red styling and rejection timestamp
4. WHEN a document is pending THEN it SHALL show "Pending Review" status with yellow styling
5. WHEN a document is superseded THEN it SHALL show "Superseded" status with gray styling
6. WHEN viewing document history THEN each historical document SHALL show its final status

### Requirement 3

**User Story:** As an admin reviewing applications, I want verification actions (verify/reject buttons) to only appear on the latest document version so that I don't accidentally act on outdated documents.

#### Acceptance Criteria

1. WHEN viewing grouped documents THEN verify/reject buttons SHALL only appear on the latest uploaded document
2. WHEN a document has been superseded THEN it SHALL NOT show verification buttons
3. WHEN the latest document is already verified or rejected THEN it SHALL show reset option instead of verify/reject
4. WHEN clicking verify or reject THEN the action SHALL only apply to the latest document version
5. WHEN a verification action is in progress THEN the buttons SHALL show loading state

### Requirement 4

**User Story:** As an admin reviewing applications, I want to see the complete document history for each type so that I can understand the progression of document submissions and decisions.

#### Acceptance Criteria

1. WHEN viewing a document group THEN all historical versions SHALL be visible in chronological order
2. WHEN viewing historical documents THEN each SHALL show upload timestamp, status, and status timestamp
3. WHEN a document was rejected THEN the rejection reason SHALL be displayed if available
4. WHEN viewing document history THEN the most recent document SHALL be displayed first
5. WHEN expanding document history THEN older versions SHALL be clearly distinguished from current version

### Requirement 5

**User Story:** As an admin reviewing applications, I want to view and download any version of a document so that I can compare versions or access historical submissions.

#### Acceptance Criteria

1. WHEN viewing any document version THEN view and download buttons SHALL be available
2. WHEN clicking view document THEN it SHALL open in a new tab with proper signed URL
3. WHEN clicking download document THEN it SHALL download with descriptive filename including version info
4. WHEN document URL generation fails THEN appropriate error message SHALL be shown
5. WHEN viewing historical documents THEN they SHALL have same view/download functionality as current version

### Requirement 6

**User Story:** As an admin reviewing applications, I want the document verification workflow to properly handle the re-upload scenario so that the system maintains data integrity and clear audit trails.

#### Acceptance Criteria

1. WHEN a document is re-uploaded THEN the previous version SHALL be marked as superseded
2. WHEN marking a document as superseded THEN its status SHALL be preserved for audit purposes
3. WHEN a new document is uploaded THEN it SHALL start with "pending" status
4. WHEN verifying the latest document THEN previous versions SHALL remain unchanged
5. WHEN rejecting the latest document THEN the user SHALL be able to upload a new version
