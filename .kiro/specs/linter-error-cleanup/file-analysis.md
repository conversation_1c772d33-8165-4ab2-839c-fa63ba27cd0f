# Linter Error File Analysis

## Files by Priority Level

### Priority 0 (Critical - React Hooks Violations)

**Files with react-hooks/rules-of-hooks errors:**

1. `app/(main)/vehicle-status/[id]/vehicle-status.tsx` - 7 conditional hook calls
2. `components/screens/BookingCalendarScreen.tsx` - 9 conditional hook calls
3. `components/screens/TaskDetailScreen.tsx` - 6 conditional hook calls

**Files with react-hooks/exhaustive-deps warnings:**

1. `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx` - missing dependencies
2. `app/(main)/vehicle-status/[id]/vehicle-status.tsx` - missing dependencies
3. `components/profile-setup-loading.tsx` - missing dependencies
4. `components/screens/GroupDetailsScreen.tsx` - missing dependencies
5. `components/screens/VehicleStatusScreen.tsx` - missing dependencies
6. `components/word-cloud-display.tsx` - missing dependencies

**Total P0 files: 6 unique files**

### Priority 1 (High - TypeScript Any Types)

**Files with @typescript-eslint/no-explicit-any errors:**

1. `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx` - 2 any types
2. `app/(main)/vehicle-status/[id]/page.tsx` - 2 any types
3. `app/(main)/vehicle-status/[id]/vehicle-status.tsx` - 2 any types
4. `app/api/word-cloud/route.ts` - 1 any type
5. `components/ImageUploader.tsx` - 1 any type
6. `components/documentUploader.tsx` - 1 any type
7. `components/BusinessOpportunityDetailsDrawer.tsx` - 5 any types
8. `components/GroupInvitationTaskContent.tsx` - 2 any types
9. `components/ListingDetailsDrawer.tsx` - 1 any type
10. `components/VehiclePhotoUpload.tsx` - 2 any types
11. `components/list-vehicle-drawer.tsx` - 2 any types
12. `components/list-vehicle-form.tsx` - 2 any types
13. `components/listing-media-upload.tsx` - 1 any type
14. `components/navigation/FallbackScreen.tsx` - 1 any type
15. `components/navigation/ScreenRenderer.tsx` - 1 any type
16. `components/screens/BookingCalendarNewScreen.tsx` - 1 any type
17. `components/screens/CommunityScreen.tsx` - 1 any type
18. `components/screens/CreateGroupScreen.tsx` - 1 any type
19. `components/screens/GroupChatScreen.tsx` - 1 any type
20. `components/screens/GroupDashboardScreen.tsx` - 1 any type
21. `components/screens/GroupFinancesScreen.tsx` - 2 any types
22. `components/screens/GroupSettingsScreen.tsx` - 3 any types
23. `components/screens/HomeScreen.tsx` - 8 any types
24. `components/screens/ListVehicleScreen.tsx` - 1 any type
25. `components/screens/MyGroupsScreen.tsx` - 1 any type
26. `components/screens/OpportunitiesScreen.tsx` - 5 any types
27. `components/screens/TaskDetailScreen.tsx` - 3 any types
28. `components/screens/VehicleDashboardScreen.tsx` - 1 any type
29. `components/screens/VehicleDetailsScreen.tsx` - 4 any types
30. `components/screens/VehicleHandoverTaskScreen.tsx` - 3 any types
31. `components/screens/VehicleMarketplaceScreen.tsx` - 2 any types
32. `components/screens/VehicleSearchScreen.tsx` - 1 any type
33. `components/screens/VehicleStatusScreen.tsx` - 6 any types
34. `components/vehicle-to-group-form.tsx` - 2 any types
35. `lib/amplifyServerUtils.ts` - 4 any types
36. `lib/fetchClient.ts` - 1 any type
37. `lib/navigation/navigationStore.ts` - 5 any types
38. `lib/sqsClient.ts` - 3 any types
39. `lib/utils.ts` - 2 any types
40. `lib/webpush.ts` - 1 any type

**Total P1 files: 40 files**

### Priority 2 (Medium - Unused Variables/Imports)

**Files with @typescript-eslint/no-unused-vars errors:**

1. `app/(main)/vehicle-dashboard/vehicle-tabs.tsx` - 4 unused items
2. `app/(main)/vehicle-details/[id]/page.tsx` - 1 unused import
3. `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx` - 1 unused function
4. `app/(main)/vehicle-search/page.tsx` - 2 unused imports
5. `app/(main)/vehicle-status/[id]/vehicle-status.tsx` - 1 unused import
6. `app/StoreProvider.tsx` - 2 unused imports
7. `components/ImageUploader.tsx` - 1 unused variable
8. `app/error.tsx` - 1 unused import
9. `app/fraction-purchase-new/[id]/page.tsx` - 2 unused imports
10. `components/ApplicationDocumentUploader.tsx` - 3 unused items
11. `components/FormImageUpload.tsx` - 2 unused items
12. `components/ListingDetailsDrawer.tsx` - 1 unused prop
13. `components/SWRProvider.tsx` - 1 unused parameter
14. `components/VehicleImageGallery.tsx` - 1 unused import
15. `components/error-boundary.tsx` - 1 unused import
16. `components/list-vehicle-drawer.tsx` - 1 unused import
17. `components/list-vehicle-form.tsx` - 2 unused imports
18. `components/listing-media-upload.tsx` - 1 unused prop
19. `components/missing-party.tsx` - 1 unused import
20. `components/navigation/ScreenRenderer.tsx` - 1 unused type
21. `components/profile-setup-loading.tsx` - 1 unused import
22. `components/screens/AddVehicleToGroupScreen.tsx` - 1 unused variable
23. `components/screens/BookingCalendarNewScreen.tsx` - 1 unused import
24. `components/screens/BookingCalendarScreen.tsx` - 2 unused items
25. `components/screens/BookingConfirmationScreen.tsx` - 1 unused import
26. `components/screens/BookingDetailsScreen.tsx` - 1 unused import
27. `components/screens/CommunityScreen.tsx` - 1 unused parameter
28. `components/screens/CreateGroupScreen.tsx` - 8 unused items
29. `components/screens/GroupChatScreen.tsx` - 4 unused items
30. `components/screens/GroupDashboardScreen.tsx` - 2 unused items
31. `components/screens/GroupFinancesScreen.tsx` - 5 unused imports
32. `components/screens/GroupSettingsScreen.tsx` - 4 unused imports
33. `components/screens/HomeScreen.tsx` - 9 unused items
34. `components/screens/ListVehicleScreen.tsx` - 2 unused items
35. `components/screens/ListingDetailsScreen.tsx` - 5 unused imports
36. `components/screens/ListingManagementScreen.tsx` - 3 unused items
37. `components/screens/MemberDetailsScreen.tsx` - 1 unused import
38. `components/screens/MemberManagementScreen.tsx` - 1 unused import
39. `components/screens/MyGroupsScreen.tsx` - 9 unused items
40. `components/screens/OpportunitiesScreen.tsx` - 4 unused items
41. `components/screens/ProfileScreen.tsx` - 1 unused parameter
42. `components/screens/TaskDetailScreen.tsx` - 12 unused imports
43. `components/screens/TasksScreen.tsx` - 3 unused items
44. `components/screens/VehicleDashboardScreen.tsx` - 4 unused items
45. `components/screens/VehicleDetailsScreen.tsx` - 6 unused items
46. `components/screens/VehicleHandoverTaskScreen.tsx` - 2 unused items
47. `components/screens/VehicleMarketplaceScreen.tsx` - 1 unused parameter
48. `components/screens/VehicleSearchScreen.tsx` - 1 unused parameter
49. `components/screens/VehicleStatusScreen.tsx` - 5 unused items
50. `components/ui/calendar.tsx` - 2 unused props
51. `components/ui/chart.tsx` - 1 unused parameter
52. `components/ui/notification-settings.tsx` - 1 unused import
53. `components/ui/use-toast.ts` - 1 unused variable
54. `components/vehicle-to-group-form.tsx` - 3 unused items
55. `components/word-cloud-display.tsx` - 1 unused parameter
56. `lib/amplifyServerUtils.ts` - 3 unused imports
57. `lib/profile.ts` - 1 unused variable
58. `lib/utils.ts` - 1 unused parameter
59. `lib/word-cloud-job-processor.ts` - 1 unused parameter

**Total P2 files: 59 files**

### Priority 3 (Low - JSX Entity Escaping)

**Files with react/no-unescaped-entities errors:**

1. `app/test-upload/page.tsx` - 6 unescaped quotes
2. `components/DocumentStatusBadge.example.tsx` - 6 unescaped quotes
3. `components/GroupInvitationTaskContent.tsx` - 2 unescaped apostrophes
4. `components/VehicleImageGallery.tsx` - 2 unescaped quotes
5. `components/list-vehicle-form.tsx` - 1 unescaped apostrophe
6. `components/navigation/FallbackScreen.tsx` - 2 unescaped apostrophes
7. `components/profile-setup-loading.tsx` - 1 unescaped apostrophe
8. `components/screens/AddMembersScreen.tsx` - 2 unescaped quotes
9. `components/screens/BookingDetailsScreen.tsx` - 2 unescaped quotes
10. `components/screens/MyGroupsScreen.tsx` - 3 unescaped entities
11. `components/screens/OpportunitiesScreen.tsx` - 1 unescaped apostrophe
12. `components/screens/TaskDetailScreen.tsx` - 4 unescaped apostrophes
13. `components/screens/TasksScreen.tsx` - 1 unescaped apostrophe
14. `components/screens/VehicleDetailsScreen.tsx` - 2 unescaped apostrophes
15. `components/ui/pwa-install-banner.tsx` - 4 unescaped entities
16. `components/word-cloud-display.tsx` - 4 unescaped quotes

**Total P3 files: 16 files**

### Priority 4 (Low - Image Optimization)

**Files with @next/next/no-img-element and jsx-a11y/alt-text warnings:**

1. `components/FormImageUpload.tsx` - 2 img elements
2. `components/VehicleImageGallery.tsx` - 2 img elements
3. `components/screens/GroupChatScreen.tsx` - 1 missing alt text
4. `components/screens/ListingDetailsScreen.tsx` - 3 img elements
5. `components/screens/ListingManagementScreen.tsx` - 3 img elements
6. `components/screens/VehicleStatusScreen.tsx` - 3 img elements
7. `components/vehicle-group-list.tsx` - 1 img element

**Total P4 files: 7 files**

### Priority 5 (Very Low - Miscellaneous)

**Files with prefer-const, no-require-imports, no-unused-expressions:**

1. `components/screens/BookingCalendarScreen.tsx` - prefer-const
2. `components/screens/OpportunitiesScreen.tsx` - prefer-const
3. `lib/types/applications.js` - require import
4. `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx` - unused expressions
5. `components/screens/VehicleHandoverTaskScreen.tsx` - unused expression

**Total P5 files: 5 files**

## Summary

- **Total unique files to fix: 87**
- **Priority 0 (Critical): 6 files**
- **Priority 1 (High): 40 files**
- **Priority 2 (Medium): 59 files**
- **Priority 3 (Low): 16 files**
- **Priority 4 (Low): 7 files**
- **Priority 5 (Very Low): 5 files**

Note: Some files appear in multiple priority levels and will be processed completely when first encountered in priority order.
