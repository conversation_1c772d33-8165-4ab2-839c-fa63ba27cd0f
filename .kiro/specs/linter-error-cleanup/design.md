# Linter Error Cleanup Design

## Overview

This design outlines a systematic approach to cleaning up linter errors across the application. The cleanup will be performed in priority order, starting with critical React Hooks violations that can cause runtime errors, followed by type safety improvements, and ending with code cleanliness improvements.

## Architecture

### Error Classification System

The linter errors are classified into six priority levels:

1. **Critical (P0)**: React Hooks violations that can cause runtime errors
2. **High (P1)**: TypeScript `any` types that reduce type safety
3. **Medium (P2)**: Unused code that clutters the codebase
4. **Low (P3)**: JSX entity escaping issues
5. **Low (P4)**: Image optimization warnings
6. **Low (P5)**: Miscellaneous style and convention issues

### Processing Strategy

- **File-by-file approach**: Process each file completely before moving to the next
- **Priority-first within files**: Within each file, fix errors in priority order
- **Validation after each file**: Run linter on individual files to verify fixes
- **Batch verification**: Run full linter suite after completing each priority level

## Components and Interfaces

### Error Analysis Component

**Purpose**: Analyze and categorize linter errors from the npm run lint output

**Interface**:

```typescript
interface LinterError {
  file: string;
  line: number;
  column: number;
  rule: string;
  message: string;
  severity: "error" | "warning";
  priority: 0 | 1 | 2 | 3 | 4 | 5;
}

interface ErrorAnalysis {
  totalErrors: number;
  errorsByPriority: Record<number, LinterError[]>;
  errorsByFile: Record<string, LinterError[]>;
  errorsByRule: Record<string, LinterError[]>;
}
```

### File Processor Component

**Purpose**: Process individual files to fix linter errors

**Interface**:

```typescript
interface FileProcessor {
  processFile(filePath: string, errors: LinterError[]): Promise<void>;
  validateFile(filePath: string): Promise<LinterError[]>;
}
```

### Fix Strategy Implementations

#### React Hooks Fixer

- Move conditional hooks to component top level
- Add missing dependencies to useEffect
- Restructure components to avoid conditional hook calls

#### TypeScript Type Fixer

- Replace `any` with proper interfaces
- Infer types from usage patterns
- Create type definitions for complex objects

#### Unused Code Remover

- Remove unused imports
- Remove unused variables and functions
- Mark intentionally unused items with underscore prefix

#### JSX Entity Fixer

- Replace unescaped quotes with `&quot;` or `{'"'}`
- Replace unescaped apostrophes with `&apos;` or `{"'"}`

#### Image Optimizer

- Replace `<img>` with Next.js `<Image>`
- Add proper alt attributes
- Handle responsive image requirements

## Data Models

### Error Priority Mapping

```typescript
const ERROR_PRIORITY_MAP: Record<string, number> = {
  "react-hooks/rules-of-hooks": 0,
  "react-hooks/exhaustive-deps": 0,
  "@typescript-eslint/no-explicit-any": 1,
  "@typescript-eslint/no-unused-vars": 2,
  "react/no-unescaped-entities": 3,
  "@next/next/no-img-element": 4,
  "jsx-a11y/alt-text": 4,
  "prefer-const": 5,
  "@typescript-eslint/no-require-imports": 5,
  "@typescript-eslint/no-unused-expressions": 5,
};
```

### File Processing Order

Files will be processed in order of error count and criticality:

1. Files with P0 errors (React Hooks violations)
2. Files with high error counts
3. Remaining files alphabetically

## Error Handling

### Backup Strategy

- Create git commit before starting each priority level
- Allow rollback if fixes introduce new issues

### Validation Strategy

- Run linter on individual files after processing
- Run full test suite after completing each priority level
- Manual review of critical changes

### Conflict Resolution

- If fixing one error introduces another, prioritize the higher priority error
- Document any intentional rule suppressions
- Use ESLint disable comments sparingly and with justification

## Testing Strategy

### Automated Testing

- Run existing test suite after each priority level completion
- Verify no new TypeScript compilation errors
- Ensure application still builds successfully

### Manual Testing

- Spot check critical components after React Hooks fixes
- Verify UI components still render correctly after JSX entity fixes
- Test image loading after Image component replacements

### Regression Prevention

- Add pre-commit hooks to prevent reintroduction of fixed errors
- Update ESLint configuration if needed
- Document coding standards for team

## Implementation Phases

### Phase 1: Critical React Hooks Fixes (P0)

- Target files: Components with conditional hook calls
- Estimated impact: ~15 files with critical violations
- Risk level: High (can break functionality)

### Phase 2: TypeScript Type Safety (P1)

- Target files: Files with `any` types
- Estimated impact: ~30 files
- Risk level: Medium (may reveal hidden bugs)

### Phase 3: Code Cleanup (P2)

- Target files: Files with unused imports/variables
- Estimated impact: ~50 files
- Risk level: Low (cosmetic changes)

### Phase 4: JSX and Image Optimization (P3-P4)

- Target files: Components with JSX issues and image usage
- Estimated impact: ~25 files
- Risk level: Low (mostly cosmetic)

### Phase 5: Final Cleanup (P5)

- Target files: Remaining miscellaneous issues
- Estimated impact: ~20 files
- Risk level: Very low (style improvements)

## Success Metrics

- Zero linter errors after completion
- No new TypeScript compilation errors
- All existing tests continue to pass
- Application builds and runs successfully
- Improved code maintainability and type safety
