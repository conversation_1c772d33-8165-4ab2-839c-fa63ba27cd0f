# Linter Error Cleanup Implementation Plan

## Phase 0: Critical TypeScript Compilation Errors (Priority 0)

- [ ] 0.1 Fix module resolution errors in payment contract files
  - Fix import paths in `actions/admin/deposits.ts` (3 module resolution errors)
  - Fix import paths in `drizzle-actions/admin/deposits.ts` (5 module resolution errors)
  - Fix import paths in `actions/admin/contracts.ts` (3 module resolution errors)
  - Fix import paths in `drizzle-actions/admin/contracts.ts` (6 module resolution errors)
  - Fix import paths in `actions/admin/weekly-earnings.ts` (3 module resolution errors)
  - Fix import paths in `drizzle-actions/admin/weekly-earnings.ts` (5 module resolution errors)
  - _Requirements: Critical compilation fixes_

- [ ] 0.2 Validate TypeScript compilation
  - Run `npx tsc --noEmit --skipLibCheck` to verify all files compile
  - Ensure no module resolution errors remain
  - _Requirements: Code must compile before linter fixes_

## Phase 1: Critical React Hooks Violations (Priority 0)

- [ ] 1. Fix React Hooks violations in vehicle-status component
  - Fix 7 conditional hook calls in `app/(main)/vehicle-status/[id]/vehicle-status.tsx`
  - Add missing dependencies to useEffect hooks
  - Restructure component to move all hooks to top level
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 2. Fix React Hooks violations in BookingCalendarScreen
  - Fix 9 conditional hook calls in `components/screens/BookingCalendarScreen.tsx`
  - Restructure component logic to avoid conditional hooks
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 3. Fix React Hooks violations in TaskDetailScreen
  - Fix 6 conditional hook calls in `components/screens/TaskDetailScreen.tsx`
  - Move hooks to component top level
  - _Requirements: 1.1, 1.2, 1.3_

- [ ] 4. Fix useEffect dependency warnings
  - Add missing dependencies in `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx`
  - Add missing dependencies in `components/profile-setup-loading.tsx`
  - Add missing dependencies in `components/screens/GroupDetailsScreen.tsx`
  - Add missing dependencies in `components/screens/VehicleStatusScreen.tsx`
  - Add missing dependencies in `components/word-cloud-display.tsx`
  - _Requirements: 1.2_

- [ ] 5. Validate Phase 1 completion
  - Run linter to verify no P0 errors remain
  - Run test suite to ensure no regressions
  - _Requirements: 1.4_

## Phase 2: TypeScript Type Safety (Priority 1)

- [ ] 6. Replace 'any' types in vehicle-related components
  - Fix any types in `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx`
  - Fix any types in `app/(main)/vehicle-status/[id]/page.tsx`
  - Fix any types in `app/(main)/vehicle-status/[id]/vehicle-status.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 7. Replace 'any' types in API and utility files
  - Fix any type in `app/api/word-cloud/route.ts`
  - Fix any types in `lib/amplifyServerUtils.ts`
  - Fix any type in `lib/fetchClient.ts`
  - Fix any types in `lib/navigation/navigationStore.ts`
  - Fix any types in `lib/sqsClient.ts`
  - Fix any types in `lib/utils.ts`
  - Fix any type in `lib/webpush.ts`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 8. Replace 'any' types in upload components
  - Fix any type in `components/ImageUploader.tsx`
  - Fix any type in `components/documentUploader.tsx`
  - Fix any types in `components/VehiclePhotoUpload.tsx`
  - Fix any type in `components/listing-media-upload.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 9. Replace 'any' types in business logic components
  - Fix any types in `components/BusinessOpportunityDetailsDrawer.tsx`
  - Fix any types in `components/GroupInvitationTaskContent.tsx`
  - Fix any type in `components/ListingDetailsDrawer.tsx`
  - Fix any types in `components/list-vehicle-drawer.tsx`
  - Fix any types in `components/list-vehicle-form.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 10. Replace 'any' types in navigation components
  - Fix any type in `components/navigation/FallbackScreen.tsx`
  - Fix any type in `components/navigation/ScreenRenderer.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 11. Replace 'any' types in screen components (Part 1)
  - Fix any type in `components/screens/BookingCalendarNewScreen.tsx`
  - Fix any type in `components/screens/CommunityScreen.tsx`
  - Fix any type in `components/screens/CreateGroupScreen.tsx`
  - Fix any type in `components/screens/GroupChatScreen.tsx`
  - Fix any type in `components/screens/GroupDashboardScreen.tsx`
  - Fix any types in `components/screens/GroupFinancesScreen.tsx`
  - Fix any types in `components/screens/GroupSettingsScreen.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 12. Replace 'any' types in screen components (Part 2)
  - Fix any types in `components/screens/HomeScreen.tsx`
  - Fix any type in `components/screens/ListVehicleScreen.tsx`
  - Fix any type in `components/screens/MyGroupsScreen.tsx`
  - Fix any types in `components/screens/OpportunitiesScreen.tsx`
  - Fix any types in `components/screens/TaskDetailScreen.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 13. Replace 'any' types in vehicle screen components
  - Fix any type in `components/screens/VehicleDashboardScreen.tsx`
  - Fix any types in `components/screens/VehicleDetailsScreen.tsx`
  - Fix any types in `components/screens/VehicleHandoverTaskScreen.tsx`
  - Fix any types in `components/screens/VehicleMarketplaceScreen.tsx`
  - Fix any type in `components/screens/VehicleSearchScreen.tsx`
  - Fix any types in `components/screens/VehicleStatusScreen.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 14. Replace 'any' types in remaining components
  - Fix any types in `components/vehicle-to-group-form.tsx`
  - _Requirements: 2.1, 2.2, 2.3_

- [ ] 15. Validate Phase 2 completion
  - Run linter to verify no P1 errors remain
  - Run TypeScript compiler to ensure no new type errors
  - _Requirements: 2.4_

## Phase 3: Unused Code Cleanup (Priority 2)

- [ ] 16. Clean unused imports and variables in main app files
  - Remove unused items in `app/(main)/vehicle-dashboard/vehicle-tabs.tsx`
  - Remove unused items in `app/(main)/vehicle-details/[id]/page.tsx`
  - Remove unused items in `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx`
  - Remove unused items in `app/(main)/vehicle-search/page.tsx`
  - Remove unused items in `app/(main)/vehicle-status/[id]/vehicle-status.tsx`
  - Remove unused items in `app/StoreProvider.tsx`
  - Remove unused items in `app/error.tsx`
  - Remove unused items in `app/fraction-purchase-new/[id]/page.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 17. Clean unused imports and variables in component files (Part 1)
  - Remove unused items in `components/ImageUploader.tsx`
  - Remove unused items in `components/ApplicationDocumentUploader.tsx`
  - Remove unused items in `components/FormImageUpload.tsx`
  - Remove unused items in `components/ListingDetailsDrawer.tsx`
  - Remove unused items in `components/SWRProvider.tsx`
  - Remove unused items in `components/VehicleImageGallery.tsx`
  - Remove unused items in `components/error-boundary.tsx`
  - Remove unused items in `components/list-vehicle-drawer.tsx`
  - Remove unused items in `components/list-vehicle-form.tsx`
  - Remove unused items in `components/listing-media-upload.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 18. Clean unused imports and variables in component files (Part 2)
  - Remove unused items in `components/missing-party.tsx`
  - Remove unused items in `components/navigation/ScreenRenderer.tsx`
  - Remove unused items in `components/profile-setup-loading.tsx`
  - Remove unused items in `components/vehicle-to-group-form.tsx`
  - Remove unused items in `components/word-cloud-display.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 19. Clean unused imports and variables in screen components (Part 1)
  - Remove unused items in `components/screens/AddVehicleToGroupScreen.tsx`
  - Remove unused items in `components/screens/BookingCalendarNewScreen.tsx`
  - Remove unused items in `components/screens/BookingCalendarScreen.tsx`
  - Remove unused items in `components/screens/BookingConfirmationScreen.tsx`
  - Remove unused items in `components/screens/BookingDetailsScreen.tsx`
  - Remove unused items in `components/screens/CommunityScreen.tsx`
  - Remove unused items in `components/screens/CreateGroupScreen.tsx`
  - Remove unused items in `components/screens/GroupChatScreen.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 20. Clean unused imports and variables in screen components (Part 2)
  - Remove unused items in `components/screens/GroupDashboardScreen.tsx`
  - Remove unused items in `components/screens/GroupFinancesScreen.tsx`
  - Remove unused items in `components/screens/GroupSettingsScreen.tsx`
  - Remove unused items in `components/screens/HomeScreen.tsx`
  - Remove unused items in `components/screens/ListVehicleScreen.tsx`
  - Remove unused items in `components/screens/ListingDetailsScreen.tsx`
  - Remove unused items in `components/screens/ListingManagementScreen.tsx`
  - Remove unused items in `components/screens/MemberDetailsScreen.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 21. Clean unused imports and variables in screen components (Part 3)
  - Remove unused items in `components/screens/MemberManagementScreen.tsx`
  - Remove unused items in `components/screens/MyGroupsScreen.tsx`
  - Remove unused items in `components/screens/OpportunitiesScreen.tsx`
  - Remove unused items in `components/screens/ProfileScreen.tsx`
  - Remove unused items in `components/screens/TaskDetailScreen.tsx`
  - Remove unused items in `components/screens/TasksScreen.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 22. Clean unused imports and variables in vehicle screen components
  - Remove unused items in `components/screens/VehicleDashboardScreen.tsx`
  - Remove unused items in `components/screens/VehicleDetailsScreen.tsx`
  - Remove unused items in `components/screens/VehicleHandoverTaskScreen.tsx`
  - Remove unused items in `components/screens/VehicleMarketplaceScreen.tsx`
  - Remove unused items in `components/screens/VehicleSearchScreen.tsx`
  - Remove unused items in `components/screens/VehicleStatusScreen.tsx`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 23. Clean unused imports and variables in UI and lib files
  - Remove unused items in `components/ui/calendar.tsx`
  - Remove unused items in `components/ui/chart.tsx`
  - Remove unused items in `components/ui/notification-settings.tsx`
  - Remove unused items in `components/ui/use-toast.ts`
  - Remove unused items in `lib/amplifyServerUtils.ts`
  - Remove unused items in `lib/profile.ts`
  - Remove unused items in `lib/utils.ts`
  - Remove unused items in `lib/word-cloud-job-processor.ts`
  - _Requirements: 3.1, 3.2, 3.3_

- [ ] 24. Validate Phase 3 completion
  - Run linter to verify no P2 errors remain
  - Ensure no functionality is broken by removed code
  - _Requirements: 3.4_

## Phase 4: JSX Entity Escaping (Priority 3)

- [ ] 25. Fix JSX entity escaping in test and example files
  - Fix unescaped quotes in `app/test-upload/page.tsx`
  - Fix unescaped quotes in `components/DocumentStatusBadge.example.tsx`
  - _Requirements: 4.1, 4.2_

- [ ] 26. Fix JSX entity escaping in component files
  - Fix unescaped entities in `components/GroupInvitationTaskContent.tsx`
  - Fix unescaped entities in `components/VehicleImageGallery.tsx`
  - Fix unescaped entities in `components/list-vehicle-form.tsx`
  - Fix unescaped entities in `components/navigation/FallbackScreen.tsx`
  - Fix unescaped entities in `components/profile-setup-loading.tsx`
  - Fix unescaped entities in `components/ui/pwa-install-banner.tsx`
  - Fix unescaped entities in `components/word-cloud-display.tsx`
  - _Requirements: 4.1, 4.2_

- [ ] 27. Fix JSX entity escaping in screen components
  - Fix unescaped entities in `components/screens/AddMembersScreen.tsx`
  - Fix unescaped entities in `components/screens/BookingDetailsScreen.tsx`
  - Fix unescaped entities in `components/screens/MyGroupsScreen.tsx`
  - Fix unescaped entities in `components/screens/OpportunitiesScreen.tsx`
  - Fix unescaped entities in `components/screens/TaskDetailScreen.tsx`
  - Fix unescaped entities in `components/screens/TasksScreen.tsx`
  - Fix unescaped entities in `components/screens/VehicleDetailsScreen.tsx`
  - _Requirements: 4.1, 4.2_

- [ ] 28. Validate Phase 4 completion
  - Run linter to verify no P3 errors remain
  - Test UI rendering to ensure entities display correctly
  - _Requirements: 4.3_

## Phase 5: Image Optimization (Priority 4)

- [ ] 29. Replace img elements with Next.js Image component
  - Replace img elements in `components/FormImageUpload.tsx`
  - Replace img elements in `components/VehicleImageGallery.tsx`
  - Add missing alt text in `components/screens/GroupChatScreen.tsx`
  - Replace img elements in `components/screens/ListingDetailsScreen.tsx`
  - Replace img elements in `components/screens/ListingManagementScreen.tsx`
  - Replace img elements in `components/screens/VehicleStatusScreen.tsx`
  - Replace img elements in `components/vehicle-group-list.tsx`
  - _Requirements: 5.1, 5.2_

- [ ] 30. Validate Phase 5 completion
  - Run linter to verify no P4 errors remain
  - Test image loading and display functionality
  - _Requirements: 5.3_

## Phase 6: Final Cleanup (Priority 5)

- [ ] 31. Fix miscellaneous linting issues
  - Fix prefer-const in `components/screens/BookingCalendarScreen.tsx`
  - Fix prefer-const in `components/screens/OpportunitiesScreen.tsx`
  - Convert require import in `lib/types/applications.js`
  - Fix unused expressions in `app/(main)/vehicle-handover/[id]/vehicle-handover.tsx`
  - Fix unused expression in `components/screens/VehicleHandoverTaskScreen.tsx`
  - _Requirements: 6.1, 6.2, 6.3_

- [ ] 32. Final validation and cleanup
  - Run complete linter suite to verify zero errors
  - Run full test suite to ensure no regressions
  - Create summary report of all fixes applied
  - _Requirements: 6.4_
