import { NextRequest, NextResponse } from 'next/server';
import { saveSubscription } from '@/actions/push-notifications';
import { getUserAttributes } from '@/lib/amplifyServerUtils';

export async function POST(request: NextRequest) {
  try {
    // Get user attributes - this will handle authentication
    const attributes = await getUserAttributes();
    
    if (!attributes?.sub) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { subscription, userAgent, deviceInfo } = await request.json();
    
    if (!subscription || !subscription.endpoint || !subscription.keys) {
      return NextResponse.json(
        { error: 'Invalid subscription object' },
        { status: 400 }
      );
    }
    
    // Get party ID from user attributes
    const partyId = attributes["custom:db_id"] 
      ? parseInt(attributes["custom:db_id"]) 
      : undefined;
    
    const result = await saveSubscription(
      attributes.sub, // Use the user ID from attributes
      partyId,
      subscription, 
      userAgent,
      deviceInfo
    );
    
    if (result.success) {
      return NextResponse.json({ 
        success: true,
        message: 'Successfully subscribed to push notifications'
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Subscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 