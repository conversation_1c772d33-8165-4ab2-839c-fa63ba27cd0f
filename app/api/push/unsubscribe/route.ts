import { NextRequest, NextResponse } from 'next/server';
import { removeSubscription } from '@/actions/push-notifications';
import { getUserAttributes } from '@/lib/amplifyServerUtils';

export async function POST(request: NextRequest) {
  try {
    // Get user attributes - this will handle authentication
    const attributes = await getUserAttributes();
    
    if (!attributes?.sub) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const { endpoint } = await request.json();
    
    if (!endpoint) {
      return NextResponse.json(
        { error: 'Endpoint is required' },
        { status: 400 }
      );
    }
    
    const result = await removeSubscription(attributes.sub, endpoint);
    
    if (result.success) {
      return NextResponse.json({ 
        success: true,
        message: 'Successfully unsubscribed from push notifications'
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Unsubscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 