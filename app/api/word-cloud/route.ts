import { NextRequest, NextResponse } from 'next/server';
import { generateWordCloudAnalysis } from '@/actions/word-cloud-processor';
import { queueWordCloudAnalysis, getWordCloudJobStatus, listWordCloudJobs } from '@/lib/word-cloud-job-processor';
import type { FormType } from '@/db/queries';

// GET /api/word-cloud - List existing analyses or get job status
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    const status = searchParams.get('status');

    if (jobId) {
      // Get specific job status
      const job = await getWordCloudJobStatus(jobId);
      if (!job) {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      return NextResponse.json({ job });
    }

    // List jobs with optional status filter
    const jobs = await listWordCloudJobs(status as any);
    return NextResponse.json({ jobs });

  } catch (error) {
    console.error('Error in word cloud GET:', error);
    return NextResponse.json(
      { error: 'Failed to retrieve word cloud data' }, 
      { status: 500 }
    );
  }
}

// POST /api/word-cloud - Generate word cloud analysis
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      formTypes,
      fieldNames,
      dateRangeStart,
      dateRangeEnd,
      maxWords = 100,
      includesPhrases = true,
      immediate = false,
      priority = 'medium',
      presetName
    } = body;

    // Validate form types
    if (formTypes && formTypes !== 'all' && Array.isArray(formTypes)) {
      const validFormTypes: FormType[] = ['business', 'co-own', 'management', 'rideshare', 'monetise', 'waitlist'];
      const invalidTypes = formTypes.filter((type: string) => !validFormTypes.includes(type as FormType));
      if (invalidTypes.length > 0) {
        return NextResponse.json(
          { error: `Invalid form types: ${invalidTypes.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Parse dates if provided
    const parsedDateStart = dateRangeStart ? new Date(dateRangeStart) : undefined;
    const parsedDateEnd = dateRangeEnd ? new Date(dateRangeEnd) : undefined;

    if (immediate) {
      // Generate immediately (for small datasets or real-time requests)
      const analysis = await generateWordCloudAnalysis({
        formTypes,
        fieldNames,
        dateRangeStart: parsedDateStart,
        dateRangeEnd: parsedDateEnd,
        maxWords,
        includesPhrases
      });

      return NextResponse.json({ 
        success: true,
        immediate: true,
        analysis 
      });
    } else {
      // Queue for background processing (recommended for larger datasets)
      const jobId = await queueWordCloudAnalysis({
        formTypes,
        fieldNames,
        dateRangeStart: parsedDateStart,
        dateRangeEnd: parsedDateEnd,
        maxWords,
        includesPhrases,
        presetName,
        priority,
        requestedBy: 'api_user' // In real app, get from auth context
      });

      return NextResponse.json({ 
        success: true,
        immediate: false,
        jobId,
        message: 'Word cloud analysis queued for processing'
      });
    }

  } catch (error) {
    console.error('Error in word cloud POST:', error);
    return NextResponse.json(
      { error: 'Failed to generate word cloud analysis' }, 
      { status: 500 }
    );
  }
} 