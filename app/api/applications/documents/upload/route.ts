import { NextRequest, NextResponse } from "next/server";
import { uploadApplicationDocuments } from "@/drizzle-actions/applications";
import { getUserAttributes } from "@/lib/amplifyServerUtils";

export async function POST(request: NextRequest) {
  try {
    // Get user attributes - this will handle authentication
    const attributes = await getUserAttributes();
    
    if (!attributes?.sub) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { applicationId, documents } = body;

    if (!applicationId || !documents || !Array.isArray(documents)) {
      return NextResponse.json(
        { error: "Application ID and documents array are required" },
        { status: 400 }
      );
    }

    // Validate document structure
    for (const doc of documents) {
      if (!doc.documentType || !doc.documentUrl) {
        return NextResponse.json(
          { error: "Each document must have documentType and documentUrl" },
          { status: 400 }
        );
      }
    }

    const result = await uploadApplicationDocuments(applicationId, documents);

    if (result.success) {
      return NextResponse.json({ 
        success: true,
        message: "Documents uploaded successfully"
      });
    } else {
      return NextResponse.json(
        { error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error in document upload API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
