import outputs from "@/amplify_outputs.json";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";
import { CookieStorage } from "aws-amplify/utils";
import type { Metadata, Viewport } from "next";
import { Poppins } from "next/font/google";
import type React from "react";
import ClientErrorHandler from "@/components/client-error-handler";
import "./globals.css";

Amplify.configure(outputs);

cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

export const metadata: Metadata = {
  title: "Poolly - Vehicle Co-ownership Platform",
  description: "Making vehicle ownership possible for everyone through co-ownership and sharing",
  generator: "v0.dev",
  applicationName: "Poolly",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Poolly",
  },
  formatDetection: {
    telephone: false,
  },
  robots: {
    index: false,
    follow: false,
  },
  manifest: "/manifest.json",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  viewportFit: "cover",
  themeColor: "#0066cc",
};

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "700"],
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body className={`${poppins.className} bg-white`}>
        <ClientErrorHandler>
          {children}
        </ClientErrorHandler>
      </body>
    </html>
  );
}
