"use client";

import { useRef, useState } from "react";
import { Upload, X, Loader, FileText } from "lucide-react";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";
import { useUserAttributes } from "@/hooks/useUserAttributes";

export interface UploadedDocument {
  file: File;
  previewUrl: string;
  s3Path: string;
}

interface DocumentUploaderProps {
  document: UploadedDocument | null;
  onDocumentChange: (document: UploadedDocument | null) => void;
  onError?: (error: string) => void;
  title?: string;
}

export default function DocumentUploader({
  document,
  onDocumentChange,
  onError,
  title = "Click to upload a document",
}: DocumentUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const { attributes } = useUserAttributes();

  const handleError = (message: string) => {
    console.error(message);
    onError?.(message);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (document) {
      handleError("A document is already uploaded. Remove it first.");
      return;
    }

    setIsUploading(true);
    try {
      const uploadResult = await DocumentUpload(
        file,
        `vehicleDocuments/${attributes?.sub}/`
      );
      if (uploadResult?.path) {
        const uploadedDoc: UploadedDocument = {
          file,
          previewUrl: URL.createObjectURL(file),
          s3Path: uploadResult.path,
        };
        onDocumentChange(uploadedDoc);
      } else {
        throw new Error("Upload failed");
      }
    } catch (err: any) {
      handleError(err?.message || "Failed to upload document.");
    } finally {
      setIsUploading(false);
      e.target.value = ""; // reset file input
    }
  };

  const handleRemove = async () => {
    if (!document) return;

    setIsUploading(true);
    try {
      await DocumentDelete(document.s3Path);
      URL.revokeObjectURL(document.previewUrl);
      onDocumentChange(null);
    } catch {
      handleError("Failed to delete document.");
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="bg-white border border-gray-100 rounded-xl shadow-md p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-[#333333] font-medium">Upload Document</h3>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv,.zip,.rar,.jpg,.jpeg,.png"
          onChange={handleFileChange}
          disabled={isUploading || Boolean(document)}
        />
        <button
          type="button"
          className="bg-[#009639] text-white p-2 rounded-full shadow-sm disabled:opacity-50"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading || Boolean(document)}
        >
          {isUploading ? (
            <Loader size={20} className="animate-spin" />
          ) : (
            <Upload size={20} />
          )}
        </button>
      </div>

      {document ? (
        <div className="flex items-center justify-between bg-[#f2f2f2] rounded-lg px-3 py-2">
          <a
            href={document.previewUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center space-x-2"
          >
            <FileText size={20} className="text-[#009639]" />
            <span className="truncate max-w-[200px]">{document.file.name}</span>
          </a>
          <button
            type="button"
            className="bg-white rounded-full p-1 disabled:opacity-50"
            onClick={handleRemove}
            disabled={isUploading}
          >
            <X size={14} className="text-red-500" />
          </button>
        </div>
      ) : (
        <div
          className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload size={32} className="text-[#009639] mb-2" />
          <p className="text-[#797879] text-center">{title}</p>
        </div>
      )}
    </div>
  );
}
