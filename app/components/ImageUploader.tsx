"use client";

import { useRef, useState } from "react";
import Image from "next/image";
import { Upload, X, Loader } from "lucide-react";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";

export interface UploadedImage {
  file: File;
  previewUrl: string;
  s3Path: string;
}

interface ImageUploaderProps {
  images: UploadedImage[];
  onImagesChange: (images: UploadedImage[]) => void;
  uploadPath?: string;
  minImages?: number;
  maxImages?: number;
  title?: string;
  className?: string;
  borderEnabled?: boolean;
  onError?: (error: string) => void;
}

export default function ImageUploader({

  images,
  onImagesChange,
  uploadPath = "listingsMedia",
  minImages = 1,
  maxImages = 10,
  title = "Upload Images",
  className = "",
  borderEnabled = true,
  onError
}: ImageUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);

  const handleError = (message: string) => {
    console.error(message);
    onError?.(message);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if adding new files would exceed maxImages
    if (images.length + files.length > maxImages) {
      handleError(`You can only upload up to ${maxImages} images`);
      return;
    }

    setIsUploading(true);
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        const uploadResult = await DocumentUpload(file, uploadPath);
        
        if (uploadResult && uploadResult.path) {
          return {
            file,
            previewUrl: URL.createObjectURL(file),
            s3Path: uploadResult.path,
          };
        }
        throw new Error("Upload failed");
      });
      
      const uploadedImages = await Promise.all(uploadPromises);
      onImagesChange([...images, ...uploadedImages]);
    } catch (err: any) {
      handleError(err?.message || "Failed to upload images. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemove = async (index: number) => {
    const imageToRemove = images[index];
    if (!imageToRemove) return;
    
    setIsUploading(true);
    try {
      await DocumentDelete(imageToRemove.s3Path);
      URL.revokeObjectURL(imageToRemove.previewUrl);
      onImagesChange(images.filter((_, i) => i !== index));
    } catch (error) {
      handleError("Failed to delete image. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };



  return (
    <div className={`bg-white ${borderEnabled ? "border border-gray-100 rounded-xl shadow-md p-4" : ""} ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-[#333333] font-medium">{title}</h3>
        <input
          type="file"
          ref={fileInputRef}
          className="hidden"
          accept="image/*"
          multiple
          onChange={handleFileChange}
          disabled={isUploading || images.length >= maxImages}
        />
        <button
          type="button"
          className="bg-[#009639] text-white p-2 rounded-full shadow-sm disabled:opacity-50"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading || images.length >= maxImages}
        >
          {isUploading ? <Loader size={20} className="animate-spin" /> : <Upload size={20} />}
        </button>
      </div>

      {images.length > 0 ? (
        <div className="grid grid-cols-3 gap-2">
          {images.map((image, index) => (
            <div
              key={index}
              className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
            >
              <Image
                src={image.previewUrl}
                alt={`Image ${index + 1}`}
                fill
                className="object-cover"
              />
              <button
                type="button"
                className="absolute top-1 right-1 bg-white rounded-full p-1 disabled:opacity-50"
                onClick={() => handleRemove(index)}
                disabled={isUploading}
              >
                <X size={14} className="text-red-500" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <div
          className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload size={32} className="text-[#009639] mb-2" />
          <p className="text-[#797879] text-center">
            Upload {minImages > 1 ? `at least ${minImages} images` : "images"} 
            {maxImages < Infinity ? ` (max ${maxImages})` : ""}
          </p>
        </div>
      )}
    </div>
  );
} 