"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";

export default function WelcomeScreen() {
  const router = useRouter();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isSkipPressed, setIsSkipPressed] = useState(false);

  // Define the carousel slides
  const slides = [
    {
      title: "Making it possible",
      titleHighlight: "for everyone",
      description:
        "Poolly makes car ownership affordable through fractional sharing, turning dreams into reality.",
      color: "#009639",
    },
    {
      image: "/images/cars/car-slide-2.png",
      title: "With Poolly, ",
      titleHighlight: "you're in.",
      description:
        "Join our community of co-owners and access quality vehicles for both personal and business use.",
      color: "#009639",
    },
    {
      image: "/images/cars/car-slide-3.png",
      title: "No limits. Just possibilities.",
      subtitle: "Let's get started!",
      description:
        "Manage your vehicle shares, schedule usage, and even generate income - all from one app.",
      color: "#009639",
    },
  ];

  // Auto-advance the carousel
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
    }, 5000);
    return () => clearInterval(interval);
  }, [slides.length]);

  // Handle manual navigation
  const goToSlide = (index: number): void => {
    setCurrentSlide(index);
  };

  const goToNextSlide = () => {
    setCurrentSlide((prev) => (prev === slides.length - 1 ? 0 : prev + 1));
  };

  const goToPrevSlide = () => {
    setCurrentSlide((prev) => (prev === 0 ? slides.length - 1 : prev - 1));
  };

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Carousel Container */}
      <div className="relative flex-1 flex flex-col">
        {/* Slides */}
        <div className="relative flex-1 overflow-hidden">
          {slides.map((slide, index) => (
            <div
              key={index}
              className={`absolute inset-0 flex flex-col transition-opacity duration-500 ease-in-out ${
                currentSlide === index ? "opacity-100 z-10" : "opacity-0 z-0"
              }`}
            >
              {/* Gradient background with subtle pattern */}
              <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50"></div>
              <div className="absolute inset-0 opacity-5">
                <div
                  className="absolute inset-0"
                  style={{
                    backgroundImage:
                      "url('data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23009639' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E')",
                    backgroundSize: "20px 20px",
                  }}
                ></div>
              </div>

              {/* Content */}
              <div className="relative z-10 flex flex-col items-center h-full px-6 py-8">
                {/* Status bar mockup */}
                {/* <div className="w-full flex justify-between items-center mb-2">
                  <div className="text-xs text-gray-400">9:41</div>
                  <div className="flex space-x-1">
                    <div className="w-4 h-2 bg-gray-400 rounded-sm"></div>
                    <div className="w-4 h-2 bg-gray-400 rounded-sm"></div>
                    <div className="w-4 h-2 bg-gray-400 rounded-sm"></div>
                  </div>
                </div> */}

                {/* Skip button */}
                <div className="w-full flex justify-end mb-4">
                  <button
                    className={`text-sm text-gray-500 font-medium hover:text-green-500 hover:scale-105 transition-all duration-150 ease-out active:scale-95 ${
                      isSkipPressed 
                        ? 'scale-95 opacity-60' 
                        : 'scale-100 opacity-100'
                    }`}
                    onMouseDown={() => setIsSkipPressed(true)}
                    onMouseUp={() => setIsSkipPressed(false)}
                    onMouseLeave={() => setIsSkipPressed(false)}
                    onTouchStart={() => setIsSkipPressed(true)}
                    onTouchEnd={() => setIsSkipPressed(false)}
                    onClick={() => {
                      // Add a brief delay to show the animation
                      setTimeout(() => router.push("/home"), 100);
                    }}
                  >
                    Skip
                  </button>
                </div>

                {/* Decorative elements - blue dots for slide 1 and 2, blue shapes for slide 3 */}
                {index === 0 && (
                  <>
                    <div className="absolute top-32 left-16 w-2 h-2 rounded-full bg-[#009639]/30"></div>
                    <div className="absolute bottom-64 left-8 w-2 h-2 rounded-full bg-[#009639]/30"></div>
                    <div className="absolute top-64 right-12 w-2 h-2 rounded-full bg-[#009639]/30"></div>
                    <div className="absolute bottom-48 right-16 w-2 h-2 rounded-full bg-[#009639]/30"></div>

                    {/* Car image */}
                    <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md mb-4">
                      <div className="relative w-full h-64 flex items-center justify-center">
                        <div className="absolute bottom-0 w-full h-1/6 flex items-center justify-center">
                          <div className="w-4/5 h-full border-b-4 border-[#009639]/30 rounded-full"></div>
                        </div>
                        <Image
                          src="/images/cars/tesla-model-s.png"
                          alt="Tesla Model S"
                          width={400}
                          height={200}
                          className="object-contain z-10"
                          style={{
                            filter:
                              "drop-shadow(0px 10px 15px rgba(0,0,0,0.2))",
                          }}
                        />
                      </div>
                    </div>

                    {/* Fractional Vehicle Selling Card */}
                    <div className="absolute top-40 right-6 bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 w-64 text-sm border border-white/50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#009639] flex items-center justify-center mr-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="white"
                              className="w-5 h-5"
                            >
                              <path d="M6.25 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM3.25 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM19.75 7.5a.75.75 0 00-1.5 0v2.25H16a.75.75 0 000 1.5h2.25v2.25a.75.75 0 001.5 0v-2.25H22a.75.75 0 000-1.5h-2.25V7.5z" />
                            </svg>
                          </div>
                          <span className="font-semibold text-gray-800">
                            Sell Fractions
                          </span>
                        </div>
                        <div className="bg-[#FFD700]/20 px-2 py-1 rounded-full text-xs font-medium text-[#7A5A00]">
                          New
                        </div>
                      </div>

                      <div className="flex items-center mb-3">
                        {/* <div className="mr-3">
                          <Image
                            src="/images/cars/car-small.png"
                            alt="Car"
                            width={50}
                            height={35}
                            className="object-contain"
                          />
                        </div> */}
                        <div>
                          <div className="font-medium text-gray-800">Volvo</div>
                          <div className="text-xs text-gray-500">
                            2022 • 25,000 km
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-50 p-2 rounded-md mb-3">
                        <div className="flex justify-between mb-1">
                          <span className="text-xs text-gray-500">
                            Total Value:
                          </span>
                          <span className="text-xs font-medium">R450,000</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-500">
                            Your Share:
                          </span>
                          <span className="text-xs font-medium">
                            25% (R112,500)
                          </span>
                        </div>
                      </div>

                      <button className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-2 rounded-full text-xs font-medium shadow-sm">
                        Sell Your Share
                      </button>
                    </div>
                  </>
                )}

                {index === 1 && (
                  <>
                    <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md mb-4">
                      {/* Brand colored dots */}
                      <div className="absolute top-1/3 left-1/4 w-3 h-3 rounded-full bg-[#009639]/40"></div>
                      <div className="absolute bottom-1/3 right-1/4 w-3 h-3 rounded-full bg-[#009639]/40"></div>
                      <div className="absolute top-1/4 right-1/3 w-2 h-2 rounded-full bg-[#FFD700]/40"></div>
                      <div className="absolute bottom-1/4 left-1/3 w-2 h-2 rounded-full bg-[#FFD700]/40"></div>

                      {/* Car image with arc */}
                      <div className="relative w-full h-64 flex items-center justify-center">
                        <div className="absolute bottom-0 w-full h-1/6 flex items-center justify-center">
                          <div className="w-4/5 h-full border-b-4 border-[#009639]/30 rounded-full"></div>
                        </div>
                        <Image
                          src="/images/cars/car-slide-2.png"
                          alt="Car"
                          width={400}
                          height={200}
                          className="object-contain z-10"
                          style={{
                            filter:
                              "drop-shadow(0px 10px 15px rgba(0,0,0,0.2))",
                          }}
                        />

                        {/* Handover Card */}
                        <div className="absolute -top-20 -right-4 bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 w-56 transform rotate-3 z-20 border border-white/50">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center">
                              <div className="w-8 h-8 rounded-full bg-[#009639] flex items-center justify-center mr-2">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="white"
                                  className="w-5 h-5"
                                >
                                  <path d="M4.5 6.375a4.125 4.125 0 118.25 0 4.125 4.125 0 01-8.25 0zM14.25 8.625a3.375 3.375 0 116.75 0 3.375 3.375 0 01-6.75 0zM1.5 19.125a7.125 7.125 0 0114.25 0v.003l-.001.119a.75.75 0 01-.363.63 13.067 13.067 0 01-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 01-.364-.63l-.001-.122zM17.25 19.128l-.001.144a2.25 2.25 0 01-.233.96 10.088 10.088 0 005.06-1.01.75.75 0 00.42-.643 4.875 4.875 0 00-6.957-4.611 8.586 8.586 0 011.71 5.157v.003z" />
                                </svg>
                              </div>
                              <span className="font-semibold text-gray-800 text-sm">
                                Vehicle Handover
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center mb-3">
                            {/* <div className="w-10 h-10 rounded-full bg-gray-100 overflow-hidden relative mr-2">
                              <Image
                                src="/images/avatars/avatar-1.jpg"
                                alt="Member"
                                fill
                                className="object-cover"
                              />
                            </div> */}
                            <div className="flex-1">
                              <div className="text-sm font-medium">
                                Sarah Johnson
                              </div>
                              <div className="text-xs text-gray-500">
                                Next user • 2 days
                              </div>
                            </div>
                          </div>

                          <div className="bg-gray-50 p-2 rounded-md mb-3 text-xs">
                            <div className="flex justify-between mb-1">
                              <span className="text-gray-500">Handover:</span>
                              <span className="font-medium">May 15, 08:00</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-500">Location:</span>
                              <span className="font-medium">Home Base</span>
                            </div>
                          </div>

                          <div className="flex space-x-2">
                            <button className="flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-1.5 rounded-full text-xs font-medium shadow-sm">
                              Confirm
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )}

                {index === 2 && (
                  <div className="flex-1 flex flex-col items-center justify-center w-full max-w-md mb-4">
                    <div className="relative w-full h-64 flex items-center justify-center">
                      {/* Rotated square shapes */}
                      <div className="absolute -left-8 top-8 w-40 h-40 bg-[#009639]/40 transform rotate-45"></div>
                      <div className="absolute -right-8 bottom-8 w-40 h-40 bg-[#009639]/40 transform rotate-45"></div>

                      {/* Car image */}
                      <div className="relative z-10">
                        <Image
                          src={slide.image || "/placeholder.svg"}
                          alt="Car"
                          width={400}
                          height={200}
                          className="object-contain"
                          style={{
                            filter:
                              "drop-shadow(0px 10px 15px rgba(0,0,0,0.2))",
                          }}
                        />
                      </div>

                      {/* Payment Notification Cards */}
                      <div className="absolute -top-4 -left-4 bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 w-48 transform -rotate-6 z-20 border border-white/50">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 rounded-full bg-[#009639] flex items-center justify-center mr-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="white"
                              className="w-5 h-5"
                            >
                              <path d="M12 7.5a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5z" />
                              <path
                                fillRule="evenodd"
                                d="M1.5 4.875C1.5 3.839 2.34 3 3.375 3h17.25c1.035 0 1.875.84 1.875 1.875v9.75c0 1.036-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 14.625v-9.75zM8.25 9.75a3.75 3.75 0 117.5 0 3.75 3.75 0 01-7.5 0zM18.75 9a.75.75 0 00-.75.75v.008c0 .414.336.75.75.75h.008a.75.75 0 00.75-.75V9.75a.75.75 0 00-.75-.75h-.008zM4.5 9.75A.75.75 0 015.25 9h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75H5.25a.75.75 0 01-.75-.75V9.75z"
                                clipRule="evenodd"
                              />
                              <path d="M2.25 18a.75.75 0 000 1.5c5.4 0 10.63.722 15.6 2.075 1.19.324 2.4-.558 2.4-1.82V18.75a.75.75 0 00-.75-.75H2.25z" />
                            </svg>
                          </div>
                          <span className="font-semibold text-gray-800 text-sm">
                            Payment Received
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-xs text-gray-500">
                              Rental Income
                            </div>
                            <div className="font-medium text-gray-800">
                              R2,500.00
                            </div>
                          </div>
                          <div className="bg-green-100 px-2 py-1 rounded-full text-xs font-medium text-green-800">
                            Paid
                          </div>
                        </div>
                      </div>

                      <div className="absolute -bottom-4 -right-4 bg-white/90 backdrop-blur-sm rounded-lg shadow-md p-3 w-48 transform rotate-6 z-20 border border-white/50">
                        <div className="flex items-center mb-2">
                          <div className="w-8 h-8 rounded-full bg-[#FFD700] flex items-center justify-center mr-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 24 24"
                              fill="white"
                              className="w-5 h-5"
                            >
                              <path d="M4.5 3.75a3 3 0 00-3 3v.75h21v-.75a3 3 0 00-3-3h-15z" />
                              <path
                                fillRule="evenodd"
                                d="M22.5 9.75h-21v7.5a3 3 0 003 3h15a3 3 0 003-3v-7.5zm-18 3.75a.75.75 0 01.75-.75h6a.75.75 0 010 1.5h-6a.75.75 0 01-.75-.75zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                          <span className="font-semibold text-gray-800 text-sm">
                            Monthly Summary
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <div>
                            <div className="text-xs text-gray-500">
                              Total Earnings
                            </div>
                            <div className="font-medium text-gray-800">
                              R8,750.00
                            </div>
                          </div>
                          <div className="bg-[#FFD700]/20 px-2 py-1 rounded-full text-xs font-medium text-[#7A5A00]">
                            +15%
                          </div>
                        </div>
                        <div className="flex space-x-2 mt-3 pt-2 border-t border-gray-100">
                          <button className="flex-1 bg-white border border-[#009639] text-[#009639] py-1.5 rounded-full text-xs font-medium">
                            Details
                          </button>
                          <button className="flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-1.5 rounded-full text-xs font-medium shadow-sm">
                            Withdraw
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Text Content - Glassmorphism Card */}
                <div className="w-full max-w-md text-center mt-auto mb-8 bg-white backdrop-blur-sm rounded-xl p-6 border border-white">
                  <h2 className="text-2xl font-bold text-[#333333] mb-2">
                    {slide.titleHighlight ? (
                      <>
                        {slide.title}{" "}
                        <span className="text-[#009639]">
                          {slide.titleHighlight}
                        </span>
                      </>
                    ) : (
                      <>{slide.title}</>
                    )}
                  </h2>

                  {slide.subtitle && (
                    <h3 className="text-xl font-bold text-[#333333] mb-2">
                      {slide.subtitle}
                    </h3>
                  )}

                  <p className="text-[#797879] text-center text-sm px-4">
                    {slide.description}
                  </p>
                </div>

                {/* Progress bar */}
                <div className="w-full max-w-xs flex justify-center space-x-2 mb-6">
                  {slides.map((_, i) => (
                    <div
                      key={i}
                      className={`h-1 rounded-full ${
                        i === index ? "bg-[#009639] w-12" : "bg-gray-200 w-6"
                      }`}
                    />
                  ))}
                </div>

                {/* Next button for slides 0 and 1, Get Started for slide 2 */}
                {index < 2 ? (
                  <button
                    className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full font-semibold shadow-md mb-4 max-w-xs backdrop-blur-sm border border-[#00b347]/20"
                    onClick={goToNextSlide}
                  >
                    Next
                  </button>
                ) : (
                  <button
                    className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full font-semibold shadow-md mb-4 max-w-xs backdrop-blur-sm border border-[#00b347]/20"
                    onClick={() => router.push("/home")}
                  >
                    Get started
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
