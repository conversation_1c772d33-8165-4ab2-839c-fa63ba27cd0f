"use client";

import { signOut } from "aws-amplify/auth";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { signOutAction } from "@/actions/signout";
import { clearUserAttributesCache } from "@/lib/userAttributes";

export default function LogoutScreen() {
  const router = useRouter();
  const [signingOut, setSigningOut] = useState(false);

  const handleSignOut = async () => {
    setSigningOut(true);
    try {
      // Clear server-side session
      signOutAction();
      
      // Clear client-side cache
      clearUserAttributesCache();
      
      // Sign out from Amplify
      await signOut({ global: true });
      
      router.push("/home");
    } catch (error) {
      console.error("Error signing out:", error);
      setSigningOut(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-6 py-8">
      {/* Header */}
      <div className="flex items-center mb-8">
        <button
          className="bg-[#e6ffe6] rounded-full p-2 mr-4 shadow-sm"
          onClick={() => router.back()}
        >
          <ArrowLeft size={24} className="text-[#009639]" />
        </button>
        <h1 className="text-2xl font-bold text-[#333333]">Sign Out</h1>
      </div>

      <div className="max-w-md mx-auto">
        <p className="text-[#797879] mb-8">
          You are about to sign out from your account, Please confirm below to
          proceed with signing out.
        </p>

        <button
          className="w-full bg-[#009639] text-white py-4 rounded-full text-xl font-semibold shadow-sm flex justify-center items-center"
          onClick={handleSignOut}
          disabled={signingOut}
        >
          {signingOut ? (
            <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin"></div>
          ) : (
            "Sign Out"
          )}
        </button>
      </div>
    </div>
  );
}
