@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import "@aws-amplify/ui-react/styles.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", Arial, Helvetica, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Custom utility classes for our ride-sharing app */
  .ride-primary-btn {
    @apply bg-[#009639] text-white font-medium py-3 px-4 rounded-full text-center transition-all hover:bg-[#007A2F];
  }

  .ride-secondary-btn {
    @apply border border-[#009639] text-[#009639] font-medium py-3 px-4 rounded-full text-center transition-all hover:bg-[#FFD700] hover:border-[#FFD700] hover:text-white;
  }

  .ride-card {
    @apply bg-white rounded-xl shadow-sm overflow-hidden;
  }

  .ride-map-marker {
    @apply bg-white rounded-full flex items-center justify-center shadow-md;
  }

  .ride-avatar {
    @apply rounded-full overflow-hidden bg-[#f2f2f2];
  }

  .ride-input {
    @apply w-full px-4 py-3 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#0286ff];
  }

  .ride-badge {
    @apply text-xs px-2 py-0.5 rounded-full;
  }

  .ride-success-badge {
    @apply ride-badge bg-green-100 text-green-800;
  }

  .ride-warning-badge {
    @apply ride-badge bg-yellow-100 text-yellow-800;
  }

  .ride-info-badge {
    @apply ride-badge bg-blue-100 text-blue-800;
  }

  /* Safe area utilities for mobile browsers */
  .pb-safe-bottom {
    padding-bottom: env(safe-area-inset-bottom, 0px);
  }

  .mb-safe-bottom {
    margin-bottom: env(safe-area-inset-bottom, 0px);
  }

  .pt-safe-top {
    padding-top: env(safe-area-inset-top, 0px);
  }

  .pl-safe-left {
    padding-left: env(safe-area-inset-left, 0px);
  }

  .pr-safe-right {
    padding-right: env(safe-area-inset-right, 0px);
  }

  /* Navigation specific safe area utilities */
  .pb-nav-safe {
    padding-bottom: calc(68px + env(safe-area-inset-bottom, 0px));
  }

  .mb-nav-safe {
    margin-bottom: calc(68px + env(safe-area-inset-bottom, 0px));
  }

  /* Hide scrollbar utility for native mobile feel */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Chrome, Safari and Opera */
  }
}

@layer base {
  :root {
    /* Base colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Poolly brand colors */
    --ride-primary: 142 100% 29%; /* #009639 */
    --ride-primary-foreground: 0 0% 100%;
    --ride-secondary: 142 100% 24%; /* #007A2F */
    --ride-secondary-foreground: 0 0% 100%;
    --ride-accent: 50 100% 50%; /* #FFD700 */
    --ride-accent-foreground: 0 0% 0%;
    --ride-background: 0 0% 100%; /* #FFFFFF */
    --ride-text-primary: 0 0% 20%; /* #333333 */
    --ride-text-secondary: 0 0% 47%; /* #797879 */
    --ride-border: 220 10% 85%; /* #d6d9dd */
    --ride-success: 142 76% 36%; /* #22c55e */
    --ride-warning: 38 92% 50%; /* #f59e0b */
    --ride-error: 0 84% 60%; /* #ef4444 */

    /* Original theme colors */
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Ensure full viewport height for authentication */
html, body, #__next {
  height: 100% !important;
  min-height: 100vh !important;
  background: white !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Custom Amplify Authenticator Styling - Full Height Mobile Design */

/* Main authenticator container - FULL HEIGHT */
.amplify-authenticator,
.amplify-authenticator > div,
.amplify-authenticator .amplify-flex,
.amplify-authenticator [data-amplify-authenticator],
.amplify-authenticator [data-amplify-authenticator-container] {
  background-color: white !important;
  min-height: 100vh !important;
  height: 100% !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Specific targeting for SignIn and SignUp containers */
.amplify-authenticator .amplify-tabs,
.amplify-authenticator .amplify-tabs-panel,
.amplify-authenticator [data-amplify-router],
.amplify-authenticator [data-amplify-router] > div {
  background-color: white !important;
  min-height: 100vh !important;
  height: 100% !important;
  width: 100% !important;
  max-width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Form containers for both SignIn and SignUp */
.amplify-authenticator .amplify-card,
.amplify-authenticator .amplify-flex[data-amplify-container],
.amplify-authenticator form {
  background-color: white !important;
  padding: 1.5rem !important;
  width: 100% !important;
  max-width: 100% !important;
  margin: 0 !important;
  min-height: 100vh !important;
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-start !important;
}

/* Remove all borders and shadows */
.amplify-authenticator *,
.amplify-authenticator *::before,
.amplify-authenticator *::after {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Custom header styling */
.custom-auth-header {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-top: 1rem;
  width: 100%;
}

.custom-back-button {
  background-color: #e6ffe6 !important;
  border-radius: 50% !important;
  padding: 0.5rem !important;
  margin-right: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  border: none !important;
}

.custom-back-button:hover {
  background-color: #d1ffd1 !important;
}

.custom-back-button svg {
  color: #009639 !important;
  width: 24px !important;
  height: 24px !important;
}

.custom-auth-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  color: #333333 !important;
  margin: 0 !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
}

/* Form fields styling */
.amplify-authenticator .amplify-field,
.amplify-authenticator .amplify-field-group {
  margin-bottom: 1.5rem !important;
  width: 100% !important;
}

.amplify-authenticator .amplify-field .amplify-label,
.amplify-authenticator .amplify-label {
  color: #333333 !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
}

/* Input fields - borderless design */
.amplify-authenticator .amplify-input,
.amplify-authenticator .amplify-select,
.amplify-authenticator input[type="email"],
.amplify-authenticator input[type="password"],
.amplify-authenticator input[type="text"],
.amplify-authenticator input[type="tel"] {
  background-color: #f9f9f9 !important;
  padding: 0.875rem 1rem !important;
  border-radius: 0.5rem !important;
  width: 100% !important;
  font-size: 1rem !important;
  color: #333333 !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
  transition: background-color 0.2s, box-shadow 0.2s !important;
  border: none !important;
}

.amplify-authenticator .amplify-input:focus,
.amplify-authenticator .amplify-select:focus,
.amplify-authenticator input:focus {
  background-color: white !important;
  box-shadow: 0 0 0 2px #009639 !important;
}

.amplify-authenticator .amplify-input::placeholder,
.amplify-authenticator input::placeholder {
  color: #9ca3af !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
}

/* Password field container fix */
.amplify-authenticator .amplify-field-group__outer-end {
  position: absolute !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
}

.amplify-authenticator .amplify-field-group {
  position: relative !important;
}

.amplify-authenticator .amplify-field-group .amplify-input,
.amplify-authenticator .amplify-field-group input {
  padding-right: 3rem !important;
}

/* Password visibility button */
.amplify-authenticator .amplify-button[data-amplify-fieldcontrol-show],
.amplify-authenticator .amplify-button[data-amplify-fieldcontrol-hide] {
  background: transparent !important;
  color: #6b7280 !important;
  padding: 0.5rem !important;
  min-width: auto !important;
  height: auto !important;
  border-radius: 0.25rem !important;
  border: none !important;
}

.amplify-authenticator .amplify-button[data-amplify-fieldcontrol-show]:hover,
.amplify-authenticator .amplify-button[data-amplify-fieldcontrol-hide]:hover {
  background-color: #f3f4f6 !important;
  color: #374151 !important;
}

/* Error messages */
.amplify-authenticator .amplify-field__error-text,
.amplify-authenticator .amplify-text[data-variation="error"] {
  color: #dc2626 !important;
  font-size: 0.875rem !important;
  margin-top: 0.25rem !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
}

/* ALL BUTTONS - Ensure green styling */
.amplify-authenticator .amplify-button[data-variation="primary"],
.amplify-authenticator button[type="submit"],
.amplify-authenticator .amplify-button--primary,
.amplify-authenticator .amplify-button[type="submit"] {
  background-color: #009639 !important;
  background: #009639 !important;
  color: white !important;
  padding: 0.875rem 1.5rem !important;
  border-radius: 0.5rem !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  width: 100% !important;
  margin-top: 1.5rem !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
  transition: background-color 0.2s !important;
  border: none !important;
  cursor: pointer !important;
}

.amplify-authenticator .amplify-button[data-variation="primary"]:hover,
.amplify-authenticator button[type="submit"]:hover,
.amplify-authenticator .amplify-button--primary:hover,
.amplify-authenticator .amplify-button[type="submit"]:hover {
  background-color: #007A2F !important;
  background: #007A2F !important;
}

.amplify-authenticator .amplify-button[data-variation="primary"]:active,
.amplify-authenticator button[type="submit"]:active,
.amplify-authenticator .amplify-button--primary:active,
.amplify-authenticator .amplify-button[type="submit"]:active {
  background-color: #006225 !important;
  background: #006225 !important;
}

/* Secondary buttons */
.amplify-authenticator .amplify-button[data-variation="link"] {
  color: #009639 !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
  background: transparent !important;
  border: none !important;
  padding: 0.5rem !important;
  cursor: pointer !important;
}

.amplify-authenticator .amplify-button[data-variation="link"]:hover {
  color: #007A2F !important;
  text-decoration: underline !important;
}

/* Forgot password link */
.amplify-authenticator .amplify-text a,
.amplify-authenticator .amplify-link {
  color: #009639 !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  font-family: 'Poppins', Arial, Helvetica, sans-serif !important;
}

.amplify-authenticator .amplify-text a:hover,
.amplify-authenticator .amplify-link:hover {
  color: #007A2F !important;
  text-decoration: underline !important;
}

/* Tabs styling - hide tabs to show only active form */
.amplify-authenticator .amplify-tabs-list {
  display: none !important;
}

.amplify-authenticator .amplify-tabs {
  margin-bottom: 0 !important;
  height: 100% !important;
  min-height: 100vh !important;
}

.amplify-authenticator .amplify-tabs-panel {
  height: 100% !important;
  min-height: 100vh !important;
  padding: 0 !important;
}

/* Footer content styling */
.amplify-authenticator .amplify-flex[data-amplify-footer] {
  margin-top: 2rem !important;
  text-align: center !important;
}

/* Loading states */
.amplify-authenticator .amplify-loader {
  color: #009639 !important;
}

/* Force hide Amplify footer branding */
.amplify-authenticator [data-amplify-footer],
.amplify-authenticator__footer,
.amplify-authenticator .amplify-text[data-amplify-footer] {
  display: none !important;
}

/* Responsive design */
@media (min-width: 640px) {
  .amplify-authenticator .amplify-card,
  .amplify-authenticator form {
    max-width: 400px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
  }
  
  .custom-auth-header {
    margin-bottom: 2.5rem !important;
  }
  
  .custom-auth-title {
    font-size: 1.75rem !important;
  }
}

@media (min-width: 768px) {
  .amplify-authenticator .amplify-card,
  .amplify-authenticator form {
    padding: 3rem !important;
  }
}
