"use client";

import { useState } from "react";
import ApplicationDocumentUploader, {
  type UploadedDocument,
} from "@/components/ApplicationDocumentUploader";

export default function TestUploadPage() {
  const [documents, setDocuments] = useState<UploadedDocument[]>([
    {
      name: "Driver's License",
      documentType: "drivers_license",
      uploaded: false,
      required: true,
    },
    {
      name: "Bank Statement",
      documentType: "bank_statement",
      uploaded: false,
      required: true,
    },
    {
      name: "Proof of Residence",
      documentType: "proof_of_residence",
      uploaded: false,
      required: true,
      isSpecial: true,
    },
    {
      name: "Selfie",
      documentType: "selfie",
      uploaded: false,
      required: true,
      isSpecial: true,
    },
  ]);

  const [error, setError] = useState<string | null>(null);
  const [uploadComplete, setUploadComplete] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Test Document Upload
          </h1>

          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700">{error}</p>
            </div>
          )}

          {uploadComplete && (
            <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-700">Upload completed successfully!</p>
            </div>
          )}

          <div className="space-y-6">
            <ApplicationDocumentUploader
              applicationType="ehailing"
              documents={documents}
              setDocuments={setDocuments}
              onUploadComplete={() => {
                setUploadComplete(true);
                setTimeout(() => setUploadComplete(false), 3000);
              }}
              onError={(error) => {
                setError(error);
                setTimeout(() => setError(null), 5000);
              }}
            />
          </div>

          <div className="mt-8">
            <h2 className="text-lg font-semibold mb-4">Upload Status:</h2>
            <div className="space-y-2">
              {documents.map((doc, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    doc.uploaded
                      ? "bg-green-50 border-green-200"
                      : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">{doc.name}</span>
                    <span
                      className={`text-sm ${
                        doc.uploaded ? "text-green-600" : "text-gray-500"
                      }`}
                    >
                      {doc.uploaded ? "✓ Uploaded" : "Pending"}
                    </span>
                  </div>
                  {doc.s3Path && (
                    <p className="text-xs text-gray-500 mt-1">
                      Path: {doc.s3Path}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">
              Testing Instructions:
            </h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Try uploading different file types (.pdf, .jpg, .png)</li>
              <li>• Test the selfie camera functionality</li>
              <li>
                • Test proof of residence options (formal document vs location +
                affidavit)
              </li>
              <li>• Check browser console for any errors</li>
              <li>• Verify files appear in the status section below</li>
              <li>
                • <strong>Fixed:</strong> Added bucket name to resolve
                "NoBucket" error
              </li>
            </ul>
          </div>

          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">Recent Fix:</h3>
            <p className="text-sm text-green-800">
              ✅ Fixed "NoBucket: Missing bucket name" error by specifying
              bucket: "poolly" in uploadData options
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
