"use client";

import * as Sentry from "@sentry/nextjs";
import { useEffect, useState } from "react";

export default function GlobalError({ 
  error,
  reset 
}: { 
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    Sentry.captureException(error);
  }, [error]);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      // Clear any cached chunks that might be causing issues
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
      }
      
      // Small delay to ensure cache clearing completes
      setTimeout(() => {
        reset();
        setIsRetrying(false);
      }, 500);
    } catch (err) {
      console.error('Error during retry:', err);
      setIsRetrying(false);
      // Fallback to page reload
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    window.location.href = '/home';
  };

  // Check if this is likely a chunk loading error
  const isChunkError = error.message?.includes('Loading chunk') || 
                      error.message?.includes('Failed to fetch') ||
                      error.name === 'ChunkLoadError';

  const getErrorMessage = () => {
    if (isChunkError) {
      return "We're experiencing some trouble loading the page. Please refresh to continue.";
    }
    return "We're experiencing some technical difficulties and our team is actively working to resolve this. We appreciate your patience.";
  };

  const getErrorTitle = () => {
    if (isChunkError) return "Loading Issue";
    return "Service Temporarily Unavailable";
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Poolly - Service Update</title>
        <style>{`
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
          }
          .container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
          }
          .error-card {
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 480px;
            width: 100%;
          }
          .logo {
            width: 120px;
            height: 40px;
            background-color: #009639;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 32px;
            color: white;
            font-weight: 700;
            font-size: 18px;
          }
          .icon-container {
            width: 80px;
            height: 80px;
            background-color: #f0f9ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 24px;
          }
          .icon {
            width: 40px;
            height: 40px;
            color: #009639;
          }
          .title {
            font-size: 24px;
            font-weight: 700;
            color: #111827;
            margin-bottom: 16px;
          }
          .message {
            color: #6b7280;
            margin-bottom: 32px;
            font-size: 16px;
            line-height: 1.5;
          }
          .button-group {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }
          .button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s;
            border: none;
            cursor: pointer;
            font-size: 16px;
          }
          .button-primary {
            background-color: #009639;
            color: white;
          }
          .button-primary:hover:not(:disabled) {
            background-color: #007A2F;
          }
          .button-secondary {
            background-color: #f3f4f6;
            color: #374151;
          }
          .button-secondary:hover:not(:disabled) {
            background-color: #e5e7eb;
          }
          .button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
          .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }
          @keyframes spin {
            to { transform: rotate(360deg); }
          }
          @media (min-width: 640px) {
            .button-group {
              flex-direction: row;
            }
          }
        `}</style>
      </head>
      <body>
        <div className="container">
          <div className="error-card">
            <div className="logo">Poolly</div>
            
            <div className="icon-container">
              <svg className="icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            
            <h1 className="title">
              {getErrorTitle()}
            </h1>
            
            <p className="message">
              {getErrorMessage()}
            </p>

            <div className="button-group">
              <button 
                className="button button-primary"
                onClick={handleRetry}
                disabled={isRetrying}
              >
                {isRetrying ? (
                  <>
                    <div className="spinner" />
                    Refreshing...
                  </>
                ) : (
                  <>
                    <svg width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    {isChunkError ? 'Refresh Page' : 'Try Again'}
                  </>
                )}
              </button>
              
              <button 
                className="button button-secondary"
                onClick={handleGoHome}
                disabled={isRetrying}
              >
                <svg width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Return to Home
              </button>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}