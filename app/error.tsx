"use client";

import { apiErrorFormater } from "@/lib/utils";
import { useEffect, useState } from "react";
import { AlertCircle, RotateCcw, Home, ArrowLeft } from "lucide-react";
import BottomNavigation from "./components/bottom-navigation";

export default function PostFormError({
  error,
  reset,
}: {
  error: Error;
  reset: () => void;
}) {
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    console.error("Captured error:", error);
  }, [error]);

  const handleRetry = async () => {
    setIsRetrying(true);
    
    // Clear any cached data that might be causing issues
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.filter(name => name.includes('next')).map(cacheName => caches.delete(cacheName))
        );
      }
      
      // Small delay to ensure operations complete
      setTimeout(() => {
        reset();
        setIsRetrying(false);
      }, 1000);
    } catch (err) {
      console.error('Error during retry:', err);
      setIsRetrying(false);
      reset(); // Fallback to just reset
    }
  };

  const handleGoHome = () => {
    window.location.href = '/home';
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      handleGoHome();
    }
  };

  // Check if this is likely a chunk loading error
  const isChunkError = error.message?.includes('Loading chunk') || 
                      error.message?.includes('Failed to fetch') ||
                      error.name === 'ChunkLoadError';

  // Check if this is a network error
  const isNetworkError = error.message?.includes('Failed to fetch') ||
                        error.message?.includes('NetworkError') ||
                        error.name === 'NetworkError';

  const getErrorMessage = () => {
    if (isChunkError) {
      return "We're experiencing some trouble loading the page.";
    }
    if (isNetworkError) {
      return "We're having trouble connecting to our servers. Please check your internet connection and try again.";
    }
    return "We're experiencing some technical difficulties and our team is actively working to resolve this. Please try again in a moment.";
  };

  const getErrorTitle = () => {
    if (isChunkError) return "Loading Issue";
    if (isNetworkError) return "Connection Issue";
    return "Service Temporarily Unavailable";
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5] flex flex-col">
      {/* Header */}
      <div className="bg-[#009639] px-4 py-4 flex items-center border-b border-gray-100">
        <button
          onClick={handleGoBack}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 mr-4"
          disabled={isRetrying}
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-white">Service Update</h1>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-6 pb-20">
        <div className="bg-white rounded-2xl p-8 shadow-sm text-center max-w-md w-full">
          {/* Poolly Logo */}
       
          
          {/* Error Icon */}
          <div className="w-20 h-20 bg-[#f0f9ff] rounded-full flex items-center justify-center mx-auto mb-6">
            <RotateCcw size={40} className="text-[#009639]" />
          </div>
          
          {/* Error Title */}
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {getErrorTitle()}
          </h2>
          
          {/* Error Message */}
          <p className="text-gray-600 mb-8 leading-relaxed">
            {getErrorMessage()}
          </p>

          {/* API Error Details - Only show if it's a validation error */}
          {apiErrorFormater(error)?.errors && !isChunkError && !isNetworkError && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6 text-left">
              <h4 className="font-medium text-orange-800 mb-2">Additional Information:</h4>
              <ul className="text-sm text-orange-700 space-y-1">
                {Object.entries(apiErrorFormater(error).errors).map(
                  ([field, messages]) =>
                    Array.isArray(messages)
                      ? messages.map((msg, i) => (
                          <li key={`${field}-${i}`} className="flex items-start">
                            <span className="w-1 h-1 bg-orange-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                            {msg}
                          </li>
                        ))
                      : null
                )}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="space-y-3">
            <button
              onClick={handleRetry}
              disabled={isRetrying}
              className="w-full flex items-center justify-center gap-2 bg-[#009639] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#007A2F] transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
            >
              {isRetrying ? (
                <>
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Updating...
                </>
              ) : (
                <>
                  <RotateCcw size={20} />
                  {isChunkError ? 'Refresh Page' : 'Try Again'}
                </>
              )}
            </button>
            
            <button
              onClick={handleGoHome}
              disabled={isRetrying}
              className="w-full flex items-center justify-center gap-2 bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-60"
            >
              <Home size={20} />
              Return to Home
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomNavigation />
    </div>
  );
}
