"use client";

import React, { useState, useEffect } from "react";
import {
  FileText,
  Upload,
  Download,
  Eye,
  Clock,
  CheckCircle,
  AlertTriangle,
  Trash2,
  RotateCcw,
  Plus,
  History,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

// Import our contract actions
import {
  getAssignmentContractAction,
  getAssignmentContractHistoryAction,
  uploadAssignmentContractAction,
  replaceAssignmentContractAction,
  generateContractDownloadAction,
  deleteAssignmentContractAction,
  checkAssignmentHasContractAction,
} from "@/actions/admin/contracts";

// Import types
import { ContractRecord } from "@/types/payment-contract";

interface AssignmentContractIntegrationProps {
  assignmentId: string;
  driverName: string;
  onContractStatusChange?: (hasContract: boolean) => void;
}

export default function AssignmentContractIntegration({
  assignmentId,
  driverName,
  onContractStatusChange,
}: AssignmentContractIntegrationProps) {
  const [activeContract, setActiveContract] = useState<ContractRecord | null>(
    null
  );
  const [contractHistory, setContractHistory] = useState<ContractRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasContract, setHasContract] = useState(false);

  // Dialog states
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showReplaceDialog, setShowReplaceDialog] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Form states
  const [uploadFormData, setUploadFormData] = useState({
    file: null as File | null,
    notes: "",
  });
  const [replaceFormData, setReplaceFormData] = useState({
    file: null as File | null,
    replacementReason: "",
    notes: "",
  });
  const [deleteReason, setDeleteReason] = useState("");

  // Load contract data
  useEffect(() => {
    loadContractData();
  }, [assignmentId]);

  const loadContractData = async () => {
    if (!assignmentId) return;

    setLoading(true);
    try {
      const assignmentIdNum = parseInt(assignmentId);

      const [contractResult, historyResult, hasContractResult] =
        await Promise.all([
          getAssignmentContractAction(assignmentIdNum),
          getAssignmentContractHistoryAction(assignmentIdNum),
          checkAssignmentHasContractAction(assignmentIdNum),
        ]);

      if (contractResult.success) {
        setActiveContract(contractResult.data || null);
      }

      if (historyResult.success) {
        setContractHistory(historyResult.data || []);
      }

      if (hasContractResult.success) {
        const contractStatus = hasContractResult.hasContract || false;
        setHasContract(contractStatus);
        onContractStatusChange?.(contractStatus);
      }
    } catch (error) {
      console.error("Error loading contract data:", error);
      toast.error("Failed to load contract data");
    } finally {
      setLoading(false);
    }
  };

  const handleUploadContract = async () => {
    if (!uploadFormData.file) {
      toast.error("Please select a contract file");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", assignmentId);
      formData.append("file", uploadFormData.file);
      formData.append("notes", uploadFormData.notes);

      const result = await uploadAssignmentContractAction(null, formData);

      if (result.success) {
        toast.success("Contract uploaded successfully");
        setShowUploadDialog(false);
        setUploadFormData({ file: null, notes: "" });
        // Reload contract data
        loadContractData();
      } else {
        toast.error(result.error || "Failed to upload contract");
      }
    } catch (error) {
      console.error("Error uploading contract:", error);
      toast.error("Failed to upload contract");
    }
  };

  const handleReplaceContract = async () => {
    if (!replaceFormData.file || !replaceFormData.replacementReason.trim()) {
      toast.error("Please provide a file and replacement reason");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", assignmentId);
      formData.append("file", replaceFormData.file);
      formData.append("replacementReason", replaceFormData.replacementReason);
      formData.append("notes", replaceFormData.notes);

      const result = await replaceAssignmentContractAction(null, formData);

      if (result.success) {
        toast.success("Contract replaced successfully");
        setShowReplaceDialog(false);
        setReplaceFormData({ file: null, replacementReason: "", notes: "" });
        // Reload contract data
        loadContractData();
      } else {
        toast.error(result.error || "Failed to replace contract");
      }
    } catch (error) {
      console.error("Error replacing contract:", error);
      toast.error("Failed to replace contract");
    }
  };

  const handleDownloadContract = async () => {
    if (!activeContract) return;

    try {
      const result = await generateContractDownloadAction(activeContract.id);
      if (result.success && result.data) {
        // Create a temporary link and trigger download
        const link = document.createElement("a");
        link.href = result.data;
        link.download = `contract-${assignmentId}-${activeContract.originalFilename}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success("Contract download started");
      } else {
        toast.error("Failed to generate download link");
      }
    } catch (error) {
      console.error("Error downloading contract:", error);
      toast.error("Failed to download contract");
    }
  };

  const handleDeleteContract = async () => {
    if (!activeContract || !deleteReason.trim()) {
      toast.error("Please provide a deletion reason");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("contractId", activeContract.id.toString());
      formData.append("reason", deleteReason);

      const result = await deleteAssignmentContractAction(null, formData);

      if (result.success) {
        toast.success("Contract deleted successfully");
        setShowDeleteDialog(false);
        setDeleteReason("");
        // Reload contract data
        loadContractData();
      } else {
        toast.error(result.error || "Failed to delete contract");
      }
    } catch (error) {
      console.error("Error deleting contract:", error);
      toast.error("Failed to delete contract");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getContractStatusColor = (isActive: boolean) => {
    return isActive
      ? "bg-green-100 text-green-800 border-green-200"
      : "bg-gray-100 text-gray-800 border-gray-200";
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Contract Management</h3>
          <p className="text-sm text-gray-500">
            Manage assignment contracts for {driverName}
          </p>
        </div>
        <div className="flex gap-2">
          {!hasContract ? (
            <Button
              onClick={() => setShowUploadDialog(true)}
              className="bg-[#009639] hover:bg-[#007A2F]"
            >
              <Upload size={16} className="mr-2" />
              Upload Contract
            </Button>
          ) : (
            <>
              <Button
                onClick={() => setShowReplaceDialog(true)}
                variant="outline"
              >
                <RotateCcw size={16} className="mr-2" />
                Replace Contract
              </Button>
              <Button
                onClick={() => setShowHistoryDialog(true)}
                variant="outline"
              >
                <History size={16} className="mr-2" />
                View History
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Contract Status Summary */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Contract Status</p>
                <p className="text-xl font-bold">
                  {hasContract ? (
                    <span className="text-green-600">Active</span>
                  ) : (
                    <span className="text-red-600">Missing</span>
                  )}
                </p>
              </div>
              {hasContract ? (
                <CheckCircle className="text-green-600" size={24} />
              ) : (
                <AlertTriangle className="text-red-600" size={24} />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Contract History</p>
                <p className="text-xl font-bold text-blue-600">
                  {contractHistory.length}
                </p>
              </div>
              <History className="text-blue-600" size={24} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Last Updated</p>
                <p className="text-xl font-bold text-purple-600">
                  {activeContract
                    ? formatDate(activeContract.uploadedAt)
                    : "N/A"}
                </p>
              </div>
              <Clock className="text-purple-600" size={24} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Contract Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-[#009639]" />
            Active Contract
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Clock className="animate-spin mr-2" size={20} />
              Loading contract...
            </div>
          ) : activeContract ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-lg">
                    {activeContract.originalFilename}
                  </p>
                  <p className="text-sm text-gray-500">
                    Uploaded: {formatDate(activeContract.uploadedAt)}
                  </p>
                  <p className="text-sm text-gray-500">
                    Size: {formatFileSize(activeContract.fileSize)}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge
                    variant="outline"
                    className={getContractStatusColor(activeContract.isActive)}
                  >
                    {activeContract.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>

              {activeContract.notes && (
                <div>
                  <Label className="text-sm text-gray-500">Notes</Label>
                  <p className="text-sm text-gray-700 mt-1">
                    {activeContract.notes}
                  </p>
                </div>
              )}

              {/* Contract Actions */}
              <div className="flex gap-2 pt-4 border-t">
                <Button
                  onClick={handleDownloadContract}
                  size="sm"
                  className="bg-[#009639] hover:bg-[#007A2F]"
                >
                  <Download size={16} className="mr-1" />
                  Download
                </Button>
                <Button
                  onClick={() => setShowReplaceDialog(true)}
                  size="sm"
                  variant="outline"
                >
                  <RotateCcw size={16} className="mr-1" />
                  Replace
                </Button>
                <Button
                  onClick={() => setShowDeleteDialog(true)}
                  size="sm"
                  variant="destructive"
                >
                  <Trash2 size={16} className="mr-1" />
                  Delete
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText size={48} className="mx-auto mb-4 text-gray-300" />
              <p className="font-medium">No contract uploaded</p>
              <p className="text-sm">Upload a contract to get started</p>
              <Button
                onClick={() => setShowUploadDialog(true)}
                className="mt-4 bg-[#009639] hover:bg-[#007A2F]"
              >
                <Upload size={16} className="mr-2" />
                Upload First Contract
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Contract Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Assignment Contract</DialogTitle>
            <DialogDescription>
              Upload a new contract for {driverName}'s assignment
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="contractFile">Contract File (PDF only)</Label>
              <Input
                id="contractFile"
                type="file"
                accept=".pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  setUploadFormData((prev) => ({
                    ...prev,
                    file: file || null,
                  }));
                }}
              />
              {uploadFormData.file && (
                <p className="text-sm text-gray-500 mt-1">
                  Selected: {uploadFormData.file.name} (
                  {formatFileSize(uploadFormData.file.size)})
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="uploadNotes">Notes (Optional)</Label>
              <Textarea
                id="uploadNotes"
                placeholder="Additional notes about this contract..."
                value={uploadFormData.notes}
                onChange={(e) =>
                  setUploadFormData((prev) => ({
                    ...prev,
                    notes: e.target.value,
                  }))
                }
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowUploadDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUploadContract}
              className="bg-[#009639] hover:bg-[#007A2F]"
              disabled={!uploadFormData.file}
            >
              <Upload size={16} className="mr-2" />
              Upload Contract
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Replace Contract Dialog */}
      <Dialog open={showReplaceDialog} onOpenChange={setShowReplaceDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Replace Current Contract</DialogTitle>
            <DialogDescription>
              Upload a new contract to replace the current one
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="replaceFile">New Contract File (PDF only)</Label>
              <Input
                id="replaceFile"
                type="file"
                accept=".pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  setReplaceFormData((prev) => ({
                    ...prev,
                    file: file || null,
                  }));
                }}
              />
              {replaceFormData.file && (
                <p className="text-sm text-gray-500 mt-1">
                  Selected: {replaceFormData.file.name} (
                  {formatFileSize(replaceFormData.file.size)})
                </p>
              )}
            </div>
            <div>
              <Label htmlFor="replacementReason">Replacement Reason *</Label>
              <Textarea
                id="replacementReason"
                placeholder="Why is this contract being replaced? (Required)"
                value={replaceFormData.replacementReason}
                onChange={(e) =>
                  setReplaceFormData((prev) => ({
                    ...prev,
                    replacementReason: e.target.value,
                  }))
                }
                rows={2}
                required
              />
            </div>
            <div>
              <Label htmlFor="replaceNotes">Additional Notes (Optional)</Label>
              <Textarea
                id="replaceNotes"
                placeholder="Additional notes about the new contract..."
                value={replaceFormData.notes}
                onChange={(e) =>
                  setReplaceFormData((prev) => ({
                    ...prev,
                    notes: e.target.value,
                  }))
                }
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowReplaceDialog(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleReplaceContract}
              className="bg-[#009639] hover:bg-[#007A2F]"
              disabled={
                !replaceFormData.file ||
                !replaceFormData.replacementReason.trim()
              }
            >
              <RotateCcw size={16} className="mr-2" />
              Replace Contract
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Contract History Dialog */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Contract History</DialogTitle>
            <DialogDescription>
              Complete history of contracts for this assignment
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {contractHistory.length > 0 ? (
              contractHistory.map((contract) => (
                <Card
                  key={contract.id}
                  className={`${contract.isActive ? "border-green-200 bg-green-50" : "border-gray-200"}`}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">
                          {contract.originalFilename}
                        </p>
                        <p className="text-sm text-gray-500">
                          Uploaded: {formatDate(contract.uploadedAt)} | Size:{" "}
                          {formatFileSize(contract.fileSize)}
                        </p>
                        {contract.notes && (
                          <p className="text-sm text-gray-600 mt-1">
                            {contract.notes}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className={getContractStatusColor(contract.isActive)}
                        >
                          {contract.isActive ? "Current" : "Replaced"}
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => {
                            // Download historical contract
                            generateContractDownloadAction(contract.id).then(
                              (result) => {
                                if (result.success && result.data) {
                                  const link = document.createElement("a");
                                  link.href = result.data;
                                  link.download = `contract-${assignmentId}-${contract.originalFilename}`;
                                  document.body.appendChild(link);
                                  link.click();
                                  document.body.removeChild(link);
                                }
                              }
                            );
                          }}
                        >
                          <Download size={14} />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <History size={48} className="mx-auto mb-4 text-gray-300" />
                <p>No contract history found</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowHistoryDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Contract Confirmation */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Contract</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. The contract will be permanently
              deleted from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <Label htmlFor="deleteReason">Deletion Reason *</Label>
            <Textarea
              id="deleteReason"
              placeholder="Why is this contract being deleted? (Required)"
              value={deleteReason}
              onChange={(e) => setDeleteReason(e.target.value)}
              rows={3}
              className="mt-2"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteContract}
              className="bg-red-600 hover:bg-red-700"
              disabled={!deleteReason.trim()}
            >
              Delete Contract
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
