"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  FileText,
  Upload,
  Download,
  CheckCircle,
  AlertTriangle,
  Eye,
  DollarSign,
  Users,
  Car,
  Calendar,
  CreditCard,
  TrendingUp,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

// Import our integration components
import AssignmentDepositIntegration from "./AssignmentDepositIntegration";
import AssignmentContractIntegration from "./AssignmentContractIntegration";

// Import actions
import { getContractsSummaryAction } from "@/actions/admin/contracts";
import { getDepositsSummaryAction } from "@/actions/admin/deposits";

interface Assignment {
  id: string;
  driverId: string;
  driverName: string;
  driverEmail: string;
  driverPhone: string;
  vehicleId: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "pending_setup" | "contract_uploaded" | "active" | "terminated";
  weeklyRate: number;
  initiationFee: number;
  initiationFeePaid: number;
  outstandingBalance: number;
  contractUploaded: boolean;
  documentsComplete: boolean;
  lastPaymentDate?: string;
  nextPaymentDue: string;
  paymentStatus: "current" | "overdue" | "pending";
  notes?: string;
  // Enhanced fields for financial integration
  depositBalance?: number;
  totalDeposits?: number;
  hasActiveContract?: boolean;
  contractCount?: number;
  lastContractUpload?: string;
}

interface EnhancedAssignmentsTableProps {
  assignments: Assignment[];
  loading: boolean;
  onAssignmentSelect: (assignment: Assignment) => void;
  onRefresh: () => void;
}

export default function EnhancedAssignmentsTable({
  assignments: initialAssignments,
  loading,
  onAssignmentSelect,
  onRefresh,
}: EnhancedAssignmentsTableProps) {
  const [assignments, setAssignments] =
    useState<Assignment[]>(initialAssignments);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedAssignment, setSelectedAssignment] =
    useState<Assignment | null>(null);
  const [selectedTab, setSelectedTab] = useState("deposits");
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  // Financial summary state
  const [financialSummary, setFinancialSummary] = useState({
    totalDeposits: 0,
    totalDepositBalance: 0,
    totalOutstanding: 0,
    contractsUploaded: 0,
    activeAssignments: 0,
  });

  // Load enhanced assignment data
  useEffect(() => {
    loadEnhancedAssignmentData();
  }, [initialAssignments]);

  const loadEnhancedAssignmentData = async () => {
    if (initialAssignments.length === 0) return;

    try {
      const assignmentIds = initialAssignments.map((a) => parseInt(a.id));

      const [depositsResult, contractsResult] = await Promise.all([
        getDepositsSummaryAction(assignmentIds),
        getContractsSummaryAction(assignmentIds),
      ]);

      // Enhance assignments with financial data
      const enhancedAssignments = initialAssignments.map((assignment) => {
        const depositSummary = depositsResult.success
          ? depositsResult.data?.find(
              (d) => d.assignmentId === parseInt(assignment.id)
            )
          : null;

        const contractSummary = contractsResult.success
          ? contractsResult.data?.find(
              (c) => c.assignmentId === parseInt(assignment.id)
            )
          : null;

        return {
          ...assignment,
          depositBalance: depositSummary?.totalBalanceRemaining || 0,
          totalDeposits: depositSummary?.totalDepositAmount || 0,
          hasActiveContract: contractSummary?.hasActiveContract || false,
          contractCount: contractSummary?.contractCount || 0,
          lastContractUpload: contractSummary?.latestContractDate,
        };
      });

      setAssignments(enhancedAssignments);

      // Calculate financial summary
      const summary = enhancedAssignments.reduce(
        (acc, assignment) => ({
          totalDeposits: acc.totalDeposits + (assignment.totalDeposits || 0),
          totalDepositBalance:
            acc.totalDepositBalance + (assignment.depositBalance || 0),
          totalOutstanding:
            acc.totalOutstanding + assignment.outstandingBalance,
          contractsUploaded:
            acc.contractsUploaded + (assignment.hasActiveContract ? 1 : 0),
          activeAssignments:
            acc.activeAssignments + (assignment.status === "active" ? 1 : 0),
        }),
        {
          totalDeposits: 0,
          totalDepositBalance: 0,
          totalOutstanding: 0,
          contractsUploaded: 0,
          activeAssignments: 0,
        }
      );

      setFinancialSummary(summary);
    } catch (error) {
      console.error("Error loading enhanced assignment data:", error);
      toast.error("Failed to load financial data");
    }
  };

  // Filter assignments based on search and status
  const filteredAssignments = assignments.filter((assignment) => {
    const matchesSearch =
      assignment.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assignment.vehicleName
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      assignment.driverEmail.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter =
      filterStatus === "all" || assignment.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const handleViewDetails = (
    assignment: Assignment,
    tab: string = "deposits"
  ) => {
    setSelectedAssignment(assignment);
    setSelectedTab(tab);
    setIsDetailsDialogOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "contract_uploaded":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending_setup":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "current":
        return "bg-green-100 text-green-800 border-green-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatCurrency = (amount: number) => {
    return `R${amount.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      {/* Financial Overview Dashboard */}
      <div className="grid grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-[#009639]" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Total Deposits
                </p>
                <p className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalDeposits)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">
                  Deposit Balance
                </p>
                <p className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalDepositBalance)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Outstanding</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalOutstanding)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Contracts</p>
                <p className="text-2xl font-bold">
                  {financialSummary.contractsUploaded}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active</p>
                <p className="text-2xl font-bold">
                  {financialSummary.activeAssignments}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search assignments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" />
                Status: {filterStatus === "all" ? "All" : filterStatus}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setFilterStatus("all")}>
                All Statuses
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => setFilterStatus("active")}>
                Active
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setFilterStatus("contract_uploaded")}
              >
                Contract Uploaded
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setFilterStatus("pending_setup")}
              >
                Pending Setup
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setFilterStatus("terminated")}>
                Terminated
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Button onClick={onRefresh} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Enhanced Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Assignment Management</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Driver</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Contract</TableHead>
                <TableHead>Deposit Balance</TableHead>
                <TableHead>Outstanding</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                      <span className="ml-2">Loading assignments...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredAssignments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8">
                    <div className="text-gray-500">
                      <Users size={48} className="mx-auto mb-4 text-gray-300" />
                      <p>No assignments found</p>
                      <p className="text-sm">
                        Try adjusting your search or filter criteria
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredAssignments.map((assignment) => (
                  <TableRow key={assignment.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{assignment.driverName}</p>
                        <p className="text-sm text-gray-500">
                          {assignment.driverEmail}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <p className="font-medium">{assignment.vehicleName}</p>
                        <p className="text-sm text-gray-500">
                          {assignment.vehicleRegistration}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(assignment.status)}
                      >
                        {assignment.status
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {assignment.hasActiveContract ? (
                          <Badge
                            variant="outline"
                            className="bg-green-100 text-green-800 border-green-200"
                          >
                            <CheckCircle size={12} className="mr-1" />
                            Active
                          </Badge>
                        ) : (
                          <Badge
                            variant="outline"
                            className="bg-red-100 text-red-800 border-red-200"
                          >
                            <AlertTriangle size={12} className="mr-1" />
                            Missing
                          </Badge>
                        )}
                        {assignment.contractCount &&
                          assignment.contractCount > 0 && (
                            <span className="text-xs text-gray-500">
                              ({assignment.contractCount})
                            </span>
                          )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <span
                        className={`font-medium ${(assignment.depositBalance || 0) > 0 ? "text-green-600" : "text-gray-500"}`}
                      >
                        {formatCurrency(assignment.depositBalance || 0)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                        className={`font-medium ${assignment.outstandingBalance > 0 ? "text-red-600" : "text-green-600"}`}
                      >
                        {formatCurrency(assignment.outstandingBalance)}
                      </span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            Actions
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() =>
                              handleViewDetails(assignment, "deposits")
                            }
                          >
                            <DollarSign className="mr-2 h-4 w-4" />
                            Manage Deposits
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleViewDetails(assignment, "contracts")
                            }
                          >
                            <FileText className="mr-2 h-4 w-4" />
                            Manage Contracts
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onAssignmentSelect(assignment)}
                          >
                            <Eye className="mr-2 h-4 w-4" />
                            View Full Details
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Assignment Details Dialog with Integrated Management */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              <Users className="h-6 w-6 text-[#009639]" />
              Assignment Management - {selectedAssignment?.driverName}
            </DialogTitle>
            <DialogDescription>
              Assignment ID: {selectedAssignment?.id} | Vehicle:{" "}
              {selectedAssignment?.vehicleName}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-hidden">
            <Tabs
              value={selectedTab}
              onValueChange={setSelectedTab}
              className="h-full flex flex-col"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="deposits">
                  Deposit Management
                  {selectedAssignment?.depositBalance &&
                    selectedAssignment.depositBalance > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {formatCurrency(selectedAssignment.depositBalance)}
                      </Badge>
                    )}
                </TabsTrigger>
                <TabsTrigger value="contracts">
                  Contract Management
                  {selectedAssignment?.hasActiveContract && (
                    <Badge variant="secondary" className="ml-2">
                      Active
                    </Badge>
                  )}
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto mt-4">
                <TabsContent value="deposits" className="space-y-6 m-0">
                  {selectedAssignment && (
                    <AssignmentDepositIntegration
                      assignmentId={selectedAssignment.id}
                      driverName={selectedAssignment.driverName}
                    />
                  )}
                </TabsContent>

                <TabsContent value="contracts" className="space-y-6 m-0">
                  {selectedAssignment && (
                    <AssignmentContractIntegration
                      assignmentId={selectedAssignment.id}
                      driverName={selectedAssignment.driverName}
                      onContractStatusChange={(hasContract) => {
                        // Update assignment in local state
                        setAssignments((prev) =>
                          prev.map((a) =>
                            a.id === selectedAssignment.id
                              ? { ...a, hasActiveContract: hasContract }
                              : a
                          )
                        );
                        // Refresh financial summary
                        loadEnhancedAssignmentData();
                      }}
                    />
                  )}
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
