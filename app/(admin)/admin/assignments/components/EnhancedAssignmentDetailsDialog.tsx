"use client";

import React, { useState, useEffect } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  User,
  Car,
  DollarSign,
  FileText,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Upload,
  Download,
  Edit,
  XCircle,
  Phone,
  Mail,
  MapPin,
  CreditCard,
  TrendingUp,
  Receipt,
  AlertCircle,
  Clock,
  Plus,
  Eye,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import VehicleImageGallery from "@/components/VehicleImageGallery";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import type { AssignmentDetailsDialogProps } from "../../types/assignments";

// Import our new actions
import {
  getAssignmentDepositsAction,
  getAssignmentDepositBalanceAction,
  recordDepositPaymentAction,
} from "@/actions/admin/deposits";
import {
  getAssignmentContractAction,
  uploadAssignmentContractAction,
  generateContractDownloadAction,
} from "@/actions/admin/contracts";
import {
  getWeeklyEarningsAction,
  getEarningsStatsAction,
} from "@/actions/admin/weekly-earnings";
import {
  getAssignmentPayoutsAction,
  calculatePayoutAmountAction,
} from "@/actions/admin/payouts";
import {
  getAssignmentDebtsAction,
  getDebtStatsAction,
} from "@/actions/admin/debt-management";

// Import types
import {
  DepositRecord,
  ContractRecord,
  WeeklyEarningsRecord,
  PayoutRecord,
  DebtRecord,
} from "@/types/payment-contract";

// Enhanced Assignment Details Dialog with integrated payment/contract management
export default function EnhancedAssignmentDetailsDialog({
  isOpen,
  onClose,
  assignment,
  mode,
  onUpdate,
  onRecordPayment,
  onTerminate,
}: AssignmentDetailsDialogProps) {
  const [activeTab, setActiveTab] = useState("overview");
  const [isEditing, setIsEditing] = useState(mode === "edit");
  const [editedNotes, setEditedNotes] = useState(assignment?.notes || "");
  const [terminationReason, setTerminationReason] = useState("");
  const [showTerminationDialog, setShowTerminationDialog] = useState(false);

  // New state for payment/contract management
  const [deposits, setDeposits] = useState<DepositRecord[]>([]);
  const [depositBalance, setDepositBalance] = useState<number>(0);
  const [contract, setContract] = useState<ContractRecord | null>(null);
  const [weeklyEarnings, setWeeklyEarnings] = useState<WeeklyEarningsRecord[]>(
    []
  );
  const [payouts, setPayouts] = useState<PayoutRecord[]>([]);
  const [debts, setDebts] = useState<DebtRecord[]>([]);
  const [loading, setLoading] = useState(false);

  // Stats state
  const [earningsStats, setEarningsStats] = useState<any>(null);
  const [debtStats, setDebtStats] = useState<any>(null);

  // Form states
  const [showDepositForm, setShowDepositForm] = useState(false);
  const [showContractUpload, setShowContractUpload] = useState(false);
  const [depositFormData, setDepositFormData] = useState({
    depositAmount: "",
    weeklyExpectedEarnings: "",
    amountPaid: "",
    paymentMethod: "bank_transfer",
    paymentReference: "",
    notes: "",
  });

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  // Load assignment financial data when dialog opens
  useEffect(() => {
    if (isOpen && assignment) {
      loadAssignmentFinancialData();
    }
  }, [isOpen, assignment]);

  const loadAssignmentFinancialData = async () => {
    if (!assignment) return;

    setLoading(true);
    try {
      const assignmentId = parseInt(assignment.id);

      // Load all financial data in parallel
      const [
        depositsResult,
        balanceResult,
        contractResult,
        earningsResult,
        payoutsResult,
        debtsResult,
        earningsStatsResult,
        debtStatsResult,
      ] = await Promise.all([
        getAssignmentDepositsAction(assignmentId),
        getAssignmentDepositBalanceAction(assignmentId),
        getAssignmentContractAction(assignmentId),
        getWeeklyEarningsAction(assignmentId),
        getAssignmentPayoutsAction(assignmentId),
        getAssignmentDebtsAction(assignmentId, false), // Only outstanding debts
        getEarningsStatsAction(assignmentId, "month"),
        getDebtStatsAction([assignmentId], "month"),
      ]);

      // Update state with results
      if (depositsResult.success) setDeposits(depositsResult.data || []);
      if (balanceResult.success) setDepositBalance(balanceResult.data || 0);
      if (contractResult.success) setContract(contractResult.data || null);
      if (earningsResult.success) setWeeklyEarnings(earningsResult.data || []);
      if (payoutsResult.success) setPayouts(payoutsResult.data || []);
      if (debtsResult.success) setDebts(debtsResult.data || []);
      if (earningsStatsResult.success)
        setEarningsStats(earningsStatsResult.data);
      if (debtStatsResult.success) setDebtStats(debtStatsResult.data);
    } catch (error) {
      console.error("Error loading assignment financial data:", error);
      toast.error("Failed to load financial data");
    } finally {
      setLoading(false);
    }
  };

  if (!assignment) return null;

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setIsEditing(false);
      setEditedNotes(assignment?.notes || "");
      setShowTerminationDialog(false);
      setShowDepositForm(false);
      setShowContractUpload(false);
      onClose();
    }
  };

  const handleSaveNotes = () => {
    if (onUpdate) {
      onUpdate({ ...assignment, notes: editedNotes });
    }
    setIsEditing(false);
  };

  const handleTerminate = () => {
    if (onTerminate && terminationReason.trim()) {
      onTerminate(assignment.id, terminationReason);
      setShowTerminationDialog(false);
      onClose();
    }
  };

  const handleRecordDeposit = async () => {
    if (!assignment) return;

    try {
      const formData = new FormData();
      formData.append("assignmentId", assignment.id);
      formData.append("depositAmount", depositFormData.depositAmount);
      formData.append(
        "weeklyExpectedEarnings",
        depositFormData.weeklyExpectedEarnings
      );
      formData.append("amountPaid", depositFormData.amountPaid);
      formData.append("paymentMethod", depositFormData.paymentMethod);
      formData.append("paymentReference", depositFormData.paymentReference);
      formData.append("notes", depositFormData.notes);

      const result = await recordDepositPaymentAction(null, formData);

      if (result.success) {
        toast.success("Deposit payment recorded successfully");
        setShowDepositForm(false);
        setDepositFormData({
          depositAmount: "",
          weeklyExpectedEarnings: "",
          amountPaid: "",
          paymentMethod: "bank_transfer",
          paymentReference: "",
          notes: "",
        });
        // Reload financial data
        loadAssignmentFinancialData();
      } else {
        toast.error(result.error || "Failed to record deposit payment");
      }
    } catch (error) {
      console.error("Error recording deposit:", error);
      toast.error("Failed to record deposit payment");
    }
  };

  const handleContractUpload = async (file: File) => {
    if (!assignment) return;

    try {
      const formData = new FormData();
      formData.append("assignmentId", assignment.id);
      formData.append("file", file);
      formData.append("notes", "Contract uploaded via assignment details");

      const result = await uploadAssignmentContractAction(null, formData);

      if (result.success) {
        toast.success("Contract uploaded successfully");
        setShowContractUpload(false);
        // Reload contract data
        loadAssignmentFinancialData();
      } else {
        toast.error(result.error || "Failed to upload contract");
      }
    } catch (error) {
      console.error("Error uploading contract:", error);
      toast.error("Failed to upload contract");
    }
  };

  const handleDownloadContract = async () => {
    if (!contract) return;

    try {
      const result = await generateContractDownloadAction(contract.id);
      if (result.success && result.data) {
        window.open(result.data, "_blank");
      } else {
        toast.error("Failed to generate download link");
      }
    } catch (error) {
      console.error("Error downloading contract:", error);
      toast.error("Failed to download contract");
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "contract_uploaded":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending_setup":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    return `R${num.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <User className="h-6 w-6 text-[#009639]" />
            Assignment Details - {assignment.driverName}
          </DialogTitle>
          <DialogDescription>
            Assignment ID: {assignment.id} | Vehicle: {assignment.vehicleName}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full flex flex-col"
          >
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="deposits">
                Deposits
                {depositBalance > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {formatCurrency(depositBalance)}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="contracts">
                Contracts
                {contract && (
                  <Badge variant="secondary" className="ml-2">
                    Active
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="earnings">
                Earnings
                {weeklyEarnings.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {weeklyEarnings.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="payouts">
                Payouts
                {payouts.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {payouts.length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="debts">
                Debts
                {debts.length > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {debts.length}
                  </Badge>
                )}
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto mt-4">
              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6 m-0">
                <div className="grid grid-cols-2 gap-6">
                  {/* Driver Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <User className="h-5 w-5 text-[#009639]" />
                        Driver Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <Label className="text-sm text-gray-500">
                            Full Name
                          </Label>
                          <p className="font-medium">{assignment.driverName}</p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Email</Label>
                          <p className="font-medium flex items-center gap-2">
                            <Mail size={16} />
                            {assignment.driverEmail}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Phone</Label>
                          <p className="font-medium flex items-center gap-2">
                            <Phone size={16} />
                            {assignment.driverPhone}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Email</Label>
                          <p className="font-medium flex items-center gap-2">
                            <Mail size={16} />
                            {assignment.driverEmail}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Vehicle Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Car className="h-5 w-5 text-[#009639]" />
                        Vehicle Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div>
                          <Label className="text-sm text-gray-500">
                            Vehicle Name
                          </Label>
                          <p className="font-medium">
                            {assignment.vehicleName}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Registration
                          </Label>
                          <p className="font-medium">
                            {assignment.vehicleRegistration}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Weekly Rate
                          </Label>
                          <p className="font-medium">
                            {formatCurrency(assignment.weeklyRate)}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Assignment Date
                          </Label>
                          <p className="font-medium flex items-center gap-2">
                            <Calendar size={16} />
                            {new Date(
                              assignment.assignmentDate
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Financial Overview */}
                <div className="grid grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Deposit Balance
                          </p>
                          <p className="text-xl font-bold text-[#009639]">
                            {formatCurrency(depositBalance)}
                          </p>
                        </div>
                        <CreditCard className="text-[#009639]" size={20} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Monthly Earnings
                          </p>
                          <p className="text-xl font-bold text-blue-600">
                            {formatCurrency(earningsStats?.totalEarnings || 0)}
                          </p>
                        </div>
                        <TrendingUp className="text-blue-600" size={20} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Total Payouts</p>
                          <p className="text-xl font-bold text-green-600">
                            {formatCurrency(
                              payouts.reduce(
                                (sum, p) =>
                                  sum + parseFloat(p.netEarnings.toString()),
                                0
                              )
                            )}
                          </p>
                        </div>
                        <Receipt className="text-green-600" size={20} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Outstanding Debts
                          </p>
                          <p className="text-xl font-bold text-red-600">
                            {formatCurrency(
                              debtStats?.totalOutstandingDebt || 0
                            )}
                          </p>
                        </div>
                        <AlertCircle className="text-red-600" size={20} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Status & Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <CheckCircle className="h-5 w-5 text-[#009639]" />
                      Status & Actions
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <Label className="text-sm text-gray-500">
                            Assignment Status
                          </Label>
                          <div className="mt-1">
                            <Badge
                              variant="outline"
                              className={getStatusColor(assignment.status)}
                            >
                              {assignment.status
                                .replace("_", " ")
                                .replace(/\b\w/g, (l) => l.toUpperCase())}
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Payment Status
                          </Label>
                          <div className="mt-1">
                            <Badge
                              variant="outline"
                              className={getPaymentStatusColor(
                                assignment.paymentStatus
                              )}
                            >
                              {assignment.paymentStatus
                                .charAt(0)
                                .toUpperCase() +
                                assignment.paymentStatus.slice(1)}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        {assignment.status !== "terminated" && (
                          <>
                            <Button
                              onClick={() => setShowDepositForm(true)}
                              size="sm"
                              className="bg-[#009639] hover:bg-[#007A2F]"
                            >
                              <DollarSign size={16} className="mr-1" />
                              Record Deposit
                            </Button>
                            <Button
                              onClick={() => setShowTerminationDialog(true)}
                              size="sm"
                              variant="destructive"
                            >
                              <XCircle size={16} className="mr-1" />
                              Terminate
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Notes */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-[#009639]" />
                        Assignment Notes
                      </div>
                      <Button
                        onClick={() => setIsEditing(!isEditing)}
                        size="sm"
                        variant="outline"
                      >
                        <Edit size={16} className="mr-1" />
                        {isEditing ? "Cancel" : "Edit"}
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {isEditing ? (
                      <div className="space-y-3">
                        <Textarea
                          value={editedNotes}
                          onChange={(e) => setEditedNotes(e.target.value)}
                          placeholder="Add notes about this assignment..."
                          rows={4}
                        />
                        <Button
                          onClick={handleSaveNotes}
                          size="sm"
                          className="bg-[#009639] hover:bg-[#007A2F]"
                        >
                          Save Notes
                        </Button>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-600">
                        {assignment.notes || "No notes added yet."}
                      </p>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Deposits Tab */}
              <TabsContent value="deposits" className="space-y-6 m-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">
                      Deposit Management
                    </h3>
                    <p className="text-sm text-gray-500">
                      Track deposit payments and balance
                    </p>
                  </div>
                  <Button
                    onClick={() => setShowDepositForm(true)}
                    className="bg-[#009639] hover:bg-[#007A2F]"
                  >
                    <Plus size={16} className="mr-2" />
                    Record Deposit
                  </Button>
                </div>

                {/* Deposit Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Current Balance
                          </p>
                          <p className="text-2xl font-bold text-[#009639]">
                            {formatCurrency(depositBalance)}
                          </p>
                        </div>
                        <CreditCard className="text-[#009639]" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Total Deposits
                          </p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatCurrency(
                              deposits.reduce(
                                (sum, d) => sum + d.depositAmount,
                                0
                              )
                            )}
                          </p>
                        </div>
                        <TrendingUp className="text-blue-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Payments Made</p>
                          <p className="text-2xl font-bold text-green-600">
                            {deposits.length}
                          </p>
                        </div>
                        <Receipt className="text-green-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Deposit History */}
                <Card>
                  <CardHeader>
                    <CardTitle>Deposit History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <Clock className="animate-spin mr-2" size={20} />
                        Loading deposits...
                      </div>
                    ) : deposits.length > 0 ? (
                      <div className="space-y-3">
                        {deposits.map((deposit) => (
                          <div
                            key={deposit.id}
                            className="border rounded-lg p-4"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  Deposit:{" "}
                                  {formatCurrency(deposit.depositAmount)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Payment Method:{" "}
                                  {deposit.paymentMethod || "Not specified"}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Recorded: {formatDate(deposit.createdAt)}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-[#009639]">
                                  Paid: {formatCurrency(deposit.amountPaid)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Balance:{" "}
                                  {formatCurrency(deposit.balanceRemaining)}
                                </p>
                              </div>
                            </div>
                            {deposit.notes && (
                              <p className="text-sm text-gray-600 mt-2">
                                {deposit.notes}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <CreditCard
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p>No deposits recorded yet</p>
                        <p className="text-sm">
                          Click "Record Deposit" to get started
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Deposit Form Modal */}
                {showDepositForm && (
                  <Dialog
                    open={showDepositForm}
                    onOpenChange={setShowDepositForm}
                  >
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Record Deposit Payment</DialogTitle>
                        <DialogDescription>
                          Record a new deposit payment for this assignment
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="depositAmount">
                              Deposit Amount
                            </Label>
                            <Input
                              id="depositAmount"
                              type="number"
                              placeholder="0.00"
                              value={depositFormData.depositAmount}
                              onChange={(e) =>
                                setDepositFormData((prev) => ({
                                  ...prev,
                                  depositAmount: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor="weeklyExpectedEarnings">
                              Weekly Expected Earnings
                            </Label>
                            <Input
                              id="weeklyExpectedEarnings"
                              type="number"
                              placeholder="0.00"
                              value={depositFormData.weeklyExpectedEarnings}
                              onChange={(e) =>
                                setDepositFormData((prev) => ({
                                  ...prev,
                                  weeklyExpectedEarnings: e.target.value,
                                }))
                              }
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="amountPaid">Amount Paid</Label>
                            <Input
                              id="amountPaid"
                              type="number"
                              placeholder="0.00"
                              value={depositFormData.amountPaid}
                              onChange={(e) =>
                                setDepositFormData((prev) => ({
                                  ...prev,
                                  amountPaid: e.target.value,
                                }))
                              }
                            />
                          </div>
                          <div>
                            <Label htmlFor="paymentMethod">
                              Payment Method
                            </Label>
                            <Select
                              value={depositFormData.paymentMethod}
                              onValueChange={(value) =>
                                setDepositFormData((prev) => ({
                                  ...prev,
                                  paymentMethod: value,
                                }))
                              }
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="bank_transfer">
                                  Bank Transfer
                                </SelectItem>
                                <SelectItem value="eft">EFT</SelectItem>
                                <SelectItem value="cash">Cash</SelectItem>
                                <SelectItem value="card">Card</SelectItem>
                                <SelectItem value="other">Other</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="paymentReference">
                            Payment Reference
                          </Label>
                          <Input
                            id="paymentReference"
                            placeholder="Reference number or transaction ID"
                            value={depositFormData.paymentReference}
                            onChange={(e) =>
                              setDepositFormData((prev) => ({
                                ...prev,
                                paymentReference: e.target.value,
                              }))
                            }
                          />
                        </div>
                        <div>
                          <Label htmlFor="notes">Notes</Label>
                          <Textarea
                            id="notes"
                            placeholder="Additional notes..."
                            value={depositFormData.notes}
                            onChange={(e) =>
                              setDepositFormData((prev) => ({
                                ...prev,
                                notes: e.target.value,
                              }))
                            }
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setShowDepositForm(false)}
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleRecordDeposit}
                          className="bg-[#009639] hover:bg-[#007A2F]"
                        >
                          Record Deposit
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                )}
              </TabsContent>

              {/* Contracts Tab */}
              <TabsContent value="contracts" className="space-y-6 m-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">
                      Contract Management
                    </h3>
                    <p className="text-sm text-gray-500">
                      View and manage assignment contracts
                    </p>
                  </div>
                  <Button
                    onClick={() => setShowContractUpload(true)}
                    className="bg-[#009639] hover:bg-[#007A2F]"
                  >
                    <Upload size={16} className="mr-2" />
                    Upload Contract
                  </Button>
                </div>

                {/* Contract Details */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <FileText className="h-5 w-5 text-[#009639]" />
                      Contract Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <Clock className="animate-spin mr-2" size={20} />
                        Loading contract...
                      </div>
                    ) : contract ? (
                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm text-gray-500">
                            Contract Status
                          </Label>
                          <div className="mt-1">
                            <Badge
                              variant="outline"
                              className="bg-green-100 text-green-800 border-green-200"
                            >
                              Active
                            </Badge>
                          </div>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Upload Date
                          </Label>
                          <p className="font-medium">
                            {formatDate(contract.uploadedAt)}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">
                            Contract File
                          </Label>
                          <div className="flex items-center gap-2">
                            <Button
                              onClick={handleDownloadContract}
                              size="sm"
                              variant="outline"
                              className="bg-gray-100 hover:bg-gray-200"
                            >
                              <Download size={16} className="mr-1" />
                              Download Contract
                            </Button>
                          </div>
                        </div>
                        <div>
                          <Label className="text-sm text-gray-500">Notes</Label>
                          <p className="text-sm text-gray-600">
                            {contract.notes || "No notes added."}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <FileText
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p>No contract uploaded for this assignment.</p>
                        <p className="text-sm">
                          Click "Upload Contract" to add one.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Earnings Tab */}
              <TabsContent value="earnings" className="space-y-6 m-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Weekly Earnings</h3>
                    <p className="text-sm text-gray-500">
                      Track weekly earnings and shortfalls
                    </p>
                  </div>
                </div>

                {/* Earnings Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Total Earnings
                          </p>
                          <p className="text-2xl font-bold text-blue-600">
                            {formatCurrency(earningsStats?.totalEarnings || 0)}
                          </p>
                        </div>
                        <TrendingUp className="text-blue-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Average Weekly
                          </p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(
                              earningsStats?.averageWeeklyEarnings || 0
                            )}
                          </p>
                        </div>
                        <Receipt className="text-green-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Weeks Recorded
                          </p>
                          <p className="text-2xl font-bold text-purple-600">
                            {weeklyEarnings.length}
                          </p>
                        </div>
                        <Calendar className="text-purple-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Earnings History */}
                <Card>
                  <CardHeader>
                    <CardTitle>Earnings History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <Clock className="animate-spin mr-2" size={20} />
                        Loading earnings...
                      </div>
                    ) : weeklyEarnings.length > 0 ? (
                      <div className="space-y-3">
                        {weeklyEarnings.map((earning) => (
                          <div
                            key={earning.id}
                            className="border rounded-lg p-4"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  Week: {formatDate(earning.weekStartDate)} -{" "}
                                  {formatDate(earning.weekEndDate)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Total Earnings:{" "}
                                  {formatCurrency(earning.grossEarnings)}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-blue-600">
                                  Gross: {formatCurrency(earning.grossEarnings)}
                                </p>
                                {earning.earningsShortfall &&
                                  parseFloat(
                                    earning.earningsShortfall.toString()
                                  ) > 0 && (
                                    <p className="text-sm text-red-600">
                                      Shortfall:{" "}
                                      {formatCurrency(
                                        earning.earningsShortfall
                                      )}
                                    </p>
                                  )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <TrendingUp
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p>No earnings recorded yet</p>
                        <p className="text-sm">
                          Earnings tracking starts after deposits are made
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Payouts Tab */}
              <TabsContent value="payouts" className="space-y-6 m-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Driver Payouts</h3>
                    <p className="text-sm text-gray-500">
                      Track payout history and approvals
                    </p>
                  </div>
                </div>

                {/* Payout Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Total Payouts</p>
                          <p className="text-2xl font-bold text-green-600">
                            {formatCurrency(
                              payouts.reduce(
                                (sum, p) =>
                                  sum + parseFloat(p.grossEarnings.toString()),
                                0
                              )
                            )}
                          </p>
                        </div>
                        <Receipt className="text-green-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Average Payout
                          </p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatCurrency(
                              payouts.length > 0
                                ? payouts.reduce(
                                    (sum, p) =>
                                      sum +
                                      parseFloat(p.grossEarnings.toString()),
                                    0
                                  ) / payouts.length
                                : 0
                            )}
                          </p>
                        </div>
                        <TrendingUp className="text-purple-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Payout Count</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {payouts.length}
                          </p>
                        </div>
                        <Calendar className="text-blue-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Payout History */}
                <Card>
                  <CardHeader>
                    <CardTitle>Payout History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <Clock className="animate-spin mr-2" size={20} />
                        Loading payouts...
                      </div>
                    ) : payouts.length > 0 ? (
                      <div className="space-y-3">
                        {payouts.map((payout) => (
                          <div
                            key={payout.id}
                            className="border rounded-lg p-4"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  {formatCurrency(payout.grossEarnings)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  {formatDate(payout.processedAt)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Method: {payout.paymentMethod}
                                </p>
                              </div>
                              <div className="text-right">
                                <Badge
                                  variant="outline"
                                  className={
                                    payout.status === "approved"
                                      ? "bg-green-100 text-green-800"
                                      : payout.status === "rejected"
                                        ? "bg-red-100 text-red-800"
                                        : payout.status === "processed"
                                          ? "bg-blue-100 text-blue-800"
                                          : "bg-yellow-100 text-yellow-800"
                                  }
                                >
                                  {payout.status
                                    .replace("_", " ")
                                    .charAt(0)
                                    .toUpperCase() +
                                    payout.status.replace("_", " ").slice(1)}
                                </Badge>
                              </div>
                            </div>
                            {payout.notes && (
                              <p className="text-sm text-gray-600 mt-2">
                                {payout.notes}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <Receipt
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p>No payouts processed yet</p>
                        <p className="text-sm">
                          Payouts are processed based on weekly earnings
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Debts Tab */}
              <TabsContent value="debts" className="space-y-6 m-0">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Outstanding Debts</h3>
                    <p className="text-sm text-gray-500">
                      Track debts and shortfalls
                    </p>
                  </div>
                </div>

                {/* Debt Summary */}
                <div className="grid grid-cols-3 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">
                            Total Outstanding
                          </p>
                          <p className="text-2xl font-bold text-red-600">
                            {formatCurrency(
                              debtStats?.totalOutstandingDebt || 0
                            )}
                          </p>
                        </div>
                        <AlertCircle className="text-red-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Average Debt</p>
                          <p className="text-2xl font-bold text-purple-600">
                            {formatCurrency(debtStats?.averageDebtAmount || 0)}
                          </p>
                        </div>
                        <TrendingUp className="text-purple-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-gray-500">Active Debts</p>
                          <p className="text-2xl font-bold text-blue-600">
                            {debts.length}
                          </p>
                        </div>
                        <Calendar className="text-blue-600" size={24} />
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Debt History */}
                <Card>
                  <CardHeader>
                    <CardTitle>Debt History</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {loading ? (
                      <div className="flex items-center justify-center py-8">
                        <Clock className="animate-spin mr-2" size={20} />
                        Loading debts...
                      </div>
                    ) : debts.length > 0 ? (
                      <div className="space-y-3">
                        {debts.map((debt) => (
                          <div key={debt.id} className="border rounded-lg p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">
                                  {debt.debtSourceType
                                    .replace("_", " ")
                                    .replace(/\b\w/g, (l) =>
                                      l.toUpperCase()
                                    )}{" "}
                                  Debt
                                </p>
                                <p className="text-sm text-gray-500">
                                  Created: {formatDate(debt.transactionDate)}
                                </p>
                                <p className="text-sm text-gray-500">
                                  Type: {debt.debtSourceType.replace("_", " ")}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-red-600">
                                  {formatCurrency(debt.debtAmount)}
                                </p>
                                <Badge
                                  variant="outline"
                                  className="bg-red-100 text-red-800"
                                >
                                  Outstanding
                                </Badge>
                              </div>
                            </div>
                            {debt.notes && (
                              <p className="text-sm text-gray-600 mt-2">
                                {debt.notes}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <CheckCircle
                          size={48}
                          className="mx-auto mb-4 text-gray-300"
                        />
                        <p>No outstanding debts</p>
                        <p className="text-sm">
                          Great! This assignment has no debt issues
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Contract Upload Modal */}
        {showContractUpload && (
          <Dialog
            open={showContractUpload}
            onOpenChange={setShowContractUpload}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Upload Assignment Contract</DialogTitle>
                <DialogDescription>
                  Upload a PDF contract for this assignment.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="contractFile">Contract File (PDF)</Label>
                  <Input
                    id="contractFile"
                    type="file"
                    accept=".pdf"
                    onChange={(e) => {
                      if (e.target.files && e.target.files[0]) {
                        handleContractUpload(e.target.files[0]);
                      }
                    }}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowContractUpload(false)}
                >
                  Cancel
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Termination Dialog */}
        {showTerminationDialog && (
          <Dialog
            open={showTerminationDialog}
            onOpenChange={setShowTerminationDialog}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Terminate Assignment</DialogTitle>
                <DialogDescription>
                  This action will terminate the assignment. Please provide a
                  reason.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="terminationReason">Termination Reason</Label>
                  <Textarea
                    id="terminationReason"
                    placeholder="Please provide a detailed reason for termination..."
                    value={terminationReason}
                    onChange={(e) => setTerminationReason(e.target.value)}
                    rows={4}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setShowTerminationDialog(false)}
                >
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleTerminate}>
                  Terminate Assignment
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}
