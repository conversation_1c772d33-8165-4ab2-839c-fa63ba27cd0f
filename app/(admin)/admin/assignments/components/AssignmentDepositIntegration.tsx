"use client";

import React, { useState, useEffect } from "react";
import {
  DollarSign,
  CreditCard,
  TrendingUp,
  Receipt,
  Plus,
  Clock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

// Import our new actions
import {
  getAssignmentDepositsAction,
  getAssignmentDepositBalanceAction,
  recordDepositPaymentAction,
} from "@/actions/admin/deposits";

interface AssignmentDepositIntegrationProps {
  assignmentId: string;
  driverName: string;
}

export default function AssignmentDepositIntegration({
  assignmentId,
  driverName,
}: AssignmentDepositIntegrationProps) {
  const [deposits, setDeposits] = useState<any[]>([]);
  const [depositBalance, setDepositBalance] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [showDepositForm, setShowDepositForm] = useState(false);

  const [depositFormData, setDepositFormData] = useState({
    depositAmount: "",
    weeklyExpectedEarnings: "",
    amountPaid: "",
    paymentMethod: "bank_transfer",
    paymentReference: "",
    notes: "",
  });

  // Load deposit data
  useEffect(() => {
    loadDepositData();
  }, [assignmentId]);

  const loadDepositData = async () => {
    if (!assignmentId) return;

    setLoading(true);
    try {
      const assignmentIdNum = parseInt(assignmentId);
      
      const [depositsResult, balanceResult] = await Promise.all([
        getAssignmentDepositsAction(assignmentIdNum),
        getAssignmentDepositBalanceAction(assignmentIdNum),
      ]);

      if (depositsResult.success) setDeposits(depositsResult.data || []);
      if (balanceResult.success) setDepositBalance(balanceResult.data || 0);
    } catch (error) {
      console.error("Error loading deposit data:", error);
      toast.error("Failed to load deposit data");
    } finally {
      setLoading(false);
    }
  };

  const handleRecordDeposit = async () => {
    try {
      const formData = new FormData();
      formData.append("assignmentId", assignmentId);
      formData.append("depositAmount", depositFormData.depositAmount);
      formData.append("weeklyExpectedEarnings", depositFormData.weeklyExpectedEarnings);
      formData.append("amountPaid", depositFormData.amountPaid);
      formData.append("paymentMethod", depositFormData.paymentMethod);
      formData.append("paymentReference", depositFormData.paymentReference);
      formData.append("notes", depositFormData.notes);

      const result = await recordDepositPaymentAction(null, formData);

      if (result.success) {
        toast.success("Deposit payment recorded successfully");
        setShowDepositForm(false);
        setDepositFormData({
          depositAmount: "",
          weeklyExpectedEarnings: "",
          amountPaid: "",
          paymentMethod: "bank_transfer",
          paymentReference: "",
          notes: "",
        });
        // Reload data
        loadDepositData();
      } else {
        toast.error(result.error || "Failed to record deposit payment");
      }
    } catch (error) {
      console.error("Error recording deposit:", error);
      toast.error("Failed to record deposit payment");
    }
  };

  const formatCurrency = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `R${num.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Deposit Management</h3>
          <p className="text-sm text-gray-500">Track deposit payments for {driverName}</p>
        </div>
        <Button onClick={() => setShowDepositForm(true)} className="bg-[#009639] hover:bg-[#007A2F]">
          <Plus size={16} className="mr-2" />
          Record Deposit
        </Button>
      </div>

      {/* Deposit Summary */}
      <div className="grid grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Current Balance</p>
                <p className="text-2xl font-bold text-[#009639]">
                  {formatCurrency(depositBalance)}
                </p>
              </div>
              <CreditCard className="text-[#009639]" size={24} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Deposits</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatCurrency(
                    deposits.reduce((sum, d) => sum + (d.depositAmount || 0), 0)
                  )}
                </p>
              </div>
              <TrendingUp className="text-blue-600" size={24} />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Deposits Count</p>
                <p className="text-2xl font-bold text-green-600">
                  {deposits.length}
                </p>
              </div>
              <Receipt className="text-green-600" size={24} />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Deposit History */}
      <Card>
        <CardHeader>
          <CardTitle>Deposit History</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Clock className="animate-spin mr-2" size={20} />
              Loading deposits...
            </div>
          ) : deposits.length > 0 ? (
            <div className="space-y-3">
              {deposits.map((deposit) => (
                <div key={deposit.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">
                        Deposit: {formatCurrency(deposit.depositAmount || 0)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Recorded: {formatDate(deposit.createdAt)}
                      </p>
                      {deposit.paymentMethod && (
                        <p className="text-sm text-gray-500">
                          Method: {deposit.paymentMethod}
                        </p>
                      )}
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-[#009639]">
                        Paid: {formatCurrency(deposit.amountPaid || 0)}
                      </p>
                      <p className="text-sm text-gray-500">
                        Balance: {formatCurrency(deposit.balanceRemaining || 0)}
                      </p>
                    </div>
                  </div>
                  {deposit.notes && (
                    <p className="text-sm text-gray-600 mt-2">{deposit.notes}</p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <CreditCard size={48} className="mx-auto mb-4 text-gray-300" />
              <p>No deposits recorded yet</p>
              <p className="text-sm">Click "Record Deposit" to get started</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Deposit Form Modal */}
      {showDepositForm && (
        <Dialog open={showDepositForm} onOpenChange={setShowDepositForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Record Deposit Payment</DialogTitle>
              <DialogDescription>
                Record a new deposit payment for {driverName}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="depositAmount">Deposit Amount</Label>
                  <Input
                    id="depositAmount"
                    type="number"
                    placeholder="0.00"
                    value={depositFormData.depositAmount}
                    onChange={(e) => setDepositFormData(prev => ({
                      ...prev,
                      depositAmount: e.target.value
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="weeklyExpectedEarnings">Weekly Expected Earnings</Label>
                  <Input
                    id="weeklyExpectedEarnings"
                    type="number"
                    placeholder="0.00"
                    value={depositFormData.weeklyExpectedEarnings}
                    onChange={(e) => setDepositFormData(prev => ({
                      ...prev,
                      weeklyExpectedEarnings: e.target.value
                    }))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="amountPaid">Amount Paid</Label>
                  <Input
                    id="amountPaid"
                    type="number"
                    placeholder="0.00"
                    value={depositFormData.amountPaid}
                    onChange={(e) => setDepositFormData(prev => ({
                      ...prev,
                      amountPaid: e.target.value
                    }))}
                  />
                </div>
                <div>
                  <Label htmlFor="paymentMethod">Payment Method</Label>
                  <Select
                    value={depositFormData.paymentMethod}
                    onValueChange={(value) => setDepositFormData(prev => ({
                      ...prev,
                      paymentMethod: value
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                      <SelectItem value="eft">EFT</SelectItem>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="card">Card</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="paymentReference">Payment Reference</Label>
                <Input
                  id="paymentReference"
                  placeholder="Reference number or transaction ID"
                  value={depositFormData.paymentReference}
                  onChange={(e) => setDepositFormData(prev => ({
                    ...prev,
                    paymentReference: e.target.value
                  }))}
                />
              </div>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Additional notes..."
                  value={depositFormData.notes}
                  onChange={(e) => setDepositFormData(prev => ({
                    ...prev,
                    notes: e.target.value
                  }))}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowDepositForm(false)}>
                Cancel
              </Button>
              <Button onClick={handleRecordDeposit} className="bg-[#009639] hover:bg-[#007A2F]">
                Record Deposit
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
} 