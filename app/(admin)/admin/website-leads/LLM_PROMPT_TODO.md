# LLM PROMPT: Website Leads Word Cloud Implementation

## 🎯 **CONTEXT**
You are implementing a word cloud analysis system for a vehicle sharing/management platform's website leads data. The system analyzes semi-structured JSON form submissions to extract meaningful business insights through text analysis and visualization.

## 📊 **DATA STRUCTURE**
The form submissions contain:
- **Structured fields**: `fullName`, `companyName`, `email`, `phone`, `formType`
- **Semi-structured JSON**: `formData` object with varying fields per form type
- **Form types**: `business`, `co-own`, `management`, `rideshare`, `monetise`, `waitlist`

## 🚀 **IMPLEMENTATION REQUIREMENTS**

### 1. **Database Schema**
Create tables for persistent word cloud storage:
- `word_cloud_analysis` - Main analysis sessions with metadata
- `word_cloud_words` - Individual words with frequency, weight, and categories
- `word_cloud_phrases` - N-gram phrases (bigrams, trigrams)
- `word_cloud_presets` - Pre-configured analysis templates

### 2. **Text Processing Engine**
Build comprehensive text analysis with:
- **Advanced stop words filtering** (100+ common words + industry-specific terms)
- **JSON field extraction** (recursive parsing of nested objects)
- **Word categorization** (business, location, action, time, quantity, vehicle, general)
- **N-gram phrase generation** (2-3 word meaningful phrases)
- **Frequency analysis and weight calculation**

### 3. **Stop Words Configuration**
Exclude these categories:
- **Articles & prepositions**: the, a, an, and, or, but, in, on, at, to, for, of, with, by, etc.
- **Pronouns & possessives**: I, me, my, we, our, you, your, he, him, his, she, her, etc.
- **Verbs & auxiliaries**: am, is, are, was, were, be, been, have, has, had, get, want, need, like, etc.
- **Connectors**: because, since, although, however, therefore, thus, meanwhile, etc.
- **Vehicle industry terms**: car, cars, drive, driver, driving, license, vehicle, fleet, rental, rent
- **Business process terms**: income, payment, option, interest, ownership, applicant, work, working
- **Form-specific terms**: papers, message, mobile, switchreason, looking, own, use, week, weekly

### 4. **Background Job System**
Implement asynchronous processing:
- **Priority-based job queue** (high/medium/low)
- **Job status tracking** (pending/processing/completed/failed)
- **Automatic result caching** in database
- **Scalable architecture** (in-memory for dev, Redis/SQS for production)

### 5. **React Component**
Create interactive visualization:
- **CSS-based word cloud** (no external libraries needed)
- **Category-based color coding** (business=blue, location=green, action=amber, etc.)
- **Size-based frequency display** (font size 14px-32px based on weight)
- **Tabbed interface**: Word Cloud, Phrases, Categories
- **Multiple preset views**: All Leads, Business Focus, Investment Interest, Service Requests

### 6. **API Endpoints**
Build RESTful interface:
- **POST `/api/word-cloud`**: Generate immediate or queue background analysis
- **GET `/api/word-cloud`**: List analyses or check job status
- **Parameter validation**: Form types, date ranges, field selection
- **Error handling**: Comprehensive try-catch with meaningful responses

## 🎨 **PRESET CONFIGURATIONS**

1. **All Leads Overview** (`all_leads`)
   - Form types: 'all'
   - Max words: 50
   - Description: "Complete overview"

2. **Business Solutions Focus** (`business_focus`)
   - Form types: ['business']
   - Max words: 75
   - Description: "Business-focused leads"

3. **Investment Interest** (`investment_interest`)
   - Form types: ['co-own']
   - Max words: 60
   - Description: "Co-ownership inquiries"

4. **Service Requests** (`service_requests`)
   - Form types: ['management', 'rideshare']
   - Max words: 40
   - Description: "Management & E-Hailing"

## ⚡ **PERFORMANCE CONSIDERATIONS**

- **Text caching**: Avoid re-processing same submissions
- **Incremental updates**: Only analyze new submissions since last run
- **Batch processing**: Handle large datasets in chunks
- **Memory optimization**: Stream processing for large form collections
- **Database indexing**: Optimize queries on analysis_id, frequency, created_at

## 🔧 **CUSTOMIZATION OPTIONS**

- **Field targeting**: Analyze specific JSON fields vs. all text
- **Date filtering**: Time-based analysis (last 30 days, custom ranges)
- **Category expansion**: Add business-specific word categories
- **Weight algorithms**: Custom frequency-to-weight calculations
- **Visualization themes**: Different color schemes and layouts

## 🎯 **EXPECTED OUTCOMES**

The word cloud should surface:
- **Location patterns**: Cities, regions, geographic distribution
- **Business types**: Industries, company scales, business models
- **Customer pain points**: Specific challenges and motivations
- **Investment scales**: Financial capacity indicators
- **Service preferences**: Specific feature requests and priorities

## 🚨 **CRITICAL SUCCESS FACTORS**

1. **Clean data**: Comprehensive stop word filtering for meaningful results
2. **Performance**: Background processing for datasets >1000 submissions
3. **Insights**: Focus on unique terms that differentiate lead segments
4. **User experience**: Interactive, responsive, and intuitive interface
5. **Scalability**: Architecture that grows with data volume

## 📋 **INTEGRATION CHECKLIST**

- [ ] Database schema created and indexed
- [ ] Text processing engine with stop words implemented
- [ ] Background job processor with queue system
- [ ] React component with interactive features
- [ ] API endpoints with validation and error handling
- [ ] Integration into existing website leads page
- [ ] Testing with real form submission data
- [ ] Performance optimization and monitoring
- [ ] Documentation and maintenance procedures

## 🔍 **PROMPT FOR IMPLEMENTATION**

"Implement a comprehensive word cloud analysis system for website lead forms. Focus on extracting meaningful business insights from semi-structured JSON data while filtering out common words that don't provide value. The system should handle large datasets asynchronously, provide interactive visualizations, and integrate seamlessly into the existing admin interface. Prioritize clean, actionable insights over raw word frequency."

---

**Note**: This system is designed to complement existing lead analytics (line charts, statistics) by providing qualitative text insights that reveal customer motivations, geographic patterns, and business opportunities not visible in quantitative metrics. 