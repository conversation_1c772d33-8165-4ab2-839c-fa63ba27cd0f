import { useEffect } from "react";

/**
 * Hook to ensure body styles are properly cleaned up when dialogs close
 * This prevents the "stuck screen" issue when dropdowns and modals interact
 */
export function useBodyStyleCleanup(isOpen: boolean) {
  useEffect(() => {
    if (!isOpen) {
      // Small delay to ensure all dialog cleanup is complete
      const timer = setTimeout(() => {
        // Force reset body styles that might be left over from dialog interactions
        document.body.style.pointerEvents = "";
        document.body.style.overflow = "";

        // Remove any data attributes that might be left over
        document.body.removeAttribute("data-scroll-locked");

        // Remove any classes that might be left over
        document.body.classList.remove("overflow-hidden");
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [isOpen]);
}

/**
 * Force cleanup of body styles - use this as a last resort
 */
export function forceBodyStyleCleanup() {
  document.body.style.pointerEvents = "";
  document.body.style.overflow = "";
  document.body.removeAttribute("data-scroll-locked");
  document.body.classList.remove("overflow-hidden");
}
