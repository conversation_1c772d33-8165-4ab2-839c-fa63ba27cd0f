"use client";

import React, { useState } from "react";
import {
  Clock,
  Search,
  Filter,
  ChevronDown,
  CheckCircle,
  XCircle,
  Eye,
  AlertTriangle,
  Calendar,
  Car,
  Users,
  Handshake,
  ArrowRight,
  ArrowLeft,
  MoreHorizontal,
  FileText,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { ListingApprovalData } from "@/drizzle-actions/admin/listings";

interface ApprovalsClientProps {
  pendingApprovals: ListingApprovalData[];
  allApprovals: ListingApprovalData[];
  approvalStats: Array<{
    name: string;
    value: number | string;
    change: string;
    icon: React.ReactNode;
    color: string;
  }>;
}

// Transform h_listings data to match the existing UI format
const transformListingToApproval = (listing: ListingApprovalData) => {
  const listingDetails = listing.listingDetails
    ? JSON.parse(listing.listingDetails)
    : {};

  return {
    id: listing.id, // Use the correct field name
    type: listing.listingType, // All are vehicle listings from h_listings
    user: listing.partyName || "Unknown User",
    userId: listing.partyId,
    time: `Submitted ${getTimeAgo(listing.approvalStatusAt)}`, // Use approvalStatusAt
    date: new Date(listing.approvalStatusAt).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }),
    status: listing.approvalStatus || "pending",
    details: getListingDetails(listing, listingDetails),
  };
};

// Helper function to get time ago
const getTimeAgo = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = Math.floor(
    (now.getTime() - date.getTime()) / (1000 * 60 * 60)
  );

  if (diffInHours < 1) return "less than 1 hour ago";
  if (diffInHours === 1) return "1 hour ago";
  if (diffInHours < 24) return `${diffInHours} hours ago`;

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays === 1) return "1 day ago";
  return `${diffInDays} days ago`;
};

// Helper function to get listing details
const getListingDetails = (
  listing: ListingApprovalData,
  listingDetails: any
) => {
  if (listing.sourceType === "vehicle" && listing.sourceDetails) {
    const { make, model, year, registrationNumber } = listing.sourceDetails;
    const vehicleInfo = `${make || "Unknown"} ${model || "Model"} (${year || "N/A"})`;

    // Show rental rates for rental listings
    if (listing.listingType === "rental") {
      const rates = [];
      if (listingDetails.dailyRate)
        rates.push(
          `Daily: R${parseFloat(listingDetails.dailyRate).toLocaleString()}`
        );
      if (listingDetails.weeklyRate)
        rates.push(
          `Weekly: R${parseFloat(listingDetails.weeklyRate).toLocaleString()}`
        );
      if (listingDetails.monthlyRate)
        rates.push(
          `Monthly: R${parseFloat(listingDetails.monthlyRate).toLocaleString()}`
        );

      const rateInfo = rates.length > 0 ? ` • ${rates.join(", ")}` : "";
      return registrationNumber
        ? `${vehicleInfo} - ${registrationNumber}${rateInfo}`
        : `${vehicleInfo}${rateInfo}`;
    }

    return registrationNumber
      ? `${vehicleInfo} - ${registrationNumber}`
      : vehicleInfo;
  } else if (listing.sourceType === "catalog" && listing.sourceDetails) {
    const { catalogName, catalogDescription } = listing.sourceDetails;
    return `${catalogName || "Catalog Item"} - ${catalogDescription || "No description"}`;
  }

  // Fallback to listing type and basic info
  return `${listing.listingType} - ${listing.sourceType}`;
};

export default function ApprovalsClient({
  pendingApprovals,
  allApprovals,
  approvalStats,
}: ApprovalsClientProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [selectedApprovals, setSelectedApprovals] = useState<number[]>([]);

  // Transform the h_listings data to match the existing UI format
  const transformedPendingApprovals = pendingApprovals.map(
    transformListingToApproval
  );
  const transformedAllApprovals = allApprovals.map(transformListingToApproval);

  // Use pending approvals for the main table, but could switch based on filter
  const approvalsData =
    statusFilter === "pending" || statusFilter === "all"
      ? transformedPendingApprovals
      : transformedAllApprovals.filter(
          (a) => statusFilter === "all" || a.status === statusFilter
        );

  // Filter approvals based on search query, status, and type
  const filteredApprovals = approvalsData.filter((approval) => {
    const matchesSearch =
      approval.user.toLowerCase().includes(searchQuery.toLowerCase()) ||
      approval.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
      approval.details.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      statusFilter === "all" || approval.status === statusFilter;
    const matchesType = typeFilter === "all" || approval.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  // Get approval type icon
  const getApprovalTypeIcon = (type: string) => {
    switch (type) {
      case "rental":
        return <Car size={16} className="text-blue-500" />;
      case "fractional":
        return <Car size={16} className="text-blue-500" />;
      case "e-hailing":
        return <Car size={16} className="text-blue-500" />;
      case "Vehicle Registration":
        return <Car size={16} className="text-blue-500" />;
      case "Group Formation":
        return <Users size={16} className="text-green-500" />;
      case "Partner Application":
        return <Handshake size={16} className="text-yellow-500" />;
      case "Vehicle Handover":
        return <ArrowRight size={16} className="text-purple-500" />;
      case "User Verification":
        return <FileText size={16} className="text-orange-500" />;
      default:
        return <AlertTriangle size={16} className="text-gray-500" />;
    }
  };

  // Get priority badge color
  // const getPriorityBadge = (priority: string) => {
  //   switch (priority) {
  //     case "high":
  //       return "bg-red-100 text-red-800";
  //     case "medium":
  //       return "bg-yellow-100 text-yellow-800";
  //     case "low":
  //       return "bg-green-100 text-green-800";
  //     default:
  //       return "bg-gray-100 text-gray-800";
  //   }
  // };

  // Handle select all approvals
  const handleSelectAll = () => {
    if (selectedApprovals.length === filteredApprovals.length) {
      setSelectedApprovals([]);
    } else {
      setSelectedApprovals(filteredApprovals.map((approval) => approval.id));
    }
  };

  // Handle select individual approval
  const handleSelectApproval = (approvalId: number) => {
    if (selectedApprovals.includes(approvalId)) {
      setSelectedApprovals(selectedApprovals.filter((id) => id !== approvalId));
    } else {
      setSelectedApprovals([...selectedApprovals, approvalId]);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold text-gray-800 mb-6">Approvals</h1>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {approvalStats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="p-4 flex items-center">
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                style={{
                  backgroundColor:
                    stat.name === "Approved Today"
                      ? "#00963920"
                      : stat.name === "Rejected Today"
                        ? "#dc354520"
                        : stat.name === "Pending Approvals"
                          ? "#FFD70020"
                          : "#6c757d20",
                }}
              >
                <div
                  className={
                    stat.name === "Approved Today"
                      ? "text-[#009639]"
                      : stat.name === "Rejected Today"
                        ? "text-[#dc3545]"
                        : stat.name === "Pending Approvals"
                          ? "text-[#FFD700]"
                          : "text-[#6c757d]"
                  }
                >
                  {stat.icon}
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">{stat.name}</p>
                <div className="flex items-center">
                  <p className="text-xl font-semibold">{stat.value}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="mb-6">
        <CardHeader className="pb-0">
          <CardTitle className="text-lg flex items-center">
            <Clock size={18} className="text-[#009639] mr-2" />
            Pending Approvals
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Search and Filter Bar */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-4 pt-2">
            <div className="relative w-full md:w-96">
              <Search
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                size={16}
              />
              <Input
                type="text"
                placeholder="Search approvals..."
                className="pl-10 border-gray-200 focus-visible:ring-[#009639]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2 w-full md:w-auto">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[140px] border-gray-200 focus:ring-[#009639]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[140px] border-gray-200 focus:ring-[#009639]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Vehicle Listing">
                    Vehicle Listing
                  </SelectItem>
                  <SelectItem value="Vehicle Registration">
                    Vehicle Registration
                  </SelectItem>
                  <SelectItem value="Group Formation">
                    Group Formation
                  </SelectItem>
                  <SelectItem value="Partner Application">
                    Partner Application
                  </SelectItem>
                  <SelectItem value="Vehicle Handover">
                    Vehicle Handover
                  </SelectItem>
                  <SelectItem value="User Verification">
                    User Verification
                  </SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                className="flex items-center gap-2 border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              >
                <Filter size={16} />
                More Filters
              </Button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedApprovals.length > 0 && (
            <div className="bg-gray-50 p-3 rounded-md mb-4 flex justify-between items-center">
              <p className="text-sm">
                <span className="font-medium">{selectedApprovals.length}</span>{" "}
                items selected
              </p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#009639] border-[#009639] bg-white hover:bg-[#00963910] hover:text-[#007A2F]"
                >
                  <CheckCircle size={14} className="mr-1" /> Approve All
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-[#dc3545] border-[#dc3545] bg-white hover:bg-[#dc354510] hover:text-[#b02a37]"
                >
                  <XCircle size={14} className="mr-1" /> Reject All
                </Button>
              </div>
            </div>
          )}

          {/* Approvals Table */}
          <div className="border rounded-md overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[40px]">
                    <Checkbox
                      checked={
                        filteredApprovals.length > 0 &&
                        selectedApprovals.length === filteredApprovals.length
                      }
                      onCheckedChange={handleSelectAll}
                      aria-label="Select all"
                    />
                  </TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Vehicle & Rates</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredApprovals.map((approval) => (
                  <TableRow key={approval.id}>
                    <TableCell>
                      <Checkbox
                        checked={selectedApprovals.includes(approval.id)}
                        onCheckedChange={() =>
                          handleSelectApproval(approval.id)
                        }
                        aria-label={`Select approval ${approval.id}`}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                          {getApprovalTypeIcon(approval.type)}
                        </div>
                        <span className="text-sm font-medium">
                          {approval.type.charAt(0).toUpperCase() +
                            approval.type.slice(1)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Link
                        href={`/admin/users/${approval.userId}`}
                        className="text-[#009639] hover:text-[#007A2F] text-sm font-medium"
                      >
                        {approval.user}
                      </Link>
                    </TableCell>
                    <TableCell className="max-w-md">
                      <div className="space-y-1">
                        <p className="text-sm font-medium truncate">
                          {approval.details}
                        </p>
                        <p className="text-xs text-gray-500">
                          {approval.type} listing
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="text-sm text-gray-600">
                          {approval.date}
                        </span>
                        <span className="text-xs text-gray-400">
                          {approval.time}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-[#009639] hover:bg-[#00963910] hover:text-[#007A2F]"
                        >
                          <CheckCircle size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-[#dc3545] hover:bg-[#dc354510] hover:text-[#b02a37]"
                        >
                          <XCircle size={16} />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-[#0d6efd] hover:bg-[#0d6efd10] hover:text-[#0a58ca]"
                          asChild
                        >
                          <Link href={`/admin/approvals/${approval.id}`}>
                            <Eye size={16} />
                          </Link>
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                            >
                              <MoreHorizontal size={16} />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/approvals/${approval.id}`}>
                                View Details
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem>Assign to Me</DropdownMenuItem>
                            <DropdownMenuItem>
                              Request More Info
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              Mark as Fraudulent
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredApprovals.length === 0 && (
            <div className="py-12 text-center">
              <AlertTriangle size={40} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-1">
                No approvals found
              </h3>
              <p className="text-sm text-gray-500">
                Try adjusting your search or filter criteria
              </p>
            </div>
          )}

          <div className="p-4 flex justify-between items-center">
            <p className="text-sm text-gray-500">
              Showing {filteredApprovals.length} of {approvalsData.length}{" "}
              approvals
            </p>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              >
                <ArrowLeft size={14} className="mr-1" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center border-gray-200 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
              >
                Next
                <ArrowRight size={14} className="ml-1" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
