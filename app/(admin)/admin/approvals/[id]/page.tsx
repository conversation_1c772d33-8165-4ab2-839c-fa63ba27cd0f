import React from "react";
import { notFound } from "next/navigation";
import { getListingApprovalById } from "@/drizzle-actions/admin/listings";
import ApprovalReviewPageClient from "./ApprovalReviewPageClient";

export default async function ApprovalReviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  
  try {
    const listingId = parseInt(id);
    if (isNaN(listingId)) {
      notFound();
    }

    const approvalData = await getListingApprovalById(listingId);
    
    if (!approvalData) {
      notFound();
    }

    return <ApprovalReviewPageClient approvalData={approvalData} />;
  } catch (error) {
    console.error("Error fetching approval data:", error);
    notFound();
  }
}
