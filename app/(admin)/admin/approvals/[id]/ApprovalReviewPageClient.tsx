"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Clock,
  Car,
  Phone,
  Mail,
  AlertTriangle,
  Download,
  Eye,
  Star,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ListingApprovalData } from "@/drizzle-actions/admin/listings";
import {
  approveListingAction,
  rejectListingAction,
} from "@/actions/admin/approvals";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { prepareListingTypeAndDetails } from "@/drizzle-actions/create-listings";
import { useAmplifyImage } from "@/hooks/use-amplify-image";

interface ApprovalReviewPageClientProps {
  approvalData: ListingApprovalData;
}

// Image Modal Component
function ImageModal({
  isOpen,
  onClose,
  imageUrl,
  alt,
}: {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  alt: string;
}) {
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-2">
        <div className="relative">
          <img
            src={imageUrl}
            alt={alt}
            className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white"
            onClick={onClose}
          >
            <XCircle size={20} />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Component for displaying individual vehicle images
function VehicleImage({
  mediaPath,
  alt,
  index,
  totalImages,
}: {
  mediaPath: string;
  alt: string;
  index: number;
  totalImages: number;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { imageUrl, isLoading, error } = useAmplifyImage(
    mediaPath,
    "/placeholder.svg?height=192&width=256",
    {
      validateObjectExistence: true,
      expiresIn: 900, // 15 minutes
    }
  );

  console.log("🖼️ Vehicle image debug:", {
    mediaPath,
    imageUrl,
    isLoading,
    error,
  });

  if (isLoading) {
    return (
      <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  if (
    error ||
    !imageUrl ||
    imageUrl === "/placeholder.svg?height=192&width=256"
  ) {
    return (
      <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
        <div className="text-center">
          <Car size={24} className="mx-auto text-gray-400 mb-2" />
          <p className="text-sm text-gray-500">Image unavailable</p>
          <p className="text-xs text-gray-400 mt-1">Path: {mediaPath}</p>
          {error && <p className="text-xs text-red-400 mt-1">Error: {error}</p>}
        </div>
      </div>
    );
  }

  return (
    <>
      <div
        className="relative group cursor-pointer"
        onClick={() => setIsModalOpen(true)}
      >
        <img
          src={imageUrl}
          alt={alt}
          className="w-full h-48 object-cover rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
          onError={(e) => {
            console.error("❌ Image failed to load:", {
              imageUrl,
              mediaPath,
              error: e,
            });
          }}
        />
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity rounded-lg flex items-center justify-center">
          <Button
            variant="secondary"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={(e) => {
              e.stopPropagation();
              setIsModalOpen(true);
            }}
          >
            <Eye size={16} className="mr-2" />
            View Full Size
          </Button>
        </div>
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          {index + 1} of {totalImages}
        </div>
      </div>

      <ImageModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        imageUrl={imageUrl}
        alt={alt}
      />
    </>
  );
}

export default function ApprovalReviewPageClient({
  approvalData,
}: ApprovalReviewPageClientProps) {
  const router = useRouter();
  const [currentTab, setCurrentTab] = useState(
    approvalData.approvalStatus === "approved" ||
      approvalData.approvalStatus === "rejected"
      ? "decision"
      : "overview"
  );
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);
  const [documentStatuses, setDocumentStatuses] = useState<
    Record<string, boolean | "failed">
  >({});
  const [isProcessing, setIsProcessing] = useState(false);

  // Parse listing details JSON
  const listingDetails = approvalData.listingDetails
    ? JSON.parse(approvalData.listingDetails)
    : {};

  // Debug logging
  console.log("🔍 Listing Details Debug:", {
    approvalData,
    listingDetails,
    hasDetails: Object.keys(listingDetails).length > 0,
    listingType: approvalData.listingType,
  });

  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = async () => {
    if (!pendingDecision) return;

    // Validate rejection reason
    if (pendingDecision === "rejected" && !decisionReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }

    setIsProcessing(true);

    try {
      let result;

      if (pendingDecision === "approved") {
        result = await approveListingAction(approvalData.id, decisionReason);
      } else {
        result = await rejectListingAction(approvalData.id, decisionReason);
      }

      if (result.success) {
        toast.success(result.message);
        setIsDecisionDialogOpen(false);
        setPendingDecision(null);
        setDecisionReason("");

        // Redirect back to approvals list
        router.push("/admin/approvals");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error processing decision:", error);
      toast.error("An unexpected error occurred. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  // Mock documents for now - in real implementation, get from listing details or separate table
  const mockDocuments = [
    {
      name: "Vehicle Registration",
      uploaded: true,
      verified: true,
      fileUrl: "/docs/registration.pdf",
    },
    {
      name: "Driver's License",
      uploaded: true,
      verified: false,
      fileUrl: "/docs/license.pdf",
    },
    {
      name: "Insurance Certificate",
      uploaded: true,
      verified: true,
      fileUrl: "/docs/insurance.pdf",
    },
    {
      name: "Vehicle Photos",
      uploaded: true,
      verified: false,
      fileUrl: "/docs/photos.zip",
    },
  ];

  const handleDocumentVerification = (
    docName: string,
    status: boolean | "failed"
  ) => {
    setDocumentStatuses((prev) => ({
      ...prev,
      [docName]: status,
    }));
    console.log(`Document ${docName} verification:`, status);
  };

  const getDocumentVerificationStatus = (doc: (typeof mockDocuments)[0]) => {
    const overrideStatus = documentStatuses[doc.name];
    if (overrideStatus !== undefined) {
      return overrideStatus;
    }
    return doc.verified;
  };

  const getDocumentStatus = (doc: (typeof mockDocuments)[0]) => {
    const verificationStatus = getDocumentVerificationStatus(doc);

    if (!doc.uploaded)
      return {
        color: "text-gray-400",
        bgColor: "bg-gray-100",
        icon: <XCircle size={16} />,
        text: "Not uploaded",
      };
    if (verificationStatus === "failed")
      return {
        color: "text-red-600",
        bgColor: "bg-red-100",
        icon: <XCircle size={16} />,
        text: "Verification failed",
      };
    if (verificationStatus === false)
      return {
        color: "text-yellow-600",
        bgColor: "bg-yellow-100",
        icon: <Clock size={16} />,
        text: "Pending verification",
      };
    if (verificationStatus === true)
      return {
        color: "text-green-600",
        bgColor: "bg-green-100",
        icon: <CheckCircle size={16} />,
        text: "Verified",
      };
    return {
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      icon: <Eye size={16} />,
      text: "Under review",
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/approvals">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              Vehicle Listing Review
            </h1>
            <p className="text-gray-600 mt-1">
              {approvalData.partyName} • {approvalData.sourceDetails.make}{" "}
              {approvalData.sourceDetails.model}{" "}
              {approvalData.sourceDetails.year}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(approvalData.approvalStatus)} px-3 py-1`}
          >
            {approvalData.approvalStatus.replace("_", " ")}
          </Badge>
          {approvalData.approvalStatus === "pending" && (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => handleDecision("rejected")}
              >
                <XCircle size={16} className="mr-2" />
                Reject
              </Button>
              <Button
                className="bg-[#009639] hover:bg-[#007A2F]"
                onClick={() => handleDecision("approved")}
              >
                <CheckCircle size={16} className="mr-2" />
                Approve
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Application Status Alert */}
      {approvalData.approvalStatus === "pending" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="text-yellow-600 mr-3" size={20} />
              <div>
                <p className="font-medium text-yellow-800">
                  Listing Pending Review
                </p>
                <p className="text-sm text-yellow-700">
                  This listing is waiting for your review and decision.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger
                value="decision"
                disabled={approvalData.approvalStatus === "under_review"}
              >
                Decision
                {(approvalData.approvalStatus === "approved" ||
                  approvalData.approvalStatus === "rejected") && (
                  <CheckCircle size={14} className="ml-1 text-green-600" />
                )}
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* User Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    Vehicle Owner Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {approvalData.partyName || "Unknown"}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Application Date
                      </Label>
                      <p className="text-lg">
                        {formatDate(approvalData.approvalStatusAt)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 mr-2" />
                        <p>
                          {approvalData.contactEmail ||
                            approvalData.partyEmail ||
                            "Not provided"}
                        </p>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <div className="flex items-center">
                        <Phone size={16} className="text-gray-400 mr-2" />
                        <p>{approvalData.contactPhone || "Not provided"}</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car size={20} className="text-[#009639]" />
                    Vehicle Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Make & Model
                      </Label>
                      <p className="text-lg font-medium">
                        {approvalData.sourceDetails.make}{" "}
                        {approvalData.sourceDetails.model}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Year
                      </Label>
                      <p className="text-lg">
                        {approvalData.sourceDetails.year || "N/A"}
                      </p>
                    </div>
                    {approvalData.sourceDetails.registrationNumber && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Registration Number
                        </Label>
                        <p className="text-lg font-medium">
                          {approvalData.sourceDetails.registrationNumber}
                        </p>
                      </div>
                    )}
                    {approvalData.sourceDetails.color && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Color
                        </Label>
                        <p className="text-lg font-medium capitalize">
                          {approvalData.sourceDetails.color}
                        </p>
                      </div>
                    )}
                    {approvalData.sourceDetails.vinNumber && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          VIN Number
                        </Label>
                        <p className="text-lg font-medium">
                          {approvalData.sourceDetails.vinNumber}
                        </p>
                      </div>
                    )}
                    {approvalData.sourceDetails.purchaseDate && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Purchase Date
                        </Label>
                        <p className="text-lg">
                          {formatDate(approvalData.sourceDetails.purchaseDate)}
                        </p>
                      </div>
                    )}
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Listing Type
                      </Label>
                      <p className="text-lg capitalize">
                        {approvalData.listingType}
                      </p>
                    </div>
                  </div>

                  {/* Financial Details from JSON */}
                  {Object.keys(listingDetails).length > 0 &&
                    (() => {
                      console.log("💰 Financial Details Rendering:", {
                        listingType: approvalData.listingType,
                        isRental: approvalData.listingType === "rental",
                        hasRates: !!(
                          listingDetails.rates?.daily ||
                          listingDetails.rates?.weekly ||
                          listingDetails.rates?.monthly
                        ),
                        hasFlatRates: !!(
                          listingDetails.dailyRate ||
                          listingDetails.weeklyRate ||
                          listingDetails.monthlyRate
                        ),
                        listingDetails,
                      });
                      return (
                        <div className="border-t pt-4">
                          <Label className="text-sm font-medium text-gray-500 mb-4 block">
                            Financial & Rental Details
                          </Label>

                          {/* Multi-Rate Structure for Rental Listings */}
                          {approvalData.listingType === "rental" && (
                            <div className="space-y-6">
                              {/* Rental Rates */}
                              {(listingDetails.rates?.daily ||
                                listingDetails.rates?.weekly ||
                                listingDetails.rates?.monthly ||
                                listingDetails.dailyRate ||
                                listingDetails.weeklyRate ||
                                listingDetails.monthlyRate) && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-500 mb-3 block">
                                    Rental Rates
                                  </Label>
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {(listingDetails.rates?.daily ||
                                      listingDetails.dailyRate) && (
                                      <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                        <p className="text-sm font-medium text-blue-800">
                                          Daily Rate
                                        </p>
                                        <p className="text-lg font-bold text-blue-900">
                                          R{" "}
                                          {parseFloat(
                                            listingDetails.rates?.daily ||
                                              listingDetails.dailyRate
                                          ).toLocaleString()}
                                        </p>
                                      </div>
                                    )}
                                    {(listingDetails.rates?.weekly ||
                                      listingDetails.weeklyRate) && (
                                      <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                                        <p className="text-sm font-medium text-green-800">
                                          Weekly Rate
                                        </p>
                                        <p className="text-lg font-bold text-green-900">
                                          R{" "}
                                          {parseFloat(
                                            listingDetails.rates?.weekly ||
                                              listingDetails.weeklyRate
                                          ).toLocaleString()}
                                        </p>
                                      </div>
                                    )}
                                    {(listingDetails.rates?.monthly ||
                                      listingDetails.monthlyRate) && (
                                      <div className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                                        <p className="text-sm font-medium text-purple-800">
                                          Monthly Rate
                                        </p>
                                        <p className="text-lg font-bold text-purple-900">
                                          R{" "}
                                          {parseFloat(
                                            listingDetails.rates?.monthly ||
                                              listingDetails.monthlyRate
                                          ).toLocaleString()}
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}

                              {/* Available Rental Periods */}
                              {(listingDetails.availablePeriods ||
                                listingDetails.rentalPeriods) && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-500 mb-2 block">
                                    Available Rental Periods
                                  </Label>
                                  <div className="flex flex-wrap gap-2">
                                    {(
                                      listingDetails.availablePeriods ||
                                      listingDetails.rentalPeriods
                                    ).map((period: string) => (
                                      <Badge
                                        key={period}
                                        variant="outline"
                                        className="capitalize"
                                      >
                                        {period}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              )}

                              {/* Rental Purpose */}
                              {(listingDetails.purpose ||
                                listingDetails.rentalPurpose) && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-500 mb-2 block">
                                    Rental Purpose
                                  </Label>
                                  <p className="text-lg capitalize">
                                    {listingDetails.purpose ||
                                      listingDetails.rentalPurpose}
                                  </p>
                                </div>
                              )}

                              {/* Deposit Information */}
                              {(listingDetails.deposit?.required !==
                                undefined ||
                                listingDetails.depositRequired !==
                                  undefined) && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-500 mb-2 block">
                                    Security Deposit
                                  </Label>
                                  <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                                    <div className="flex items-center gap-2">
                                      <AlertTriangle
                                        size={16}
                                        className="text-yellow-600"
                                      />
                                      <span className="font-medium text-yellow-800">
                                        {listingDetails.deposit?.required ||
                                        listingDetails.depositRequired
                                          ? "Deposit Required"
                                          : "No Deposit Required"}
                                      </span>
                                    </div>
                                    {(listingDetails.deposit?.required ||
                                      listingDetails.depositRequired) &&
                                      (listingDetails.deposit?.amount ||
                                        listingDetails.depositAmount) && (
                                        <p className="text-lg font-bold text-yellow-900 mt-1">
                                          R{" "}
                                          {parseFloat(
                                            listingDetails.deposit?.amount ||
                                              listingDetails.depositAmount
                                          ).toLocaleString()}
                                        </p>
                                      )}
                                  </div>
                                </div>
                              )}

                              {/* Driver Requirements */}
                              {(listingDetails.driverRequirements?.minimumAge ||
                                listingDetails.driverRequirements
                                  ?.minimumExperience ||
                                listingDetails.driverRequirements
                                  ?.preferredGender ||
                                listingDetails.minimumAge ||
                                listingDetails.minimumExperience ||
                                listingDetails.preferredGender) && (
                                <div>
                                  <Label className="text-sm font-medium text-gray-500 mb-3 block">
                                    Driver Requirements
                                  </Label>
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {(listingDetails.driverRequirements
                                      ?.minimumAge ||
                                      listingDetails.minimumAge) && (
                                      <div>
                                        <p className="text-sm text-gray-500">
                                          Minimum Age
                                        </p>
                                        <p className="text-lg font-medium">
                                          {listingDetails.driverRequirements
                                            ?.minimumAge ||
                                            listingDetails.minimumAge}{" "}
                                          years
                                        </p>
                                      </div>
                                    )}
                                    {(listingDetails.driverRequirements
                                      ?.minimumExperience !== undefined ||
                                      listingDetails.minimumExperience !==
                                        undefined) && (
                                      <div>
                                        <p className="text-sm text-gray-500">
                                          Minimum Experience
                                        </p>
                                        <p className="text-lg font-medium">
                                          {(listingDetails.driverRequirements
                                            ?.minimumExperience ||
                                            listingDetails.minimumExperience) ===
                                            "0" ||
                                          (listingDetails.driverRequirements
                                            ?.minimumExperience ||
                                            listingDetails.minimumExperience) ===
                                            0
                                            ? "No experience required"
                                            : `${listingDetails.driverRequirements?.minimumExperience || listingDetails.minimumExperience} years`}
                                        </p>
                                      </div>
                                    )}
                                    {(listingDetails.driverRequirements
                                      ?.preferredGender ||
                                      listingDetails.preferredGender) && (
                                      <div>
                                        <p className="text-sm text-gray-500">
                                          Gender Preference
                                        </p>
                                        <p className="text-lg font-medium capitalize">
                                          {listingDetails.driverRequirements
                                            ?.preferredGender ||
                                            listingDetails.preferredGender}
                                        </p>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Legacy/Single Rate Structure - for backward compatibility */}
                          {approvalData.listingType !== "rental" && (
                            <div className="grid grid-cols-2 gap-4">
                              {listingDetails.rate && (
                                <div>
                                  <p className="text-sm text-gray-500">Rate</p>
                                  <p className="font-semibold">
                                    R{listingDetails.rate}
                                    {listingDetails.type && (
                                      <span className="text-sm text-gray-500 ml-1">
                                        /{listingDetails.type}
                                      </span>
                                    )}
                                  </p>
                                </div>
                              )}

                              {/* 🆕 NEW: Show rental purpose */}
                              {listingDetails.purpose && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Rental Purpose
                                  </p>
                                  <p className="font-semibold capitalize">
                                    {listingDetails.purpose}
                                  </p>
                                </div>
                              )}

                              {/* 🆕 NEW: Show rental period/type */}
                              {listingDetails.type && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Rental Period
                                  </p>
                                  <p className="font-semibold capitalize">
                                    {listingDetails.type}
                                  </p>
                                </div>
                              )}

                              {/* Show deposit info if available */}
                              {listingDetails.depositRequired !== undefined && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Deposit Required
                                  </p>
                                  <p className="font-semibold">
                                    {listingDetails.depositRequired
                                      ? "Yes"
                                      : "No"}
                                    {listingDetails.depositRequired &&
                                      listingDetails.depositAmount && (
                                        <span className="text-sm ml-1">
                                          (R
                                          {parseFloat(
                                            listingDetails.depositAmount
                                          ).toLocaleString()}
                                          )
                                        </span>
                                      )}
                                  </p>
                                </div>
                              )}

                              {/* Existing fractional fields */}
                              {listingDetails.fraction && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Fraction
                                  </p>
                                  <p className="font-semibold">
                                    {(listingDetails.fraction * 100).toFixed(1)}
                                    %
                                  </p>
                                </div>
                              )}
                              {listingDetails.amount && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Amount
                                  </p>
                                  <p className="font-semibold">
                                    R{listingDetails.amount}
                                  </p>
                                </div>
                              )}
                              {listingDetails.allowPartialPurchase !==
                                undefined && (
                                <div>
                                  <p className="text-sm text-gray-500">
                                    Partial Purchase
                                  </p>
                                  <p className="font-semibold">
                                    {listingDetails.allowPartialPurchase
                                      ? "Yes"
                                      : "No"}
                                  </p>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })()}
                </CardContent>
              </Card>

              {/* Vehicle Images Card */}
              {approvalData.sourceType === "vehicle" &&
                approvalData.vehicleMedia &&
                approvalData.vehicleMedia.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Car size={20} className="text-[#009639]" />
                        Vehicle Images
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {approvalData.vehicleMedia?.map((image, index) => (
                          <VehicleImage
                            key={image.id}
                            mediaPath={image.mediaPath}
                            alt={`Vehicle image ${index + 1}`}
                            index={index}
                            totalImages={approvalData.vehicleMedia?.length || 0}
                          />
                        ))}
                      </div>
                      {approvalData.vehicleMedia.length === 0 && (
                        <div className="text-center py-8">
                          <Car
                            size={48}
                            className="mx-auto text-gray-300 mb-4"
                          />
                          <p className="text-gray-500">
                            No vehicle images available
                          </p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                )}

              {/* Preferences/Experience Card */}
              {approvalData.listingType === "ehailing-platform" && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Star size={20} className="text-[#009639]" />
                      E-hailing Experience
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Previous Experience
                        </Label>
                        <p className="text-lg">
                          {listingDetails.hasExperience ? "Yes" : "No"}
                        </p>
                      </div>
                      {listingDetails.company && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Company
                          </Label>
                          <p className="text-lg">{listingDetails.company}</p>
                        </div>
                      )}
                      {listingDetails.duration && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Duration
                          </Label>
                          <p className="text-lg">{listingDetails.duration}</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Vehicle Documents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockDocuments.map((doc, index) => {
                      const status = getDocumentStatus(doc);
                      return (
                        <div key={index} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div
                                className={`p-2 rounded-full ${status.bgColor}`}
                              >
                                <div className={status.color}>
                                  {status.icon}
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium">{doc.name}</h4>
                                <p className={`text-sm ${status.color}`}>
                                  {status.text}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <Eye size={14} />
                                View
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                              >
                                <Download size={14} />
                                Download
                              </Button>
                            </div>
                          </div>

                          {/* Document Verification Actions */}
                          {doc.uploaded && (
                            <div className="mt-4 pt-4 border-t">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">
                                  Document Verification
                                </span>
                                <div className="flex items-center space-x-2">
                                  {/* Show verification actions only if not already verified */}
                                  {getDocumentVerificationStatus(doc) ===
                                    false && (
                                    <>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.name,
                                            true
                                          )
                                        }
                                      >
                                        <CheckCircle size={14} />
                                        Approve
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
                                        onClick={() =>
                                          handleDocumentVerification(
                                            doc.name,
                                            "failed"
                                          )
                                        }
                                      >
                                        <XCircle size={14} />
                                        Reject
                                      </Button>
                                    </>
                                  )}

                                  {/* Reset verification if already verified or failed */}
                                  {(getDocumentVerificationStatus(doc) ===
                                    true ||
                                    getDocumentVerificationStatus(doc) ===
                                      "failed") && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="flex items-center gap-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                      onClick={() =>
                                        handleDocumentVerification(
                                          doc.name,
                                          false
                                        )
                                      }
                                    >
                                      <Clock size={14} />
                                      Reset
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="decision" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    {approvalData.approvalStatus === "approved" ? (
                      <CheckCircle size={20} className="text-green-600" />
                    ) : approvalData.approvalStatus === "rejected" ? (
                      <XCircle size={20} className="text-red-600" />
                    ) : (
                      <CheckCircle size={20} className="text-[#009639]" />
                    )}
                    Listing Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Show decision summary if already decided */}
                  {(approvalData.approvalStatus === "approved" ||
                    approvalData.approvalStatus === "rejected") && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 rounded-lg border-2 border-dashed">
                        <div className="flex items-center gap-3">
                          {approvalData.approvalStatus === "approved" ? (
                            <CheckCircle size={24} className="text-green-600" />
                          ) : (
                            <XCircle size={24} className="text-red-600" />
                          )}
                          <div>
                            <p className="font-semibold text-lg capitalize">
                              {approvalData.approvalStatus}
                            </p>
                            <p className="text-sm text-gray-600">
                              Decision made on{" "}
                              {formatDate(approvalData.approvalStatusAt)}
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className={`${getStatusColor(approvalData.approvalStatus)} text-sm px-3 py-1`}
                        >
                          {approvalData.approvalStatus.replace("_", " ")}
                        </Badge>
                      </div>

                      {approvalData.approvalReason && (
                        <div>
                          <Label className="text-sm font-medium text-gray-700">
                            Decision Reason
                          </Label>
                          <div className="mt-2 p-3 bg-gray-50 rounded-lg border">
                            <p className="text-sm text-gray-800">
                              {approvalData.approvalReason}
                            </p>
                          </div>
                        </div>
                      )}

                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500">
                          This listing has already been processed. Contact
                          support if you need to make changes.
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Show decision form if still pending */}
                  {approvalData.approvalStatus === "pending" && (
                    <>
                      <div>
                        <Label
                          htmlFor="decision-reason"
                          className="text-sm font-medium"
                        >
                          Decision Reason (Optional)
                        </Label>
                        <Textarea
                          id="decision-reason"
                          value={decisionReason}
                          onChange={(e) => setDecisionReason(e.target.value)}
                          placeholder="Add a note about your decision..."
                          className="mt-2"
                          rows={4}
                        />
                      </div>

                      <div className="flex space-x-4">
                        <Button
                          variant="outline"
                          className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                          onClick={() => handleDecision("rejected")}
                        >
                          <XCircle size={16} className="mr-2" />
                          Reject Listing
                        </Button>
                        <Button
                          className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
                          onClick={() => handleDecision("approved")}
                        >
                          <CheckCircle size={16} className="mr-2" />
                          Approve Listing
                        </Button>
                      </div>
                    </>
                  )}

                  {/* Show under review message */}
                  {approvalData.approvalStatus === "under_review" && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 rounded-lg border-2 border-dashed border-yellow-200 bg-yellow-50">
                        <div className="flex items-center gap-3">
                          <Clock size={24} className="text-yellow-600" />
                          <div>
                            <p className="font-semibold text-lg">
                              Under Review
                            </p>
                            <p className="text-sm text-gray-600">
                              This listing is currently being reviewed by
                              another admin
                            </p>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className={`${getStatusColor(approvalData.approvalStatus)} text-sm px-3 py-1`}
                        >
                          {approvalData.approvalStatus.replace("_", " ")}
                        </Badge>
                      </div>

                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500">
                          Please wait for the current review to complete before
                          making any changes.
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Summary & Timeline */}
        <div className="space-y-6">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(approvalData.approvalStatus)} text-xs`}
                >
                  {approvalData.approvalStatus.replace("_", " ")}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Documents</span>
                <span className="text-sm font-medium">
                  {mockDocuments.filter((doc) => doc.uploaded).length} /{" "}
                  {mockDocuments.length} uploaded
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Verified</span>
                <span className="text-sm font-medium">
                  {
                    mockDocuments.filter(
                      (doc) => getDocumentVerificationStatus(doc) === true
                    ).length
                  }{" "}
                  / {mockDocuments.filter((doc) => doc.uploaded).length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Listing Type</span>
                <span className="text-sm font-medium capitalize">
                  {approvalData.listingType}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Mail size={16} className="mr-2" />
                Contact Owner
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <Download size={16} className="mr-2" />
                Download All Documents
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                size="sm"
              >
                <FileText size={16} className="mr-2" />
                View Full Details
              </Button>
            </CardContent>
          </Card>

          {/* Timeline Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Listing Submitted</p>
                    <p className="text-xs text-gray-500">
                      {formatShortDate(approvalData.approvalStatusAt)}
                    </p>
                  </div>
                </div>

                {approvalData.approvalStatus === "under_review" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Under Review</p>
                      <p className="text-xs text-gray-500">In progress</p>
                    </div>
                  </div>
                )}

                {approvalData.approvalStatus === "approved" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Approved</p>
                      <p className="text-xs text-gray-500">
                        {formatShortDate(approvalData.approvalStatusAt)}
                      </p>
                      {approvalData.approvalReason && (
                        <p className="text-xs text-gray-600 mt-1">
                          {approvalData.approvalReason}
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {approvalData.approvalStatus === "rejected" && (
                  <div className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Rejected</p>
                      <p className="text-xs text-gray-500">
                        {formatShortDate(approvalData.approvalStatusAt)}
                      </p>
                      {approvalData.approvalReason && (
                        <p className="text-xs text-gray-600 mt-1">
                          {approvalData.approvalReason}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Decision Confirmation Dialog */}
      <Dialog
        open={isDecisionDialogOpen}
        onOpenChange={setIsDecisionDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"} Listing
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to{" "}
              {pendingDecision === "approved" ? "approve" : "reject"} this
              vehicle listing? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reason" className="text-sm font-medium">
                Reason (Optional)
              </Label>
              <Textarea
                id="reason"
                value={decisionReason}
                onChange={(e) => setDecisionReason(e.target.value)}
                placeholder={`Add a note about why you ${
                  pendingDecision === "approved" ? "approved" : "rejected"
                } this listing...`}
                className="mt-2"
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancel
            </Button>
            <Button
              onClick={confirmDecision}
              disabled={isProcessing}
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                <>
                  {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
                  Listing
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
