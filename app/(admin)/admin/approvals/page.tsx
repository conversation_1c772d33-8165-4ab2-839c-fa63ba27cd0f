export const dynamic = "force-dynamic";
export const revalidate = 0;

import React from "react";
import {
  getPendingListingApprovals,
  getAllListingApprovals,
} from "@/drizzle-actions/admin/listings";
import ApprovalsClient from "./approvals-client";
import { Clock, CheckCircle, XCircle } from "lucide-react";

export default async function ApprovalsPage() {
  // Fetch real data from database
  const pendingApprovals = await getPendingListingApprovals();
  const allApprovals = await getAllListingApprovals();

  // Calculate statistics from real data
  const approvalStats = [
    {
      name: "Pending Approvals",
      value: pendingApprovals.length,
      change: "+2", // TODO: Calculate actual change
      icon: <Clock size={18} />,
      color: "#FFD700",
    },
    {
      name: "Approved Today",
      value: allApprovals.filter(
        (a) =>
          a.approvalStatus === "approved" &&
          new Date(a.approvalStatusAt || new Date()).toDateString() ===
            new Date().toDateString()
      ).length,
      change: "+3", // TODO: Calculate actual change
      icon: <CheckCircle size={18} />,
      color: "#009639",
    },
    {
      name: "Rejected Today",
      value: allApprovals.filter(
        (a) =>
          a.approvalStatus === "rejected" &&
          new Date(a.approvalStatusAt || new Date()).toDateString() ===
            new Date().toDateString()
      ).length,
      change: "-1", // TODO: Calculate actual change
      icon: <XCircle size={18} />,
      color: "#dc3545",
    },
    {
      name: "Average Response Time",
      value: "4.2h", // TODO: Calculate from real data
      change: "-0.5h",
      icon: <Clock size={18} />,
      color: "#6c757d",
    },
  ];

  return (
    <ApprovalsClient
      pendingApprovals={pendingApprovals}
      allApprovals={allApprovals}
      approvalStats={approvalStats}
    />
  );
}
