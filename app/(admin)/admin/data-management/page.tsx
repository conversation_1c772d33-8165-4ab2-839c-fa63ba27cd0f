"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Database, 
  Car, 
  Settings, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Download,
  Globe,
  Users,
  MapPin,
  Building,
  Mail
} from "lucide-react";
import { 
  seedVehicleMakes, 
  seedVehicleModels, 
  seedVehicleVariants, 
  seedPartyTypes,
  seedContactPointTypes,
  seedSouthAfricanProvinces,
  seedSouthAfricanCities,
  seedSouthAfricanData 
} from "@/drizzle-actions/seed-data";
import { seedCountries } from "@/drizzle-actions/seed-countries";

interface SeedStatus {
  countries: "idle" | "loading" | "success" | "error";
  partyTypes: "idle" | "loading" | "success" | "error";
  contactPointTypes: "idle" | "loading" | "success" | "error";
  provinces: "idle" | "loading" | "success" | "error";
  cities: "idle" | "loading" | "success" | "error";
  makes: "idle" | "loading" | "success" | "error";
  models: "idle" | "loading" | "success" | "error";
  variants: "idle" | "loading" | "success" | "error";
}

interface SeedResults {
  countries?: { created: number; skipped: number };
  partyTypes?: { created: number; skipped: number };
  contactPointTypes?: { created: number; skipped: number };
  provinces?: { created: number; skipped: number };
  cities?: { created: number; skipped: number };
  makes?: { created: number; skipped: number };
  models?: { created: number; skipped: number };
  variants?: { created: number; skipped: number };
}

export default function DataManagementPage() {
  const [status, setStatus] = useState<SeedStatus>({
    countries: "idle",
    partyTypes: "idle",
    contactPointTypes: "idle",
    provinces: "idle",
    cities: "idle",
    makes: "idle",
    models: "idle",
    variants: "idle"
  });
  const [results, setResults] = useState<SeedResults>({});
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleSeedCountries = async () => {
    setStatus(prev => ({ ...prev, countries: "loading" }));
    setErrors(prev => ({ ...prev, countries: "" }));
    
    try {
      const result = await seedCountries();
      setResults(prev => ({ ...prev, countries: result }));
      setStatus(prev => ({ ...prev, countries: "success" }));
    } catch (error) {
      console.error("Error seeding countries:", error);
      setErrors(prev => ({ ...prev, countries: error instanceof Error ? error.message : "Failed to seed countries" }));
      setStatus(prev => ({ ...prev, countries: "error" }));
    }
  };

  const handleSeedPartyTypes = async () => {
    setStatus(prev => ({ ...prev, partyTypes: "loading" }));
    setErrors(prev => ({ ...prev, partyTypes: "" }));
    
    try {
      const result = await seedPartyTypes();
      setResults(prev => ({ ...prev, partyTypes: result }));
      setStatus(prev => ({ ...prev, partyTypes: "success" }));
    } catch (error) {
      console.error("Error seeding party types:", error);
      setErrors(prev => ({ ...prev, partyTypes: error instanceof Error ? error.message : "Failed to seed party types" }));
      setStatus(prev => ({ ...prev, partyTypes: "error" }));
    }
  };

  const handleSeedContactPointTypes = async () => {
    setStatus(prev => ({ ...prev, contactPointTypes: "loading" }));
    setErrors(prev => ({ ...prev, contactPointTypes: "" }));
    
    try {
      const result = await seedContactPointTypes();
      setResults(prev => ({ ...prev, contactPointTypes: result }));
      setStatus(prev => ({ ...prev, contactPointTypes: "success" }));
    } catch (error) {
      console.error("Error seeding contact point types:", error);
      setErrors(prev => ({ ...prev, contactPointTypes: error instanceof Error ? error.message : "Failed to seed contact point types" }));
      setStatus(prev => ({ ...prev, contactPointTypes: "error" }));
    }
  };

  const handleSeedProvinces = async () => {
    setStatus(prev => ({ ...prev, provinces: "loading" }));
    setErrors(prev => ({ ...prev, provinces: "" }));
    
    try {
      const result = await seedSouthAfricanProvinces();
      setResults(prev => ({ ...prev, provinces: result }));
      setStatus(prev => ({ ...prev, provinces: "success" }));
    } catch (error) {
      console.error("Error seeding provinces:", error);
      setErrors(prev => ({ ...prev, provinces: error instanceof Error ? error.message : "Failed to seed provinces" }));
      setStatus(prev => ({ ...prev, provinces: "error" }));
    }
  };

  const handleSeedCities = async () => {
    setStatus(prev => ({ ...prev, cities: "loading" }));
    setErrors(prev => ({ ...prev, cities: "" }));
    
    try {
      const result = await seedSouthAfricanCities();
      setResults(prev => ({ ...prev, cities: result }));
      setStatus(prev => ({ ...prev, cities: "success" }));
    } catch (error) {
      console.error("Error seeding cities:", error);
      setErrors(prev => ({ ...prev, cities: error instanceof Error ? error.message : "Failed to seed cities" }));
      setStatus(prev => ({ ...prev, cities: "error" }));
    }
  };

  const handleSeedMakes = async () => {
    setStatus(prev => ({ ...prev, makes: "loading" }));
    setErrors(prev => ({ ...prev, makes: "" }));
    
    try {
      const result = await seedVehicleMakes();
      setResults(prev => ({ ...prev, makes: result }));
      setStatus(prev => ({ ...prev, makes: "success" }));
    } catch (error) {
      console.error("Error seeding makes:", error);
      setErrors(prev => ({ ...prev, makes: error instanceof Error ? error.message : "Failed to seed vehicle makes" }));
      setStatus(prev => ({ ...prev, makes: "error" }));
    }
  };

  const handleSeedModels = async () => {
    setStatus(prev => ({ ...prev, models: "loading" }));
    setErrors(prev => ({ ...prev, models: "" }));
    
    try {
      const result = await seedVehicleModels();
      setResults(prev => ({ ...prev, models: result }));
      setStatus(prev => ({ ...prev, models: "success" }));
    } catch (error) {
      console.error("Error seeding models:", error);
      setErrors(prev => ({ ...prev, models: error instanceof Error ? error.message : "Failed to seed vehicle models" }));
      setStatus(prev => ({ ...prev, models: "error" }));
    }
  };

  const handleSeedVariants = async () => {
    setStatus(prev => ({ ...prev, variants: "loading" }));
    setErrors(prev => ({ ...prev, variants: "" }));
    
    try {
      const result = await seedVehicleVariants();
      setResults(prev => ({ ...prev, variants: result }));
      setStatus(prev => ({ ...prev, variants: "success" }));
    } catch (error) {
      console.error("Error seeding variants:", error);
      setErrors(prev => ({ ...prev, variants: error instanceof Error ? error.message : "Failed to seed vehicle variants" }));
      setStatus(prev => ({ ...prev, variants: "error" }));
    }
  };

  const handleSeedAll = async () => {
    await handleSeedCountries();
    await handleSeedPartyTypes();
    await handleSeedContactPointTypes();
    await handleSeedProvinces();
    await handleSeedCities();
    await handleSeedMakes();
    await handleSeedModels();
    await handleSeedVariants();
  };

  const getStatusIcon = (currentStatus: string) => {
    switch (currentStatus) {
      case "loading":
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Database className="h-4 w-4" />;
    }
  };

  const getStatusBadge = (currentStatus: string) => {
    switch (currentStatus) {
      case "loading":
        return <Badge variant="outline">Loading...</Badge>;
      case "success":
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case "error":
        return <Badge variant="destructive">Error</Badge>;
      default:
        return <Badge variant="secondary">Ready</Badge>;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Data Management</h1>
          <p className="text-muted-foreground">
            Seed database tables with essential data for the platform
          </p>
        </div>
        <Button 
          onClick={handleSeedAll}
          disabled={Object.values(status).some(s => s === "loading")}
          className="bg-[#009639] hover:bg-[#007A2F]"
        >
          <Download className="mr-2 h-4 w-4" />
          Seed All Data
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {/* Countries */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Countries
              </CardTitle>
              <CardDescription>
                Seed countries data starting with South Africa
              </CardDescription>
            </div>
            {getStatusIcon(status.countries)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.countries)}
              </div>
              
              {results.countries && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.countries.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.countries.skipped}</span>
                  </div>
                </div>
              )}

              {errors.countries && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.countries}
                </div>
              )}

              <Button 
                onClick={handleSeedCountries}
                disabled={status.countries === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.countries === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Countries
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Party Types */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Party Types
              </CardTitle>
              <CardDescription>
                Seed party types: Individual, Company, Group
              </CardDescription>
            </div>
            {getStatusIcon(status.partyTypes)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.partyTypes)}
              </div>
              
              {results.partyTypes && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.partyTypes.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.partyTypes.skipped}</span>
                  </div>
                </div>
              )}

              {errors.partyTypes && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.partyTypes}
                </div>
              )}

              <Button 
                onClick={handleSeedPartyTypes}
                disabled={status.partyTypes === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.partyTypes === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Party Types
              </Button>
            </div>
          </CardContent>
                  </Card>

        {/* Contact Point Types */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Contact Point Types
              </CardTitle>
              <CardDescription>
                Seed contact point types: Email, Phone
              </CardDescription>
            </div>
            {getStatusIcon(status.contactPointTypes)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.contactPointTypes)}
              </div>
              
              {results.contactPointTypes && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.contactPointTypes.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.contactPointTypes.skipped}</span>
                  </div>
                </div>
              )}

              {errors.contactPointTypes && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.contactPointTypes}
                </div>
              )}

              <Button 
                onClick={handleSeedContactPointTypes}
                disabled={status.contactPointTypes === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.contactPointTypes === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Contact Types
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* SA Provinces */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                SA Provinces
              </CardTitle>
              <CardDescription>
                Seed all 9 South African provinces
              </CardDescription>
            </div>
            {getStatusIcon(status.provinces)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.provinces)}
              </div>
              
              {results.provinces && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.provinces.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.provinces.skipped}</span>
                  </div>
                </div>
              )}

              {errors.provinces && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.provinces}
                </div>
              )}

              <Button 
                onClick={handleSeedProvinces}
                disabled={status.provinces === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.provinces === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Provinces
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* SA Cities */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Building className="h-4 w-4" />
                SA Cities
              </CardTitle>
              <CardDescription>
                Seed top cities for each SA province (45 cities)
              </CardDescription>
            </div>
            {getStatusIcon(status.cities)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.cities)}
              </div>
              
              {results.cities && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.cities.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.cities.skipped}</span>
                  </div>
                </div>
              )}

              {errors.cities && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.cities}
                </div>
              )}

              <Button 
                onClick={handleSeedCities}
                disabled={status.cities === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.cities === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Cities
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Makes */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Car className="h-4 w-4" />
                Vehicle Makes
              </CardTitle>
              <CardDescription>
                Seed popular vehicle manufacturers
              </CardDescription>
            </div>
            {getStatusIcon(status.makes)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.makes)}
              </div>
              
              {results.makes && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.makes.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.makes.skipped}</span>
                  </div>
                </div>
              )}

              {errors.makes && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.makes}
                </div>
              )}

              <Button 
                onClick={handleSeedMakes}
                disabled={status.makes === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.makes === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Makes
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Models */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Vehicle Models
              </CardTitle>
              <CardDescription>
                Seed popular vehicle models by make
              </CardDescription>
            </div>
            {getStatusIcon(status.models)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.models)}
              </div>
              
              {results.models && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.models.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.models.skipped}</span>
                  </div>
                </div>
              )}

              {errors.models && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.models}
                </div>
              )}

              <Button 
                onClick={handleSeedModels}
                disabled={status.models === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.models === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Models
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Variants */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="space-y-1">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Car className="h-4 w-4" />
                Vehicle Variants
              </CardTitle>
              <CardDescription>
                Seed vehicle transmission and fuel variants
              </CardDescription>
            </div>
            {getStatusIcon(status.variants)}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                {getStatusBadge(status.variants)}
              </div>
              
              {results.variants && (
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span>Created:</span>
                    <span className="font-medium">{results.variants.created}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Skipped:</span>
                    <span className="font-medium">{results.variants.skipped}</span>
                  </div>
                </div>
              )}

              {errors.variants && (
                <div className="p-3 text-sm text-red-700 bg-red-50 rounded-md">
                  {errors.variants}
                </div>
              )}

              <Button 
                onClick={handleSeedVariants}
                disabled={status.variants === "loading"}
                variant="outline"
                className="w-full"
              >
                {status.variants === "loading" ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Database className="mr-2 h-4 w-4" />
                )}
                Seed Variants
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Information Section */}
      <Card>
        <CardHeader>
          <CardTitle>Seeding Information</CardTitle>
          <CardDescription>
            Important details about the data seeding process
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Countries</h3>
              <p className="text-sm text-muted-foreground">
                Includes South Africa first, followed by SADC countries, rest of Africa, and major countries worldwide. Essential for vehicle registration and company operations.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Party Types</h3>
              <p className="text-sm text-muted-foreground">
                Essential entity types: Individual (users), Company (business entities), and Group (vehicle sharing groups). Required for user registration and entity management.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Contact Point Types</h3>
              <p className="text-sm text-muted-foreground">
                Communication channel types: Email and Phone. Includes validation patterns for each type. Essential for user contact information management.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">SA Provinces</h3>
              <p className="text-sm text-muted-foreground">
                All 9 South African provinces: Gauteng, Western Cape, KwaZulu-Natal, Eastern Cape, Limpopo, Mpumalanga, North West, Northern Cape, and Free State.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">SA Cities</h3>
              <p className="text-sm text-muted-foreground">
                Top 5 cities/municipal areas for each province (45 total). Includes major metropolitan areas like Johannesburg, Cape Town, Durban, and other key economic centers.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Vehicle Makes</h3>
              <p className="text-sm text-muted-foreground">
                Includes major automotive manufacturers like Toyota, BMW, Mercedes-Benz, Ford, Volkswagen, plus e-hailing popular brands like Suzuki, Maruti, Tata, Datsun, and Chinese manufacturers.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Vehicle Models</h3>
              <p className="text-sm text-muted-foreground">
                Popular models for each make spanning multiple years (2015-2024). Special focus on e-hailing vehicles like Suzuki Dzire, Honda Amaze, Hyundai Accent, Toyota Etios, and other compact sedans.
              </p>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-sm">Vehicle Variants</h3>
              <p className="text-sm text-muted-foreground">
                Model-specific variants with appropriate transmission and fuel combinations for each vehicle (e.g., "Toyota Corolla 2020 Manual Petrol"). Generates 2-4 variants per model based on vehicle category.
              </p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="font-semibold text-sm mb-2">Important Notes:</h3>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Seed Countries first, as they are referenced by provinces and other entities</li>
              <li>• Seed Party Types second, as they are required for user and entity management</li>
              <li>• Seed Contact Point Types third, as they are required for user contact information</li>
              <li>• Seed SA Provinces fourth, as they depend on Countries and are referenced by Cities</li>
              <li>• Seed SA Cities fifth, as they depend on both Countries and Provinces</li>
              <li>• Seed Vehicle Makes sixth, as Models depend on Makes</li>
              <li>• Seed Vehicle Models seventh, as Variants depend on Models</li>
              <li>• Vehicle Variants will generate ~2,500+ variants for all models</li>
              <li>• Existing records will be skipped to prevent duplicates</li>
              <li>• All seeded data will be marked as active by default</li>
              <li>• Variant generation may take several minutes due to volume</li>
              <li>• Geographic data (provinces/cities) enables location-based group management</li>
              <li>• Contact Point Types include validation patterns for email and phone formats</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 