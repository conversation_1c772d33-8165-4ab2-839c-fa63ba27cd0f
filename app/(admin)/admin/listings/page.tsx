"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  ChevronDown,
  MoreVertical,
  Car,
  Eye,
  DollarSign,
  Percent,
  Download,
  Plus,
  Clock,
  CheckCircle,
  TrendingUp,
  Calendar,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface VehicleListing {
  id: string;
  ownerName: string;
  ownerEmail: string;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  submittedDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  listingType: "rental" | "co_ownership" | "lease_to_own";
  askingPrice: number;
  fractionOffer?: number;
  condition: "new" | "used";
  mileage?: number;
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  images: string[];
  location: string;
  // New fields for multi-option rental
  listingDetails?: {
    rates?: {
      daily?: number;
      weekly?: number;
      monthly?: number;
    };
    availablePeriods?: Array<"daily" | "weekly" | "monthly">;
    purpose?: "business" | "individual";
    deposit?: {
      required: boolean;
      amount?: number;
    };
    driverRequirements?: {
      minimumAge?: number;
      minimumExperience?: number;
      preferredGender?: string;
    };
  };
}

// Simplified interface for mock data - more flexible for prototyping
interface MockListing {
  id: string | number;
  vehicle: string;
  ownerName: string;
  ownerEmail: string;
  price: number;
  fractionPrice: number;
  fractionSize: string;
  availableFractions: number;
  totalFractions: number;
  location: string;
  listingType: "rental" | "co_ownership" | "lease_to_own";
  status?:
    | "pending"
    | "under_review"
    | "approved"
    | "rejected"
    | "Active"
    | "Sold Out";
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  images: string[];
  listedDate?: string;
  views?: number;
  askingPrice?: number;
  fractionOffer?: number;
  // New fields for multi-option rental
  listingDetails?: {
    rates?: {
      daily?: number;
      weekly?: number;
      monthly?: number;
    };
    availablePeriods?: Array<"daily" | "weekly" | "monthly">;
    purpose?: "business" | "individual";
    deposit?: {
      required: boolean;
      amount?: number;
    };
    driverRequirements?: {
      minimumAge?: number;
      minimumExperience?: number;
      preferredGender?: string;
    };
  };
}

export default function ListingsPage() {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<
    "all" | "rental" | "co_ownership" | "lease_to_own"
  >("all");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [sortBy, setSortBy] = useState<
    "listedDate" | "price" | "vehicle" | "views"
  >("listedDate");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  // Mock data - replace with actual API call
  const listings: MockListing[] = [
    {
      id: "1",
      vehicle: "Toyota Fortuner",
      ownerName: "John Doe",
      ownerEmail: "<EMAIL>",
      price: 225000,
      fractionPrice: 45000,
      fractionSize: "20%",
      availableFractions: 3,
      totalFractions: 5,
      location: "Cape Town",
      listingType: "co_ownership" as const,
      status: "pending" as const,
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: false },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: false },
      ],
      images: ["/images/cars/bmw-x3.jpg"],
      askingPrice: 225000,
      fractionOffer: 0.2,
      listingDetails: {
        rates: {
          daily: 1500,
          weekly: 8000,
          monthly: 25000,
        },
        availablePeriods: ["daily", "weekly", "monthly"],
        purpose: "individual",
        deposit: {
          required: true,
          amount: 50000,
        },
        driverRequirements: {
          minimumAge: 21,
          minimumExperience: 1,
          preferredGender: "male",
        },
      },
    },
    {
      id: "2",
      vehicle: "VW Polo",
      ownerName: "Jane Smith",
      ownerEmail: "<EMAIL>",
      price: 180000,
      fractionPrice: 36000,
      fractionSize: "20%",
      availableFractions: 2,
      totalFractions: 5,
      location: "Johannesburg",
      listingType: "rental" as const,
      status: "under_review" as const,
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
      images: ["/images/cars/toyota-corolla.jpg"],
      askingPrice: 180000,
      fractionOffer: 0.2,
      listingDetails: {
        rates: {
          daily: 1000,
          weekly: 5000,
          monthly: 15000,
        },
        availablePeriods: ["daily", "weekly", "monthly"],
        purpose: "individual",
        deposit: {
          required: true,
          amount: 20000,
        },
        driverRequirements: {
          minimumAge: 20,
          minimumExperience: 0,
          preferredGender: "any",
        },
      },
    },
    {
      id: "3",
      vehicle: "Honda Civic",
      ownerName: "Peter Jones",
      ownerEmail: "<EMAIL>",
      price: 220000,
      fractionPrice: 55000,
      fractionSize: "25%",
      availableFractions: 0,
      totalFractions: 4,
      location: "Durban",
      listingType: "lease_to_own" as const,
      status: "approved" as const,
      listedDate: "2024-01-10",
      views: 150,
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: true },
      ],
      images: ["/images/cars/honda-civic.jpg"],
      askingPrice: 220000,
      fractionOffer: 0.25,
      listingDetails: {
        rates: {
          daily: 1200,
          weekly: 6000,
          monthly: 18000,
        },
        availablePeriods: ["daily", "weekly", "monthly"],
        purpose: "individual",
        deposit: {
          required: true,
          amount: 30000,
        },
        driverRequirements: {
          minimumAge: 20,
          minimumExperience: 0,
          preferredGender: "any",
        },
      },
    },
  ];

  // Helper functions - MOVED HERE, BEFORE THE RETURN STATEMENT
  const getTabCount = (tab: "active" | "history") => {
    if (tab === "active") {
      return listings.filter(
        (listing) =>
          listing.status === "pending" || listing.status === "under_review"
      ).length;
    }
    return listings.filter(
      (listing) =>
        listing.status === "approved" || listing.status === "rejected"
    ).length;
  };

  const getFilterCount = (status: typeof filterStatus) => {
    if (status === "all") return listings.length;
    return listings.filter((listing) => listing.status === status).length;
  };

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "co_ownership":
        return "Co-ownership";
      case "lease_to_own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            Vehicle Listing Management
          </h1>
          <p className="text-gray-600 mt-1">
            Review and approve vehicle listings from EARN users
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Data
          </Button>
          <Button className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Listing
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Listings</p>
                <p className="text-2xl font-bold mt-1">{listings.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Car className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    listings.filter(
                      (listing) =>
                        listing.status === "pending" ||
                        listing.status === "under_review"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    listings.filter((listing) => listing.status === "approved")
                      .length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approval Rate</p>
                <p className="text-2xl font-bold mt-1">
                  {Math.round(
                    (listings.filter((listing) => listing.status === "approved")
                      .length /
                      listings.filter(
                        (listing) =>
                          listing.status === "approved" ||
                          listing.status === "rejected"
                      ).length) *
                      100
                  )}
                  %
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs
              value={currentTab}
              onValueChange={(value) =>
                setCurrentTab(value as "active" | "history")
              }
            >
              <TabsList>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <Clock size={16} />
                  Active Listings ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="flex items-center gap-2"
                >
                  <Calendar size={16} />
                  Listing History ({getTabCount("history")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search listings..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent w-64"
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Type:{" "}
                    {filterType === "all"
                      ? "All"
                      : getListingTypeLabel(filterType)}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Types" },
                    { key: "rental", label: "Rental" },
                    { key: "co_ownership", label: "Co-ownership" },
                    { key: "lease_to_own", label: "Lease-to-Own" },
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterType(filter.key as typeof filterType)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {
                          listings.filter(
                            (listing) =>
                              filter.key === "all" ||
                              listing.listingType === filter.key
                          ).length
                        }
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Status:{" "}
                    {filterStatus === "all"
                      ? "All"
                      : filterStatus.replace("_", " ")}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Listings" },
                    ...(currentTab === "active"
                      ? [
                          { key: "pending", label: "Pending" },
                          { key: "under_review", label: "Under Review" },
                        ]
                      : [
                          { key: "approved", label: "Approved" },
                          { key: "rejected", label: "Rejected" },
                        ]),
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterStatus(filter.key as typeof filterStatus)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {getFilterCount(filter.key as typeof filterStatus)}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Vehicle</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Listing Type</TableHead>
                <TableHead>Rental Rates</TableHead>
                <TableHead>Details</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {listings.map((listing) => (
                <TableRow key={listing.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Car className="h-4 w-4 text-[#009639]" />
                      <Link
                        href={`/admin/vehicles/${listing.id}`}
                        className="text-[#009639] hover:text-[#007A2F]"
                      >
                        {listing.vehicle}
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">
                        {listing.ownerName || "Unknown Owner"}
                      </div>
                      <div className="text-sm text-gray-500">
                        {listing.ownerEmail || ""}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="capitalize">
                      {getListingTypeLabel(listing.listingType)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {listing.listingType === "rental" &&
                    listing.listingDetails?.rates ? (
                      <div className="space-y-1">
                        {listing.listingDetails.rates.daily && (
                          <div className="text-sm">
                            <span className="font-medium">Daily:</span> R{" "}
                            {listing.listingDetails.rates.daily.toLocaleString()}
                          </div>
                        )}
                        {listing.listingDetails.rates.weekly && (
                          <div className="text-sm">
                            <span className="font-medium">Weekly:</span> R{" "}
                            {listing.listingDetails.rates.weekly.toLocaleString()}
                          </div>
                        )}
                        {listing.listingDetails.rates.monthly && (
                          <div className="text-sm">
                            <span className="font-medium">Monthly:</span> R{" "}
                            {listing.listingDetails.rates.monthly.toLocaleString()}
                          </div>
                        )}
                      </div>
                    ) : listing.listingType === "co_ownership" ? (
                      <div className="space-y-1">
                        <div className="text-sm font-medium">
                          R {listing.askingPrice?.toLocaleString()}
                        </div>
                        {listing.fractionOffer && (
                          <div className="text-xs text-gray-500">
                            {(listing.fractionOffer * 100).toFixed(0)}% fraction
                          </div>
                        )}
                      </div>
                    ) : (
                      <span className="text-gray-500">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {listing.listingDetails?.purpose && (
                        <div className="text-sm">
                          <span className="capitalize">
                            {listing.listingDetails.purpose}
                          </span>{" "}
                          use
                        </div>
                      )}
                      {listing.listingDetails?.deposit?.required && (
                        <div className="text-xs text-blue-600">
                          Deposit: R{" "}
                          {listing.listingDetails.deposit.amount?.toLocaleString() ||
                            "TBD"}
                        </div>
                      )}
                      {listing.listingDetails?.availablePeriods && (
                        <div className="text-xs text-gray-500">
                          Periods:{" "}
                          {listing.listingDetails.availablePeriods.join(", ")}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{listing.location}</TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={
                        listing.status === "approved"
                          ? "bg-green-100 text-green-800"
                          : listing.status === "rejected"
                            ? "bg-red-100 text-red-800"
                            : listing.status === "under_review"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-yellow-100 text-yellow-800"
                      }
                    >
                      {listing.status?.replace("_", " ") || "pending"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/admin/listings/${listing.id}`}
                      className="text-[#009639] hover:text-[#007A2F] font-medium"
                    >
                      View Details
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
