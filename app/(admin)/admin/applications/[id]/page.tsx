import React from "react";
import { notFound } from "next/navigation";
import { getApplicationById } from "@/drizzle-actions/applications";
import ApplicationReviewPageClient from "./ApplicationReviewPageClient";
import type { ApplicationWithDetails } from "@/types/applications";

// Helper function to ensure the application data is properly typed
function ensureApplicationDataTyped(app: any): ApplicationWithDetails {
  return {
    ...app,
    listing: {
      ...app.listing,
      listingType: app.listing.listingType as
        | "rental"
        | "fractional"
        | "ehailing-platform",
    },
  };
}

export default async function ApplicationReviewPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  try {
    const applicationId = parseInt(id);
    if (isNaN(applicationId)) {
      notFound();
    }

    const result = await getApplicationById(applicationId);

    if (!result.success || !result.application) {
      notFound();
    }

    const typedApplication = ensureApplicationDataTyped(result.application);

    return <ApplicationReviewPageClient application={typedApplication} />;
  } catch (error) {
    console.error("Error fetching application data:", error);
    notFound();
  }
}
