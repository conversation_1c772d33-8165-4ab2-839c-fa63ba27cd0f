"use client";

import React, {
  startTransition,
  useState,
  useTransition,
  useEffect,
} from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Eye,
  Clock,
  Star,
  Car,
  Phone,
  Mail,
  AlertTriangle,
  Archive,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import type { ApplicationWithDetails } from "@/types/applications";
import {
  makeApplicationDecisionAction,
  updateDocumentStatusAction,
} from "@/actions/applications";
import { useActionState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  groupDocumentsByType,
  canVerifyDocument,
  type DocumentWithStatus,
} from "@/lib/document-grouping";

// Helper functions for document status (based on working LegalDocumentsSheet.tsx)
const getDocumentStatusIcon = (status?: string | null) => {
  switch (status) {
    case "approved":
    case "verified":
      return <CheckCircle size={16} className="text-green-500" />;
    case "uploaded":
    case "pending":
      return <Clock size={16} className="text-yellow-500" />;
    case "rejected":
      return <XCircle size={16} className="text-red-500" />;
    case "superseded":
      return <Archive size={16} className="text-gray-500" />;
    default:
      return <FileText size={16} className="text-gray-500" />;
  }
};

const getDocumentStatusText = (status?: string | null) => {
  switch (status) {
    case "approved":
      return "Approved";
    case "verified":
      return "Verified";
    case "uploaded":
      return "Uploaded";
    case "pending":
      return "Pending Review";
    case "rejected":
      return "Rejected";
    case "superseded":
      return "Superseded";
    default:
      return "Pending Review";
  }
};

const getDocumentStatusColor = (status?: string | null) => {
  switch (status) {
    case "approved":
    case "verified":
      return "text-green-700 bg-green-100 border-green-200";
    case "uploaded":
    case "pending":
      return "text-yellow-700 bg-yellow-100 border-yellow-200";
    case "rejected":
      return "text-red-700 bg-red-100 border-red-200";
    case "superseded":
      return "text-gray-700 bg-gray-100 border-gray-200";
    default:
      return "text-gray-700 bg-gray-100 border-gray-200";
  }
};

// Helper function to get application status color (different from document status)
const getStatusColor = (status: string) => {
  switch (status) {
    case "approved":
      return "text-green-700 bg-green-100 border-green-200";
    case "rejected":
      return "text-red-700 bg-red-100 border-red-200";
    case "under_review":
      return "text-blue-700 bg-blue-100 border-blue-200";
    case "pending":
    default:
      return "text-yellow-700 bg-yellow-100 border-yellow-200";
  }
};

export default function ApplicationReviewPageClient({
  application,
}: {
  application: ApplicationWithDetails;
}) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);
  const [verifyingDocuments, setVerifyingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [rejectingDocuments, setRejectingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [resettingDocuments, setResettingDocuments] = useState<Set<string>>(
    new Set()
  );
  const router = useRouter();
  // Helper function to format dates consistently
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Helper function to format dates with time
  const formatDateTime = (dateString: string | Date) => {
    const date = new Date(dateString);
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatShortDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Form states for server actions
  const [decisionState, decisionAction] = useActionState(
    makeApplicationDecisionAction,
    null
  );
  const [documentState, documentAction] = useActionState(
    updateDocumentStatusAction,
    null
  );

  // Log decision state changes
  React.useEffect(() => {
    if (decisionState) {
      console.log("🚀 [CLIENT] decisionState changed:", decisionState);
      if (decisionState.success) {
        console.log("✅ [CLIENT] Decision action succeeded");
      } else if (decisionState.error) {
        console.log("❌ [CLIENT] Decision action failed:", decisionState.error);
      }
    }
  }, [decisionState]);

  // Log document state changes
  React.useEffect(() => {
    if (documentState) {
      console.log("🚀 [CLIENT] documentState changed:", documentState);
      if (documentState.success) {
        console.log("✅ [CLIENT] Document status updated successfully");
        // Show specific success message based on the action
        const updatedStatus = documentState.status || "updated";
        let message = "Document status updated successfully";
        if (updatedStatus === "verified") {
          message = "Document verified successfully";
        } else if (updatedStatus === "rejected") {
          message = "Document rejected successfully";
        } else if (updatedStatus === "pending") {
          message = "Document reset to pending review";
        }
        toast.success(message);
        // Refresh to show updated data
        router.refresh();
        // Clear all loading states
        setVerifyingDocuments(new Set());
        setRejectingDocuments(new Set());
        setResettingDocuments(new Set());
      } else if (documentState.error) {
        console.log(
          "❌ [CLIENT] Document status update failed:",
          documentState.error
        );
        toast.error(documentState.error);
        // Clear all loading states
        setVerifyingDocuments(new Set());
        setRejectingDocuments(new Set());
        setResettingDocuments(new Set());
      }
    }
  }, [documentState, router]);

  // Helper functions to extract data from the application
  const getApplicantName = () => {
    return `${application.applicant.firstName} ${application.applicant.lastName}`;
  };

  const getVehicleName = () => {
    if (application.vehicle) {
      return `${application.vehicle.make} ${application.vehicle.model} ${application.vehicle.year}`;
    }
    if (application.catalog) {
      return `${application.catalog.make} ${application.catalog.model} ${application.catalog.year}`;
    }
    return "Unknown Vehicle";
  };

  const getCurrentStatus = () => {
    return application.latestDecision?.decision || "pending";
  };

  const getWeeklyRate = () => {
    return application.catalog?.weeklyFeeTarget || 0;
  };

  const getApplicationType = () => {
    // Derive application type from listing type and application details
    const listingType = application.listing.listingType;
    const details = application.applicationDetails;

    if (listingType === "ehailing-platform") {
      return "E-hailing Application";
    } else if (listingType === "rental") {
      return "Rental Application";
    } else if (listingType === "fractional") {
      return "Fractional Ownership Application";
    }
    return "Application";
  };

  const hasDocuments = () => {
    return application.documents && application.documents.length > 0;
  };

  const getUploadedDocumentsCount = () => {
    return application.documents.filter((d) => d.documentUrl).length;
  };

  const getVerifiedDocumentsCount = () => {
    return application.documents.filter((d) => d.status === "verified").length;
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    console.log("🚀 [CLIENT] handleDecision called with:", decision);
    console.log("🚀 [CLIENT] Application ID:", application.id);
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = () => {
    console.log("🚀 [CLIENT] confirmDecision called");
    console.log("🚀 [CLIENT] pendingDecision:", pendingDecision);
    console.log("🚀 [CLIENT] decisionReason:", decisionReason);

    if (!pendingDecision) {
      console.log("❌ [CLIENT] No pending decision, returning early");
      return;
    }

    const formData = new FormData();
    formData.append("applicationId", application.id.toString());
    formData.append("decision", pendingDecision);
    if (decisionReason) {
      formData.append("reason", decisionReason);
    }

    console.log("🚀 [CLIENT] FormData created:");
    console.log("🚀 [CLIENT] - applicationId:", application.id.toString());
    console.log("🚀 [CLIENT] - decision:", pendingDecision);
    console.log(
      "🚀 [CLIENT] - reason:",
      decisionReason || "No reason provided"
    );

    // Use startTransition to call the action
    startTransition(() => {
      console.log("🚀 [CLIENT] Starting transition to call decisionAction");
      decisionAction(formData);
    });

    // Handle success immediately since this is a form submission
    setIsDecisionDialogOpen(false);
    setPendingDecision(null);
    setDecisionReason("");

    toast.success(`Application ${pendingDecision} successfully`);

    // Redirect back to applications list
    // window.location.href = "/admin/applications";
    router.refresh();
  };

  const handleDocumentVerification = async (
    documentId: number,
    status: "verified" | "rejected" | "pending"
  ) => {
    const formData = new FormData();
    formData.append("documentId", documentId.toString());
    formData.append("status", status);

    const docKey = `${documentId}-${status}`;

    try {
      // Add to appropriate loading state
      if (status === "verified") {
        setVerifyingDocuments((prev) => new Set(prev).add(docKey));
      } else if (status === "rejected") {
        setRejectingDocuments((prev) => new Set(prev).add(docKey));
      } else if (status === "pending") {
        setResettingDocuments((prev) => new Set(prev).add(docKey));
      }

      startTransition(() => {
        documentAction(formData);
      });
      // Toast will be shown in useEffect when documentState changes
    } catch (error) {
      console.error("Document verification error:", error);
      toast.error("Failed to update document status");
      // Remove from loading state on error
      if (status === "verified") {
        setVerifyingDocuments((prev) => {
          const newSet = new Set(prev);
          newSet.delete(docKey);
          return newSet;
        });
      } else if (status === "rejected") {
        setRejectingDocuments((prev) => {
          const newSet = new Set(prev);
          newSet.delete(docKey);
          return newSet;
        });
      } else if (status === "pending") {
        setResettingDocuments((prev) => {
          const newSet = new Set(prev);
          newSet.delete(docKey);
          return newSet;
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/applications">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              {getApplicationType()}
            </h1>
            <p className="text-gray-600 mt-1">
              {getApplicantName()} • {getVehicleName()}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(getCurrentStatus())} px-3 py-1`}
          >
            <span className="capitalize">
              {getCurrentStatus().replace("_", " ")}
            </span>
          </Badge>
          {getCurrentStatus() === "pending" ||
          getCurrentStatus() === "under_review" ? (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                className="text-red-600 border-red-200 hover:bg-red-50"
                onClick={() => handleDecision("rejected")}
              >
                <XCircle size={16} className="mr-2" />
                Reject
              </Button>
              <Button
                className="bg-[#009639] hover:bg-[#007A2F]"
                onClick={() => handleDecision("approved")}
              >
                <CheckCircle size={16} className="mr-2" />
                Approve
              </Button>
            </div>
          ) : null}
        </div>
      </div>

      {/* Application Status Alert */}
      {getCurrentStatus() === "pending" && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center">
              <AlertTriangle className="text-yellow-600 mr-3" size={20} />
              <div>
                <p className="font-medium text-yellow-800">
                  Application Pending Review
                </p>
                <p className="text-sm text-yellow-700">
                  This application is waiting for your review and decision.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="decision">Decision</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Applicant Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    Applicant Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {getApplicantName()}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Application Date
                      </Label>
                      <p className="text-lg">
                        {formatDate(application.createdAt)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <div className="flex items-center">
                        <Mail size={16} className="text-gray-400 mr-2" />
                        <p>{application.applicant.email}</p>
                      </div>
                    </div>
                    {application.applicant.phone && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Phone Number
                        </Label>
                        <div className="flex items-center">
                          <Phone size={16} className="text-gray-400 mr-2" />
                          <p>{application.applicant.phone}</p>
                        </div>
                      </div>
                    )}

                    {/* E-hailing specific fields */}
                    {application.listing.listingType === "ehailing-platform" &&
                      application.applicationDetails.hasEhailingExperience !==
                        undefined && (
                        <>
                          <div>
                            <Label className="text-sm font-medium text-gray-500">
                              E-hailing Experience
                            </Label>
                            <p className="text-lg">
                              {application.applicationDetails
                                .hasEhailingExperience
                                ? "Yes"
                                : "No"}
                            </p>
                          </div>
                          {application.applicationDetails.ehailingCompany && (
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                E-hailing Company
                              </Label>
                              <p className="text-lg">
                                {application.applicationDetails.ehailingCompany}
                              </p>
                            </div>
                          )}
                          {application.applicationDetails
                            .drivingExperienceYears && (
                            <div>
                              <Label className="text-sm font-medium text-gray-500">
                                Driving Experience
                              </Label>
                              <p className="text-lg">
                                {
                                  application.applicationDetails
                                    .drivingExperienceYears
                                }{" "}
                                years
                              </p>
                            </div>
                          )}
                        </>
                      )}

                    {/* Rental/Fractional specific fields */}
                    {(application.listing.listingType === "rental" ||
                      application.listing.listingType === "fractional") &&
                      application.applicationDetails.purpose && (
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Purpose
                          </Label>
                          <p className="text-lg">
                            {application.applicationDetails.purpose}
                          </p>
                        </div>
                      )}
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car size={20} className="text-[#009639]" />
                    Vehicle Details
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Vehicle
                      </Label>
                      <p className="text-lg font-medium">{getVehicleName()}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Listing Type
                      </Label>
                      <p className="text-lg capitalize">
                        {application.listing.listingType}
                      </p>
                    </div>
                    {application.vehicle?.registration && (
                      <div>
                        <Label className="text-sm font-medium text-gray-500">
                          Registration
                        </Label>
                        <p className="text-lg">
                          {application.vehicle.registration}
                        </p>
                      </div>
                    )}
                    {application.catalog && (
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-500">
                            Weekly Rate
                          </Label>
                          <p className="text-lg">
                            R{getWeeklyRate().toLocaleString()}
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                  {application.applicationDetails &&
                    Object.keys(application.applicationDetails).length > 0 && (
                      <div className="mt-4">
                        <Label className="text-sm font-medium text-gray-500">
                          Additional Details
                        </Label>
                        <div className="text-sm text-gray-700 mt-1 p-3 bg-gray-50 rounded-md">
                          {Object.entries(application.applicationDetails).map(
                            ([key, value]) => (
                              <div
                                key={key}
                                className="flex justify-between py-1"
                              >
                                <span className="font-medium">
                                  {key
                                    .replace(/([A-Z])/g, " $1")
                                    .replace(/^./, (str) => str.toUpperCase())}
                                  :
                                </span>
                                <span>
                                  {typeof value === "boolean"
                                    ? value
                                      ? "Yes"
                                      : "No"
                                    : String(value)}
                                </span>
                              </div>
                            )
                          )}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="documents" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Document Verification
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {hasDocuments() ? (
                    <div className="space-y-4">
                      {/* Group documents by type and show properly */}
                      {groupDocumentsByType(
                        application.documents.map((doc) => ({
                          ...doc,
                          isLatest: true, // We'll determine this properly in grouping
                          version: 1, // We'll determine this properly in grouping
                          status: doc.status as DocumentWithStatus["status"],
                        }))
                      ).map((group) => (
                        <div key={group.documentType} className="space-y-3">
                          <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                            {group.documentType}
                            {group.hasMultipleVersions && (
                              <span className="ml-2 text-sm text-gray-500">
                                ({group.documents.length} versions)
                              </span>
                            )}
                          </h3>
                          {group.documents.map((doc, index) => {
                            // Ensure status has a fallback value
                            const effectiveStatus = doc.status || "pending";

                            return (
                              <div
                                key={doc.id}
                                className={`border rounded-lg p-6 hover:shadow-md transition-shadow ${
                                  doc.isLatest
                                    ? "border-gray-200 bg-white"
                                    : "border-gray-100 bg-gray-50"
                                }`}
                              >
                                <div className="flex items-center justify-between mb-4">
                                  <div className="flex items-center gap-3">
                                    <h4 className="text-lg font-semibold text-gray-900">
                                      Version {doc.version}
                                    </h4>
                                    {!doc.isLatest && (
                                      <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                        Older Version
                                      </span>
                                    )}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {getDocumentStatusIcon(effectiveStatus)}
                                    <Badge
                                      variant="outline"
                                      className={getDocumentStatusColor(
                                        effectiveStatus
                                      )}
                                    >
                                      {getDocumentStatusText(effectiveStatus)}
                                    </Badge>
                                  </div>
                                </div>

                                {/* Document Timestamps */}
                                <div className="mb-4 text-sm text-gray-600 space-y-1">
                                  <p>
                                    <span className="font-medium">
                                      Uploaded:
                                    </span>{" "}
                                    {formatDateTime(doc.uploadedAt)}
                                  </p>
                                  {doc.status && doc.statusAt && (
                                    <p>
                                      <span className="font-medium">
                                        Status updated:
                                      </span>{" "}
                                      {formatDateTime(doc.statusAt)}
                                    </p>
                                  )}
                                </div>

                                {doc.documentUrl && (
                                  <div className="space-y-3">
                                    {/* Document Actions Row */}
                                    <div className="flex flex-wrap gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-2"
                                        onClick={async () => {
                                          try {
                                            console.log("🔍 [DEBUG] Document URL from DB:", doc.documentUrl);
                                            const { generateDocumentUrl } =
                                              await import("@/lib/utils");
                                            const signedUrl =
                                              await generateDocumentUrl(
                                                doc.documentUrl
                                              );
                                            console.log("🔍 [DEBUG] Generated signed URL:", signedUrl);
                                            window.open(signedUrl, "_blank");
                                          } catch (error) {
                                            console.error(
                                              "Failed to generate document URL:",
                                              error
                                            );
                                            alert(
                                              "Could not open document. Please try again."
                                            );
                                          }
                                        }}
                                      >
                                        <Eye size={16} />
                                        View Document
                                      </Button>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="flex items-center gap-2"
                                        onClick={async () => {
                                          try {
                                            console.log("🔍 [DEBUG] Download - Document URL from DB:", doc.documentUrl);
                                            const { generateDocumentUrl } =
                                              await import("@/lib/utils");
                                            const signedUrl =
                                              await generateDocumentUrl(
                                                doc.documentUrl
                                              );
                                            console.log("🔍 [DEBUG] Download - Generated signed URL:", signedUrl);
                                            const link =
                                              document.createElement("a");
                                            link.href = signedUrl;
                                            link.download = `${doc.documentType}.pdf`;
                                            document.body.appendChild(link);
                                            link.click();
                                            document.body.removeChild(link);
                                          } catch (error) {
                                            console.error(
                                              "Failed to download document:",
                                              error
                                            );
                                            alert(
                                              "Could not download document. Please try again."
                                            );
                                          }
                                        }}
                                      >
                                        <Download size={16} />
                                        Download
                                      </Button>
                                    </div>

                                    {/* Verification Actions Row - Only for latest documents */}
                                    {doc.isLatest && (
                                      <div className="border-t border-gray-100 pt-3">
                                        <div className="flex items-center justify-between">
                                          <span className="text-sm font-medium text-gray-700">
                                            Document Verification
                                          </span>
                                          <div className="flex gap-2">
                                            {/* Only show verify/reject buttons if document is pending/uploaded */}
                                            {canVerifyDocument(doc) &&
                                              effectiveStatus !== "verified" &&
                                              effectiveStatus !==
                                                "rejected" && (
                                                <>
                                                  <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="flex items-center gap-1 text-green-600 border-green-200 hover:bg-green-50"
                                                    disabled={verifyingDocuments.has(
                                                      `${doc.id}-verified`
                                                    )}
                                                    onClick={() =>
                                                      handleDocumentVerification(
                                                        doc.id,
                                                        "verified"
                                                      )
                                                    }
                                                  >
                                                    {verifyingDocuments.has(
                                                      `${doc.id}-verified`
                                                    ) ? (
                                                      <Clock
                                                        size={14}
                                                        className="animate-spin"
                                                      />
                                                    ) : (
                                                      <CheckCircle size={14} />
                                                    )}
                                                    {verifyingDocuments.has(
                                                      `${doc.id}-verified`
                                                    )
                                                      ? "Verifying..."
                                                      : "Verify"}
                                                  </Button>
                                                  <Button
                                                    variant="outline"
                                                    size="sm"
                                                    className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
                                                    disabled={rejectingDocuments.has(
                                                      `${doc.id}-rejected`
                                                    )}
                                                    onClick={() =>
                                                      handleDocumentVerification(
                                                        doc.id,
                                                        "rejected"
                                                      )
                                                    }
                                                  >
                                                    {rejectingDocuments.has(
                                                      `${doc.id}-rejected`
                                                    ) ? (
                                                      <Clock
                                                        size={14}
                                                        className="animate-spin"
                                                      />
                                                    ) : (
                                                      <XCircle size={14} />
                                                    )}
                                                    {rejectingDocuments.has(
                                                      `${doc.id}-rejected`
                                                    )
                                                      ? "Rejecting..."
                                                      : "Reject"}
                                                  </Button>
                                                </>
                                              )}

                                            {/* Reset verification if already verified or rejected */}
                                            {(effectiveStatus === "verified" ||
                                              effectiveStatus ===
                                                "rejected") && (
                                              <Button
                                                variant="outline"
                                                size="sm"
                                                className="flex items-center gap-1 text-yellow-600 border-yellow-200 hover:bg-yellow-50"
                                                disabled={resettingDocuments.has(
                                                  `${doc.id}-pending`
                                                )}
                                                onClick={() =>
                                                  handleDocumentVerification(
                                                    doc.id,
                                                    "pending"
                                                  )
                                                }
                                              >
                                                {resettingDocuments.has(
                                                  `${doc.id}-pending`
                                                ) ? (
                                                  <Clock
                                                    size={14}
                                                    className="animate-spin"
                                                  />
                                                ) : (
                                                  <Clock size={14} />
                                                )}
                                                {resettingDocuments.has(
                                                  `${doc.id}-pending`
                                                )
                                                  ? "Resetting..."
                                                  : "Reset to Pending"}
                                              </Button>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FileText
                        size={48}
                        className="mx-auto mb-4 text-gray-300"
                      />
                      <p>No documents uploaded yet</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="decision" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle size={20} className="text-[#009639]" />
                    Application Decision
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label
                      htmlFor="decision-reason"
                      className="text-sm font-medium"
                    >
                      Decision Reason (Optional)
                    </Label>
                    <Textarea
                      id="decision-reason"
                      value={decisionReason}
                      onChange={(e) => setDecisionReason(e.target.value)}
                      placeholder="Add a note about your decision..."
                      className="mt-2"
                      rows={4}
                    />
                  </div>

                  <div className="flex space-x-4">
                    <Button
                      variant="outline"
                      className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                      onClick={() => handleDecision("rejected")}
                    >
                      <XCircle size={16} className="mr-2" />
                      Reject Application
                    </Button>
                    <Button
                      className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
                      onClick={() => handleDecision("approved")}
                    >
                      <CheckCircle size={16} className="mr-2" />
                      Approve Application
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Right Column - Quick Info & Actions */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Application Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Status</span>
                <Badge
                  variant="outline"
                  className={`${getStatusColor(getCurrentStatus())}`}
                >
                  {getCurrentStatus().replace("_", " ")}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Documents</span>
                <span className="text-sm font-medium">
                  {getUploadedDocumentsCount()}/{application.documents.length}{" "}
                  uploaded
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Verified</span>
                <span className="text-sm font-medium">
                  {getVerifiedDocumentsCount()}/{getUploadedDocumentsCount()}{" "}
                  verified
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">Application Type</span>
                <span className="text-sm font-medium">
                  {getApplicationType()}
                </span>
              </div>

              {application.listing.listingType === "ehailing-platform" &&
                application.applicationDetails.hasEhailingExperience !==
                  undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500">
                      E-hailing Experience
                    </span>
                    <span className="text-sm font-medium">
                      {application.applicationDetails.hasEhailingExperience
                        ? "Yes"
                        : "No"}
                    </span>
                  </div>
                )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Mail size={16} className="mr-2" />
                Contact Applicant
              </Button>
              {hasDocuments() && (
                <Button variant="outline" className="w-full justify-start">
                  <Download size={16} className="mr-2" />
                  Download All Documents
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Application Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-[#009639] rounded-full mt-2 mr-3"></div>
                  <div>
                    <p className="text-sm font-medium">Application Submitted</p>
                    <p className="text-xs text-gray-500">
                      {formatShortDate(application.createdAt)}
                    </p>
                  </div>
                </div>
                {hasDocuments() && (
                  <div className="flex items-start">
                    <div
                      className={`w-2 h-2 rounded-full mt-2 mr-3 ${getUploadedDocumentsCount() > 0 ? "bg-[#009639]" : "bg-gray-300"}`}
                    ></div>
                    <div>
                      <p
                        className={`text-sm font-medium ${getUploadedDocumentsCount() > 0 ? "text-gray-900" : "text-gray-500"}`}
                      >
                        Documents Uploaded
                      </p>
                      <p className="text-xs text-gray-500">
                        {getUploadedDocumentsCount()} of{" "}
                        {application.documents.length} completed
                      </p>
                    </div>
                  </div>
                )}
                <div className="flex items-start">
                  <div
                    className={`w-2 h-2 rounded-full mt-2 mr-3 ${getCurrentStatus() !== "pending" ? "bg-[#009639]" : "bg-gray-300"}`}
                  ></div>
                  <div>
                    <p
                      className={`text-sm font-medium ${getCurrentStatus() !== "pending" ? "text-gray-900" : "text-gray-500"}`}
                    >
                      Under Review
                    </p>
                    <p className="text-xs text-gray-500">
                      {getCurrentStatus() === "pending"
                        ? "Pending admin decision"
                        : `Status: ${getCurrentStatus()}`}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Decision Confirmation Dialog */}
      <Dialog
        open={isDecisionDialogOpen}
        onOpenChange={setIsDecisionDialogOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {pendingDecision} this application from{" "}
              {getApplicantName()}?
              {pendingDecision === "approved" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant and update their application
                  status.
                </span>
              )}
              {pendingDecision === "rejected" && (
                <span className="block mt-2 text-sm">
                  This will notify the applicant that their application was not
                  successful.
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          {decisionReason && (
            <div className="py-4">
              <Label className="text-sm font-medium">Decision Reason:</Label>
              <p className="text-sm text-gray-600 mt-1 p-3 bg-gray-50 rounded-md">
                {decisionReason}
              </p>
            </div>
          )}

          <DialogFooter className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setIsDecisionDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className={
                pendingDecision === "approved"
                  ? "bg-[#009639] hover:bg-[#007A2F]"
                  : "bg-red-600 hover:bg-red-700"
              }
              onClick={confirmDecision}
            >
              {pendingDecision === "approved" ? "Approve" : "Reject"}{" "}
              Application
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
