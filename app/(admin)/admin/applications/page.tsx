"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  User,
  Car,
  FileText,
  MoreVertical,
  ChevronDown,
  Users,
  TrendingUp,
  AlertCircle,
  Mail,
  RefreshCw,
  Loader2,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { toast } from "sonner";

import {
  getAllApplicationsForAdminAction,
  getApplicationStatisticsForAdminAction,
  refreshAdminApplicationsAction,
} from "@/actions/admin/applications";
import type { AdminApplicationWithDetails } from "@/drizzle-actions/admin/applications";

export default function ApplicationsPage() {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [filterType, setFilterType] = useState<
    "all" | "ehailing-platform" | "rental" | "fractional"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Real data state
  const [applications, setApplications] = useState<
    AdminApplicationWithDetails[]
  >([]);
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    underReview: 0,
    approved: 0,
    rejected: 0,
    byType: {} as Record<string, number>,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Data fetching functions
  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const filters = {
        status: filterStatus === "all" ? undefined : filterStatus,
        applicationType: filterType === "all" ? undefined : filterType,
        searchQuery: searchQuery || undefined,
      };

      const result = await getAllApplicationsForAdminAction(filters);

      if (result.success && result.applications) {
        setApplications(result.applications);
      } else {
        setError(result.error || "Failed to fetch applications");
        setApplications([]);
      }
    } catch (err) {
      console.error("Error fetching applications:", err);
      setError("Failed to fetch applications");
      setApplications([]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const result = await getApplicationStatisticsForAdminAction();

      if (result.success && result.statistics) {
        setStatistics(result.statistics);
      }
    } catch (err) {
      console.error("Error fetching statistics:", err);
    }
  };

  // Fetch data on component mount and when filters change
  useEffect(() => {
    fetchApplications();
  }, [filterStatus, filterType, searchQuery]);

  useEffect(() => {
    fetchStatistics();
  }, []);

  // Refresh data handler
  const handleRefresh = async () => {
    await Promise.all([fetchApplications(), fetchStatistics()]);
    toast.success("Applications data refreshed");
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock size={14} />;
      case "under_review":
        return <Eye size={14} />;
      case "approved":
        return <CheckCircle size={14} />;
      case "rejected":
        return <XCircle size={14} />;
      default:
        return <Clock size={14} />;
    }
  };

  // Helper function to get application status
  const getApplicationStatus = (app: AdminApplicationWithDetails): string => {
    return app.latestDecision?.decision || "pending";
  };

  // Helper function to get vehicle name
  const getVehicleName = (app: AdminApplicationWithDetails): string => {
    if (app.vehicle) {
      return `${app.vehicle.make} ${app.vehicle.model} ${app.vehicle.year}`;
    }
    if (app.catalog) {
      return `${app.catalog.make} ${app.catalog.model} ${app.catalog.year}`;
    }
    return "Unknown Vehicle";
  };

  // Filter applications based on current tab
  const activeApplications = applications.filter((app) => {
    const status = getApplicationStatus(app);
    return status === "pending" || status === "under_review";
  });

  const historicalApplications = applications.filter((app) => {
    const status = getApplicationStatus(app);
    return status === "approved" || status === "rejected";
  });

  const currentApplications =
    currentTab === "active" ? activeApplications : historicalApplications;

  // Note: Additional filtering is now handled server-side in fetchApplications
  const filteredApplications = currentApplications;

  const getFilterCount = (filter: typeof filterStatus) => {
    if (filter === "all") return currentApplications.length;
    return currentApplications.filter((app) => {
      const status = getApplicationStatus(app);
      return status === filter;
    }).length;
  };

  const getTabCount = (tab: "active" | "history") => {
    return tab === "active"
      ? activeApplications.length
      : historicalApplications.length;
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Application Management
        </h1>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <RefreshCw size={16} />
            )}
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Data
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter size={16} />
            Advanced Filters
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Applications</p>
                <p className="text-2xl font-bold mt-1">{statistics.total}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <FileText className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold mt-1">
                  {statistics.pending + statistics.underReview}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold mt-1">{statistics.approved}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approval Rate</p>
                <p className="text-2xl font-bold mt-1">
                  {statistics.approved + statistics.rejected > 0
                    ? Math.round(
                        (statistics.approved /
                          (statistics.approved + statistics.rejected)) *
                          100
                      )
                    : 0}
                  %
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs
              value={currentTab}
              onValueChange={(value) =>
                setCurrentTab(value as "active" | "history")
              }
            >
              <TabsList>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <Clock size={16} />
                  Active Applications ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="flex items-center gap-2"
                >
                  <Calendar size={16} />
                  Application History ({getTabCount("history")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search applications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent w-64"
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Type:{" "}
                    {filterType === "all"
                      ? "All"
                      : filterType === "rental"
                        ? "Rental"
                        : filterType === "fractional"
                          ? "Fractional"
                          : "E-hailing Platform"}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Types" },
                    { key: "ehailing-platform", label: "E-hailing Platform" },
                    { key: "rental", label: "Rental Application" },
                    { key: "fractional", label: "Fractional Ownership" },
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterType(filter.key as typeof filterType)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {
                          applications.filter(
                            (app) =>
                              filter.key === "all" ||
                              app.listing.listingType === filter.key
                          ).length
                        }
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Status:{" "}
                    {filterStatus === "all"
                      ? "All"
                      : filterStatus.replace("_", " ")}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Applications" },
                    ...(currentTab === "active"
                      ? [
                          { key: "pending", label: "Pending" },
                          { key: "under_review", label: "Under Review" },
                        ]
                      : [
                          { key: "approved", label: "Approved" },
                          { key: "rejected", label: "Rejected" },
                        ]),
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterStatus(filter.key as typeof filterStatus)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {getFilterCount(filter.key as typeof filterStatus)}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <Users size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No Applications Found
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? "Try adjusting your search criteria"
                  : "No applications match the selected filter"}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant</TableHead>
                    <TableHead>Vehicle/Type</TableHead>
                    <TableHead>Applied Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Experience</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Rate/Details</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-12">
                        <Loader2
                          size={32}
                          className="animate-spin mx-auto mb-4 text-gray-400"
                        />
                        <p className="text-gray-500">Loading applications...</p>
                      </TableCell>
                    </TableRow>
                  ) : error ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-12">
                        <AlertCircle
                          size={32}
                          className="mx-auto mb-4 text-red-400"
                        />
                        <p className="text-red-600 mb-2">
                          Error loading applications
                        </p>
                        <p className="text-gray-500 text-sm">{error}</p>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleRefresh}
                          className="mt-4"
                        >
                          Try Again
                        </Button>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredApplications.map((application) => {
                      const status = getApplicationStatus(application);
                      const vehicleName = getVehicleName(application);
                      const applicantName = `${application.applicant.firstName} ${application.applicant.lastName}`;

                      return (
                        <TableRow
                          key={application.id}
                          className="hover:bg-gray-50"
                        >
                          <TableCell>
                            <div>
                              <div className="font-medium text-gray-900">
                                {applicantName}
                              </div>
                              <div className="text-sm text-gray-500">
                                {application.applicant.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Car size={16} className="text-gray-400 mr-2" />
                              <div>
                                <span className="text-sm font-medium">
                                  {vehicleName}
                                </span>
                                <div className="text-xs text-gray-500">
                                  {application.listing.listingType ===
                                    "ehailing-platform" && "E-hailing Platform"}
                                  {application.listing.listingType ===
                                    "rental" && "Rental Application"}
                                  {application.listing.listingType ===
                                    "fractional" && "Fractional Ownership"}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(
                                application.createdAt
                              ).toLocaleDateString("en-GB", {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                              })}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-col gap-1">
                              <Badge
                                variant="outline"
                                className={`flex items-center gap-1 w-fit ${getStatusColor(status)}`}
                              >
                                {getStatusIcon(status)}
                                <span className="capitalize">
                                  {status.replace("_", " ")}
                                </span>
                              </Badge>
                              {application.latestDecision && (
                                <div className="text-xs text-gray-500">
                                  {application.latestDecision.reviewerName ? (
                                    <span>
                                      by{" "}
                                      {application.latestDecision.reviewerName}
                                    </span>
                                  ) : (
                                    <span>Decision made</span>
                                  )}
                                  <span className="ml-1">
                                    (
                                    {new Date(
                                      application.latestDecision.decisionAt
                                    ).toLocaleDateString("en-GB")}
                                    )
                                  </span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {(() => {
                              const appDetails = application.applicationDetails;

                              // Check for e-hailing experience
                              if (appDetails?.hasEhailingExperience) {
                                return (
                                  <div className="flex items-center text-green-600">
                                    <CheckCircle size={14} className="mr-1" />
                                    <span className="text-sm">
                                      {appDetails.ehailingCompany} (
                                      {appDetails.drivingExperienceYears ||
                                        "N/A"}{" "}
                                      years)
                                    </span>
                                  </div>
                                );
                              }

                              // Check for general driving experience
                              if (appDetails?.drivingExperienceYears) {
                                return (
                                  <div className="flex items-center text-green-600">
                                    <CheckCircle size={14} className="mr-1" />
                                    <span className="text-sm">
                                      {appDetails.drivingExperienceYears} years
                                      driving
                                    </span>
                                  </div>
                                );
                              }

                              // Vehicle listing applications
                              if (
                                application.listing.sourceType === "vehicle"
                              ) {
                                return (
                                  <div className="text-sm text-gray-500">
                                    Vehicle Owner
                                  </div>
                                );
                              }

                              // No experience
                              return (
                                <div className="flex items-center text-yellow-600">
                                  <AlertCircle size={14} className="mr-1" />
                                  <span className="text-sm">No experience</span>
                                </div>
                              );
                            })()}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {application.documents.length > 0 ? (
                                <div className="flex items-center gap-2">
                                  <span className="text-green-600 font-medium">
                                    {application.documents.length}
                                  </span>
                                  <span className="text-gray-500">
                                    documents
                                  </span>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0"
                                      >
                                        <MoreVertical size={12} />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuLabel>
                                        Documents
                                      </DropdownMenuLabel>
                                      <DropdownMenuSeparator />
                                      {application.documents.map((doc) => (
                                        <DropdownMenuItem
                                          key={doc.id}
                                          className="flex items-center justify-between"
                                        >
                                          <span className="text-sm">
                                            {doc.documentType}
                                          </span>
                                          <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-4 w-4 p-0 ml-2"
                                            onClick={() =>
                                              window.open(
                                                doc.documentUrl,
                                                "_blank"
                                              )
                                            }
                                          >
                                            <Eye size={12} />
                                          </Button>
                                        </DropdownMenuItem>
                                      ))}
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              ) : (
                                <div className="flex items-center text-gray-400">
                                  <AlertCircle size={14} className="mr-1" />
                                  <span>No documents</span>
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {(() => {
                              const appDetails = application.applicationDetails;

                              // E-hailing platform applications - show weekly rate
                              if (
                                application.listing.listingType ===
                                  "ehailing-platform" &&
                                application.catalog?.weeklyFeeTarget
                              ) {
                                return (
                                  <div>
                                    <div className="font-medium">
                                      R
                                      {application.catalog.weeklyFeeTarget.toLocaleString()}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      per week
                                    </div>
                                  </div>
                                );
                              }

                              // Vehicle listing applications
                              if (
                                application.listing.sourceType === "vehicle"
                              ) {
                                return (
                                  <div className="text-sm text-gray-500">
                                    Vehicle Listing
                                  </div>
                                );
                              }

                              // Rental/Fractional applications - show applicant preferences
                              if (appDetails?.applicantPreferences) {
                                const prefs = appDetails.applicantPreferences;
                                return (
                                  <div>
                                    <div className="text-sm font-medium">
                                      Min Driver Age:{" "}
                                      {prefs.minAge
                                        ? prefs.minAge
                                        : "No Preference"}
                                    </div>

                                    <div className="text-xs text-gray-500 capitalize">
                                      Preferred Gender:{" "}
                                      {prefs.gender
                                        ? prefs.gender
                                        : "No Preference"}
                                    </div>
                                  </div>
                                );
                              }

                              return (
                                <div className="text-sm text-gray-500">N/A</div>
                              );
                            })()}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                asChild
                                className="h-8 w-8 p-0"
                              >
                                <Link
                                  href={`/admin/applications/${application.id}`}
                                >
                                  <Eye size={16} className="text-gray-600" />
                                </Link>
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
