"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON><PERSON>Up,
  TrendingDown,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Calendar,
  Download,
  Filter,
  FileText,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Bar,
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";

// Mock data for analytics
const applicationTrendsData = [
  { name: "Jan", applications: 45, approved: 32, rejected: 8, pending: 5 },
  { name: "Feb", applications: 52, approved: 38, rejected: 9, pending: 5 },
  { name: "<PERSON>", applications: 48, approved: 35, rejected: 7, pending: 6 },
  { name: "Apr", applications: 61, approved: 44, rejected: 12, pending: 5 },
  { name: "May", applications: 55, approved: 40, rejected: 10, pending: 5 },
  { name: "<PERSON>", applications: 67, approved: 48, rejected: 14, pending: 5 },
];

const approvalRateData = [
  { name: "Week 1", rate: 78 },
  { name: "Week 2", rate: 82 },
  { name: "Week 3", rate: 75 },
  { name: "Week 4", rate: 85 },
  { name: "Week 5", rate: 79 },
  { name: "Week 6", rate: 88 },
];

const experienceDistribution = [
  { name: "No Experience", value: 35, color: "#ef4444" },
  { name: "1-2 Years", value: 25, color: "#f59e0b" },
  { name: "2-5 Years", value: 30, color: "#10b981" },
  { name: "5+ Years", value: 10, color: "#009639" },
];

const processingTimeData = [
  { name: "Same Day", value: 15 },
  { name: "1-2 Days", value: 45 },
  { name: "3-5 Days", value: 30 },
  { name: "1+ Week", value: 10 },
];

export default function ApplicationAnalyticsPage() {
  const [timeRange, setTimeRange] = useState("6months");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Application Analytics</h1>
          <p className="text-gray-600 mt-1">
            Insights and trends for vehicle lease applications
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Filter size={16} />
            Filter Period
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Report
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Applications</p>
                <p className="text-2xl font-bold mt-1">328</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="text-green-500 mr-1" size={16} />
                  <span className="text-sm text-green-600">+12.5%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last month</span>
                </div>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <FileText className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approval Rate</p>
                <p className="text-2xl font-bold mt-1">82.3%</p>
                <div className="flex items-center mt-2">
                  <TrendingUp className="text-green-500 mr-1" size={16} />
                  <span className="text-sm text-green-600">+3.2%</span>
                  <span className="text-sm text-gray-500 ml-1">vs last month</span>
                </div>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg. Processing Time</p>
                <p className="text-2xl font-bold mt-1">2.4 days</p>
                <div className="flex items-center mt-2">
                  <TrendingDown className="text-green-500 mr-1" size={16} />
                  <span className="text-sm text-green-600">-0.3 days</span>
                  <span className="text-sm text-gray-500 ml-1">vs last month</span>
                </div>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold mt-1">23</p>
                <div className="flex items-center mt-2">
                  <TrendingDown className="text-green-500 mr-1" size={16} />
                  <span className="text-sm text-green-600">-5</span>
                  <span className="text-sm text-gray-500 ml-1">vs yesterday</span>
                </div>
              </div>
              <div className="w-12 h-12 rounded-full bg-orange-100 flex items-center justify-center">
                <Users className="text-orange-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Application Trends */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2">
                <BarChart3 size={20} className="text-[#009639]" />
                Application Trends
              </CardTitle>
              <Tabs value={timeRange} onValueChange={setTimeRange}>
                <TabsList>
                  <TabsTrigger value="3months">3M</TabsTrigger>
                  <TabsTrigger value="6months">6M</TabsTrigger>
                  <TabsTrigger value="1year">1Y</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={applicationTrendsData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="applications" fill="#009639" name="Total Applications" />
                  <Bar dataKey="approved" fill="#10b981" name="Approved" />
                  <Bar dataKey="rejected" fill="#ef4444" name="Rejected" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Approval Rate Trend */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp size={20} className="text-[#009639]" />
              Approval Rate Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={approvalRateData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => [`${value}%`, "Approval Rate"]} />
                  <Line
                    type="monotone"
                    dataKey="rate"
                    stroke="#009639"
                    strokeWidth={3}
                    dot={{ r: 6 }}
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Experience Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users size={20} className="text-[#009639]" />
              Experience Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[250px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={experienceDistribution}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {experienceDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Processing Time Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock size={20} className="text-[#009639]" />
            Processing Time Distribution
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {processingTimeData.map((item, index) => (
              <div key={index} className="text-center p-4 bg-gray-50 rounded-lg">
                <p className="text-2xl font-bold text-[#009639]">{item.value}%</p>
                <p className="text-sm text-gray-600 mt-1">{item.name}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
