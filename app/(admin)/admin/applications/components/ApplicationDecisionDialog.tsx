"use client";

import React from "react";
import { CheckCircle, XCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface ApplicationDecisionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  decision: "approved" | "rejected" | null;
  applicantName: string;
  vehicleName: string;
  weeklyRate: number;
  reason?: string;
}

export default function ApplicationDecisionDialog({
  isOpen,
  onClose,
  onConfirm,
  decision,
  applicantName,
  vehicleName,
  weeklyRate,
  reason,
}: ApplicationDecisionDialogProps) {
  if (!decision) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="flex items-center gap-3">
            {decision === "approved" ? (
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={24} />
              </div>
            ) : (
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <XCircle className="text-red-600" size={24} />
              </div>
            )}
            <div>
              <DialogTitle className="text-xl">
                {decision === "approved" ? "Approve" : "Reject"} Application
              </DialogTitle>
              <DialogDescription className="text-base">
                {applicantName} • {vehicleName}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="py-6 space-y-4">
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Application Summary</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Applicant:</span>
                <span className="font-medium">{applicantName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Vehicle:</span>
                <span className="font-medium">{vehicleName}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Weekly Rate:</span>
                <span className="font-medium">R{weeklyRate.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {reason && (
            <div className="bg-blue-50 rounded-lg p-4">
              <Label className="text-sm font-medium text-blue-900">Decision Reason:</Label>
              <p className="text-sm text-blue-800 mt-1">{reason}</p>
            </div>
          )}

          <div className={`rounded-lg p-4 ${
            decision === "approved" 
              ? "bg-green-50 border border-green-200" 
              : "bg-red-50 border border-red-200"
          }`}>
            <p className={`text-sm font-medium ${
              decision === "approved" ? "text-green-800" : "text-red-800"
            }`}>
              {decision === "approved" ? "✓ " : "✗ "}
              {decision === "approved" 
                ? "This will approve the application and notify the applicant to proceed with the lease process."
                : "This will reject the application and notify the applicant that their application was not successful."
              }
            </p>
          </div>
        </div>

        <DialogFooter className="flex space-x-3">
          <Button variant="outline" onClick={onClose} className="flex-1">
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            className={`flex-1 ${
              decision === "approved"
                ? "bg-[#009639] hover:bg-[#007A2F]"
                : "bg-red-600 hover:bg-red-700"
            }`}
          >
            {decision === "approved" ? (
              <>
                <CheckCircle size={16} className="mr-2" />
                Approve Application
              </>
            ) : (
              <>
                <XCircle size={16} className="mr-2" />
                Reject Application
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
