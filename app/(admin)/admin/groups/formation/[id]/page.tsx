"use client";

import React, { useState } from "react";
import { useRouter, usePara<PERSON> } from "next/navigation";
import {
  ArrowLeft,
  Users,
  FileText,
  CheckCircle,
  Clock,
  AlertTriangle,
  Eye,
  Mail,
  Phone,
  MapPin,
  Download,
  Upload,
  Building,
  Shield,
  User,
} from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import MemberDetailsModal from "../components/MemberDetailsModal";
import DocumentUploadModal from "../components/DocumentUploadModal";

// Mock data for group formation
const getGroupFormationData = (id: string) => {
  return {
    id,
    name: "City Commuters",
    memberCount: 8,
    vehicleCount: 2,
    location: "Cape Town",
    monthlyRevenue: 45000,
    formationStatus: "Ready for Formation",
    businessType: "Ride-sharing Collective",
    estimatedValue: 850000,
    leadMember: "<PERSON>",
    leadEmail: "<EMAIL>",
    leadPhone: "+27 82 345 6789",
    dateCreated: "2024-01-20",
    complianceScore: 95,
    formationProgress: 25,
    currentStep: 1,
    totalSteps: 4,
    members: [
      {
        id: 1,
        name: "Sarah Johnson",
        email: "<EMAIL>",
        phone: "+27 82 345 6789",
        role: "Lead Member",
        joinDate: "2024-01-20",
        ownership: "25%",
        status: "Active",
        documentsSubmitted: true,
        kycCompleted: true,
      },
      {
        id: 2,
        name: "Michael Chen",
        email: "<EMAIL>",
        phone: "+27 83 456 7890",
        role: "Member",
        joinDate: "2024-01-22",
        ownership: "20%",
        status: "Active",
        documentsSubmitted: true,
        kycCompleted: false,
      },
      {
        id: 3,
        name: "Lisa Williams",
        email: "<EMAIL>",
        phone: "+27 84 567 8901",
        role: "Member",
        joinDate: "2024-01-25",
        ownership: "15%",
        status: "Active",
        documentsSubmitted: false,
        kycCompleted: false,
      },
      {
        id: 4,
        name: "David Thompson",
        email: "<EMAIL>",
        phone: "+27 85 678 9012",
        role: "Member",
        joinDate: "2024-01-28",
        ownership: "15%",
        status: "Pending",
        documentsSubmitted: false,
        kycCompleted: false,
      },
      {
        id: 5,
        name: "Emma Davis",
        email: "<EMAIL>",
        phone: "+27 86 789 0123",
        role: "Member",
        joinDate: "2024-02-01",
        ownership: "10%",
        status: "Active",
        documentsSubmitted: true,
        kycCompleted: true,
      },
      {
        id: 6,
        name: "James Wilson",
        email: "<EMAIL>",
        phone: "+27 87 890 1234",
        role: "Member",
        joinDate: "2024-02-03",
        ownership: "10%",
        status: "Active",
        documentsSubmitted: false,
        kycCompleted: true,
      },
      {
        id: 7,
        name: "Sophie Brown",
        email: "<EMAIL>",
        phone: "+27 88 901 2345",
        role: "Member",
        joinDate: "2024-02-05",
        ownership: "3%",
        status: "Active",
        documentsSubmitted: true,
        kycCompleted: false,
      },
      {
        id: 8,
        name: "Robert Taylor",
        email: "<EMAIL>",
        phone: "+27 89 012 3456",
        role: "Member",
        joinDate: "2024-02-08",
        ownership: "2%",
        status: "Pending",
        documentsSubmitted: false,
        kycCompleted: false,
      },
    ],
    vehicles: [
      {
        id: 1,
        make: "Toyota",
        model: "Fortuner",
        year: 2022,
        registration: "CA 123 456",
        condition: "Excellent",
        mileage: 25000,
        value: 450000,
      },
      {
        id: 2,
        make: "Honda",
        model: "Civic",
        year: 2021,
        registration: "CA 789 012",
        condition: "Good",
        mileage: 35000,
        value: 320000,
      },
    ],
    formationSteps: [
      {
        step: 1,
        title: "Group Details Review",
        description: "Review group information and member details",
        status: "completed",
        completedDate: "2024-02-10",
      },
      {
        step: 2,
        title: "Member Documentation",
        description: "Collect and verify all member documents",
        status: "in_progress",
        completedDate: null,
      },
      {
        step: 3,
        title: "SPV Company Setup",
        description: "Create Special Purpose Vehicle company structure",
        status: "pending",
        completedDate: null,
      },
      {
        step: 4,
        title: "Registration & Finalization",
        description: "Complete company registration and documentation",
        status: "pending",
        completedDate: null,
      },
    ],
    requiredDocuments: [
      {
        name: "Memorandum of Incorporation",
        status: "pending",
        required: true,
        uploadedBy: null,
        uploadedDate: null,
      },
      {
        name: "Articles of Association",
        status: "pending",
        required: true,
        uploadedBy: null,
        uploadedDate: null,
      },
      {
        name: "Shareholder Agreement",
        status: "pending",
        required: true,
        uploadedBy: null,
        uploadedDate: null,
      },
      {
        name: "Tax Registration Certificate",
        status: "pending",
        required: true,
        uploadedBy: null,
        uploadedDate: null,
      },
      {
        name: "Banking Resolution",
        status: "pending",
        required: true,
        uploadedBy: null,
        uploadedDate: null,
      },
    ],
  };
};

export default function GroupFormationWorkflow() {
  const router = useRouter();
  const params = useParams();
  const groupId = params.id as string;

  const [currentTab, setCurrentTab] = useState("overview");
  const [group] = useState(() => getGroupFormationData(groupId));
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const [isMemberModalOpen, setIsMemberModalOpen] = useState(false);
  const [isDocumentModalOpen, setIsDocumentModalOpen] = useState(false);

  const handleViewMember = (member: any) => {
    setSelectedMember(member);
    setIsMemberModalOpen(true);
  };

  const handleDocumentUpload = (document: any) => {
    // Handle document upload logic here
    console.log("Document uploaded:", document);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={16} className="text-green-600" />;
      case "in_progress":
        return <Clock size={16} className="text-blue-600" />;
      case "pending":
        return <AlertTriangle size={16} className="text-gray-600" />;
      default:
        return <AlertTriangle size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => router.push("/admin/groups")}
            className="flex items-center text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Groups
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              Company Formation: {group.name}
            </h1>
            <p className="text-gray-500">
              Manage the transition from group to formal company
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={
              group.formationStatus === "Ready for Formation"
                ? "bg-green-100 text-green-800"
                : "bg-blue-100 text-blue-800"
            }
          >
            {group.formationStatus}
          </Badge>
        </div>
      </div>

      {/* Progress Overview */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Formation Progress</h3>
            <span className="text-sm text-gray-500">
              Step {group.currentStep} of {group.totalSteps}
            </span>
          </div>
          <Progress value={group.formationProgress} className="mb-4" />
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {group.formationSteps.map((step) => (
              <div
                key={step.step}
                className="flex items-center space-x-3 p-3 rounded-lg border"
              >
                {getStatusIcon(step.status)}
                <div>
                  <p className="font-medium text-sm">{step.title}</p>
                  <p className="text-xs text-gray-500">{step.description}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs
        value={currentTab}
        onValueChange={setCurrentTab}
        className="space-y-6"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Eye size={16} />
            Overview
          </TabsTrigger>
          <TabsTrigger value="members" className="flex items-center gap-2">
            <Users size={16} />
            Members ({group.members.length})
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileText size={16} />
            Documents
          </TabsTrigger>
          <TabsTrigger value="company" className="flex items-center gap-2">
            <Building size={16} />
            Company Setup
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Group Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users size={18} className="text-[#009639] mr-2" />
                  Group Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-500">Group Name</span>
                  <span className="font-medium">{group.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Members</span>
                  <span className="font-medium">{group.members.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Vehicles</span>
                  <span className="font-medium">{group.vehicles.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Location</span>
                  <span className="font-medium flex items-center">
                    <MapPin size={14} className="mr-1" />
                    {group.location}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Created</span>
                  <span className="font-medium">
                    {new Date(group.dateCreated).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Estimated Value</span>
                  <span className="font-medium">
                    R{group.estimatedValue.toLocaleString()}
                  </span>
                </div>
              </CardContent>
            </Card>

            {/* Lead Contact */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User size={18} className="text-[#009639] mr-2" />
                  Lead Contact
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-500">Name</span>
                  <span className="font-medium">{group.leadMember}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Email</span>
                  <span className="font-medium flex items-center">
                    <Mail size={14} className="mr-1" />
                    {group.leadEmail}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Phone</span>
                  <span className="font-medium flex items-center">
                    <Phone size={14} className="mr-1" />
                    {group.leadPhone}
                  </span>
                </div>
                <Separator />
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Mail size={14} className="mr-1" />
                    Email Lead
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    <Phone size={14} className="mr-1" />
                    Call Lead
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Formation Steps */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle size={18} className="text-[#009639] mr-2" />
                Formation Steps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {group.formationSteps.map((step) => (
                  <div key={step.step} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          step.status === "completed"
                            ? "bg-green-100 text-green-600"
                            : step.status === "in_progress"
                              ? "bg-blue-100 text-blue-600"
                              : "bg-gray-100 text-gray-600"
                        }`}
                      >
                        {step.status === "completed" ? (
                          <CheckCircle size={16} />
                        ) : (
                          <span className="text-sm font-medium">
                            {step.step}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{step.title}</h4>
                        <Badge
                          variant="outline"
                          className={getStatusBadge(step.status)}
                        >
                          {step.status.replace("_", " ")}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">
                        {step.description}
                      </p>
                      {step.completedDate && (
                        <p className="text-xs text-gray-400 mt-1">
                          Completed:{" "}
                          {new Date(step.completedDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Users size={18} className="text-[#009639] mr-2" />
                  Group Members
                </CardTitle>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Mail size={14} className="mr-1" />
                    Email All Members
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download size={14} className="mr-1" />
                    Export Member List
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {group.members.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center">
                        <User size={20} className="text-[#009639]" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{member.name}</h4>
                          {member.role === "Lead Member" && (
                            <Badge
                              variant="outline"
                              className="bg-blue-100 text-blue-800"
                            >
                              Lead
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span className="flex items-center">
                            <Mail size={12} className="mr-1" />
                            {member.email}
                          </span>
                          <span className="flex items-center">
                            <Phone size={12} className="mr-1" />
                            {member.phone}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                          <span>Ownership: {member.ownership}</span>
                          <span>
                            Joined:{" "}
                            {new Date(member.joinDate).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="flex items-center space-x-2 mb-1">
                          <Badge
                            variant="outline"
                            className={
                              member.status === "Active"
                                ? "bg-green-100 text-green-800"
                                : "bg-yellow-100 text-yellow-800"
                            }
                          >
                            {member.status}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2 text-xs">
                          <span
                            className={`flex items-center ${
                              member.documentsSubmitted
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {member.documentsSubmitted ? (
                              <CheckCircle size={12} className="mr-1" />
                            ) : (
                              <AlertTriangle size={12} className="mr-1" />
                            )}
                            Docs
                          </span>
                          <span
                            className={`flex items-center ${
                              member.kycCompleted
                                ? "text-green-600"
                                : "text-red-600"
                            }`}
                          >
                            {member.kycCompleted ? (
                              <CheckCircle size={12} className="mr-1" />
                            ) : (
                              <AlertTriangle size={12} className="mr-1" />
                            )}
                            KYC
                          </span>
                        </div>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewMember(member)}
                        >
                          <Eye size={14} className="mr-1" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Mail size={14} className="mr-1" />
                          Request Docs
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Member Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Total Members</p>
                    <p className="text-2xl font-bold">{group.members.length}</p>
                  </div>
                  <Users size={20} className="text-[#009639]" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Documents Complete</p>
                    <p className="text-2xl font-bold">
                      {group.members.filter((m) => m.documentsSubmitted).length}
                    </p>
                  </div>
                  <FileText size={20} className="text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">KYC Complete</p>
                    <p className="text-2xl font-bold">
                      {group.members.filter((m) => m.kycCompleted).length}
                    </p>
                  </div>
                  <Shield size={20} className="text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-500">Active Members</p>
                    <p className="text-2xl font-bold">
                      {
                        group.members.filter((m) => m.status === "Active")
                          .length
                      }
                    </p>
                  </div>
                  <CheckCircle size={20} className="text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <FileText size={18} className="text-[#009639] mr-2" />
                  Required Documents
                </CardTitle>
                <Button
                  className="bg-[#009639] hover:bg-[#007A2F]"
                  onClick={() => setIsDocumentModalOpen(true)}
                >
                  <Upload size={14} className="mr-1" />
                  Upload Document
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {group.requiredDocuments.map((doc, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center space-x-4">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          doc.status === "completed"
                            ? "bg-green-100"
                            : doc.status === "uploaded"
                              ? "bg-blue-100"
                              : "bg-gray-100"
                        }`}
                      >
                        <FileText
                          size={20}
                          className={
                            doc.status === "completed"
                              ? "text-green-600"
                              : doc.status === "uploaded"
                                ? "text-blue-600"
                                : "text-gray-600"
                          }
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">{doc.name}</h4>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          {doc.required && (
                            <Badge
                              variant="outline"
                              className="bg-red-100 text-red-800"
                            >
                              Required
                            </Badge>
                          )}
                          {doc.uploadedBy && (
                            <span>Uploaded by {doc.uploadedBy}</span>
                          )}
                          {doc.uploadedDate && (
                            <span>
                              on{" "}
                              {new Date(doc.uploadedDate).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge
                        variant="outline"
                        className={
                          doc.status === "completed"
                            ? "bg-green-100 text-green-800"
                            : doc.status === "uploaded"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-gray-100 text-gray-800"
                        }
                      >
                        {doc.status === "pending" ? "Not Uploaded" : doc.status}
                      </Badge>
                      {doc.status === "pending" ? (
                        <Button variant="outline" size="sm">
                          <Upload size={14} className="mr-1" />
                          Upload
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm">
                          <Download size={14} className="mr-1" />
                          Download
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Setup Tab */}
        <TabsContent value="company" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building size={18} className="text-[#009639] mr-2" />
                SPV Company Setup
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium">Company Information</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-500">Proposed Name</span>
                      <span className="font-medium">
                        {group.name} (Pty) Ltd
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Business Type</span>
                      <span className="font-medium">Private Company</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Registration Type</span>
                      <span className="font-medium">
                        Special Purpose Vehicle
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">Jurisdiction</span>
                      <span className="font-medium">South Africa</span>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">Formation Status</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Name Reservation</span>
                      <Badge
                        variant="outline"
                        className="bg-gray-100 text-gray-800"
                      >
                        Pending
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">MOI Preparation</span>
                      <Badge
                        variant="outline"
                        className="bg-gray-100 text-gray-800"
                      >
                        Pending
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">CIPC Registration</span>
                      <Badge
                        variant="outline"
                        className="bg-gray-100 text-gray-800"
                      >
                        Pending
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-500">Tax Registration</span>
                      <Badge
                        variant="outline"
                        className="bg-gray-100 text-gray-800"
                      >
                        Pending
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Action Required</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                  >
                    <div className="text-left">
                      <div className="font-medium">Reserve Company Name</div>
                      <div className="text-sm text-gray-500">
                        Check availability and reserve the company name
                      </div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                  >
                    <div className="text-left">
                      <div className="font-medium">Prepare MOI</div>
                      <div className="text-sm text-gray-500">
                        Draft Memorandum of Incorporation
                      </div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                  >
                    <div className="text-left">
                      <div className="font-medium">Setup Banking</div>
                      <div className="text-sm text-gray-500">
                        Open corporate bank account
                      </div>
                    </div>
                  </Button>
                  <Button
                    variant="outline"
                    className="justify-start h-auto p-4"
                  >
                    <div className="text-left">
                      <div className="font-medium">Finalize Registration</div>
                      <div className="text-sm text-gray-500">
                        Complete CIPC registration process
                      </div>
                    </div>
                  </Button>
                </div>
              </div>

              <Separator />

              <div className="flex justify-end space-x-4">
                <Button variant="outline">Save as Draft</Button>
                <Button className="bg-[#009639] hover:bg-[#007A2F]">
                  <CheckCircle size={14} className="mr-1" />
                  Mark as Registered
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <MemberDetailsModal
        member={selectedMember}
        isOpen={isMemberModalOpen}
        onClose={() => setIsMemberModalOpen(false)}
      />

      <DocumentUploadModal
        isOpen={isDocumentModalOpen}
        onClose={() => setIsDocumentModalOpen(false)}
        onUpload={handleDocumentUpload}
      />
    </div>
  );
}
