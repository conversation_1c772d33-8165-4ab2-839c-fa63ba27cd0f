"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  Shield,
  CheckCircle,
  AlertTriangle,
  Download,
  Send,
} from "lucide-react";

interface Member {
  id: number;
  name: string;
  email: string;
  phone: string;
  role: string;
  joinDate: string;
  ownership: string;
  status: string;
  documentsSubmitted: boolean;
  kycCompleted: boolean;
  address?: string;
  idNumber?: string;
  bankDetails?: {
    bank: string;
    accountNumber: string;
    branchCode: string;
  };
  documents?: {
    name: string;
    status: string;
    uploadedDate?: string;
  }[];
}

interface MemberDetailsModalProps {
  member: Member | null;
  isOpen: boolean;
  onClose: () => void;
}

export default function MemberDetailsModal({
  member,
  isO<PERSON>,
  onClose,
}: MemberDetailsModalProps) {
  if (!member) return null;

  // Mock additional member data
  const memberDetails = {
    ...member,
    address: "123 Main Street, Cape Town, 8001",
    idNumber: "890**********",
    bankDetails: {
      bank: "First National Bank",
      accountNumber: "**********",
      branchCode: "250655",
    },
    documents: [
      {
        name: "Identity Document",
        status: "verified",
        uploadedDate: "2024-02-01",
      },
      {
        name: "Proof of Address",
        status: "verified",
        uploadedDate: "2024-02-01",
      },
      {
        name: "Bank Statement",
        status: "pending",
        uploadedDate: null,
      },
      {
        name: "Income Certificate",
        status: "uploaded",
        uploadedDate: "2024-02-05",
      },
    ],
  };

  const getDocumentStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return "bg-green-100 text-green-800";
      case "uploaded":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getDocumentStatusIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <CheckCircle size={14} className="text-green-600" />;
      case "uploaded":
        return <FileText size={14} className="text-blue-600" />;
      case "pending":
        return <AlertTriangle size={14} className="text-gray-600" />;
      default:
        return <AlertTriangle size={14} className="text-gray-600" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <User size={20} className="text-[#009639] mr-2" />
            Member Details: {memberDetails.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-lg">Personal Information</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Full Name</span>
                  <span className="font-medium">{memberDetails.name}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">ID Number</span>
                  <span className="font-medium">{memberDetails.idNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Email</span>
                  <span className="font-medium flex items-center">
                    <Mail size={14} className="mr-1" />
                    {memberDetails.email}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Phone</span>
                  <span className="font-medium flex items-center">
                    <Phone size={14} className="mr-1" />
                    {memberDetails.phone}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Address</span>
                  <span className="font-medium flex items-center">
                    <MapPin size={14} className="mr-1" />
                    {memberDetails.address}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium text-lg">Group Information</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-500">Role</span>
                  <Badge
                    variant="outline"
                    className={
                      memberDetails.role === "Lead Member"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                    }
                  >
                    {memberDetails.role}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Ownership</span>
                  <span className="font-medium">{memberDetails.ownership}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Status</span>
                  <Badge
                    variant="outline"
                    className={
                      memberDetails.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }
                  >
                    {memberDetails.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Join Date</span>
                  <span className="font-medium flex items-center">
                    <Calendar size={14} className="mr-1" />
                    {new Date(memberDetails.joinDate).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">KYC Status</span>
                  <Badge
                    variant="outline"
                    className={
                      memberDetails.kycCompleted
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }
                  >
                    {memberDetails.kycCompleted ? "Completed" : "Pending"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Banking Information */}
          <div className="space-y-4">
            <h4 className="font-medium text-lg">Banking Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex justify-between">
                <span className="text-gray-500">Bank</span>
                <span className="font-medium">{memberDetails.bankDetails.bank}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Account Number</span>
                <span className="font-medium">{memberDetails.bankDetails.accountNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Branch Code</span>
                <span className="font-medium">{memberDetails.bankDetails.branchCode}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Documents */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-lg">Documents</h4>
              <Button variant="outline" size="sm">
                <Send size={14} className="mr-1" />
                Request Missing Documents
              </Button>
            </div>
            <div className="space-y-3">
              {memberDetails.documents?.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    {getDocumentStatusIcon(doc.status)}
                    <div>
                      <span className="font-medium">{doc.name}</span>
                      {doc.uploadedDate && (
                        <p className="text-sm text-gray-500">
                          Uploaded: {new Date(doc.uploadedDate).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge
                      variant="outline"
                      className={getDocumentStatusBadge(doc.status)}
                    >
                      {doc.status}
                    </Badge>
                    {doc.status !== "pending" && (
                      <Button variant="outline" size="sm">
                        <Download size={14} className="mr-1" />
                        Download
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
            <Button variant="outline">
              <Mail size={14} className="mr-1" />
              Send Email
            </Button>
            <Button className="bg-[#009639] hover:bg-[#007A2F]">
              <Send size={14} className="mr-1" />
              Request Company Formation Details
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
