"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Search,
  Filter,
  Plus,
  Calendar,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Edit,
  Trash2,
  Download,
  Upload,
  BarChart3,
  Target,
  Percent,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

// Import our weekly earnings actions
import {
  getWeeklyEarningsAction,
  getWeeklyEarningsSummaryAction,
  recordWeeklyEarningsAction,
  updateWeeklyEarningsAction,
  deleteWeeklyEarningsAction,
  recordBulkWeeklyEarningsAction,
  getEarningsStatsAction,
} from "@/actions/admin/weekly-earnings";

// Import types
import { WeeklyEarningsRecord } from "@/types/payment-contract";

interface Assignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  platformName?: string;
  outstandingBalance: number;
}

interface WeeklyEarningsManagementProps {
  assignments: Assignment[];
  onRefresh?: () => void;
}

export default function WeeklyEarningsManagement({
  assignments,
  onRefresh,
}: WeeklyEarningsManagementProps) {
  const [earnings, setEarnings] = useState<WeeklyEarningsRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [selectedWeek, setSelectedWeek] = useState("");
  const [selectedPlatform, setSelectedPlatform] = useState("all");

  // Dialog states
  const [showRecordDialog, setShowRecordDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showBulkDialog, setShowBulkDialog] = useState(false);
  const [showStatsDialog, setShowStatsDialog] = useState(false);
  const [selectedEarning, setSelectedEarning] = useState<WeeklyEarningsRecord | null>(null);

  // Form states
  const [recordFormData, setRecordFormData] = useState({
    assignmentId: "",
    weekStart: "",
    weekEnd: "",
    totalEarnings: "",
    platformName: "uber" as "uber" | "bolt" | "indrive" | "other",
    notes: "",
  });

  const [editFormData, setEditFormData] = useState({
    totalEarnings: "",
    notes: "",
  });

  const [bulkFormData, setBulkFormData] = useState({
    weekStart: "",
    weekEnd: "",
    platformName: "uber" as "uber" | "bolt" | "indrive" | "other",
    assignmentEarnings: [] as Array<{ assignmentId: string; totalEarnings: string; notes: string }>,
  });

  // Stats state
  const [earningsStats, setEarningsStats] = useState({
    totalEarnings: "0",
    averageEarnings: "0",
    totalDrivers: 0,
    activeDrivers: 0,
    topPerformer: null as { driverName: string; earnings: string } | null,
    weeklyGrowth: "0",
  });

  const [earningsData, setEarningsData] = useState({
    summary: [] as any[],
    totalRecords: 0,
    avgWeeklyEarnings: "0",
    highestEarnings: "0",
    lowestEarnings: "0",
  });

  // Load earnings data
  useEffect(() => {
    loadEarningsData();
    loadEarningsStats();
  }, [assignments]);

  const loadEarningsData = async () => {
    if (assignments.length === 0) return;

    setLoading(true);
    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      
      const [earningsResult, summaryResult] = await Promise.all([
        getWeeklyEarningsAction(assignmentIds[0]), // Get all for first assignment as example
        getWeeklyEarningsSummaryAction(assignmentIds),
      ]);

      if (summaryResult.success && summaryResult.data) {
        setEarningsData({
          summary: summaryResult.data,
          totalRecords: summaryResult.data.length,
          avgWeeklyEarnings: summaryResult.data.reduce((sum, item) => sum + parseFloat(item.averageWeeklyEarnings || "0"), 0).toFixed(2),
          highestEarnings: Math.max(...summaryResult.data.map(item => parseFloat(item.highestWeeklyEarnings || "0"))).toFixed(2),
          lowestEarnings: Math.min(...summaryResult.data.map(item => parseFloat(item.lowestWeeklyEarnings || "0"))).toFixed(2),
        });
      }

      // Load detailed earnings for all assignments
      const allEarnings: WeeklyEarningsRecord[] = [];
      for (const assignmentId of assignmentIds) {
        const result = await getWeeklyEarningsAction(assignmentId);
        if (result.success && result.data) {
          allEarnings.push(...result.data);
        }
      }
      
      setEarnings(allEarnings);
    } catch (error) {
      console.error("Error loading earnings data:", error);
      toast.error("Failed to load earnings data");
    } finally {
      setLoading(false);
    }
  };

  const loadEarningsStats = async () => {
    if (assignments.length === 0) return;

    try {
      const assignmentIds = assignments.map(a => parseInt(a.id));
      const result = await getEarningsStatsAction(assignmentIds);
      
      if (result.success && result.data) {
        setEarningsStats({
          totalEarnings: result.data.totalEarnings,
          averageEarnings: result.data.averageEarnings,
          totalDrivers: result.data.totalDrivers,
          activeDrivers: result.data.activeDrivers,
          topPerformer: result.data.topPerformer,
          weeklyGrowth: result.data.weeklyGrowth || "0",
        });
      }
    } catch (error) {
      console.error("Error loading earnings stats:", error);
    }
  };

  const handleRecordEarnings = async () => {
    if (!recordFormData.assignmentId || !recordFormData.weekStart || !recordFormData.weekEnd || !recordFormData.totalEarnings) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("assignmentId", recordFormData.assignmentId);
      formData.append("weekStart", recordFormData.weekStart);
      formData.append("weekEnd", recordFormData.weekEnd);
      formData.append("totalEarnings", recordFormData.totalEarnings);
      formData.append("platformName", recordFormData.platformName);
      formData.append("notes", recordFormData.notes);

      const result = await recordWeeklyEarningsAction(null, formData);

      if (result.success) {
        toast.success("Weekly earnings recorded successfully");
        setShowRecordDialog(false);
        setRecordFormData({
          assignmentId: "",
          weekStart: "",
          weekEnd: "",
          totalEarnings: "",
          platformName: "uber",
          notes: "",
        });
        loadEarningsData();
        loadEarningsStats();
        onRefresh?.();
      } else {
        toast.error(result.error || "Failed to record earnings");
      }
    } catch (error) {
      console.error("Error recording earnings:", error);
      toast.error("Failed to record earnings");
    }
  };

  const handleUpdateEarnings = async () => {
    if (!selectedEarning || !editFormData.totalEarnings) {
      toast.error("Please provide earnings amount");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("earningsId", selectedEarning.id.toString());
      formData.append("totalEarnings", editFormData.totalEarnings);
      formData.append("notes", editFormData.notes);

      const result = await updateWeeklyEarningsAction(null, formData);

      if (result.success) {
        toast.success("Earnings updated successfully");
        setShowEditDialog(false);
        setSelectedEarning(null);
        setEditFormData({ totalEarnings: "", notes: "" });
        loadEarningsData();
        loadEarningsStats();
      } else {
        toast.error(result.error || "Failed to update earnings");
      }
    } catch (error) {
      console.error("Error updating earnings:", error);
      toast.error("Failed to update earnings");
    }
  };

  const handleDeleteEarnings = async (earningsId: number) => {
    if (!confirm("Are you sure you want to delete this earnings record?")) {
      return;
    }

    try {
      const formData = new FormData();
      formData.append("earningsId", earningsId.toString());

      const result = await deleteWeeklyEarningsAction(null, formData);

      if (result.success) {
        toast.success("Earnings record deleted successfully");
        loadEarningsData();
        loadEarningsStats();
      } else {
        toast.error(result.error || "Failed to delete earnings");
      }
    } catch (error) {
      console.error("Error deleting earnings:", error);
      toast.error("Failed to delete earnings");
    }
  };

  const handleBulkRecord = async () => {
    if (!bulkFormData.weekStart || !bulkFormData.weekEnd || bulkFormData.assignmentEarnings.length === 0) {
      toast.error("Please provide week dates and at least one earnings record");
      return;
    }

    try {
      const formData = new FormData();
      formData.append("weekStart", bulkFormData.weekStart);
      formData.append("weekEnd", bulkFormData.weekEnd);
      formData.append("platformName", bulkFormData.platformName);
      formData.append("earningsData", JSON.stringify(bulkFormData.assignmentEarnings));

      const result = await recordBulkWeeklyEarningsAction(null, formData);

      if (result.success) {
        toast.success(`${result.data?.successCount || 0} earnings records created successfully`);
        if (result.data?.failedCount && result.data.failedCount > 0) {
          toast.warning(`${result.data.failedCount} records failed to create`);
        }
        setShowBulkDialog(false);
        setBulkFormData({
          weekStart: "",
          weekEnd: "",
          platformName: "uber",
          assignmentEarnings: [],
        });
        loadEarningsData();
        loadEarningsStats();
      } else {
        toast.error(result.error || "Failed to record bulk earnings");
      }
    } catch (error) {
      console.error("Error recording bulk earnings:", error);
      toast.error("Failed to record bulk earnings");
    }
  };

  const openEditDialog = (earning: WeeklyEarningsRecord) => {
    setSelectedEarning(earning);
    setEditFormData({
      totalEarnings: earning.totalEarnings.toString(),
      notes: earning.notes || "",
    });
    setShowEditDialog(true);
  };

  const openBulkDialog = () => {
    setBulkFormData({
      weekStart: "",
      weekEnd: "",
      platformName: "uber",
      assignmentEarnings: assignments.map(assignment => ({
        assignmentId: assignment.id,
        totalEarnings: "",
        notes: "",
      })),
    });
    setShowBulkDialog(true);
  };

  // Filter earnings based on search and filters
  const filteredEarnings = earnings.filter(earning => {
    const assignment = assignments.find(a => a.id === earning.assignmentId.toString());
    const matchesSearch = assignment?.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         assignment?.vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = filterStatus === "all" || 
                         (filterStatus === "high" && earning.totalEarnings >= 5000) ||
                         (filterStatus === "low" && earning.totalEarnings < 2000) ||
                         (filterStatus === "avg" && earning.totalEarnings >= 2000 && earning.totalEarnings < 5000);
    
    const matchesPlatform = selectedPlatform === "all" || earning.platformName === selectedPlatform;
    
    const matchesWeek = !selectedWeek || earning.weekStart.includes(selectedWeek);
    
    return matchesSearch && matchesStatus && matchesPlatform && matchesWeek;
  });

  const formatCurrency = (amount: number | string) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return `R${num.toLocaleString()}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getEarningsStatusColor = (earnings: number) => {
    if (earnings >= 5000) return "bg-green-100 text-green-800 border-green-200";
    if (earnings >= 2000) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  const getEarningsStatusLabel = (earnings: number) => {
    if (earnings >= 5000) return "High";
    if (earnings >= 2000) return "Average";
    return "Low";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Weekly Earnings Management</h2>
          <p className="text-sm text-gray-500">Track and manage driver weekly earnings across all platforms</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowStatsDialog(true)} variant="outline">
            <BarChart3 size={16} className="mr-2" />
            View Stats
          </Button>
          <Button onClick={openBulkDialog} variant="outline">
            <Upload size={16} className="mr-2" />
            Bulk Record
          </Button>
          <Button onClick={() => setShowRecordDialog(true)} className="bg-[#009639] hover:bg-[#007A2F]">
            <Plus size={16} className="mr-2" />
            Record Earnings
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-[#009639]" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Earnings</p>
                <p className="text-2xl font-bold">{formatCurrency(earningsStats.totalEarnings)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Average Earnings</p>
                <p className="text-2xl font-bold">{formatCurrency(earningsStats.averageEarnings)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Drivers</p>
                <p className="text-2xl font-bold">{earningsStats.activeDrivers}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Weekly Growth</p>
                <p className="text-2xl font-bold">{earningsStats.weeklyGrowth}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Records</p>
                <p className="text-2xl font-bold">{earningsData.totalRecords}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Top Performer</p>
                <p className="text-lg font-bold">{earningsStats.topPerformer?.driverName || "N/A"}</p>
                {earningsStats.topPerformer && (
                  <p className="text-xs text-gray-500">{formatCurrency(earningsStats.topPerformer.earnings)}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search drivers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Earnings Level" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Levels</SelectItem>
              <SelectItem value="high">High (R5000+)</SelectItem>
              <SelectItem value="avg">Average (R2000-R5000)</SelectItem>
              <SelectItem value="low">Low (&lt;R2000)</SelectItem>
            </SelectContent>
          </Select>

          <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Platform" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Platforms</SelectItem>
              <SelectItem value="uber">Uber</SelectItem>
              <SelectItem value="bolt">Bolt</SelectItem>
              <SelectItem value="indrive">InDrive</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>

          <Input
            type="date"
            value={selectedWeek}
            onChange={(e) => setSelectedWeek(e.target.value)}
            className="w-40"
            placeholder="Filter by week"
          />
        </div>

        <Button onClick={loadEarningsData} variant="outline">
          Refresh Data
        </Button>
      </div>

      {/* Earnings Table */}
      <Card>
        <CardHeader>
          <CardTitle>Weekly Earnings Records</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Driver</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Week Period</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Earnings</TableHead>
                <TableHead>Net Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                      <span className="ml-2">Loading earnings...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : filteredEarnings.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="text-gray-500">
                      <DollarSign size={48} className="mx-auto mb-4 text-gray-300" />
                      <p>No earnings records found</p>
                      <p className="text-sm">Record your first weekly earnings to get started</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredEarnings.map((earning) => {
                  const assignment = assignments.find(a => a.id === earning.assignmentId.toString());
                  return (
                    <TableRow key={earning.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment?.driverName || "Unknown"}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment?.vehicleName || "Unknown"}</p>
                          <p className="text-sm text-gray-500">{assignment?.vehicleRegistration || ""}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{formatDate(earning.weekStart)} - {formatDate(earning.weekEnd)}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {earning.platformName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-green-600">
                          {formatCurrency(earning.totalEarnings)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className="font-bold text-blue-600">
                          {formatCurrency(earning.netEarnings)}
                        </span>
                        {earning.shortfallAmount > 0 && (
                          <p className="text-xs text-red-600">
                            Shortfall: {formatCurrency(earning.shortfallAmount)}
                          </p>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className={getEarningsStatusColor(earning.totalEarnings)}>
                          {getEarningsStatusLabel(earning.totalEarnings)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              Actions
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditDialog(earning)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Earnings
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteEarnings(earning.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Record
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  );
                })
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Record Earnings Dialog */}
      <Dialog open={showRecordDialog} onOpenChange={setShowRecordDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Weekly Earnings</DialogTitle>
            <DialogDescription>
              Record weekly earnings for a driver assignment
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="assignment">Assignment *</Label>
              <Select value={recordFormData.assignmentId} onValueChange={(value) => 
                setRecordFormData(prev => ({ ...prev, assignmentId: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select assignment" />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map(assignment => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      {assignment.driverName} - {assignment.vehicleName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="weekStart">Week Start *</Label>
                <Input
                  id="weekStart"
                  type="date"
                  value={recordFormData.weekStart}
                  onChange={(e) => setRecordFormData(prev => ({ ...prev, weekStart: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="weekEnd">Week End *</Label>
                <Input
                  id="weekEnd"
                  type="date"
                  value={recordFormData.weekEnd}
                  onChange={(e) => setRecordFormData(prev => ({ ...prev, weekEnd: e.target.value }))}
                />
              </div>
            </div>
            <div>
              <Label htmlFor="totalEarnings">Total Earnings (R) *</Label>
              <Input
                id="totalEarnings"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={recordFormData.totalEarnings}
                onChange={(e) => setRecordFormData(prev => ({ ...prev, totalEarnings: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="platform">Platform *</Label>
              <Select value={recordFormData.platformName} onValueChange={(value: "uber" | "bolt" | "indrive" | "other") => 
                setRecordFormData(prev => ({ ...prev, platformName: value }))
              }>
                <SelectTrigger>
                  <SelectValue placeholder="Select platform" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="uber">Uber</SelectItem>
                  <SelectItem value="bolt">Bolt</SelectItem>
                  <SelectItem value="indrive">InDrive</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes..."
                value={recordFormData.notes}
                onChange={(e) => setRecordFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowRecordDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleRecordEarnings} className="bg-[#009639] hover:bg-[#007A2F]">
              Record Earnings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Earnings Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Weekly Earnings</DialogTitle>
            <DialogDescription>
              Update earnings record for {selectedEarning && assignments.find(a => a.id === selectedEarning.assignmentId.toString())?.driverName}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editEarnings">Total Earnings (R) *</Label>
              <Input
                id="editEarnings"
                type="number"
                step="0.01"
                placeholder="0.00"
                value={editFormData.totalEarnings}
                onChange={(e) => setEditFormData(prev => ({ ...prev, totalEarnings: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="editNotes">Notes</Label>
              <Textarea
                id="editNotes"
                placeholder="Additional notes..."
                value={editFormData.notes}
                onChange={(e) => setEditFormData(prev => ({ ...prev, notes: e.target.value }))}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateEarnings} className="bg-[#009639] hover:bg-[#007A2F]">
              Update Earnings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Record Dialog */}
      <Dialog open={showBulkDialog} onOpenChange={setShowBulkDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Bulk Record Weekly Earnings</DialogTitle>
            <DialogDescription>
              Record earnings for multiple drivers for the same week period
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="bulkWeekStart">Week Start *</Label>
                <Input
                  id="bulkWeekStart"
                  type="date"
                  value={bulkFormData.weekStart}
                  onChange={(e) => setBulkFormData(prev => ({ ...prev, weekStart: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="bulkWeekEnd">Week End *</Label>
                <Input
                  id="bulkWeekEnd"
                  type="date"
                  value={bulkFormData.weekEnd}
                  onChange={(e) => setBulkFormData(prev => ({ ...prev, weekEnd: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="bulkPlatform">Platform *</Label>
                <Select value={bulkFormData.platformName} onValueChange={(value: "uber" | "bolt" | "indrive" | "other") => 
                  setBulkFormData(prev => ({ ...prev, platformName: value }))
                }>
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="uber">Uber</SelectItem>
                    <SelectItem value="bolt">Bolt</SelectItem>
                    <SelectItem value="indrive">InDrive</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Driver Earnings</Label>
              <div className="max-h-60 overflow-y-auto border rounded p-4">
                {bulkFormData.assignmentEarnings.map((item, index) => {
                  const assignment = assignments.find(a => a.id === item.assignmentId);
                  return (
                    <div key={item.assignmentId} className="grid grid-cols-3 gap-4 mb-4 p-3 border rounded">
                      <div>
                        <Label className="text-sm">{assignment?.driverName}</Label>
                        <p className="text-xs text-gray-500">{assignment?.vehicleName}</p>
                      </div>
                      <div>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Earnings (R)"
                          value={item.totalEarnings}
                          onChange={(e) => {
                            const updated = [...bulkFormData.assignmentEarnings];
                            updated[index].totalEarnings = e.target.value;
                            setBulkFormData(prev => ({ ...prev, assignmentEarnings: updated }));
                          }}
                        />
                      </div>
                      <div>
                        <Input
                          placeholder="Notes"
                          value={item.notes}
                          onChange={(e) => {
                            const updated = [...bulkFormData.assignmentEarnings];
                            updated[index].notes = e.target.value;
                            setBulkFormData(prev => ({ ...prev, assignmentEarnings: updated }));
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkRecord} className="bg-[#009639] hover:bg-[#007A2F]">
              Record All Earnings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Stats Dialog */}
      <Dialog open={showStatsDialog} onOpenChange={setShowStatsDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Earnings Statistics</DialogTitle>
            <DialogDescription>
              Detailed earnings statistics and performance metrics
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <TrendingUp className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Highest Earnings</p>
                      <p className="text-xl font-bold">{formatCurrency(earningsData.highestEarnings)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <TrendingDown className="h-8 w-8 text-red-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-500">Lowest Earnings</p>
                      <p className="text-xl font-bold">{formatCurrency(earningsData.lowestEarnings)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Platform Distribution</h4>
              <div className="grid grid-cols-4 gap-2">
                {["uber", "bolt", "indrive", "other"].map(platform => {
                  const count = earnings.filter(e => e.platformName === platform).length;
                  const percentage = earnings.length > 0 ? ((count / earnings.length) * 100).toFixed(1) : "0";
                  return (
                    <div key={platform} className="text-center p-2 border rounded">
                      <p className="text-xs uppercase font-medium">{platform}</p>
                      <p className="text-lg font-bold">{count}</p>
                      <p className="text-xs text-gray-500">{percentage}%</p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => setShowStatsDialog(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 