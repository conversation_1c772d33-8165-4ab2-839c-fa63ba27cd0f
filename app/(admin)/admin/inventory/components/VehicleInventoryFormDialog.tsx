"use client";

import React, { useState, useEffect, useRef } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  Car,
  FileText,
  MapPin,
  Upload,
  CheckCircle,
  AlertTriangle,
  Eye,
  X,
  Loader,
  Camera,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import FormImageUpload, { FormImageItem } from "@/components/FormImageUpload";
import type {
  InventoryVehicle,
  VehicleDocument,
  VehicleInventoryFormDialogProps,
} from "../../types/inventory";

import { DocumentUpload, DocumentDelete } from "@/lib/utils";

export default function VehicleInventoryFormDialog({
  isOpen,
  onClose,
  onConfirm,
  editingVehicle,
  mode,
  catalogItems,
  isSubmitting = false,
}: VehicleInventoryFormDialogProps) {
  const [formData, setFormData] = useState<InventoryVehicle>({
    catalogId: "",
    make: "",
    model: "",
    year: new Date().getFullYear(),
    color: "",
    vinNumber: "",
    registrationNumber: "",
    mileage: 0,
    condition: "new",
    status: "available",
    location: "",
    vehicleDocuments: [],
    notes: "",
  });

  // Separate state for the FormImageUpload component
  const [formImages, setFormImages] = useState<FormImageItem[]>([]);

  const [uploadingDocuments, setUploadingDocuments] = useState<Set<string>>(
    new Set()
  );
  const [dragOverDocument, setDragOverDocument] = useState<string | null>(null);

  const documentTypes = [
    {
      value: "registration",
      label: "Vehicle Registration Document",
      required: true,
    },
    { value: "license", label: "Vehicle License Document", required: true },
    {
      value: "dekra_inspection",
      label: "DEKRA Inspection Certificate",
      required: true,
    },
    { value: "insurance", label: "Insurance Certificate", required: true },
    { value: "service_history", label: "Service History", required: false },
    { value: "operator_license", label: "Operator License", required: true },
  ];

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  useEffect(() => {
    if (editingVehicle) {
      setFormData(editingVehicle);

      // Convert existing images to FormImageItem format
      if (editingVehicle.images && editingVehicle.images.length > 0) {
        const convertedImages: FormImageItem[] = editingVehicle.images.map(
          (img: any, index: number) => ({
            id: img.id || index,
            imageUrl: img.imageUrl,
            isPrimary: img.isPrimary,
          })
        );
        setFormImages(convertedImages);
      } else {
        setFormImages([]);
      }
    } else {
      setFormData({
        catalogId: "",
        make: "",
        model: "",
        year: new Date().getFullYear(),
        color: "",
        vinNumber: "",
        registrationNumber: "",
        mileage: 0,
        condition: "new",
        status: "available",
        location: "",
        vehicleDocuments: [],
        notes: "",
      });
      setFormImages([]);
    }
  }, [editingVehicle, isOpen]);

  const handleInputChange = (field: keyof InventoryVehicle, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const getDocumentStatus = (
    expiryDate?: string
  ): VehicleDocument["status"] => {
    if (!expiryDate) return "valid";

    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(
      now.getTime() + 30 * 24 * 60 * 60 * 1000
    );

    if (expiry < now) return "expired";
    if (expiry < thirtyDaysFromNow) return "expiring_soon";
    return "valid";
  };

  const handleDocumentUpload = async (
    documentType: string,
    file: File,
    expiryDate?: string
  ) => {
    try {
      setUploadingDocuments((prev) => new Set(prev).add(documentType));

      const uploadResult = await DocumentUpload(file, "vehicle-documents");

      if (uploadResult && uploadResult.path) {
        const documentTypeName =
          documentTypes.find((type) => type.value === documentType)?.label ||
          documentType;

        const newDocument: VehicleDocument = {
          id: `doc-${Date.now()}`,
          type: documentType as VehicleDocument["type"],
          name: documentTypeName,
          filePath: uploadResult.path,
          uploadDate: new Date().toISOString(),
          expiryDate: expiryDate || undefined,
          status: getDocumentStatus(expiryDate),
        };

        // Remove existing document of same type and add new one
        const updatedDocuments = (formData.vehicleDocuments || [])
          .filter((doc) => doc.type !== documentType)
          .concat(newDocument);

        handleInputChange("vehicleDocuments", updatedDocuments);
      }
    } catch (error) {
      console.error("Error uploading document:", error);
    } finally {
      setUploadingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(documentType);
        return newSet;
      });
    }
  };

  const handleDocumentRemove = async (documentType: string) => {
    const document = formData.vehicleDocuments?.find(
      (doc) => doc.type === documentType
    );
    if (!document) return;

    try {
      await DocumentDelete(document.filePath);
      const updatedDocuments = (formData.vehicleDocuments || []).filter(
        (doc) => doc.type !== documentType
      );
      handleInputChange("vehicleDocuments", updatedDocuments);
    } catch (error) {
      console.error("Error deleting document:", error);
    }
  };

  // Document Upload Section Component
  const DocumentUploadSection = ({
    documentType,
    existingDocument,
    isUploading,
    isDragOver,
    isReadOnly,
    onDocumentUpload,
    onDocumentRemove,
    onDragOver,
    onDragLeave,
  }: {
    documentType: { value: string; label: string; required: boolean };
    existingDocument?: VehicleDocument;
    isUploading: boolean;
    isDragOver: boolean;
    isReadOnly: boolean;
    onDocumentUpload: (type: string, file: File, expiryDate?: string) => void;
    onDocumentRemove: (type: string) => void;
    onDragOver: () => void;
    onDragLeave: () => void;
  }) => {
    const [expiryDate, setExpiryDate] = useState(
      existingDocument?.expiryDate || ""
    );
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDragOver = (e: React.DragEvent) => {
      e.preventDefault();
      onDragOver();
    };

    const handleDragLeave = (e: React.DragEvent) => {
      e.preventDefault();
      onDragLeave();
    };

    const handleDrop = (e: React.DragEvent) => {
      e.preventDefault();
      onDragLeave();

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        onDocumentUpload(documentType.value, files[0], expiryDate);
      }
    };

    const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        onDocumentUpload(documentType.value, file, expiryDate);
      }
    };

    const getStatusColor = (status: VehicleDocument["status"]) => {
      switch (status) {
        case "valid":
          return "bg-green-100 text-green-800 border-green-200";
        case "expiring_soon":
          return "bg-yellow-100 text-yellow-800 border-yellow-200";
        case "expired":
          return "bg-red-100 text-red-800 border-red-200";
        default:
          return "bg-gray-100 text-gray-800 border-gray-200";
      }
    };

    return (
      <div className="space-y-3">
        {/* Document Title and Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h4 className="font-medium text-gray-900">{documentType.label}</h4>
            {documentType.required && (
              <span className="text-xs text-red-500">*Required</span>
            )}
          </div>
          {existingDocument && (
            <Badge
              variant="outline"
              className={getStatusColor(existingDocument.status)}
            >
              {existingDocument.status === "valid" && (
                <CheckCircle size={12} className="mr-1" />
              )}
              {existingDocument.status !== "valid" && (
                <AlertTriangle size={12} className="mr-1" />
              )}
              {existingDocument.status.replace("_", " ")}
            </Badge>
          )}
        </div>

        {/* Upload Area - Full Width */}
        <div className="w-full">
          {existingDocument ? (
            <div className="flex items-center justify-between p-3 border rounded-lg bg-gray-50">
              <div className="flex items-center gap-2">
                <FileText size={16} className="text-gray-400" />
                <div>
                  <span className="text-sm font-medium">
                    {existingDocument.name}
                  </span>
                  <div className="text-xs text-gray-500">
                    Uploaded:{" "}
                    {new Date(existingDocument.uploadDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    window.open(existingDocument.filePath, "_blank")
                  }
                >
                  <Eye size={16} />
                </Button>
                {!isReadOnly && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => onDocumentRemove(documentType.value)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <X size={16} />
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${
                isDragOver
                  ? "border-[#009639] bg-green-50"
                  : "border-gray-300 hover:border-[#009639]"
              } ${isReadOnly ? "opacity-50 cursor-not-allowed" : ""}`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() =>
                !isReadOnly && !isUploading && fileInputRef.current?.click()
              }
            >
              <Upload
                size={32}
                className={`mx-auto mb-2 ${isDragOver ? "text-[#009639]" : "text-gray-400"}`}
              />
              <p className="text-sm text-gray-600 mb-1">
                {isDragOver
                  ? "Drop file here"
                  : "Drag & drop or click to upload"}
              </p>
              <p className="text-xs text-gray-400">
                PDF, JPG, PNG, DOC (Max 10MB)
              </p>
              <Input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                onChange={handleFileSelect}
                disabled={isReadOnly || isUploading}
                className="hidden"
              />
              {isUploading && (
                <div className="mt-2">
                  <Loader
                    size={16}
                    className="animate-spin mx-auto text-[#009639]"
                  />
                </div>
              )}
            </div>
          )}
        </div>

        {/* Expiry Date - Below Upload Area */}
        <div className="max-w-xs">
          <Label htmlFor={`expiry-${documentType.value}`}>
            Expiry Date (Optional)
          </Label>
          <Input
            id={`expiry-${documentType.value}`}
            type="date"
            value={expiryDate}
            onChange={(e) => setExpiryDate(e.target.value)}
            disabled={isReadOnly}
            className="mt-1"
          />
          {existingDocument?.expiryDate && (
            <p className="text-xs text-gray-500 mt-1">
              Expires:{" "}
              {new Date(existingDocument.expiryDate).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
    );
  };

  const handleCatalogSelect = (catalogId: string) => {
    const catalogItem = catalogItems.find(
      (item) => item.id.toString() === catalogId
    );
    if (catalogItem) {
      setFormData((prev) => ({
        ...prev,
        catalogId,
        make: catalogItem.make,
        model: catalogItem.model,
        year: catalogItem.year,
      }));
    }
  };

  const handleConfirm = () => {
    if (canConfirm()) {
      // Convert FormImageUpload data to the expected format
      const images = formImages.map((img) => ({
        id: img.id,
        imageUrl: img.imageUrl,
        isPrimary: img.isPrimary,
      }));

      // Include images in the form data
      const dataWithImages = {
        ...formData,
        images: images,
      };

      onConfirm(dataWithImages);
      // Don't call onClose here - let the parent handle it
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const canConfirm = () => {
    return (
      mode === "view" ||
      (formData.catalogId &&
        formData.color.trim() &&
        formData.vinNumber.trim() &&
        formData.registrationNumber.trim() &&
        formData.location.trim() &&
        !isSubmitting)
    );
  };

  const getDialogTitle = () => {
    switch (mode) {
      case "add":
        return "Add Vehicle to Inventory";
      case "edit":
        return "Edit Vehicle";
      case "view":
        return "Vehicle Details";
      default:
        return "";
    }
  };

  const getDialogDescription = () => {
    switch (mode) {
      case "add":
        return "Add a new vehicle instance to the inventory";
      case "edit":
        return "Update vehicle information and status";
      case "view":
        return "View detailed vehicle information";
      default:
        return "";
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "excellent":
        return "bg-green-100 text-green-800";
      case "good":
        return "bg-blue-100 text-blue-800";
      case "fair":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800";
      case "assigned":
        return "bg-blue-100 text-blue-800";
      case "maintenance":
        return "bg-yellow-100 text-yellow-800";
      case "inspection":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const isReadOnly = mode === "view";

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Car className="h-5 w-5 text-[#009639]" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Vehicle Selection/Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mode === "add" && (
                <div>
                  <Label htmlFor="catalogId">Vehicle Model *</Label>
                  <Select
                    onValueChange={handleCatalogSelect}
                    value={formData.catalogId}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select vehicle model from catalog..." />
                    </SelectTrigger>
                    <SelectContent>
                      {catalogItems.map((item) => (
                        <SelectItem key={item.id} value={item.id.toString()}>
                          {item.make} {item.model} {item.year}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {(formData.make || mode !== "add") && (
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>Make</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.make}
                    </div>
                  </div>
                  <div>
                    <Label>Model</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.model}
                    </div>
                  </div>
                  <div>
                    <Label>Year</Label>
                    <div className="mt-1 p-2 bg-gray-50 rounded border text-sm">
                      {formData.year}
                    </div>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="color">Color *</Label>
                  <Input
                    id="color"
                    value={formData.color}
                    onChange={(e) => handleInputChange("color", e.target.value)}
                    placeholder="e.g., White"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="mileage">Mileage (km)</Label>
                  <Input
                    id="mileage"
                    type="number"
                    value={formData.mileage}
                    onChange={(e) =>
                      handleInputChange("mileage", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vinNumber">VIN Number *</Label>
                  <Input
                    id="vinNumber"
                    value={formData.vinNumber}
                    onChange={(e) =>
                      handleInputChange("vinNumber", e.target.value)
                    }
                    placeholder="Vehicle identification number"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="registrationNumber">
                    Registration Number *
                  </Label>
                  <Input
                    id="registrationNumber"
                    value={formData.registrationNumber}
                    onChange={(e) =>
                      handleInputChange("registrationNumber", e.target.value)
                    }
                    placeholder="e.g., CA 123-456"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Status and Location */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5 text-[#009639]" />
                Status & Location
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="condition">Condition</Label>
                  {isReadOnly ? (
                    <div className="mt-1">
                      <Badge
                        variant="outline"
                        className={getConditionColor(formData.condition)}
                      >
                        {formData.condition.charAt(0).toUpperCase() +
                          formData.condition.slice(1)}
                      </Badge>
                    </div>
                  ) : (
                    <Select
                      onValueChange={(value) =>
                        handleInputChange("condition", value)
                      }
                      value={formData.condition}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="excellent">Excellent</SelectItem>
                        <SelectItem value="good">Good</SelectItem>
                        <SelectItem value="fair">Fair</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
                <div>
                  <Label htmlFor="status">Status</Label>
                  {isReadOnly ? (
                    <div className="mt-1">
                      <Badge
                        variant="outline"
                        className={getStatusColor(formData.status)}
                      >
                        {formData.status.charAt(0).toUpperCase() +
                          formData.status.slice(1)}
                      </Badge>
                    </div>
                  ) : (
                    <Select
                      onValueChange={(value) =>
                        handleInputChange("status", value)
                      }
                      value={formData.status}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="assigned">Assigned</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="inspection">Inspection</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                </div>
                <div>
                  <Label htmlFor="location">Location *</Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) =>
                      handleInputChange("location", e.target.value)
                    }
                    placeholder="e.g., Cape Town"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Camera className="h-5 w-5 text-[#009639]" />
                Vehicle Images
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormImageUpload
                images={formImages}
                onImagesChange={setFormImages}
                maxImages={10}
                readOnly={isReadOnly}
                compact={true}
              />
              <p className="text-xs text-gray-500 text-center mt-4">
                Upload photos of the actual vehicle for identification and
                documentation purposes
              </p>
            </CardContent>
          </Card>

          {/* Vehicle Documents Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5 text-[#009639]" />
                Vehicle Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {documentTypes.map((docType, index) => {
                  const existingDocument = formData.vehicleDocuments?.find(
                    (doc) => doc.type === docType.value
                  );
                  const isUploading = uploadingDocuments.has(docType.value);
                  const isDragOver = dragOverDocument === docType.value;

                  return (
                    <div key={docType.value}>
                      <DocumentUploadSection
                        documentType={docType}
                        existingDocument={existingDocument}
                        isUploading={isUploading}
                        isDragOver={isDragOver}
                        isReadOnly={isReadOnly}
                        onDocumentUpload={handleDocumentUpload}
                        onDocumentRemove={handleDocumentRemove}
                        onDragOver={() => setDragOverDocument(docType.value)}
                        onDragLeave={() => setDragOverDocument(null)}
                      />
                      {/* Line divider between document types (except for the last one) */}
                      {index < documentTypes.length - 1 && (
                        <div className="mt-8 border-b border-gray-200"></div>
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Additional Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange("notes", e.target.value)}
                placeholder="Any additional notes about this vehicle..."
                rows={3}
                readOnly={isReadOnly}
              />
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            {mode === "view" ? "Close" : "Cancel"}
          </Button>
          {mode !== "view" && (
            <Button
              onClick={handleConfirm}
              disabled={!canConfirm()}
              className="bg-[#009639] hover:bg-[#007A2F]"
            >
              {isSubmitting ? (
                <>
                  <Loader size={16} className="mr-2 animate-spin" />
                  {mode === "add" ? "Adding Vehicle..." : "Updating Vehicle..."}
                </>
              ) : (
                <>
                  <CheckCircle size={16} className="mr-2" />
                  {mode === "add" ? "Add Vehicle" : "Update Vehicle"}
                </>
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
