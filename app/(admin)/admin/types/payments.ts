import type { BasicAssignment } from "./assignments";
export interface PaymentRecord {
  id: string;
  assignmentId: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  paymentType: "weekly_lease" | "initiation_fee" | "late_fee" | "maintenance";
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "paid" | "overdue" | "pending" | "partial";
  paymentMethod?: "bank_transfer" | "cash" | "card";
  reference?: string;
  lateFee?: number;
  notes?: string;
}

export interface PaymentRecord {
  id: string;
  paymentType: "weekly_lease" | "initiation_fee" | "late_fee" | "maintenance";
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "paid" | "overdue" | "pending" | "partial";
  paymentMethod?: "bank_transfer" | "cash" | "card";
  reference?: string;
  lateFee?: number;
  notes?: string;
}

export interface PaymentRecordDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (paymentData: any) => void;
  assignments: BasicAssignment[];
  selectedAssignmentId?: string;
}
