// Base interface with common application fields
export interface BaseApplication {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  applicationType:
    | "vehicle_lease"
    | "rental_application"
    | "co_ownership_application"
    | "vehicle_listing";
  listingType?: "rental" | "fractional" | "lease-to-own";
  weeklyRate?: number;
  // EARN side application fields
  applicantAge?: number;
  applicantGender?: "male" | "female" | "other";
  drivingExperienceYears?: number;
  documentsVerified?: boolean;
  matchesPreferences?: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
}

// Experience interface for reusability
export interface ApplicationExperience {
  hasExperience: boolean;
  company?: string;
  duration?: string;
  profileNumber?: string;
  workType?: string;
}

// Document interface for reusability
export interface ApplicationDocument {
  name: string;
  uploaded: boolean;
  verified?: boolean | "failed";
  fileUrl?: string;
}

// Original Application interface - extends base with specific fields
export interface Application extends BaseApplication {
  vehicleName: string;
  decisionDate?: string;
  decisionReason?: string;
  experience?: Omit<ApplicationExperience, "workType">;
  documents?: Omit<ApplicationDocument, "fileUrl">[];
}

// Extended ApplicationData interface with additional fields
export interface ApplicationData extends BaseApplication {
  applicantPhone: string;
  vehicleId?: string;
  vehicleName?: string;
  // E-hailing specific fields
  experience?: ApplicationExperience;
  initiationFee?: number;
  paymentArrangement?: boolean;
  // Vehicle listing specific fields
  make?: string;
  model?: string;
  year?: number;
  condition?: "new" | "used";
  mileage?: number;
  askingPrice?: number;
  fractionOffer?: number;
  location?: string;
  description?: string;
  documents: ApplicationDocument[];
}
