export interface DriverPerformance {
  id: string;
  driverName: string;
  driverEmail: string;
  driverPhone?: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "active" | "warning" | "terminated";

  // Payment Performance
  paymentScore: number;
  totalPayments: number;
  onTimePayments: number;
  latePayments: number;
  missedPayments: number;
  totalOutstanding: number;

  // Vehicle Care
  vehicleConditionScore: number;
  maintenanceRequests: number;
  damageReports: number;
  lastInspectionDate: string;
  lastInspectionScore: number;

  // Platform Performance
  platformRating?: number;
  totalTrips?: number;
  monthlyEarnings?: number;

  // Compliance
  documentsUpToDate: boolean;
  insuranceCurrent: boolean;
  licenseValid: boolean;
  activeDisputes: number;
  resolvedDisputes: number;
  warnings: number;
  lastContactDate: string;
  notes?: string;
}

export interface PerformanceMetric {
  date: string;
  paymentScore: number;
  vehicleScore: number;
  platformRating: number;
  trips: number;
  earnings: number;
}

export interface DriverPerformanceDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  driver: DriverPerformance | null;
}
