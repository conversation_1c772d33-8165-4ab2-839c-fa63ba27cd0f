import type { VehicleCatalogItem } from "@/drizzle-actions/admin/vehicle-catalog";

export interface VehicleDocument {
  id?: string;
  type:
    | "registration"
    | "license"
    | "dekra_inspection"
    | "insurance"
    | "service_history"
    | "operator_license"
    | "other";
  name: string;
  filePath: string;
  uploadDate: string;
  expiryDate?: string;
  status: "valid" | "expired" | "expiring_soon";
}

export interface InventoryVehicle {
  id?: string; // Optional for new vehicles
  catalogId: string;
  make: string;
  model: string;
  year: number;
  color: string;
  vinNumber: string;
  registrationNumber: string;
  mileage: number;
  condition: "used" | "new";
  status: "available" | "assigned" | "maintenance" | "inspection";
  location: string;
  assignedDriver?: {
    id: string;
    name: string;
    email: string;
    assignedDate: string;
  };
  vehicleDocuments?: VehicleDocument[]; // Detailed document storage
  images?: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>; // Vehicle images
  lastInspection?: string; // Optional since new vehicles might not have this
  nextService?: string;
  createdAt?: string;
  notes?: string;
}

export interface VehicleInventoryFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (vehicleData: InventoryVehicle) => void;
  editingVehicle?: InventoryVehicle | null;
  mode: "add" | "edit" | "view";
  catalogItems: VehicleCatalogItem[];
  isSubmitting?: boolean;
}
