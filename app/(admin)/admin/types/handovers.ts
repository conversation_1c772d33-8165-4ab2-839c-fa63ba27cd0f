import type { Assignment } from "./assignments";

export interface VehicleHandoverDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (handoverData: HandoverData) => void;
  assignment: Assignment | null;
}

export interface HandoverData {
  vehicleId: string;
  currentDriverId: string;
  handoverType: "return" | "transfer" | "maintenance";
  newDriverId?: string;
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  notes?: string;
  checklist: {
    vehicleInspection: boolean;
    fuelLevel: string;
  };
}
