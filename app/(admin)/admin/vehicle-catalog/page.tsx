import { getUserAttributes } from "@/lib/serverUserAttributes";
import {
  getVehicleCatalogItems,
  getVehicleCatalogFilterOptions,
  type VehicleCatalogItem,
} from "@/drizzle-actions/admin/vehicle-catalog";
import VehicleCatalogClient from "./components/VehicleCatalogClient";

interface VehicleCatalogPageProps {
  searchParams: {
    search?: string;
    category?: string;
    fuelType?: string;
    status?: string;
  };
}

export default async function VehicleCatalogPage({
  searchParams,
}: VehicleCatalogPageProps) {
  // Server-side authentication
  const userAttributes = await getUserAttributes();

  if (!userAttributes?.["custom:db_id"]) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600">Access Denied</h1>
        <p className="text-gray-600 mt-2">
          Please log in to access the vehicle catalog.
        </p>
      </div>
    );
  }

  // Server-side data fetching
  try {
    const [catalogItems, filterOptions] = await Promise.all([
      getVehicleCatalogItems(),
      getVehicleCatalogFilterOptions(),
    ]);

    return (
      <VehicleCatalogClient
        initialCatalogItems={catalogItems}
        initialFilterOptions={filterOptions}
        userAttributes={userAttributes}
        initialSearchParams={searchParams}
      />
    );
  } catch (error) {
    console.error("Error loading vehicle catalog:", error);

    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600">
          Error Loading Catalog
        </h1>
        <p className="text-gray-600 mt-2">
          Failed to load vehicle catalog items. Please try refreshing the page.
        </p>
      </div>
    );
  }
}
