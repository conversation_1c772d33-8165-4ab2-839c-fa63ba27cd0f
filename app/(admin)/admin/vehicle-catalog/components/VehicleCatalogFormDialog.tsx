"use client";

import React, { useState, useEffect, useRef } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  Car,
  DollarSign,
  Settings,
  Plus,
  X,
  CheckCircle,
  Upload,
  Camera,
  Loader,
} from "lucide-react";
import { DocumentUpload } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import FormImageUpload, { FormImageItem } from "@/components/FormImageUpload";
import type {
  VehicleCatalogItem,
  VehicleCatalogFormDialogProps,
} from "../../types/vehicle-catalog";

// Import the vehicle database actions
import {
  getAllVehicleMakes,
  getVehicleModelsByMakeId,
  getVehicleVariantsByModelId,
} from "@/drizzle-actions/vehicle-domain";

// Import the actual database types instead of defining custom ones
import type { VehicleMakeRead } from "@/types/vehicle-makes";
import type { VehicleModelRead } from "@/types/vehicle-model";
import type { VehicleVariantRead } from "@/types/vehicle-variant";

export default function VehicleCatalogFormDialog({
  isOpen,
  onClose,
  onConfirm,
  editingVehicle,
  mode = "add",
}: VehicleCatalogFormDialogProps) {
  console.log("🚀 VehicleCatalogFormDialog render:", {
    isOpen,
    mode,
    editingVehicle: editingVehicle?.id,
  });

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const [formData, setFormData] = useState<VehicleCatalogItem>({
    id: 0,
    partyId: 0,
    make: "",
    model: "",
    year: new Date().getFullYear(),
    category: "sedan",
    fuelType: "petrol",
    ehailingEligible: true,
    weeklyRate: 0,
    initiationFee: 0,
    features: [],
    platforms: [],
    images: [],
    isActive: true,
    description: "",
    imageUrl: "",
  });

  // Separate state for the FormImageUpload component
  const [formImages, setFormImages] = useState<FormImageItem[]>([]);

  // Vehicle dropdown state - update types
  const [makes, setMakes] = useState<VehicleMakeRead[]>([]);
  const [models, setModels] = useState<VehicleModelRead[]>([]);
  const [variants, setVariants] = useState<VehicleVariantRead[]>([]);
  const [selectedMake, setSelectedMake] = useState<VehicleMakeRead | null>(
    null
  );
  const [selectedModel, setSelectedModel] = useState<VehicleModelRead | null>(
    null
  );
  const [selectedVariant, setSelectedVariant] =
    useState<VehicleVariantRead | null>(null);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingVariants, setLoadingVariants] = useState(false);

  const [newFeature, setNewFeature] = useState("");
  const [newPlatform, setNewPlatform] = useState("");

  // Load makes when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadMakes();
    }
  }, [isOpen]);

  // Load models when make is selected
  useEffect(() => {
    if (selectedMake) {
      loadModels(selectedMake.id);
    } else {
      setModels([]);
      setSelectedModel(null);
      setVariants([]);
      setSelectedVariant(null);
    }
  }, [selectedMake]);

  // Load variants when model is selected
  useEffect(() => {
    if (selectedModel) {
      loadVariants(selectedModel.id);
    } else {
      setVariants([]);
      setSelectedVariant(null);
    }
  }, [selectedModel]);

  // Load makes
  const loadMakes = async () => {
    try {
      setLoadingMakes(true);
      const makesData = await getAllVehicleMakes();
      setMakes(makesData);
    } catch (error) {
      console.error("Error loading makes:", error);
    } finally {
      setLoadingMakes(false);
    }
  };

  // Load models by make
  const loadModels = async (makeId: number) => {
    try {
      setLoadingModels(true);
      setModels([]);
      const modelsData = await getVehicleModelsByMakeId(makeId);
      setModels(modelsData);
    } catch (error) {
      console.error("Error loading models:", error);
    } finally {
      setLoadingModels(false);
    }
  };

  // Load variants by model
  const loadVariants = async (modelId: number) => {
    try {
      setLoadingVariants(true);
      setVariants([]);
      const variantsData = await getVehicleVariantsByModelId(modelId);
      setVariants(variantsData);
    } catch (error) {
      console.error("Error loading variants:", error);
    } finally {
      setLoadingVariants(false);
    }
  };

  useEffect(() => {
    console.log("🔄 Dialog useEffect triggered:", {
      isOpen,
      editingVehicle: editingVehicle?.id,
    });

    if (editingVehicle) {
      setFormData(editingVehicle);

      // Convert existing images to FormImageItem format
      const convertedImages: FormImageItem[] = editingVehicle.images.map(
        (img: any, index: number) => ({
          id: img.id || index,
          imageUrl: img.imageUrl,
          isPrimary: img.isPrimary,
        })
      );
      setFormImages(convertedImages);

      // Try to find and set the selected make/model/variant based on the text values
      // This is a fallback for existing data that was stored as text
      if (editingVehicle.make && makes.length > 0) {
        const foundMake = makes.find(
          (m) => m.name.toLowerCase() === editingVehicle.make.toLowerCase()
        );
        if (foundMake) {
          setSelectedMake(foundMake);
        }
      }
    } else {
      setFormData({
        id: 0,
        partyId: 0,
        make: "",
        model: "",
        year: new Date().getFullYear(),
        category: "sedan",
        fuelType: "petrol",
        ehailingEligible: true,
        weeklyRate: 0,
        initiationFee: 0,
        features: [],
        platforms: [],
        images: [],
        isActive: true,
        description: "",
        imageUrl: "",
      });
      setFormImages([]);
      setSelectedMake(null);
      setSelectedModel(null);
      setSelectedVariant(null);
    }

    // Reset local state when dialog opens/closes
    if (!isOpen) {
      console.log("🧹 Cleaning up dialog state (isOpen = false)");
      // Reset all local state when dialog closes
      setNewFeature("");
      setNewPlatform("");
      setFormImages([]);
      setSelectedMake(null);
      setSelectedModel(null);
      setSelectedVariant(null);
    }
  }, [editingVehicle, isOpen, makes]);

  const handleInputChange = (field: keyof VehicleCatalogItem, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleMakeChange = (makeId: string) => {
    const make = makes.find((m) => m.id === parseInt(makeId));
    setSelectedMake(make || null);
    setFormData((prev) => ({
      ...prev,
      make: make?.name || "",
      model: "", // Reset model when make changes
      year: new Date().getFullYear(), // Reset year when make changes
    }));
  };

  const handleModelChange = (modelId: string) => {
    const model = models.find((m) => m.id === parseInt(modelId));
    setSelectedModel(model || null);
    setFormData((prev) => ({
      ...prev,
      model: model?.model || "",
      year: model?.first_year || new Date().getFullYear(), // Set year to first year of model
    }));
  };

  const handleVariantChange = (variantId: string) => {
    if (variantId === "none") {
      setSelectedVariant(null);
      return;
    }

    const variant = variants.find((v) => v.id === parseInt(variantId));
    setSelectedVariant(variant || null);
    setFormData((prev) => ({
      ...prev,
      year: variant?.year || prev.year,
      fuelType: variant?.fuel_type || prev.fuelType,
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature("");
    }
  };

  const removeFeature = (feature: string) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((f) => f !== feature),
    }));
  };

  const addPlatform = () => {
    if (
      newPlatform.trim() &&
      !formData.platforms?.includes(newPlatform.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        platforms: [...(prev.platforms || []), newPlatform.trim()],
      }));
      setNewPlatform("");
    }
  };

  const removePlatform = (platform: string) => {
    setFormData((prev) => ({
      ...prev,
      platforms: prev.platforms?.filter((p) => p !== platform),
    }));
  };

  const handleConfirm = () => {
    console.log("🟢 VehicleCatalogFormDialog handleConfirm called");
    if (canConfirm()) {
      // Convert FormImageUpload data to the expected format
      const images = formImages.map((img) => ({
        id: img.id,
        imageUrl: img.imageUrl,
        isPrimary: img.isPrimary,
      }));

      // Prepare data with foreign key IDs
      const dataWithForeignKeys = {
        ...formData,
        makeId: selectedMake?.id,
        modelId: selectedModel?.id,
        variantId: selectedVariant?.id,
        images: images,
        // For backward compatibility, set imageUrl to the primary image
        imageUrl:
          images.find((img) => img.isPrimary)?.imageUrl ||
          images[0]?.imageUrl ||
          "",
      };

      onConfirm(dataWithForeignKeys);
      // Don't call onClose here - let the parent handle it after successful submission
    }
  };

  const handleOpenChange = (open: boolean) => {
    console.log("🔄 VehicleCatalogFormDialog handleOpenChange:", open);
    if (!open) {
      // Reset all local state when dialog closes
      setNewFeature("");
      setNewPlatform("");
      setFormImages([]);
      onClose();
    }
  };

  const canConfirm = () => {
    return (
      // Check that make and model are selected from dropdowns (foreign key IDs exist)
      selectedMake &&
      selectedMake.id &&
      selectedModel &&
      selectedModel.id &&
      // Also check text fields for safety
      formData.make.trim() !== "" &&
      formData.model.trim() !== "" &&
      formData.year > 1900 &&
      formData.weeklyRate &&
      formData.weeklyRate > 0 &&
      formData.initiationFee &&
      formData.initiationFee >= 0
    );
  };

  const getDialogTitle = () => {
    switch (mode) {
      case "add":
        return "Add Vehicle Model";
      case "edit":
        return "Edit Vehicle Model";
      case "view":
        return "Vehicle Model Details";
      default:
        return editingVehicle ? "Edit Vehicle Model" : "Add Vehicle Model";
    }
  };

  const getDialogDescription = () => {
    switch (mode) {
      case "add":
        return "Add a new vehicle model to the catalog with specifications and pricing";
      case "edit":
        return "Update the vehicle model specifications and pricing";
      case "view":
        return "View detailed vehicle model information";
      default:
        return editingVehicle
          ? "Update the vehicle model specifications and pricing"
          : "Add a new vehicle model to the catalog with specifications and pricing";
    }
  };

  const isReadOnly = mode === "view";

  console.log("🎯 VehicleCatalogFormDialog state:", {
    isOpen,
    canConfirm: canConfirm(),
    dialogTitle: getDialogTitle(),
  });

  const commonFeatures = [
    "Air Conditioning",
    "Power Steering",
    "Electric Windows",
    "Radio/USB",
    "ABS",
    "Airbags",
    "Central Locking",
    "Bluetooth",
    "GPS Navigation",
    "Reverse Camera",
  ];

  const commonPlatforms = ["Uber", "Bolt", "InDriver"];

  console.log("📦 About to render Dialog component with:", {
    isOpen,
    mode,
    canConfirm: canConfirm(),
    dialogTitle: getDialogTitle(),
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Car className="h-5 w-5 text-[#009639]" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                {/* Make Dropdown */}
                <div>
                  <Label htmlFor="make">Make *</Label>
                  <Select
                    value={selectedMake?.id?.toString() || ""}
                    onValueChange={handleMakeChange}
                    disabled={isReadOnly || loadingMakes}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue
                        placeholder={
                          loadingMakes ? "Loading makes..." : "Select make"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {makes.map((make) => (
                        <SelectItem key={make.id} value={make.id.toString()}>
                          {make.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Model Dropdown */}
                <div>
                  <Label htmlFor="model">Model *</Label>
                  <Select
                    value={selectedModel?.id?.toString() || ""}
                    onValueChange={handleModelChange}
                    disabled={isReadOnly || loadingModels || !selectedMake}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue
                        placeholder={
                          loadingModels
                            ? "Loading models..."
                            : !selectedMake
                              ? "Select make first"
                              : "Select model"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model.id} value={model.id.toString()}>
                          {model.model}
                          {model.first_year && model.last_year && (
                            <span className="text-gray-500 ml-2">
                              ({model.first_year}-{model.last_year})
                            </span>
                          )}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Year Input (can be manually adjusted) */}
                <div>
                  <Label htmlFor="year">Year *</Label>
                  <Input
                    id="year"
                    type="number"
                    value={formData.year}
                    onChange={(e) =>
                      handleInputChange("year", Number(e.target.value))
                    }
                    min="1900"
                    max={new Date().getFullYear() + 2}
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              {/* Variant Dropdown (optional) */}
              {variants.length > 0 && (
                <div>
                  <Label htmlFor="variant">Variant (Optional)</Label>
                  <Select
                    value={selectedVariant?.id?.toString() || "none"}
                    onValueChange={handleVariantChange}
                    disabled={isReadOnly || loadingVariants}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue
                        placeholder={
                          loadingVariants
                            ? "Loading variants..."
                            : "Select variant (optional)"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No specific variant</SelectItem>
                      {variants.map((variant) => (
                        <SelectItem
                          key={variant.id}
                          value={variant.id.toString()}
                        >
                          {variant.name}
                          {variant.trim_name && (
                            <span className="text-gray-500 ml-2">
                              ({variant.trim_name})
                            </span>
                          )}
                          <span className="text-gray-500 ml-2">
                            - {variant.year} {variant.fuel_type}{" "}
                            {variant.transmission}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("category", value)
                    }
                    value={formData.category}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sedan">Sedan</SelectItem>
                      <SelectItem value="suv">SUV</SelectItem>
                      <SelectItem value="hatchback">Hatchback</SelectItem>
                      <SelectItem value="bakkie">Bakkie</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="fuelType">Fuel Type</Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("fuelType", value)
                    }
                    value={formData.fuelType}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="petrol">Petrol</SelectItem>
                      <SelectItem value="diesel">Diesel</SelectItem>
                      <SelectItem value="electric">Electric</SelectItem>
                      <SelectItem value="hybrid">Hybrid</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) =>
                    handleInputChange("isActive", e.target.checked)
                  }
                  className="rounded border-gray-300"
                  disabled={isReadOnly}
                />
                <Label htmlFor="isActive" className="text-sm">
                  Active in catalog
                </Label>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Brief description of the vehicle model..."
                  className="mt-1"
                  rows={3}
                  readOnly={isReadOnly}
                />
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Camera className="h-5 w-5 text-[#009639]" />
                Vehicle Images
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FormImageUpload
                images={formImages}
                onImagesChange={setFormImages}
                maxImages={10}
                readOnly={isReadOnly}
                compact={true}
              />
              <p className="text-xs text-gray-500 text-center mt-4">
                High-quality images help showcase the vehicle model to potential
                lessees
              </p>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <DollarSign className="h-5 w-5 text-[#009639]" />
                Pricing & Terms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="weeklyRate">Weekly Rate *</Label>
                  <Input
                    id="weeklyRate"
                    type="number"
                    value={formData.weeklyRate}
                    onChange={(e) =>
                      handleInputChange("weeklyRate", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="initiationFee">Initiation Fee</Label>
                  <Input
                    id="initiationFee"
                    type="number"
                    value={formData.initiationFee}
                    onChange={(e) =>
                      handleInputChange("initiationFee", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="h-5 w-5 text-[#009639]" />
                Vehicle Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Add a feature..."
                  onKeyPress={(e) => e.key === "Enter" && addFeature()}
                  readOnly={isReadOnly}
                />
                <Button onClick={addFeature} size="sm" disabled={isReadOnly}>
                  <Plus size={16} />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {commonFeatures.map((feature) => (
                  <Button
                    key={feature}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!formData.features.includes(feature)) {
                        setFormData((prev) => ({
                          ...prev,
                          features: [...prev.features, feature],
                        }));
                      }
                    }}
                    disabled={formData.features.includes(feature) || isReadOnly}
                    className="text-xs"
                  >
                    {feature}
                  </Button>
                ))}
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.features.map((feature) => (
                  <Badge
                    key={feature}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {feature}
                    {!isReadOnly && (
                      <button
                        onClick={() => removeFeature(feature)}
                        className="ml-1 hover:text-red-600"
                        title={`Remove ${feature}`}
                        aria-label={`Remove ${feature}`}
                      >
                        <X size={12} />
                      </button>
                    )}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Suitable Platforms */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <CheckCircle className="h-5 w-5 text-[#009639]" />
                Suitable For Platforms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newPlatform}
                  onChange={(e) => setNewPlatform(e.target.value)}
                  placeholder="Add a platform..."
                  onKeyPress={(e) => e.key === "Enter" && addPlatform()}
                  readOnly={isReadOnly}
                />
                <Button onClick={addPlatform} size="sm" disabled={isReadOnly}>
                  <Plus size={16} />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {commonPlatforms.map((platform) => (
                  <Button
                    key={platform}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!formData.platforms?.includes(platform)) {
                        setFormData((prev) => ({
                          ...prev,
                          platforms: [...(prev.platforms || []), platform],
                        }));
                      }
                    }}
                    disabled={
                      formData.platforms?.includes(platform) || isReadOnly
                    }
                    className="text-xs"
                  >
                    {platform}
                  </Button>
                ))}
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.platforms?.map((platform) => (
                  <Badge
                    key={platform}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {platform}
                    {!isReadOnly && (
                      <button
                        onClick={() => removePlatform(platform)}
                        className="ml-1 hover:text-red-600"
                        title={`Remove ${platform}`}
                        aria-label={`Remove ${platform}`}
                      >
                        <X size={12} />
                      </button>
                    )}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            {mode === "view" ? "Close" : "Cancel"}
          </Button>
          {mode !== "view" && (
            <Button
              onClick={handleConfirm}
              disabled={!canConfirm()}
              className="bg-[#009639] hover:bg-[#007A2F]"
            >
              <CheckCircle size={16} className="mr-2" />
              {mode === "edit" ? "Update Model" : "Add Model"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
