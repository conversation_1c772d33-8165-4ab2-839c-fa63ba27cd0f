"use client";

import { useState, useEffect } from "react";
import { expressInterestInListing } from "@/actions/listing-interest";
import { getCachedUserAttributes } from "@/lib/userAttributes";

export default function TestPartyCreationPage() {
  const [userAttributes, setUserAttributes] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [testResult, setTestResult] = useState<string>("");

  useEffect(() => {
    const loadUserInfo = async () => {
      try {
        const attributes = await getCachedUserAttributes();
        setUserAttributes(attributes);
        console.log("User attributes:", attributes);
      } catch (error) {
        console.error("Error loading user attributes:", error);
        setUserAttributes({ error: error instanceof Error ? error.message : "Unknown error" });
      } finally {
        setLoading(false);
      }
    };

    loadUserInfo();
  }, []);

  const testListingInterest = async () => {
    try {
      setTestResult("Testing listing interest...");
      
      // Test with a dummy listing ID (replace with an actual listing ID from your database)
      await expressInterestInListing(1);
      setTestResult("Success! Interest expressed in listing.");
    } catch (error) {
      console.error("Test error:", error);
      setTestResult(`Error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  };

  if (loading) {
    return <div className="p-6">Loading user information...</div>;
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Debug: User Authentication & Listing Interest</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">User Attributes</h2>
          <pre className="text-sm bg-white p-3 rounded border overflow-auto">
            {JSON.stringify(userAttributes, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Party ID Test</h2>
          <p className="mb-2">
            <strong>custom:db_id:</strong> {userAttributes?.["custom:db_id"] || "NOT SET"}
          </p>
          <p className="mb-2">
            <strong>Parsed as number:</strong> {
              userAttributes?.["custom:db_id"] 
                ? parseInt(userAttributes["custom:db_id"])
                : "N/A"
            }
          </p>
          <p className="mb-2">
            <strong>Is valid number:</strong> {
              userAttributes?.["custom:db_id"] 
                ? !isNaN(parseInt(userAttributes["custom:db_id"]))
                : false
            } {userAttributes?.["custom:db_id"] ? "✅" : "❌"}
          </p>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Listing Interest Test</h2>
          <button 
            onClick={testListingInterest}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mb-2"
          >
            Test Express Interest
          </button>
          {testResult && (
            <div className={`p-3 rounded text-sm ${
              testResult.includes("Error") ? "bg-red-100 text-red-800" : "bg-green-100 text-green-800"
            }`}>
              {testResult}
            </div>
          )}
        </div>

        <div className="bg-yellow-100 p-4 rounded-lg">
          <h3 className="font-semibold mb-2">Common Issues & Solutions:</h3>
          <ul className="text-sm space-y-1">
            <li>• If <code>custom:db_id</code> is missing, the user needs to complete account setup</li>
            <li>• If it's not a valid number, there's a data inconsistency issue</li>
            <li>• Check the database to ensure the party ID exists in the <code>party</code> table</li>
            <li>• Verify the user is properly authenticated with Amplify</li>
          </ul>
        </div>
      </div>
    </div>
  );
} 