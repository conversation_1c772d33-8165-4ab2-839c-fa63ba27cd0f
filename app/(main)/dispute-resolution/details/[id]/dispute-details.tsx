"use client";

import { useState } from "react";
import { use<PERSON>outer } from "next/navigation";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ArrowLeft,
  Car,
  Calendar,
  FileText,
  Users,
  MessageSquare,
  BookImage,
} from "lucide-react";
import {
  DisputeCommentCreate,
  DisputeRead,
  DisputeStatus,
  DisputeType,
  Priority,
} from "@/types/disputes";
import type { CompanyMembershipRead } from "@/types/company-ownerships";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import CommentItem from "./disputes-comments";
import { createDisputeComment } from "@/actions/dispute-comments";
import VehicleImageList from "./images-list";

export const commentSchema = z.object({
  comment: z.string().min(1, "Comment is required"),
  dispute_id: z.number().min(1, "Dispute ID is required"),
  user_id: z.number().min(1, "User ID is required"),
  reply_to_comment_id: z.number().min(1).optional(), // Add optional reply_to_comment_id
});
interface DisputeDetailsScreenProps {
  dispute: DisputeRead;
  ownershipData: CompanyMembershipRead;
  loggedInUserPartyId: number;
  vehicles: VehicleReadWithModelAndParty[];
}

export default function DisputeDetailsScreen({
  dispute,
  ownershipData,
  loggedInUserPartyId,
  vehicles,
}: DisputeDetailsScreenProps) {
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const form = useForm<DisputeCommentCreate>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      comment: "",
      dispute_id: dispute.id,
      user_id: loggedInUserPartyId,
    },
  });

  const disputeTypes = [
    {
      id: DisputeType.VEHICLE_DAMAGE,
      label: "Vehicle Damage",
      icon: <Car size={16} />,
    },
    { id: DisputeType.BOOKING, label: "Booking", icon: <Calendar size={16} /> },
    {
      id: DisputeType.MAINTENANCE_COST_DISPUTE,
      label: "Cost Dispute",
      icon: <FileText size={16} />,
    },
    {
      id: DisputeType.VEHICLE_MAINTENANCE,
      label: "Vehicle Maintenance",
      icon: <Users size={16} />,
    },
    { id: DisputeType.OTHER, label: "Other", icon: <Users size={16} /> },
  ];

  const priorities = [
    {
      id: Priority.HIGH,
      label: "High Priority",
      color: "bg-[#ffe6e6] text-[#7A0000]",
    },
    {
      id: Priority.MEDIUM,
      label: "Medium Priority",
      color: "bg-[#fff8e6] text-[#7A6500]",
    },
    {
      id: Priority.LOW,
      label: "Low Priority",
      color: "bg-[#e6ffe6] text-[#007A2F]",
    },
  ];

  const onSubmit = async (data: DisputeCommentCreate) => {
    setSubmitting(true);
    setError(null);
    try {
      await createDisputeComment(data);
      form.reset(); // Reset form after successful submission
    } catch (err: any) {
      setError(
        err instanceof Error
          ? err.message
          : "Failed to submit comment. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };

  const vehicle = vehicles.find((v) => v.id === dispute.vehicle_id);
  const partyLogging = ownershipData.individuals.find(
    (ind) => ind.individual.party_id === dispute.party_logging
  );
  const partyOffending = ownershipData.individuals.find(
    (ind) => ind.individual.party_id === dispute.party_offending
  );
  const company = ownershipData.companies.find(
    (c) => c.id === dispute.company_id
  );

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </Button>
          <h1 className="text-xl font-bold text-white">Dispute Details</h1>
        </div>
      </div>

      {/* Dispute Details */}
      <div className="p-4">
        <Card className="bg-white rounded-xl shadow-md border-gray-100">
          <CardContent className="p-4">
            <h3 className="text-[#333333] font-medium mb-3">
              Dispute Information
            </h3>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-[#333333] font-medium">
                  Dispute Name
                </p>
                <p className="text-sm text-[#333333]">{dispute.name}</p>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">
                  Description
                </p>
                <p className="text-sm text-[#333333]">{dispute.description}</p>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">
                  Dispute Type
                </p>
                <div className="flex items-center space-x-2">
                  {
                    disputeTypes.find(
                      (type) => type.id === dispute.dispute_type
                    )?.icon
                  }
                  <p className="text-sm text-[#333333]">
                    {
                      disputeTypes.find(
                        (type) => type.id === dispute.dispute_type
                      )?.label
                    }
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">Priority</p>
                <span
                  className={`px-3 py-1 rounded-full text-sm ${
                    priorities.find((p) => p.id === dispute.priority)?.color
                  }`}
                >
                  {priorities.find((p) => p.id === dispute.priority)?.label}
                </span>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">Vehicle</p>
                <p className="text-sm text-[#333333]">
                  {vehicle
                    ? `${vehicle.model?.model} ${vehicle.model?.make?.name}`
                    : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">
                  Party Logging
                </p>
                <p className="text-sm text-[#333333]">
                  {partyLogging
                    ? `${partyLogging.individual.id} ${partyLogging.individual.last_name} ${
                        partyLogging.individual.party_id === loggedInUserPartyId
                          ? "(You)"
                          : ""
                      }`
                    : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">
                  Party Offending
                </p>
                <p className="text-sm text-[#333333]">
                  {partyOffending
                    ? `${partyOffending.individual.first_name} ${partyOffending.individual.last_name}`
                    : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm text-[#333333] font-medium">Company</p>
                <p className="text-sm text-[#333333]">
                  {company?.name || "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white rounded-xl shadow-md border-gray-100 mt-4">
          <h3 className="text-[#333333] font-medium mb-3 flex items-center">
            <BookImage size={16} className="mr-2" />
            Media
          </h3>
          <VehicleImageList dispute={dispute} />
        </Card>

        {/* Comments Section */}
        <Card className="bg-white rounded-xl shadow-md border-gray-100 mt-4">
          <CardContent className="p-4">
            <h3 className="text-[#333333] font-medium mb-3 flex items-center">
              <MessageSquare size={16} className="mr-2" />
              Comments
            </h3>
            {dispute.comments.length === 0 ? (
              <p className="text-sm text-gray-500">No comments yet.</p>
            ) : (
              <div className="space-y-2">
                <CommentItem
                  replyComments={dispute.comments}
                  loggedInUserPartyId={loggedInUserPartyId}
                  ownershipData={ownershipData}
                  dispute={dispute}
                  AllComments={dispute.comments}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Comment Form */}
        {dispute.dispute_status !== DisputeStatus.RESOLVED && (
          <Card className="bg-white rounded-xl shadow-md border-gray-100 mt-4">
            <CardContent className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">Add a Comment</h3>
              {error && (
                <div className="mb-4 p-2 bg-[#ffe6e6] rounded-lg shadow-sm">
                  <p className="text-xs text-[#7A0000]">{error}</p>
                </div>
              )}
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="comment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm text-[#333333] font-medium">
                          Comment
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Enter your comment"
                            className="w-full mt-1 p-2 border border-gray-200 rounded-lg text-sm text-[#333333] focus:ring-[#009639] focus:border-[#009639]"
                            rows={4}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    disabled={submitting}
                    className="w-full py-3 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full font-semibold shadow-md"
                  >
                    {submitting ? "Submitting..." : "Post Comment"}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
