"use server";
import DisputeResolutionScreen from "./dispute-resolution";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import MissingParty from "@/components/missing-party";
import { getAllDisputes } from "@/actions/dispute";

export default async function DisputeResolution() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;
  const disputes = await getAllDisputes();
  return <DisputeResolutionScreen disputes={disputes} />;
}
