"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { deprecated_BookingRead } from "@/types/bookings";
import { IndividualRead } from "@/types/individuals";
import { VehicleReadWithModelAndParty } from "@/types/vehicles";
import type { VehicleMediaRead } from "@/types/vehicles";
import { getUrl } from "aws-amplify/storage";
import type { ContactPointRead } from "@/types/contact-points";
import {
  Car,
  Clock,
  Calendar,
  ChevronRight,
  CheckCircle,
  Mail,
  Phone,
  MapPin,
  Info,
} from "lucide-react";


export default function BookingConfirmationScreen({
  booking,
  individual,
  vehicle,
  contacts,
}: {
  booking: deprecated_BookingRead;
  individual: IndividualRead;
  vehicle: VehicleReadWithModelAndParty;
  contacts: ContactPointRead[];
}) {
  const router = useRouter();
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length) return;

      const urls: string[] = await Promise.all(
        vehicle.media.map(async (item: VehicleMediaRead) => {
          const result = await getUrl({ path: item.media_path });
          return result.url.toString();
        })
      );

      setImageUrls(urls);
    }

    loadImages();
  }, [vehicle]);

  const handleBackHome = () => {
    router.push("/home");
  };
  const contactTypeIcons: Record<string, JSX.Element> = {
    email: <Mail size={16} className="text-[#009639]" />,
    phone: <Phone size={16} className="text-[#009639]" />,
    address: <MapPin size={16} className="text-[#009639]" />,
  };
  
  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Success Message */}
      <div className="bg-[#009639] px-6 py-8 flex flex-col items-center border-b border-[#007A2F]">
        <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
          <CheckCircle size={40} className="text-[#009639]" />
        </div>
        <h1 className="text-2xl font-bold text-white mb-2">
          Booking Complete!
        </h1>
        <p className="text-white text-center">
          Your booking has been confirmed and is ready for use
        </p>
      </div>

      {/* Vehicle Details */}
      <div className="px-4 py-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          <div className="h-40 bg-[#f2f2f2] relative">
            <Image
              src={imageUrls?.[0] || "/placeholder.svg"}
              alt={vehicle?.model?.model}
              fill
              className="object-cover"
            />
          </div>

          <div className="p-4">
            <h2 className="text-lg font-bold text-[#333333] mb-1">
              {vehicle?.model?.make?.name} {vehicle?.model?.model} (
              {vehicle?.vin_number})
            </h2>
            <p className="text-[#797879] mb-3">
              Reference: {booking.reference}
            </p>

            <div className="space-y-3">
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Date & Time</p>
                  <p className="text-[#333333]">{booking.start_datetime}</p>
                </div>
              </div>
              <div className="flex items-start">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Clock size={12} className="text-[#009639]" />
                </div>
                <div>
                  <p className="text-[#797879] text-xs">Estimated Arrival</p>
                  <p className="text-[#333333]">{booking?.start_datetime}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Details */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Booking Details</h3>

          <div className="space-y-3">
            <div className="p-3 bg-[#f9f9f9] rounded-lg">
              <p className="text-[#797879] text-xs">Booked for</p>
              <p className="text-[#333333] font-medium">
                {individual?.first_name} {individual?.last_name}
              </p>
            </div>
            {contacts.map((contact) => (
              <div key={contact.id} className="p-3 bg-[#f9f9f9] rounded-lg">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  {contactTypeIcons[contact.contact_point_type.name] || (
                    <Info size={16} className="text-[#009639]" />
                  )}
                </div>
                <div>
                  <p className="text-[#797879] text-xs capitalize">
                    {contact.contact_point_type.name}
                  </p>
                  <p className="text-[#333333]">{contact.value}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="px-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Next Steps</h3>

          <div className="space-y-3">
            <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Car size={12} className="text-[#009639]" />
                </div>
                <span className="text-[#333333]">View Vehicle Details</span>
              </div>
              <button
                onClick={() =>
                  router.push(`/vehicle-status/${booking.vehicle_id}`)
                }
                className="flex items-center"
              >
                <ChevronRight size={20} className="text-[#009639]" />
              </button>
            </div>

            <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Calendar size={12} className="text-[#009639]" />
                </div>
                <span className="text-[#333333]">View All Bookings</span>
              </div>
              <button
                onClick={() =>
                  router.push(`/calendar-view`)
                }
                className="flex items-center"
              >
                <ChevronRight size={20} className="text-[#009639]" />
              </button>
            </div>

            <div className="p-3 bg-[#f9f9f9] rounded-lg flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                  <Info size={12} className="text-[#009639]" />
                </div>
                <span className="text-[#333333]">Booking Details</span>
              </div>
              <button
                onClick={() =>
                  router.push(`/booking-details/${booking.id}`)
                }
                className="flex items-center"
              >
                <ChevronRight size={20} className="text-[#009639]" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Return to Home Button */}
      <div className="px-4 py-4">
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={handleBackHome}
        >
          Return to Home
        </button>
      </div>
    </div>
  );
}
