import Image from "next/image";
import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
interface VehicleImageCarouselProps {
  images: string[];
  alt?: string;
}

export default function VehicleImageCarousel({
  images = [],
  alt = "Vehicle Image",
}: VehicleImageCarouselProps) {
  const [current, setCurrent] = useState(0);
  
  // Ensure images is a valid array
  const validImages = Array.isArray(images) ? images : [];
  
  const prevImage = () => {
    setCurrent((prev) => (prev === 0 ? validImages.length - 1 : prev - 1));
  };

  const nextImage = () => {
    setCurrent((prev) => (prev === validImages.length - 1 ? 0 : prev + 1));
  };

  if (validImages.length === 0) {
    return (
      <div className="h-48 bg-[#f2f2f2] rounded-xl flex items-center justify-center">
        <p className="text-[#797879]">No images available</p>
      </div>
    );
  }

  return (
    <div className="relative h-48 bg-[#e6ffe6] rounded-xl overflow-hidden">
      <Image
        src={`${validImages[current]}`}
        alt={alt}
        fill
        className="object-contain transition-all duration-300"
      />
      <button
        onClick={prevImage}
        className="absolute left-2 top-1/2 -translate-y-1/2 bg-white p-1 rounded-full shadow"
      >
        <ChevronLeft className="w-5 h-5" />
      </button>
      <button
        onClick={nextImage}
        className="absolute right-2 top-1/2 -translate-y-1/2 bg-white p-1 rounded-full shadow"
      >
        <ChevronRight className="w-5 h-5" />
      </button>
    </div>
  );
}
