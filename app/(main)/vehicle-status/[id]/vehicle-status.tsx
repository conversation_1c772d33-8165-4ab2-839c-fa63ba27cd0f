"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { getUrl } from "aws-amplify/storage";
import type { VehicleMediaRead } from "@/types/vehicles";
import type { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import type { VehicleMaintenanceRead } from "@/drizzle-actions/vehicle-maintenance";
import type { deprecated_BookingRead } from "@/types/bookings";
import {
  ArrowLeft,
  Phone,
  User,
  Calendar,
  ChevronRight,
  Car,
  Wrench,
  Clock,
  Mail,
  ChevronLeft,
  ChevronRight as ChevronRightPagination,
  AlertTriangle,
  Shield,
  MapPin,
  Users,
} from "lucide-react";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import VehicleImageCarousel from "./vehicleImage-carousel";
import {
  getStatusColor,
  getStatusLabel,
  formatDateForInput,
  calculateTimeRemaining,
} from "@/lib/utils";

export default function VehicleStatusScreen({
  vehicle,
  possessions = [],
  maintenanceRecords = [],
  bookings = [],
  currentUser,
}: {
  vehicle: VehicleReadWithModelAndParty;
  possessions: VehiclePossessionWithContactRead[];
  maintenanceRecords?: VehicleMaintenanceRead[];
  bookings?: deprecated_BookingRead[];
  currentUser?: any;
}) {
  const router = useRouter();

  // Early return if vehicle is null/undefined
  if (!vehicle || typeof vehicle !== 'object') {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-[#797879] mb-4">Vehicle data not available</p>
          <button
            onClick={() => router.back()}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
  const [timeRemaining, setTimeRemaining] = useState({ hours: 5, minutes: 45 });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [currentPossession, setCurrentPossession] =
    useState<VehiclePossessionWithContactRead | null>(null);
  const [activeTab, setActiveTab] = useState<"maintenance" | "bookings">(
    "maintenance"
  );
  const [maintenancePage, setMaintenancePage] = useState(1);
  const [bookingsPage, setBookingsPage] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length || !Array.isArray(vehicle.media)) return;

      try {
        const urls: string[] = await Promise.all(
          vehicle.media.map(async (item: VehicleMediaRead) => {
            const result = await getUrl({ path: item.media_path });
            return result.url.toString();
          })
        );

        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading vehicle images:", error);
        setImageUrls([]);
      }
    }

    if (vehicle) {
      loadImages();
    }
  }, [vehicle]);

  useEffect(() => {
    if (!currentPossession || !currentPossession.handover_expected_datetime)
      return;

    const updateTime = () => {
      const remaining = calculateTimeRemaining(
        currentPossession.handover_expected_datetime
      );
      setTimeRemaining(remaining);
    };

    updateTime();
    const intervalId = setInterval(updateTime, 60 * 1000);

    return () => clearInterval(intervalId);
  }, [currentPossession?.handover_expected_datetime]);

  useEffect(() => {
    async function fetchPossession() {
      if (!vehicle?.id || !Array.isArray(possessions)) return;
      
      try {
        const latest = possessions
          .filter((p) => p?.status === "pending")
          .sort(
            (a, b) =>
              new Date(b?.handover_expected_datetime || 0).getTime() -
              new Date(a?.handover_expected_datetime || 0).getTime()
          )[0];
        setCurrentPossession(latest || null);
      } catch (error) {
        console.error("Error processing possessions:", error);
        setCurrentPossession(null);
      }
    }

    if (vehicle?.id) {
      fetchPossession();
    }
  }, [vehicle?.id, possessions]);

  // Pagination logic for Maintenance - use the passed maintenanceRecords prop
  const maintenanceItems = Array.isArray(maintenanceRecords) && maintenanceRecords.length > 0 
    ? maintenanceRecords 
    : (Array.isArray(vehicle?.maintenance_items) ? vehicle.maintenance_items : []);
  const totalMaintenancePages = Math.ceil(
    (maintenanceItems?.length || 0) / itemsPerPage
  );
  const paginatedMaintenanceItems = (maintenanceItems || []).slice(
    (maintenancePage - 1) * itemsPerPage,
    maintenancePage * itemsPerPage
  );

  // Pagination logic for Bookings - use the passed bookings prop instead of vehicle.bookings
  const vehicleBookings = Array.isArray(bookings) && bookings.length > 0 
    ? bookings 
    : (Array.isArray(vehicle?.bookings) ? vehicle.bookings : []);
  const totalBookingsPages = Math.ceil((vehicleBookings?.length || 0) / itemsPerPage);
  const paginatedBookings = (vehicleBookings || []).slice(
    (bookingsPage - 1) * itemsPerPage,
    bookingsPage * itemsPerPage
  );

  return (
    <div className="min-h-screen bg-white">
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">
          {vehicle?.model?.model} Status
        </h1>
      </div>

      {/* Vehicle Image */}
      <div className="p-4">
        <VehicleImageCarousel images={imageUrls} />
      </div>

      {/* Vehicle Information */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Vehicle Information</h3>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Make & Model:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle?.model?.make?.name} {vehicle?.model?.model}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Registration:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle?.vehicle_registration || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Year:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle?.manufacturing_year || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Color:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle?.color || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">VIN:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle?.vin_number || 'Not specified'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Current User */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <User size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                {currentPossession ? "Current User" : ""}
              </h3>
            </div>
            <span className={`text-xs px-2 py-1 rounded-full shadow-sm ${
              currentPossession 
                ? "bg-[#FFD700] text-[#333333]" 
                : "bg-[#e6ffe6] text-[#007A2F]"
            }`}>
              {currentPossession ? "In Use" : "Available"}
            </span>
          </div>
          {currentPossession && (
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">
                  {currentPossession?.to_party?.individual
                    ? `${currentPossession.to_party.individual.first_name} ${
                        currentPossession.to_party.individual?.middle_name ?? ""
                      } ${currentPossession.to_party.individual.last_name}`
                    : "Unknown user"}
                </p>
                <div className="flex items-center mt-1">
                  <Calendar size={14} className="text-[#797879] mr-1" />
                  <p className="text-xs text-[#797879]">
                    Return:{" "}
                    {new Date(
                      currentPossession?.handover_expected_datetime || ""
                    ).toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="flex space-x-2">
                <button
                  className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                  onClick={() => {
                    const phoneNumber =
                      currentPossession?.to_party?.contact_points?.find(
                        (cp) => cp.contact_point_type?.name === "phone"
                      )?.value || "0000";
                    window.open(`tel:${phoneNumber}`);
                  }}
                >
                  <Phone size={20} />
                </button>
                <button
                  className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                  onClick={() => {
                    const emailAddress =
                      currentPossession?.to_party?.contact_points?.find(
                        (cp) => cp.contact_point_type?.name === "email"
                      )?.value || "<EMAIL>";
                    window.open(`mailto:${emailAddress}`);
                  }}
                >
                  <Mail size={20} />
                </button>
              </div>
            </div>
          )}
          
          {!currentPossession && (
            <div className="text-center py-4">
              <p className="text-[#333333] font-medium mb-2">Vehicle is currently available</p>
              <p className="text-xs text-[#797879] mb-3">No active possession or booking</p>
              <div className="bg-[#f0f9ff] p-3 rounded-lg border border-blue-100">
                <div className="flex items-center justify-center">
                  <MapPin size={14} className="text-blue-600 mr-1" />
                  <p className="text-xs text-blue-600">GPS tracking not currently available</p>
                </div>
              </div>
            </div>
          )}
          
          {currentPossession && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-[#797879] mb-1">
                <span>Time Remaining</span>
                <span>
                  {timeRemaining.hours}h {timeRemaining.minutes}m
                </span>
              </div>
              <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
                <div
                  className="h-full bg-[#009639] rounded-full"
                  style={{
                    width: `${
                      ((timeRemaining.hours * 60 + timeRemaining.minutes) /
                        (6 * 60)) *
                      100
                    }%`,
                  }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Emergency Contacts */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center mb-3">
            <AlertTriangle size={20} className="text-red-500 mr-2" />
            <h3 className="text-[#333333] font-medium">Emergency Contacts</h3>
          </div>
          <div className="space-y-3">
            {/* Police */}
            <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-100">
              <div className="flex items-center">
                <Shield size={16} className="text-red-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Police</p>
                  <p className="text-xs text-[#797879]">Emergency Services</p>
                </div>
              </div>
              <button
                className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-sm"
                onClick={() => window.open('tel:10111')}
              >
                Call 10111
              </button>
            </div>
            
            {/* Ambulance */}
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-100">
              <div className="flex items-center">
                <AlertTriangle size={16} className="text-blue-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Ambulance</p>
                  <p className="text-xs text-[#797879]">Medical Emergency</p>
                </div>
              </div>
              <button
                className="bg-blue-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-sm"
                onClick={() => window.open('tel:10177')}
              >
                Call 10177
              </button>
            </div>

            {/* Roadside Assistance */}
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-100">
              <div className="flex items-center">
                <Car size={16} className="text-yellow-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Roadside Assistance</p>
                  <p className="text-xs text-[#797879]">Vehicle Breakdown</p>
                </div>
              </div>
              <button
                className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-medium shadow-sm"
                onClick={() => window.open('tel:0861102872')}
              >
                Call AA
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-gray-200 mb-4">
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              activeTab === "maintenance"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
              setActiveTab("maintenance");
              setMaintenancePage(1); // Reset to first page when switching tabs
            }}
          >
            Maintenance
          </button>
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              activeTab === "bookings"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
              setActiveTab("bookings");
              setBookingsPage(1); // Reset to first page when switching tabs
            }}
          >
            Bookings
          </button>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          {activeTab === "maintenance" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Car size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">Maintenance</h3>
                </div>
              </div>
              <div className="space-y-3">
                {paginatedMaintenanceItems.length > 0 ? (
                  paginatedMaintenanceItems.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item.name}</h5>
                              <p className="text-xs text-[#797879]">
                                {item.description}
                              </p>
                            </div>
                            <div className="flex items-center gap-2 ml-auto">
                              <span
                                className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                  item.status
                                )}`}
                              >
                                {getStatusLabel(item.status)}
                              </span>
                              <button
                                className="text-[#009639] text-sm flex items-center"
                                onClick={() =>
                                  router.push(`/maintenance-details/${item.id}`)
                                }
                              >
                                <ChevronRight
                                  size={20}
                                  className="text-[#797879]"
                                />
                              </button>
                            </div>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput((item as any).dueDate || (item as any).due_date)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No maintenance items available.
                  </p>
                )}
              </div>
              {/* Maintenance Pagination */}
              {totalMaintenancePages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={maintenancePage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {maintenancePage} of {totalMaintenancePages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) =>
                        Math.min(prev + 1, totalMaintenancePages)
                      )
                    }
                    disabled={maintenancePage === totalMaintenancePages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}

          {activeTab === "bookings" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Car size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">Bookings</h3>
                </div>
              </div>
              <div className="space-y-3">
                {paginatedBookings.length > 0 ? (
                  paginatedBookings.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item?.reference}</h5>
                              <p className="text-xs text-[#797879]">
                                {item?.notes}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                item.status
                              )}`}
                            >
                              {getStatusLabel(item.status)}
                            </span>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput(item?.start_datetime)} -{" "}
                              {formatDateForInput(item?.end_datetime)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No bookings available.
                  </p>
                )}
              </div>
              {/* Bookings Pagination */}
              {totalBookingsPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={bookingsPage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {bookingsPage} of {totalBookingsPages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) =>
                        Math.min(prev + 1, totalBookingsPages)
                      )
                    }
                    disabled={bookingsPage === totalBookingsPages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] space-y-3">
        {/* Book Vehicle Button - only show if vehicle is available */}
        {!currentPossession && vehicle?.bookings?.every(booking => 
          booking.status === "Cancelled" || booking.status === "Completed" || 
          new Date(booking.end_datetime) < new Date()
        ) && (
          <button
            className="w-full py-3 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm mb-2"
            onClick={() => router.push(`/booking-calendar/${vehicle.id}`)}
          >
            Book This Vehicle
          </button>
        )}
        
        {/* Handover Button */}
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={() => router.push(`/vehicle-handover/${vehicle.id}`)}
        >
          Initiate Vehicle Handover
        </button>
      </div>
    </div>
  );
}
