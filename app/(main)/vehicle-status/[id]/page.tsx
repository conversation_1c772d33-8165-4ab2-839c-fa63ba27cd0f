"use server";
import { getVehicleByIdDrizzle } from "@/drizzle-actions/vehicle-dashboard";
// import { deprecated_getCurrentVehiclePossession } from "@/drizzle-actions/bookings";
import { getVehicleMaintenanceByVehicleIdDrizzle } from "@/drizzle-actions/vehicle-maintenance";
import { deprecated_getVehicleBookingsWithDetails } from "@/drizzle-actions/bookings";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import VehicleStatusScreen from "./vehicle-status";
export default async function VehicleStatus({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  try {
    const { id } = await params;
    console.log("Vehicle Status - Loading vehicle ID:", id);
    
    // Fetch user attributes first
    const userAttributes = await getUserAttributes().catch(error => {
      console.error("Failed to get user attributes:", error);
      return null;
    });
    console.log("User attributes loaded:", !!userAttributes);

    // Fetch vehicle data first to ensure it exists
    const vehicle = await getVehicleByIdDrizzle(+id).catch(error => {
      console.error("Failed to get vehicle:", error);
      return null;
    });
    
    if (!vehicle) {
      return <p>Vehicle not found.</p>;
    }
    console.log("Vehicle loaded:", vehicle.id);

    // Fetch additional data with proper error handling
    const [possessions, maintenanceRecords, bookings] = await Promise.all([
      Promise.resolve([]), // Temporarily disabled possessions to avoid fetchClient error
      getVehicleMaintenanceByVehicleIdDrizzle(+id).catch((error: any) => {
        console.error("Failed to get maintenance records:", error);
        return [];
      }),
      deprecated_getVehicleBookingsWithDetails(+id).catch((error: any) => {
        console.error("Failed to get bookings:", error);
        return [];
      })
    ]);

    console.log("Data loaded - possessions:", possessions?.length || 0, "maintenance:", maintenanceRecords?.length, "bookings:", bookings?.length);

    return (
      <VehicleStatusScreen 
        vehicle={vehicle} 
        possessions={possessions || []}
        maintenanceRecords={maintenanceRecords || []}
        bookings={bookings || []}
        currentUser={userAttributes}
      />
    );
  } catch (error) {
    console.error("Vehicle Status page error:", error);
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error loading vehicle status</p>
          <button
            onClick={() => window.history.back()}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
}