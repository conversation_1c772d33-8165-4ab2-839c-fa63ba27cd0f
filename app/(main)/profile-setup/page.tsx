"use server";
import { getAddressTypes } from "@/actions/address-types";
import { getContactPointTypes } from "@/actions/contact-point-types";
import { getIdentificationTypes } from "@/actions/identification-types";
import { createProfile } from "@/actions/individuals";
import { getStatuses } from "@/actions/party-statuses";
import { getPartyTypes } from "@/actions/party-types";
import BackElement from "@/app/components/back-element";
import PostForm from "./post-form";
import { getUserAttributes } from "@/lib/serverUserAttributes";

export default async function ProfileSetupScreen() {
  const [
    contactPointTypes,
    partyTypes,
    partyStatuses,
    identificationTypes,
    addressTypes,
  ] = await Promise.all([
    getContactPointTypes(),
    getPartyTypes(),
    getStatuses(),
    getIdentificationTypes(),
    getAddressTypes(),
  ]);
  const attributes = await getUserAttributes();

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <BackElement title="Profile Setup" />
      <PostForm
        action={createProfile}
        contactPointTypes={contactPointTypes}
        partyTypes={partyTypes}
        partyStatuses={partyStatuses}
        identificationTypes={identificationTypes}
        addressTypes={addressTypes}
        attributes={attributes}
      />
    </div>
  );
}
