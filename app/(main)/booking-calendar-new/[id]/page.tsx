"use server";

import { deprecated_getVehicleBookingsWithDetails, deprecated_addBookingDrizzle } from "@/drizzle-actions/bookings";
import { getVehicleByIdDrizzle } from "@/drizzle-actions/vehicle-dashboard";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import BookingCalendarScreen from "./booking-calendar-new";


export default async function BookingCalendar({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const attributes = await getUserAttributes();

  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  try {
    // Use drizzle for both data fetching and actions
    const [bookings, vehicle] = await Promise.all([
      deprecated_getVehicleBookingsWithDetails(+id),
      getVehicleByIdDrizzle(+id) // Now using drizzle for vehicle data
    ]);

    if (!vehicle) {
      return (
        <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
          <div className="text-center">
            <p className="text-[#797879] mb-4">Vehicle not found</p>
            <button
              onClick={() => history.back()}
              className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            >
              Go Back
            </button>
          </div>
        </div>
      );
    }

    return (
      <BookingCalendarScreen
        bookings={bookings}
        vehicle={vehicle}
        action={deprecated_addBookingDrizzle} // Now using drizzle action directly
        party_id={+dbId}
      />
    );
  } catch (error) {
    console.error("Error loading booking data:", error);
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error loading booking data</p>
          <button
            onClick={() => history.back()}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
}
