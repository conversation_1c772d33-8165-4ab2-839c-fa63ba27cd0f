"use client";

import React, { useState, use } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Phone,
  Mail,
  MessageSquare,
  PieChart,
  Calendar,
  Car,
  ChevronRight,
  Star,
  Clock,
  MapPin,
  Info,
} from "lucide-react";
import type { IndividualRead } from "@/types/individuals";
import { formatDateForInput } from "@/lib/utils";
import type { ContactPointRead } from "@/types/contact-points";
import type { CompanyOwnershipReadWithRelations } from "@/types/company-ownerships";
import { VehicleRead, VehicleReadWithModelAndParty } from "@/types/vehicles";
import { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import { deprecated_BookingRead } from "@/types/bookings";
import Link from "next/link";

export default function MemberDetailsScreen({
  individual,
  profilePic,
  contacts,
  ownerships,
  bookings,
  vehicles,
  possessions,
}: {
  individual: IndividualRead;
  profilePic: string | null;
  contacts: ContactPointRead[];
  vehicles: VehicleReadWithModelAndParty[];
  bookings: deprecated_BookingRead[];
  ownerships: CompanyOwnershipReadWithRelations[];
  possessions: VehiclePossessionWithContactRead[];
}): React.ReactNode {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("bookings");
  const now = new Date();
  const upcomingBookings = bookings.filter(
    (booking) => new Date(booking.start_datetime) >= now
  );
  const contactTypeIcons: Record<string, JSX.Element> = {
    email: <Mail size={16} className="text-[#009639]" />,
    phone: <Phone size={16} className="text-[#009639]" />,
    address: <MapPin size={16} className="text-[#009639]" />,
  };
  const getActivityIcon = (type: string) => {
    switch (type) {
      case "booking":
        return <Calendar size={18} className="text-[#009639]" />;
      case "payment":
        return <PieChart size={18} className="text-[#009639]" />;
      case "maintenance":
        return <Car size={18} className="text-[#009639]" />;
      default:
        return <Clock size={18} className="text-[#009639]" />;
    }
  };

  const getVehicleName = (id: number): string => {
    const vehicle = vehicles.find(
      (v: VehicleReadWithModelAndParty) => v.id === id
    );
    if (vehicle?.model?.make?.name && vehicle.model.model) {
      return `${vehicle.model.model} ${vehicle.model.make.name}`;
    }
    return "Unknown Vehicle";
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Member Details</h1>
      </div>

      {/* Member Profile */}
      <div className="bg-white px-6 py-6 flex flex-col items-center border-b border-[#f2f2f2]">
        <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-[#e6ffe6] shadow-md mb-3">
          <Image
            src={profilePic || "/placeholder.svg?height=40&width=40"}
            alt={individual.first_name}
            width={96}
            height={96}
            className="object-cover"
          />
        </div>
        <h2 className="text-xl font-bold text-[#333333] mb-1">
          {individual.first_name} {individual.last_name}
        </h2>
        <div className="flex items-center mb-2">
          <Star size={16} className="text-[#FFD700] mr-1" />
          <span className="text-sm text-[#333333]">{5}</span>
        </div>
        <p className="text-sm text-[#797879] mb-3">
          Member since {formatDateForInput(individual.created_at)}
        </p>
        <div className="flex space-x-3">
          {contacts.map((contact) => (
            <div key={contact.id} className="p-3 bg-[#f9f9f9] rounded-lg">
              <>
                {contactTypeIcons[contact.contact_point_type.name] || (
                  <Info size={16} className="text-[#009639]" />
                )}
              </>
            </div>
          ))}
        </div>
      </div>

      {/* Ownership */}
      <div className="p-4">
        {ownerships.map((ownership) => (
          <div
            key={ownership.id}
            className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100"
          >
            <h3 className="text-[#333333] font-medium mb-3">Ownership</h3>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="h-4 bg-[#f2f2f2] rounded-full overflow-hidden">
                  <div
                    className="h-full bg-[#009639] rounded-full"
                    style={{ width: `${ownership.fraction}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-lg font-bold text-[#333333] ml-3">
                {ownership.fraction}%
              </span>
            </div>
            <div className="mt-3">
              <p className="text-sm text-[#797879]">
                {ownership.company?.name}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Tabs */}
      <div className="bg-white shadow-sm mb-4 mx-4">
        <div className="flex">
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "activity" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("activity")}
          >
            Activity
            {activeTab === "activity" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`flex-1 py-4 text-sm font-medium relative ${
              activeTab === "bookings" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("bookings")}
          >
            Bookings
            {activeTab === "bookings" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Activity Tab */}
      {activeTab === "activity" && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {possessions.map((item, index) => (
              <Link href={`/booking-details/${item.id}`} key={item.id}>
                <div
                  className={`p-4 ${
                    index < possessions.length - 1
                      ? "border-b border-[#f2f2f2]"
                      : ""
                  } cursor-pointer hover:bg-gray-50`}
                >
                  <div className="flex">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                      {getActivityIcon("booking")}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className="text-[#333333] font-medium">
                          {`Booked ${getVehicleName(item.vehicle_id)}`}
                        </h3>
                        <span className="text-xs text-[#797879]"></span>
                      </div>
                      <p className="text-sm text-[#797879] mt-1">
                        {formatDateForInput(item.created_at)}-
                        {formatDateForInput(item.handover_expected_datetime)}
                      </p>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Bookings Tab */}
      {activeTab === "bookings" && (
        <div className="p-4">
          <h3 className="text-[#333333] font-medium mb-3">Upcoming Bookings</h3>
          {upcomingBookings.length > 0 ? (
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
              {upcomingBookings.map((booking, index) => (
                <Link href={`/booking-details/${booking.id}`} key={booking.id}>
                  <div
                    className={`p-4 ${
                      index < upcomingBookings.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    } cursor-pointer hover:bg-gray-50`}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#333333] font-medium">
                          {getVehicleName(booking.vehicle_id)}
                        </h3>
                        <div className="flex items-center text-sm text-[#797879] mt-1">
                          <Calendar size={14} className="mr-1" />
                          <span>
                            {formatDateForInput(booking.start_datetime)} -{" "}
                            {formatDateForInput(booking.end_datetime)}
                          </span>
                        </div>
                      </div>
                      <ChevronRight size={20} className="text-[#009639]" />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
              <p className="text-[#797879]">No upcoming bookings</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
