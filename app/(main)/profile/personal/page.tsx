"use server";

import { getAddressTypesDrizzle, getContactPointTypesDrizzle, getContactPointsByPartyIdDrizzle, getIndividualByPartyIdDrizzle, updateProfileDrizzle } from "@/drizzle-actions/personal-profile";
import type { ContactPointTypeRead } from "@/types/contact-point-types";
import type { ContactPointRead } from "@/types/contact-points";
import BackElement from "@/app/components/back-element";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import { Camera } from "lucide-react";
import Image from "next/image";
import { redirect } from "next/navigation";
import PostForm from "./post-form";
import { getProfileImageUrl } from "@/lib/profile";
import MissingParty from "@/components/missing-party";

export default async function PersonalInformationScreen() {
  const attributes = await getUserAttributes();
  const dbId = attributes?.["custom:db_id"];
  if (!dbId) return <MissingParty />;

  const [contactPointTypes, individual, addressTypes] =
    await Promise.all([
      getContactPointTypesDrizzle(),
      getIndividualByPartyIdDrizzle(+dbId),
      getAddressTypesDrizzle(),
    ]);

  if (!individual) {
    return <MissingParty />;
  }

  const contactPoints = await getContactPointsByPartyIdDrizzle(individual.party_id);

  const contactTypeMap = Object.fromEntries(
    contactPointTypes.map((c: ContactPointTypeRead) => [c.name, c.id])
  );

  const emailId = contactTypeMap["email"];
  const phoneId = contactTypeMap["phone"];
  const addressId = contactTypeMap["address"];

  const firstEmail = contactPoints.find(
    (contact: ContactPointRead) => contact.contact_point_type_id === emailId
  );
  const firstPhone = contactPoints.find(
    (contact: ContactPointRead) => contact.contact_point_type_id === phoneId
  );
  const firstAddress = contactPoints.find(
    (contact: ContactPointRead) => contact.contact_point_type_id === addressId
  );

  const imageUrl = await getProfileImageUrl(individual.party_id);

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      <BackElement title={"Personal Information"} />
      <div className="bg-white px-6 py-6 flex flex-col items-center">
        <div className="relative mb-2">
          <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-[#009639] shadow-md">
            <Image
              src={imageUrl || "/images/Poolly.White.Svg.svg"}
              alt="Profile"
              width={96}
              height={96}
              className="object-cover"
            />
          </div>
          <button className="absolute bottom-0 right-0 bg-gradient-to-r from-[#009639] to-[#007A2F] p-2 rounded-full shadow-md">
            <Camera size={16} className="text-white" />
          </button>
        </div>
        <p className="text-[#009639] text-sm font-medium">Change Photo</p>
      </div>

      <PostForm
        action={updateProfileDrizzle}
        individual={individual}
        firstEmail={firstEmail}
        firstPhone={firstPhone}
        firstAddress={firstAddress}
        contactPointTypes={contactPointTypes}
        addressTypes={addressTypes}
      />
    </div>
  );
}
