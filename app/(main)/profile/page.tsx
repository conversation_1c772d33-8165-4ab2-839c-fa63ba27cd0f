"use server";

import { getIndividualByPartyIdDrizzle } from "@/drizzle-actions/personal-profile";
import BackElement from "@/app/components/back-element";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import Image from "next/image";
import MissingParty from "@/components/missing-party";
import ProfileTabs from "./profile-tabs";
import { getProfileImageUrl } from "@/lib/profile";

export default async function ProfileScreen() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
  if (!dbId || !externalId) {
    return <MissingParty />;
  }

  const [individual, imageUrl] = await Promise.all([
    getIndividualByPartyIdDrizzle(+dbId),
    getProfileImageUrl(+dbId),
  ]);

  if (!individual) {
    return <MissingParty />;
  }

  return (
    <div className="min-h-screen">
      <BackElement title={"Home"} />
      <div className="bg-white px-6 py-6 flex items-center space-x-6 shadow-sm">
        <div className="w-20 h-20 rounded-full overflow-hidden border-2 border-gray-300">
          <Image
            src={imageUrl || "/images/Poolly.White.Svg.svg"}
            alt="Profile"
            width={80}
            height={80}
            className="object-cover"
            priority
          />
        </div>
        <div className="space-y-1">
          <p className="text-lg font-medium text-gray-800">
            {individual.first_name} {individual.last_name}
          </p>
          <p className="text-sm text-gray-600">{email}</p>
        </div>
      </div>

      <div className="mt-4">
        <ProfileTabs externalId={externalId} />
      </div>
    </div>
  );
}
