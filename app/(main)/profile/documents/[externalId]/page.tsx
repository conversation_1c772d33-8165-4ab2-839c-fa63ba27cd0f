"use server";

import { getPartyByExternalIDDrizzle, getPartyIdentificationsByPartyDrizzle } from "@/drizzle-actions/party-profile";
import MissingParty from "@/components/missing-party";
import DocumentsDisplay from "./documents-display";
interface UploadDocumentsPageProps {
  params: {
    externalId: string;
  };
}
export default async function LegalDocumentsScreen({
  params,
}: UploadDocumentsPageProps) {
  const { externalId } = await params;
  if (!externalId) return <MissingParty />;
  const party = await getPartyByExternalIDDrizzle(externalId);
  if (!(party && party.id)) return <MissingParty />;
  const documents = await getPartyIdentificationsByPartyDrizzle(party.id);

  return <DocumentsDisplay documents={documents} externalId={externalId} />;
}
