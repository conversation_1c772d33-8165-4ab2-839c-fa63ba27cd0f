"use client";

import { useState, useEffect } from "react";
import {
  ArrowLeft,
  FileText,
  Upload,
  Download,
  Eye,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  getUserApplicationDocumentsWithRequirementsAction,
  uploadSingleDocumentAction,
} from "@/actions/applications";
import { generateDocumentUrl, DocumentUpload } from "@/lib/utils";

interface LegalDocumentsSheetProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ApplicationDocument {
  id: number | null;
  documentType: string;
  documentUrl: string | null;
  uploadedAt: string | null;
  applicationId: number;
  status?: string | null;
  statusAt?: string | null;
  listingId: number;
  applicationCreatedAt: string;
  documentState: "missing" | "uploaded" | "rejected" | "approved";
  isMissing: boolean;
  // Enhanced requirement info
  documentName?: string;
  documentDescription?: string;
  isRequired?: boolean;
}

export default function LegalDocumentsSheet({
  isOpen,
  onClose,
}: LegalDocumentsSheetProps) {
  const [documents, setDocuments] = useState<ApplicationDocument[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadingDocuments, setUploadingDocuments] = useState<Set<string>>(
    new Set()
  );

  // Fetch documents when sheet opens
  useEffect(() => {
    if (isOpen) {
      fetchDocuments();
    }
  }, [isOpen]);

  const fetchDocuments = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await getUserApplicationDocumentsWithRequirementsAction();
      if (result.success) {
        setDocuments(result.documents);
      } else {
        setError(result.error || "Failed to fetch documents");
      }
    } catch (err) {
      setError("Failed to fetch documents");
      console.error("Error fetching documents:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpload = async (document: ApplicationDocument, file: File) => {
    const uploadKey = `${document.applicationId}-${document.documentType}`;

    try {
      setUploadingDocuments((prev) => new Set(prev).add(uploadKey));

      // Upload file to S3 (using same pattern as ApplicationDocumentUploader)
      const uploadResult = await DocumentUpload(file, "applications");
      const s3Path = uploadResult?.path || uploadResult;

      if (!s3Path) {
        throw new Error("Upload failed: No file path returned");
      }

      console.log(`Upload successful: ${s3Path}`);

      // Save document to database
      const result = await uploadSingleDocumentAction(
        document.applicationId,
        document.documentType,
        typeof s3Path === "string" ? s3Path : ""
      );

      if (result.success) {
        // Refresh documents list
        await fetchDocuments();
      } else {
        alert(result.error || "Failed to upload document");
      }
    } catch (error) {
      console.error("Upload failed:", error);
      alert("Failed to upload document. Please try again.");
    } finally {
      setUploadingDocuments((prev) => {
        const newSet = new Set(prev);
        newSet.delete(uploadKey);
        return newSet;
      });
    }
  };

  const handleFileSelect = (doc: ApplicationDocument) => {
    const input = window.document.createElement("input");
    input.type = "file";
    input.accept = ".pdf,.jpg,.jpeg,.png";
    input.onchange = (e: Event) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleUpload(doc, file);
      }
    };
    input.click();
  };

  const getStatusIcon = (status?: string | null) => {
    switch (status) {
      case "approved":
      case "verified":
        return <CheckCircle size={18} className="text-green-500" />;
      case "uploaded":
      case "under_review":
        return <Clock size={18} className="text-yellow-500" />;
      case "rejected":
      case "expired":
        return <AlertCircle size={18} className="text-red-500" />;
      default:
        return <FileText size={18} className="text-gray-500" />;
    }
  };

  const getStatusText = (status?: string | null) => {
    switch (status) {
      case "approved":
        return "Approved";
      case "verified":
        return "Verified";
      case "uploaded":
        return "Uploaded";
      case "under_review":
        return "Under Review";
      case "rejected":
        return "Rejected";
      case "expired":
        return "Expired";
      default:
        return "Uploaded";
    }
  };

  const getStatusColor = (status?: string | null) => {
    switch (status) {
      case "approved":
      case "verified":
        return "text-green-600 bg-green-50";
      case "uploaded":
      case "under_review":
        return "text-yellow-600 bg-yellow-50";
      case "rejected":
      case "expired":
        return "text-red-600 bg-red-50";
      default:
        return "text-blue-600 bg-blue-50";
    }
  };

  const formatDocumentName = (documentType: string) => {
    return documentType
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleView = async (document: ApplicationDocument) => {
    try {
      const url = await generateDocumentUrl(document.documentUrl);
      window.open(url, "_blank");
    } catch (error) {
      console.error("Failed to generate document URL:", error);
      alert("Could not open document. Please try again.");
    }
  };

  const handleDownload = async (doc: ApplicationDocument) => {
    if (!doc.documentUrl) return;

    try {
      const url = await generateDocumentUrl(doc.documentUrl);
      // Create a temporary link to trigger download
      const link = window.document.createElement("a");
      link.href = url;
      link.download = `${formatDocumentName(doc.documentType)}.pdf`;
      window.document.body.appendChild(link);
      link.click();
      window.document.body.removeChild(link);
    } catch (error) {
      console.error("Failed to download document:", error);
      alert("Could not download document. Please try again.");
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Legal Documents
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your legal documents
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Header with refresh button */}
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-[#333333] font-medium">
                  Application Documents
                </h3>
                <button
                  onClick={fetchDocuments}
                  disabled={loading}
                  className="flex items-center text-[#009639] text-sm font-medium hover:text-[#007A2F] disabled:opacity-50"
                >
                  <RefreshCw
                    size={16}
                    className={`mr-1 ${loading ? "animate-spin" : ""}`}
                  />
                  Refresh
                </button>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
                <p className="text-gray-500">Loading documents...</p>
              </div>
            )}

            {/* Error State */}
            {error && !loading && (
              <div className="p-4">
                <div className="bg-red-50 rounded-xl p-4 border border-red-200">
                  <h4 className="text-red-800 font-medium mb-2">Error</h4>
                  <p className="text-red-700 text-sm">{error}</p>
                  <button
                    onClick={fetchDocuments}
                    className="mt-3 text-red-600 text-sm font-medium hover:text-red-800"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            )}

            {/* Document Summary */}
            {!loading && !error && documents.length > 0 && (
              <div className="p-4 border-b border-gray-100">
                <div className="bg-[#e6ffe6] rounded-xl p-4 border border-green-700">
                  <h4 className="text-green-800 font-medium mb-2">
                    Document Status Summary
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-green-700">
                        Uploaded: {documents.filter((d) => !d.isMissing).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-orange-700">
                        Missing: {documents.filter((d) => d.isMissing).length}
                      </span>
                    </div>
                    <div>
                      <span className="text-green-700">
                        Approved:{" "}
                        {
                          documents.filter(
                            (d) => d.documentState === "approved"
                          ).length
                        }
                      </span>
                    </div>
                    <div>
                      <span className="text-red-700">
                        Rejected:{" "}
                        {
                          documents.filter(
                            (d) => d.documentState === "rejected"
                          ).length
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Documents List */}
            {!loading && !error && (
              <div className="p-4">
                {documents.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText
                      size={48}
                      className="mx-auto text-gray-300 mb-4"
                    />
                    <h4 className="text-gray-500 font-medium mb-2">
                      No Documents Found
                    </h4>
                    <p className="text-gray-400 text-sm">
                      You haven't uploaded any documents yet. Complete an
                      application to upload documents.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {documents.map((document) => (
                      <div
                        key={`${document.applicationId}-${document.documentType}`}
                        className={`rounded-xl shadow-md p-4 ${
                          document.isMissing
                            ? "bg-orange-50 border-2 border-dashed border-orange-200"
                            : "bg-white border border-gray-100"
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center">
                            <div
                              className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                                document.isMissing
                                  ? "bg-orange-100"
                                  : "bg-[#e6ffe6]"
                              }`}
                            >
                              {document.isMissing ? (
                                <Upload size={18} className="text-orange-600" />
                              ) : (
                                <FileText
                                  size={18}
                                  className="text-[#009639]"
                                />
                              )}
                            </div>
                            <div>
                              <h4 className="text-[#333333] font-medium">
                                {document.documentName ||
                                  formatDocumentName(document.documentType)}
                                {document.isRequired && (
                                  <span className="text-red-500 ml-1">*</span>
                                )}
                              </h4>
                              <p className="text-xs text-[#797879]">
                                {document.isMissing
                                  ? document.documentDescription
                                  : `Application #${document.applicationId}`}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {getStatusIcon(document.status)}
                          </div>
                        </div>

                        <div className="flex items-center justify-between mb-3">
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              document.isMissing
                                ? "text-orange-600 bg-orange-100"
                                : getStatusColor(document.status)
                            }`}
                          >
                            {document.isMissing
                              ? "Required"
                              : getStatusText(document.status)}
                          </span>
                          {!document.isMissing && (
                            <span className="text-xs text-[#797879]">
                              Uploaded: {formatDate(document.uploadedAt)}
                            </span>
                          )}
                        </div>

                        <div className="flex space-x-2">
                          {document.isMissing ||
                          document.documentState === "rejected" ? (
                            // Show upload button for missing or rejected documents
                            <button
                              className={`flex-1 py-2 px-3 rounded-full text-sm font-medium flex items-center justify-center transition-colors ${
                                uploadingDocuments.has(
                                  `${document.applicationId}-${document.documentType}`
                                )
                                  ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                                  : "bg-[#009639] text-white hover:bg-[#007A2F]"
                              }`}
                              onClick={() => handleFileSelect(document)}
                              disabled={uploadingDocuments.has(
                                `${document.applicationId}-${document.documentType}`
                              )}
                              title={
                                document.isMissing
                                  ? "Upload Document"
                                  : "Re-upload Document"
                              }
                            >
                              {uploadingDocuments.has(
                                `${document.applicationId}-${document.documentType}`
                              ) ? (
                                <>
                                  <RefreshCw
                                    size={14}
                                    className="mr-1 animate-spin"
                                  />
                                  Uploading...
                                </>
                              ) : (
                                <>
                                  <Upload size={14} className="mr-1" />
                                  {document.isMissing ? "Upload" : "Re-upload"}
                                </>
                              )}
                            </button>
                          ) : (
                            // Show view/download buttons for uploaded documents
                            <>
                              <button
                                className="bg-gray-100 text-gray-700 py-2 px-3 rounded-full text-sm flex items-center justify-center hover:bg-gray-200 transition-colors"
                                onClick={() => handleView(document)}
                                title="View Document"
                              >
                                <Eye size={14} />
                              </button>
                              <button
                                className="bg-gray-100 text-gray-700 py-2 px-3 rounded-full text-sm flex items-center justify-center hover:bg-gray-200 transition-colors"
                                onClick={() => handleDownload(document)}
                                title="Download Document"
                              >
                                <Download size={14} />
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Upload Guidelines */}
            <div className="p-4">
              <div className="bg-gray-50 rounded-xl p-4 border border-gray-100">
                <h4 className="text-[#333333] font-medium mb-2">
                  Upload Guidelines
                </h4>
                <ul className="text-sm text-[#797879] space-y-1">
                  <li>• Ensure documents are clear and readable</li>
                  <li>• Accepted formats: PDF, JPG, PNG</li>
                  <li>• Maximum file size: 10MB</li>
                  <li>• Documents must be valid and not expired</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
