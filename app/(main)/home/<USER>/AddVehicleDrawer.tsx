"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { ArrowLeft, X, Camera, CheckCircle, Car, Loader2 } from "lucide-react";
import { DocumentUpload, generateDocumentUrl } from "@/lib/utils";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  createVehicleForListing,
  getVehicleMakesForListing,
  getVehicleModelsByMakeForListing,
  getVehicleVariantsByModelForListing,
} from "@/drizzle-actions/vehicle-listing";
import { Alert, AlertDescription } from "@/components/ui/alert";

// Component to handle S3 path conversion for structured photos
const StructuredPhotoDisplay = ({
  mediaPath,
  alt,
  className = "",
}: {
  mediaPath: string;
  alt: string;
  className?: string;
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const convertPath = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        const url = await generateDocumentUrl(mediaPath);
        setImageUrl(url);
      } catch (error) {
        console.error("Error converting S3 path to URL:", error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (mediaPath) {
      convertPath();
    }
  }, [mediaPath]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
      </div>
    );
  }

  if (hasError || !imageUrl) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <Car size={24} className="text-gray-400" />
      </div>
    );
  }

  return <img src={imageUrl} alt={alt} className={className} />;
};

interface VehicleData {
  makeId?: number;
  modelId?: number;
  variantId?: number;
  make: string;
  model: string;
  year: string;
  color: string;
  vinNumber: string;
  vehicleRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  countryId?: number;
  images: string[];
}

interface AddVehicleDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onVehicleCreated: () => void;
}

export default function AddVehicleDrawer({
  isOpen,
  onClose,
  onVehicleCreated,
}: AddVehicleDrawerProps) {
  const [currentStep, setCurrentStep] = useState<"form" | "photos">("form");

  // Debug logging for step changes
  useEffect(() => {
    console.log("currentStep changed to:", currentStep);
  }, [currentStep]);
  const [isCreatingVehicle, setIsCreatingVehicle] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Vehicle form data
  const [vehicleData, setVehicleData] = useState<VehicleData>({
    makeId: undefined,
    modelId: undefined,
    variantId: undefined,
    make: "",
    model: "",
    year: "",
    color: "",
    vinNumber: "",
    vehicleRegistration: "",
    manufacturingYear: undefined,
    purchaseDate: "",
    countryId: 1,
    images: [],
  });

  // Dropdown data
  const [makes, setMakes] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [variants, setVariants] = useState<any[]>([]);
  const [selectedMake, setSelectedMake] = useState<any>(null);
  const [selectedModel, setSelectedModel] = useState<any>(null);
  const [selectedVariant, setSelectedVariant] = useState<any>(null);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingVariants, setLoadingVariants] = useState(false);

  // Photo upload state
  const [structuredPhotos, setStructuredPhotos] = useState({
    front: null as string | null,
    sideRight: null as string | null,
    sideLeft: null as string | null,
    rear: null as string | null,
    interior1: null as string | null,
    interior2: null as string | null,
    interior3: null as string | null,
    extras1: null as string | null,
    extras2: null as string | null,
    extras3: null as string | null,
    extras4: null as string | null,
  });
  const [photoUploadError, setPhotoUploadError] = useState<string | null>(null);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState<string | null>(null);

  // Load vehicle makes when drawer opens
  useEffect(() => {
    const loadMakes = async () => {
      if (!isOpen) return;

      setLoadingMakes(true);
      try {
        const makesData = await getVehicleMakesForListing();
        setMakes(makesData);
      } catch (error) {
        console.error("Error loading makes:", error);
      } finally {
        setLoadingMakes(false);
      }
    };

    loadMakes();
  }, [isOpen]);

  // Load models when make is selected
  useEffect(() => {
    const loadModels = async () => {
      if (!selectedMake) {
        setModels([]);
        setSelectedModel(null);
        setVariants([]);
        setSelectedVariant(null);
        return;
      }

      setLoadingModels(true);
      try {
        const modelsData = await getVehicleModelsByMakeForListing(
          selectedMake.id
        );
        setModels(modelsData);
        setSelectedModel(null);
        setVariants([]);
        setSelectedVariant(null);
      } catch (error) {
        console.error("Error loading models:", error);
      } finally {
        setLoadingModels(false);
      }
    };

    loadModels();
  }, [selectedMake]);

  // Load variants when model is selected
  useEffect(() => {
    const loadVariants = async () => {
      if (!selectedModel) {
        setVariants([]);
        setSelectedVariant(null);
        return;
      }

      setLoadingVariants(true);
      try {
        const variantsData = await getVehicleVariantsByModelForListing(
          selectedModel.id
        );
        setVariants(variantsData);
        setSelectedVariant(null);
      } catch (error) {
        console.error("Error loading variants:", error);
      } finally {
        setLoadingVariants(false);
      }
    };

    loadVariants();
  }, [selectedModel]);

  // Reset form when drawer closes
  useEffect(() => {
    if (!isOpen) {
      setCurrentStep("form");
      setError(null);
      setPhotoUploadError(null);
      setVehicleData({
        makeId: undefined,
        modelId: undefined,
        variantId: undefined,
        make: "",
        model: "",
        year: "",
        color: "",
        vinNumber: "",
        vehicleRegistration: "",
        manufacturingYear: undefined,
        purchaseDate: "",
        countryId: 1,
        images: [],
      });
      setSelectedMake(null);
      setSelectedModel(null);
      setSelectedVariant(null);
      setStructuredPhotos({
        front: null,
        sideRight: null,
        sideLeft: null,
        rear: null,
        interior1: null,
        interior2: null,
        interior3: null,
        extras1: null,
        extras2: null,
        extras3: null,
        extras4: null,
      });
    }
  }, [isOpen]);

  // Handle escape key to navigate back
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        e.preventDefault();
        handleBack();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      return () => document.removeEventListener("keydown", handleEscape);
    }
  }, [isOpen, currentStep]);

  const handleStructuredPhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    category: keyof typeof structuredPhotos
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setPhotoUploadError(null);
    setIsUploadingPhoto(category);

    try {
      const file = files[0];
      const uploadResult = await DocumentUpload(file, "vehicleMedia");

      if (!uploadResult?.path) {
        throw new Error("Upload failed");
      }

      setStructuredPhotos((prev) => ({
        ...prev,
        [category]: uploadResult.path,
      }));

      // Update vehicleData with all photos
      const allPhotos = Object.values({
        ...structuredPhotos,
        [category]: uploadResult.path,
      }).filter(Boolean) as string[];

      setVehicleData((prev) => ({
        ...prev,
        images: allPhotos,
      }));
    } catch (error) {
      console.error("Error uploading photo:", error);
      setPhotoUploadError("Failed to upload photo. Please try again.");
    } finally {
      setIsUploadingPhoto(null);
    }
  };

  const removeStructuredPhoto = (category: keyof typeof structuredPhotos) => {
    setStructuredPhotos((prev) => ({
      ...prev,
      [category]: null,
    }));

    // Update vehicleData with remaining photos
    const updatedPhotos = { ...structuredPhotos, [category]: null };
    const allPhotos = Object.values(updatedPhotos).filter(Boolean) as string[];
    setVehicleData((prev) => ({
      ...prev,
      images: allPhotos,
    }));
  };

  const canProceedFromForm = () => {
    return selectedModel && vehicleData.vinNumber && vehicleData.color;
  };

  const canProceedFromPhotos = () => {
    return (
      structuredPhotos.front &&
      structuredPhotos.sideRight &&
      structuredPhotos.sideLeft &&
      structuredPhotos.rear &&
      structuredPhotos.interior1
    );
  };

  const handleNext = () => {
    console.log("handleNext called, currentStep:", currentStep);
    console.log("canProceedFromForm:", canProceedFromForm());
    console.log("canProceedFromPhotos:", canProceedFromPhotos());

    if (currentStep === "form" && canProceedFromForm()) {
      console.log("Moving from form to photos");
      setCurrentStep("photos");
    } else if (currentStep === "photos" && canProceedFromPhotos()) {
      console.log("Creating vehicle");
      handleCreateVehicle();
    }
  };

  const handleBack = () => {
    console.log("handleBack called, currentStep:", currentStep);
    if (currentStep === "photos") {
      setCurrentStep("form");
      setError(null); // Clear any errors when going back
      setPhotoUploadError(null); // Clear photo upload errors
    } else {
      onClose();
    }
  };

  const handleCreateVehicle = async () => {
    if (!selectedModel || !vehicleData.vinNumber) return;

    setIsCreatingVehicle(true);
    setError(null);

    try {
      const result = await createVehicleForListing({
        vinNumber: vehicleData.vinNumber,
        modelId: selectedModel.id,
        vehicleRegistration: vehicleData.vehicleRegistration,
        manufacturingYear:
          vehicleData.manufacturingYear ||
          parseInt(vehicleData.year) ||
          undefined,
        purchaseDate: vehicleData.purchaseDate,
        color: vehicleData.color,
        countryId: vehicleData.countryId || 1,
        images: vehicleData.images,
      });

      if (result.success && result.vehicleId) {
        // Vehicle created successfully
        onVehicleCreated();
        onClose();
      } else {
        setError(result.error || "Failed to create vehicle");
      }
    } catch (error) {
      console.error("Error creating vehicle:", error);
      setError("An error occurred while creating the vehicle");
    } finally {
      setIsCreatingVehicle(false);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "form":
        return "Add New Vehicle";
      case "photos":
        return "Vehicle Photos";
      default:
        return "Add Vehicle";
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case "form":
        return "Enter your vehicle details";
      case "photos":
        return "Upload clear photos of your vehicle";
      default:
        return "Add a new vehicle to your account";
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title={currentStep === "photos" ? "Back to Details" : "Cancel"}
                onClick={handleBack}
                className="mr-3 rounded-full p-2 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={20} />
              </button>
              <div className="flex-1">
                <SheetTitle className="text-xl font-bold text-white">
                  {getStepTitle()}
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {getStepDescription()}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span
                className={
                  currentStep === "form" ? "text-[#009639] font-medium" : ""
                }
              >
                Details
              </span>
              <span
                className={
                  currentStep === "photos" ? "text-[#009639] font-medium" : ""
                }
              >
                Photos
              </span>
            </div>
            <div className="mt-2 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{
                  width: currentStep === "form" ? "50%" : "100%",
                }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {error && (
              <Alert className="mb-4 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            {currentStep === "form" && (
              <div className="space-y-4">
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Vehicle Information
                  </h4>

                  <div className="space-y-4">
                    {/* Make Selection */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Vehicle Make
                      </label>
                      <select
                        title="Select vehicle make"
                        value={selectedMake?.id || ""}
                        onChange={(e) => {
                          const make = makes.find(
                            (m) => m.id === parseInt(e.target.value)
                          );
                          setSelectedMake(make || null);
                          setVehicleData((prev) => ({
                            ...prev,
                            makeId: make?.id,
                            make: make?.name || "",
                          }));
                        }}
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        disabled={loadingMakes}
                      >
                        <option value="">
                          {loadingMakes ? "Loading makes..." : "Select make"}
                        </option>
                        {makes.map((make) => (
                          <option key={make.id} value={make.id}>
                            {make.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Model Selection */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Vehicle Model
                      </label>
                      <select
                        title="Select vehicle model"
                        value={selectedModel?.id || ""}
                        onChange={(e) => {
                          const model = models.find(
                            (m) => m.id === parseInt(e.target.value)
                          );
                          setSelectedModel(model || null);
                          setVehicleData((prev) => ({
                            ...prev,
                            modelId: model?.id,
                            model: model?.model || "",
                          }));
                        }}
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        disabled={!selectedMake || loadingModels}
                      >
                        <option value="">
                          {loadingModels ? "Loading models..." : "Select model"}
                        </option>
                        {models.map((model) => (
                          <option key={model.id} value={model.id}>
                            {model.model}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Variant Selection */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Vehicle Variant
                      </label>
                      <select
                        title="Select vehicle variant"
                        value={selectedVariant?.id || ""}
                        onChange={(e) => {
                          const variant = variants.find(
                            (v) => v.id === parseInt(e.target.value)
                          );
                          setSelectedVariant(variant || null);
                          setVehicleData((prev) => ({
                            ...prev,
                            variantId: variant?.id || undefined,
                            year: variant ? variant.year.toString() : prev.year,
                          }));
                        }}
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        disabled={!selectedModel || loadingVariants}
                      >
                        <option value="">
                          {loadingVariants
                            ? "Loading variants..."
                            : variants.length > 0
                              ? "Select variant (optional)"
                              : "No variants available"}
                        </option>
                        {variants.map((variant) => (
                          <option key={variant.id} value={variant.id}>
                            {variant.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Year and Color */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Year {selectedVariant && "(Auto-filled)"}
                        </label>
                        <input
                          type="number"
                          value={vehicleData.year}
                          onChange={(e) =>
                            setVehicleData((prev) => ({
                              ...prev,
                              year: e.target.value,
                            }))
                          }
                          className={`w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm ${
                            selectedVariant
                              ? "bg-gray-50 cursor-not-allowed"
                              : ""
                          }`}
                          placeholder="2020"
                          min="2000"
                          max="2024"
                          disabled={!!selectedVariant}
                        />
                      </div>
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Color
                        </label>
                        <input
                          type="text"
                          value={vehicleData.color}
                          onChange={(e) =>
                            setVehicleData((prev) => ({
                              ...prev,
                              color: e.target.value,
                            }))
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. White"
                        />
                      </div>
                    </div>

                    {/* VIN Number */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        VIN Number *
                      </label>
                      <input
                        type="text"
                        value={vehicleData.vinNumber}
                        onChange={(e) =>
                          setVehicleData((prev) => ({
                            ...prev,
                            vinNumber: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        placeholder="Vehicle Identification Number"
                      />
                    </div>

                    {/* Vehicle Registration */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Registration Number
                      </label>
                      <input
                        type="text"
                        value={vehicleData.vehicleRegistration || ""}
                        onChange={(e) =>
                          setVehicleData((prev) => ({
                            ...prev,
                            vehicleRegistration: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        placeholder="Vehicle registration number"
                      />
                    </div>

                    {/* Purchase Date */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Purchase Date
                      </label>
                      <input
                        title="Purchase Date"
                        type="date"
                        value={vehicleData.purchaseDate || ""}
                        onChange={(e) =>
                          setVehicleData((prev) => ({
                            ...prev,
                            purchaseDate: e.target.value,
                          }))
                        }
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "photos" && (
              <div className="space-y-4">
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Vehicle Photos
                  </h4>

                  {/* Vehicle Summary */}
                  <div className="bg-gray-50 p-3 rounded-lg mb-4">
                    <h6 className="font-medium text-[#333333] mb-1">
                      {vehicleData.make} {vehicleData.model} ({vehicleData.year}
                      )
                    </h6>
                    <p className="text-xs text-[#797879]">
                      VIN: {vehicleData.vinNumber} • Color: {vehicleData.color}
                    </p>
                  </div>

                  {/* Photo Upload Sections */}
                  <div className="space-y-6">
                    {/* Required Photos */}
                    {[
                      { key: "front", label: "Front View", required: true },
                      {
                        key: "sideRight",
                        label: "Side View Right",
                        required: true,
                      },
                      {
                        key: "sideLeft",
                        label: "Side View Left",
                        required: true,
                      },
                      { key: "rear", label: "Rear View", required: true },
                      {
                        key: "interior1",
                        label: "Interior View 1",
                        required: true,
                      },
                    ].map(({ key, label, required }) => (
                      <div key={key} className="rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <span className="text-sm font-medium text-[#333333]">
                              {label}
                            </span>
                            {required && (
                              <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                                Required
                              </span>
                            )}
                          </div>
                          {structuredPhotos[
                            key as keyof typeof structuredPhotos
                          ] && (
                            <CheckCircle size={16} className="text-green-500" />
                          )}
                        </div>
                        {!structuredPhotos[
                          key as keyof typeof structuredPhotos
                        ] ? (
                          <label
                            className={`flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg transition-colors ${
                              isUploadingPhoto === key
                                ? "cursor-not-allowed bg-gray-50"
                                : "cursor-pointer hover:border-[#009639]"
                            }`}
                          >
                            <div className="text-center">
                              {isUploadingPhoto === key ? (
                                <>
                                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#009639] mx-auto mb-1"></div>
                                  <span className="text-xs text-gray-500">
                                    Uploading...
                                  </span>
                                </>
                              ) : (
                                <>
                                  <Camera
                                    size={20}
                                    className="mx-auto mb-1 text-gray-400"
                                  />
                                  <span className="text-xs text-gray-500">
                                    {key === "front" &&
                                      "Clear shot of vehicle front"}
                                    {key === "sideRight" &&
                                      "Right side profile view"}
                                    {key === "sideLeft" &&
                                      "Left side profile view"}
                                    {key === "rear" && "Back of the vehicle"}
                                    {key === "interior1" &&
                                      "Dashboard and front seats"}
                                  </span>
                                </>
                              )}
                            </div>
                            <input
                              type="file"
                              className="hidden"
                              accept="image/*"
                              disabled={isUploadingPhoto === key}
                              onChange={(e) =>
                                handleStructuredPhotoUpload(
                                  e,
                                  key as keyof typeof structuredPhotos
                                )
                              }
                            />
                          </label>
                        ) : (
                          <div className="relative">
                            <StructuredPhotoDisplay
                              mediaPath={
                                structuredPhotos[
                                  key as keyof typeof structuredPhotos
                                ]!
                              }
                              alt={label}
                              className="w-full h-20 object-cover rounded-lg border border-gray-200"
                            />
                            <button
                              title={`Remove ${label.toLowerCase()} photo`}
                              onClick={() =>
                                removeStructuredPhoto(
                                  key as keyof typeof structuredPhotos
                                )
                              }
                              className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                            >
                              <X size={12} />
                            </button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {photoUploadError && (
                    <div className="text-red-500 text-sm text-center mt-4">
                      {photoUploadError}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                isCreatingVehicle ||
                (currentStep === "form" && !canProceedFromForm()) ||
                (currentStep === "photos" && !canProceedFromPhotos())
              }
              className={`w-full py-3 rounded-full font-semibold transition-all ${
                isCreatingVehicle ||
                (currentStep === "form" && !canProceedFromForm()) ||
                (currentStep === "photos" && !canProceedFromPhotos())
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-[#009639] text-white hover:bg-[#007A2F]"
              }`}
            >
              {isCreatingVehicle ? (
                <span className="flex items-center justify-center">
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Creating Vehicle...
                </span>
              ) : currentStep === "form" ? (
                "Continue to Photos"
              ) : (
                "Create Vehicle"
              )}
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
