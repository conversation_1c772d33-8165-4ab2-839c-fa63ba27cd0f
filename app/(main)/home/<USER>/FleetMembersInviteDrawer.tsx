"use client";

import { useState } from "react";
import { ArrowLeft, Users, Mail, Plus, X, ChevronRight, User, Shield } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface FleetMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: {
    driver: boolean;
    admin: boolean;
  };
}

interface FleetMembersInviteDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onComplete: (members: FleetMember[]) => void;
  fleetData: any; // Company or Individual fleet data
}

export default function FleetMembersInviteDrawer({
  isOpen,
  onClose,
  onBack,
  onComplete,
  fleetData,
}: FleetMembersInviteDrawerProps) {
  const [members, setMembers] = useState<FleetMember[]>([]);
  const [newMember, setNewMember] = useState({
    firstName: "",
    lastName: "",
    email: "",
    roles: { driver: false, admin: false },
  });

  const addMember = () => {
    if (
      newMember.firstName.trim() &&
      newMember.lastName.trim() &&
      newMember.email.trim() &&
      (newMember.roles.driver || newMember.roles.admin)
    ) {
      const member: FleetMember = {
        id: Date.now().toString(),
        firstName: newMember.firstName.trim(),
        lastName: newMember.lastName.trim(),
        email: newMember.email.trim(),
        roles: { ...newMember.roles },
      };

      setMembers([...members, member]);
      setNewMember({
        firstName: "",
        lastName: "",
        email: "",
        roles: { driver: false, admin: false },
      });
    }
  };

  const removeMember = (id: string) => {
    setMembers(members.filter(m => m.id !== id));
  };

  const toggleRole = (role: "driver" | "admin") => {
    setNewMember({
      ...newMember,
      roles: {
        ...newMember.roles,
        [role]: !newMember.roles[role],
      },
    });
  };

  const handleComplete = () => {
    onComplete(members);
  };

  const isNewMemberValid = 
    newMember.firstName.trim() &&
    newMember.lastName.trim() &&
    newMember.email.trim() &&
    (newMember.roles.driver || newMember.roles.admin);

  const getRoleDisplay = (roles: { driver: boolean; admin: boolean }) => {
    if (roles.driver && roles.admin) return "Driver & Admin";
    if (roles.driver) return "Driver";
    if (roles.admin) return "Admin";
    return "";
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                title="Back"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Invite Fleet Members
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Add members to {fleetData?.groupName || "your fleet"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Add Member Form */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-4">
                  Invite New Member
                </h4>

                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-4">
                    {/* Name Fields */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          First Name *
                        </label>
                        <input
                          type="text"
                          value={newMember.firstName}
                          onChange={(e) => setNewMember({ ...newMember, firstName: e.target.value })}
                          placeholder="First name"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        />
                      </div>
                      
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Last Name *
                        </label>
                        <input
                          type="text"
                          value={newMember.lastName}
                          onChange={(e) => setNewMember({ ...newMember, lastName: e.target.value })}
                          placeholder="Last name"
                          className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        />
                      </div>
                    </div>

                    {/* Email Field */}
                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        value={newMember.email}
                        onChange={(e) => setNewMember({ ...newMember, email: e.target.value })}
                        placeholder="Enter email address"
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                      />
                    </div>

                    {/* Role Selection */}
                    <div>
                      <label className="text-[#797879] text-xs mb-2 block">
                        Role(s) *
                      </label>
                      <div className="flex gap-3">
                        <button
                          type="button"
                          onClick={() => toggleRole("driver")}
                          className={`flex-1 py-2 px-3 rounded-lg border transition-colors ${
                            newMember.roles.driver
                              ? "bg-[#e6ffe6] border-[#009639] text-[#009639]"
                              : "bg-white border-gray-200 text-[#797879] hover:border-[#009639]"
                          }`}
                        >
                          <div className="flex items-center justify-center">
                            <User size={16} className="mr-2" />
                            Driver
                          </div>
                        </button>
                        
                        <button
                          type="button"
                          onClick={() => toggleRole("admin")}
                          className={`flex-1 py-2 px-3 rounded-lg border transition-colors ${
                            newMember.roles.admin
                              ? "bg-[#e6ffe6] border-[#009639] text-[#009639]"
                              : "bg-white border-gray-200 text-[#797879] hover:border-[#009639]"
                          }`}
                        >
                          <div className="flex items-center justify-center">
                            <Shield size={16} className="mr-2" />
                            Admin
                          </div>
                        </button>
                      </div>
                      <p className="text-xs text-[#797879] mt-1">
                        Select one or both roles for this member
                      </p>
                    </div>

                    {/* Add Button */}
                    <button
                      onClick={addMember}
                      disabled={!isNewMemberValid}
                      className="w-full bg-[#009639] text-white py-2 rounded-lg font-medium transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      <Plus size={16} className="mr-2" />
                      Add Member
                    </button>
                  </div>
                </div>
              </div>

              {/* Members List */}
              {members.length > 0 && (
                <div>
                  <h4 className="font-semibold text-[#333333] mb-3">
                    Members to Invite ({members.length})
                  </h4>
                  <div className="space-y-3">
                    {members.map((member) => (
                      <div
                        key={member.id}
                        className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center mb-1">
                              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                                <User size={14} className="text-[#009639]" />
                              </div>
                              <div>
                                <h5 className="font-medium text-[#333333]">
                                  {member.firstName} {member.lastName}
                                </h5>
                                <p className="text-xs text-[#797879]">{member.email}</p>
                              </div>
                            </div>
                            <div className="ml-11">
                              <span className="text-xs bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded-full">
                                {getRoleDisplay(member.roles)}
                              </span>
                            </div>
                          </div>
                          <button
                            onClick={() => removeMember(member.id)}
                            className="text-red-500 hover:text-red-700 ml-2"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleComplete}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors flex items-center justify-center"
            >
              {members.length > 0 ? `Send ${members.length} Invite${members.length > 1 ? 's' : ''}` : 'Create Fleet'}
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onBack}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Back
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
