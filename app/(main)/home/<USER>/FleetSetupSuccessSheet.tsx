"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import {
  CheckCircle,
  Users,
  Car,
  Mail,
  Plus,
  Settings,
} from "lucide-react";

interface FleetSetupSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  fleetData: {
    groupName: string;
    fleetType: "company" | "individual";
    memberCount: number;
  };
}

export default function FleetSetupSuccessSheet({
  isOpen,
  onClose,
  fleetData,
}: FleetSetupSuccessSheetProps) {
  const handleManageFleet = () => {
    // Navigate to fleet management page
    onClose();
    // TODO: Navigate to fleet dashboard
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* <PERSON> Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <CheckCircle size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Fleet Created!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              {fleetData.groupName} is ready for vehicle management
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Fleet Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3 flex items-center">
                  <Users size={16} className="mr-2 text-[#009639]" />
                  Fleet Summary
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Fleet Name:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {fleetData.groupName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Fleet Type:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {fleetData.fleetType === "company" ? "Company Fleet" : "Individual Fleet"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Members Invited:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {fleetData.memberCount} member{fleetData.memberCount !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  What happens next?
                </h4>
                <div className="space-y-3">
                  {fleetData.memberCount > 0 && (
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 mt-0.5">
                        <Mail size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Invitations Sent
                        </p>
                        <p className="text-xs text-[#797879]">
                          Fleet members will receive email invitations to join your fleet
                        </p>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <Plus size={12} className="text-[#009639]" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Add Your Vehicles
                      </p>
                      <p className="text-xs text-[#797879]">
                        Start adding vehicles to your fleet from the fleet dashboard
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <Settings size={12} className="text-[#009639]" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Configure Fleet Settings
                      </p>
                      <p className="text-xs text-[#797879]">
                        Set up permissions, tracking, and management preferences
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fleet Benefits */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Fleet Management Benefits
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Real-time vehicle tracking and monitoring</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Automated maintenance scheduling</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Digital document management</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Driver assignment and performance tracking</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Comprehensive fleet analytics</span>
                  </div>
                </div>
              </div>

              {/* Call to Action */}
              <div className="rounded-xl bg-[#e6ffe6] border border-[#009639] p-4">
                <div className="flex items-center mb-2">
                  <Car size={16} className="text-[#009639] mr-2" />
                  <h5 className="font-semibold text-[#009639]">
                    Ready to Add Vehicles?
                  </h5>
                </div>
                <p className="text-sm text-[#007A2F] mb-3">
                  Start building your fleet by adding your first vehicle. You can upload documents, set maintenance schedules, and assign drivers.
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleManageFleet}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors hover:bg-[#007A2F] flex items-center justify-center"
            >
              <Car size={16} className="mr-2" />
              Manage Fleet
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
