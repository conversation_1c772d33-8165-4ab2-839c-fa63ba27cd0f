"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>ef<PERSON>, DollarSign, Loader2 } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  submitEhailingApplication,
  submitRentalFractionalApplication,
} from "@/actions/applications";
import ApplicationDocumentUploader, {
  type UploadedDocument as ApplicationUploadedDocument,
} from "@/components/ApplicationDocumentUploader";
import Image from "next/image";
import { useAmplifyImage } from "@/hooks/use-amplify-image";

interface CatalogVehicleImageProps {
  images?: Array<{
    id: number;
    imageUrl: string;
    isPrimary: boolean;
  }>;
  make: string;
  model: string;
  className?: string;
  fill?: boolean;
}

function CatalogVehicleImage({
  images,
  make,
  model,
  className,
  fill = false,
}: CatalogVehicleImageProps) {
  // Find the primary image or use the first one
  const primaryImage = images?.find((img) => img.isPrimary) || images?.[0];

  // Use useAmplifyImage hook for S3 URLs
  const { imageUrl } = useAmplifyImage(
    primaryImage?.imageUrl || "",
    "/images/cars/suzuki-dzire.webp"
  );

  // Use the presigned URL if available, otherwise fallback to default
  const displayUrl = imageUrl || "/images/cars/suzuki-dzire.webp";

  return (
    <Image
      src={displayUrl}
      alt={`${make} ${model}`}
      className={className}
      fill={fill}
      {...(!fill && { width: 200, height: 150 })}
    />
  );
}

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  weeklyFeeTarget?: number; // ✅ Add this - target weekly fee from catalog
  rate?: number; // ✅ Add this - actual rate from listing
  period?: string; // ✅ Add this - rental period (daily, weekly, monthly, etc.)
  image?: string; // ✅ Add this - vehicle image
  requirements: {
    minDeposit: number;
    documents: string[];
  };
}

interface ApplicationProcessDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  selectedVehicle: Vehicle | null;
  listingId?: number;
  listingType?: "ehailing-platform" | "rental" | "fractional"; // Add this
}

interface DocumentUpload {
  name: string;
  uploaded: boolean;
  required: boolean;
  file?: File;
  isSpecial?: boolean; // For proof of residence and selfie
}

interface InitiationFeeData {
  needsPaymentArrangement: boolean;
}

interface ExperienceData {
  hasExperience: boolean;
  duration?: string;
  company?: string;
  reasonStopped?: string;
  workType?: "part-time" | "full-time";
  profileNumber?: string;
}

export default function ApplicationProcessDrawer({
  isOpen,
  onClose,
  onSubmit,
  selectedVehicle,
  listingId,
  listingType = "ehailing-platform", // Default to e-hailing
}: ApplicationProcessDrawerProps) {
  const cameraRef = useRef<HTMLInputElement>(null);

  // Form submission state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [applicationId, setApplicationId] = useState<number | null>(null);

  // Document upload state for ApplicationDocumentUploader
  const [applicationDocuments, setApplicationDocuments] = useState<
    ApplicationUploadedDocument[]
  >([]);

  // Create draft application when component mounts
  useEffect(() => {
    const createDraftApplication = async () => {
      if (!selectedVehicle?.id || applicationId) return;

      try {
        console.log(
          "Creating draft application for listing:",
          selectedVehicle.id
        );
        const formData = new FormData();
        formData.append("listingId", selectedVehicle.id.toString());

        if (listingType === "ehailing-platform") {
          formData.append("hasEhailingExperience", "false");
          formData.append("arrangementRequested", "false");
          const result = await submitEhailingApplication(null, formData);
          if (result.success && result.applicationId) {
            setApplicationId(result.applicationId);
            console.log(
              "Draft e-hailing application created:",
              result.applicationId
            );
          }
        } else {
          formData.append("purpose", "business");
          const result = await submitRentalFractionalApplication(
            null,
            formData
          );
          if (result.success && result.applicationId) {
            setApplicationId(result.applicationId);
            console.log(
              "Draft rental/fractional application created:",
              result.applicationId
            );
          }
        }
      } catch (error) {
        console.error("Error creating draft application:", error);
      }
    };

    if (isOpen && selectedVehicle?.id) {
      createDraftApplication();

      // Initialize documents for the application
      const requiredDocs: ApplicationUploadedDocument[] = getDocumentsByType(
        listingType
      ).map((doc) => ({
        name: doc.name,
        documentType: doc.name.toLowerCase().replace(/[^a-z0-9]/g, "_"),
        uploaded: false,
        required: doc.required,
        isSpecial: doc.isSpecial,
      }));
      setApplicationDocuments(requiredDocs);
    }
  }, [isOpen, selectedVehicle?.id, listingType, applicationId]);

  // Dynamic documents based on listing type
  const getDocumentsByType = (type: string) => {
    switch (type) {
      case "rental":
      case "fractional":
        return [
          { name: "ID Document", uploaded: false, required: true },
          { name: "Driver's license", uploaded: false, required: true },
          { name: "Proof of Income", uploaded: false, required: true },
          {
            name: "Bank Statement - 3 months",
            uploaded: false,
            required: true,
          },
          {
            name: "Proof of residence",
            uploaded: false,
            required: true,
            isSpecial: true,
          },
          { name: "Selfie", uploaded: false, required: true, isSpecial: true },
        ];
      case "ehailing-platform":
      default:
        return [
          { name: "ID Document", uploaded: false, required: true },
          { name: "Driver's license", uploaded: false, required: true },
          {
            name: "Bank Statement - 3 months",
            uploaded: false,
            required: true,
          },
          {
            name: "Proof of residence",
            uploaded: false,
            required: true,
            isSpecial: true,
          },
          { name: "Selfie", uploaded: false, required: true, isSpecial: true },
          {
            name: "PrDP (Professional driving permit)",
            uploaded: false,
            required: true,
          },
          {
            name: "Police clearance certificate",
            uploaded: false,
            required: true,
          },
        ];
    }
  };

  const [documents, setDocuments] = useState<DocumentUpload[]>(
    getDocumentsByType(listingType)
  );

  // Update documents when listingType changes
  useEffect(() => {
    setDocuments(getDocumentsByType(listingType));
  }, [listingType]);

  const [initiationFee, setInitiationFee] = useState<InitiationFeeData>({
    needsPaymentArrangement: false,
  });

  const [experience, setExperience] = useState<ExperienceData>({
    hasExperience: false,
  });

  // Dynamic step flow based on listing type
  const getStepFlow = (type: string) => {
    switch (type) {
      case "rental":
      case "fractional":
        return ["documents"]; // Only documents step, no initiation or experience
      case "ehailing-platform":
      default:
        return ["documents", "initiation", "experience"];
    }
  };

  const stepFlow = getStepFlow(listingType);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const currentStep = stepFlow[currentStepIndex];

  const handleDocumentUpload = (index: number, file: File) => {
    const updatedDocuments = [...documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      uploaded: true,
      file,
    };
    setDocuments(updatedDocuments);
  };

  const handleExperienceChange = (field: keyof ExperienceData, value: any) => {
    setExperience((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Form validation
  const canSubmitApplication = () => {
    if (!listingId) return false;

    // Experience validation
    if (experience.hasExperience) {
      return !!(
        experience.duration &&
        experience.company &&
        experience.workType &&
        experience.profileNumber
      );
    }

    return true; // No experience required
  };

  // Handle form submission based on listing type
  const handleSubmitApplication = async () => {
    if (!listingId) {
      setSubmitError("Listing ID is required");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const formData = new FormData();
      formData.append("listingId", listingId.toString());

      if (listingType === "ehailing-platform") {
        // E-hailing specific fields
        formData.append(
          "hasEhailingExperience",
          experience.hasExperience.toString()
        );

        if (experience.hasExperience) {
          formData.append("ehailingCompany", experience.company || "");
          formData.append(
            "ehailingProfileNumber",
            experience.profileNumber || ""
          );
          formData.append("ehailingWorkType", experience.workType || "");
          formData.append("drivingExperienceYears", experience.duration || "");
        }

        formData.append(
          "arrangementRequested",
          initiationFee.needsPaymentArrangement.toString()
        );

        const result = await submitEhailingApplication(null, formData);

        if (result.success && result.applicationId) {
          setApplicationId(result.applicationId);
          onSubmit();
        } else {
          setSubmitError(result.error || "Failed to submit application");
        }
      } else {
        // Rental/Fractional applications
        formData.append("listingType", listingType);
        formData.append("purpose", "business"); // Default purpose

        // We need to create a new action for rental/fractional applications
        // For now, we'll use a generic application submission
        const result = await submitRentalFractionalApplication(null, formData);

        if (result.success && result.applicationId) {
          setApplicationId(result.applicationId);
          onSubmit();
        } else {
          setSubmitError(result.error || "Failed to submit application");
        }
      }
    } catch (error) {
      console.error("Error submitting application:", error);
      setSubmitError("An unexpected error occurred. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStepIndex < stepFlow.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    } else {
      // Last step - submit application
      handleSubmitApplication();
    }
  };

  const handleBack = () => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    } else {
      onClose();
    }
  };

  const handleCameraCapture = () => {
    if (cameraRef.current) {
      cameraRef.current.click();
    }
  };

  const handleLocationCapture = async () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setProofOfResidence((prev) => ({
            ...prev,
            location: {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            },
          }));
        },
        (error) => {
          console.error("Error getting location:", error);
          alert(
            "Unable to get your location. Please ensure location access is enabled."
          );
        }
      );
    } else {
      alert("Geolocation is not supported by this browser.");
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application Process
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {listingType === "rental" || listingType === "fractional"
                    ? "Upload your required documents to complete your application"
                    : currentStep === "documents"
                      ? "Upload your required documents"
                      : currentStep === "initiation"
                        ? "Review initiation fee and payment options"
                        : "Tell us about your e-hailing experience"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-4 bg-gray-50 border-b">
            {/* Only show progress indicator if there are multiple steps */}
            {stepFlow.length > 1 && (
              <>
                <div className="flex items-center justify-between text-xs">
                  {stepFlow.map((step, index) => (
                    <span
                      key={step}
                      className={
                        index === currentStepIndex
                          ? "text-[#009639] font-medium"
                          : "text-gray-600"
                      }
                    >
                      {step === "documents"
                        ? "Documents"
                        : step === "initiation"
                          ? "Initiation Fee"
                          : "Experience"}
                    </span>
                  ))}
                </div>

                {/* Progress Bar */}
                <div className="mt-3 h-1 bg-gray-200 rounded-full">
                  <div
                    className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                    style={{
                      width: `${((currentStepIndex + 1) / stepFlow.length) * 100}%`,
                    }}
                  />
                </div>
              </>
            )}

            {/* Single step indicator for rental/fractional */}
            {stepFlow.length === 1 && (
              <div className="text-center">
                <span className="text-sm font-medium text-[#009639]">
                  Document Upload
                </span>
                <div className="mt-2 h-1 bg-[#009639] rounded-full"></div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Vehicle Summary */}
              <div className="flex items-center ">
                <h4 className="font-semibold text-[#333333] ">
                  Selected Listing
                </h4>
              </div>
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-12 bg-gray-100 rounded-lg overflow-hidden relative">
                      <CatalogVehicleImage
                        images={
                          selectedVehicle?.image
                            ? [
                                {
                                  id: selectedVehicle.id,
                                  imageUrl: selectedVehicle.image,
                                  isPrimary: true,
                                },
                              ]
                            : []
                        }
                        make={selectedVehicle?.make || ""}
                        model={selectedVehicle?.model || ""}
                        className="object-contain"
                        fill
                      />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        {selectedVehicle?.make} {selectedVehicle?.model}
                      </p>
                      <p className="text-xs text-[#797879]">
                        {selectedVehicle?.year} Model
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {listingType === "rental" && (
                      <span className="text-sm font-bold text-[#009639]">
                        R{selectedVehicle?.rate?.toLocaleString() || "TBA"}/
                        {selectedVehicle?.period || "month"}
                      </span>
                    )}
                    {listingType === "fractional" && (
                      <span className="text-sm font-bold text-[#009639]">
                        R{selectedVehicle?.rate?.toLocaleString() || "TBA"}
                      </span>
                    )}
                    {listingType === "ehailing-platform" && (
                      <span className="text-sm font-bold text-[#009639]">
                        R
                        {(
                          selectedVehicle?.weeklyFeeTarget ||
                          selectedVehicle?.weeklyRate
                        )?.toLocaleString() || "TBA"}
                        /week
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Documents Step */}
              {currentStep === "documents" && (
                <div className="space-y-6">
                  <ApplicationDocumentUploader
                    applicationType={
                      listingType === "ehailing-platform"
                        ? "ehailing"
                        : listingType === "rental"
                          ? "rental"
                          : "fractional"
                    }
                    documents={applicationDocuments}
                    setDocuments={setApplicationDocuments}
                    onError={(error) => setSubmitError(error)}
                  />
                </div>
              )}

              {/* Error Display */}
              {submitError && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertDescription className="text-red-700">
                    {submitError}
                  </AlertDescription>
                </Alert>
              )}

              {/* Initiation Fee Step */}
              {currentStep === "initiation" && (
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <DollarSign size={18} className="mr-2 text-[#009639]" />
                      <h4 className="font-semibold text-[#333333]">
                        Initiation Fee
                      </h4>
                    </div>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-4">
                        {/* Fee Display */}
                        <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h5 className="font-semibold text-[#007A2F]">
                                Initiation Fee
                              </h5>
                              <p className="text-xs text-[#007A2F] mt-1">
                                One-time payment to start your lease
                              </p>
                            </div>
                            <div className="text-right">
                              <span className="text-2xl font-bold text-[#009639]">
                                R
                                {selectedVehicle?.requirements.minDeposit.toLocaleString() ||
                                  "7,500"}
                              </span>
                            </div>
                          </div>
                        </div>

                        {/* Payment Arrangement Option */}
                        <div className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start">
                            <input
                              type="checkbox"
                              id="paymentArrangement"
                              checked={initiationFee.needsPaymentArrangement}
                              onChange={(e) =>
                                setInitiationFee((prev) => ({
                                  ...prev,
                                  needsPaymentArrangement: e.target.checked,
                                }))
                              }
                              className="mt-1 mr-3"
                            />
                            <div className="flex-1">
                              <label
                                htmlFor="paymentArrangement"
                                className="text-sm font-medium text-[#333333] cursor-pointer"
                              >
                                I need to make a payment arrangement for the
                                initiation fee
                              </label>
                              <p className="text-xs text-[#797879] mt-1">
                                Check this if you'd like to discuss payment
                                options or installments for the initiation fee.
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Payment Information */}
                        <div className="bg-gray-50 rounded-lg p-4">
                          <h6 className="font-medium text-[#333333] mb-2">
                            Payment Information
                          </h6>
                          <ul className="text-xs text-[#797879] space-y-1">
                            <li>
                              • The initiation fee is due before vehicle
                              handover
                            </li>
                            <li>
                              • Payment can be made via bank transfer or card
                            </li>
                            <li>
                              • Payment arrangements are subject to approval
                            </li>
                            <li>
                              • Weekly lease payments start after vehicle
                              handover
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Experience Step */}
              {currentStep === "experience" && (
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-[#333333] mb-3">
                      E-hailing Experience
                    </h4>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-4">
                        {/* Has Experience Question */}
                        <div>
                          <label className="text-sm font-medium text-[#333333] mb-2 block">
                            Have you driven for e-hailing before or currently?
                          </label>
                          <div className="flex space-x-4">
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === true}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", true)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">
                                Yes
                              </span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === false}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", false)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">No</span>
                            </label>
                          </div>
                        </div>

                        {/* Experience Details - Show only if has experience */}
                        {experience.hasExperience && (
                          <div className="space-y-4 pt-4 border-t border-gray-200">
                            {/* Duration */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                How long have you been driving?
                              </label>
                              <select
                                title="Duration"
                                value={experience.duration || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "duration",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select duration</option>
                                <option value="less-than-6-months">
                                  Less than 6 months
                                </option>
                                <option value="6-months-1-year">
                                  6 months - 1 year
                                </option>
                                <option value="1-2-years">1 - 2 years</option>
                                <option value="2-5-years">2 - 5 years</option>
                                <option value="more-than-5-years">
                                  More than 5 years
                                </option>
                              </select>
                            </div>

                            {/* Company */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Which company?
                              </label>
                              <select
                                title="Company"
                                value={experience.company || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "company",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select company</option>
                                <option value="uber">Uber</option>
                                <option value="bolt">Bolt</option>
                                <option value="indriver">InDriver</option>
                                <option value="other">Other</option>
                              </select>
                            </div>

                            {/* Work Type */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-2 block">
                                Part time or full time basis?
                              </label>
                              <div className="flex space-x-4">
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "part-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "part-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Part-time
                                  </span>
                                </label>
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "full-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "full-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Full-time
                                  </span>
                                </label>
                              </div>
                            </div>

                            {/* Profile Number */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Profile Number
                              </label>
                              <input
                                type="text"
                                value={experience.profileNumber || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "profileNumber",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter your profile number"
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "experience" && !canSubmitApplication()) ||
                isSubmitting
              }
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                (currentStep === "experience" && !canSubmitApplication()) ||
                isSubmitting
                  ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                  : "bg-[#009639] text-white hover:bg-[#007A2F]"
              }`}
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="mr-2 animate-spin" />
                  Submitting...
                </>
              ) : stepFlow.length === 1 ? (
                "Submit Application"
              ) : currentStep === "documents" ? (
                "Continue to Initiation Fee"
              ) : currentStep === "initiation" ? (
                "Continue to Experience"
              ) : (
                "Submit Application"
              )}
            </button>
            {currentStep === "experience" &&
              !canSubmitApplication() &&
              !isSubmitting && (
                <p className="text-xs text-[#797879] text-center mt-2">
                  Please complete all required fields
                </p>
              )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
