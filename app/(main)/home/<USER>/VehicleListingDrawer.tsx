"use client";

import { useState, useEffect, useCallback } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  Upload,
  X,
  FileText,
  Camera,
  CheckCircle,
  Car,
  PlusCircle,
  Loader2,
} from "lucide-react";
import { DocumentUpload, generateDocumentUrl } from "@/lib/utils";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { getUserVehiclesForListing } from "@/drizzle-actions/create-listings";
import { getVehicleModels } from "@/drizzle-actions/community";
import { createVehicleWithOwnershipDrizzle } from "@/drizzle-actions/listings";
import {
  createVehicleForListing,
  getVehicleMakesForListing,
  getVehicleModelsByMakeForListing,
  getVehicleVariantsByModelForListing,
} from "@/drizzle-actions/vehicle-listing";
import ListingTermsDrawer from "./ListingTermsDrawer";
import AddVehicleDrawer from "./AddVehicleDrawer";
import { Alert, AlertDescription } from "@/components/ui/alert";
// Removed VehiclePhotoUpload - using simplified photo upload interface

// Component to handle S3 path conversion for vehicle thumbnails
const VehicleThumbnail = ({
  mediaPath,
  alt,
}: {
  mediaPath: string;
  alt: string;
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const convertPath = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        const url = await generateDocumentUrl(mediaPath);
        setImageUrl(url);
      } catch (error) {
        console.error("Error converting S3 path to URL:", error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (mediaPath) {
      convertPath();
    }
  }, [mediaPath]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
      </div>
    );
  }

  if (hasError || !imageUrl) {
    return (
      <div className="flex items-center justify-center h-full">
        <Car size={24} className="text-gray-400" />
      </div>
    );
  }

  return (
    <Image
      src={imageUrl}
      alt={alt}
      width={64}
      height={64}
      className="object-cover w-full h-full"
    />
  );
};

// Component to handle S3 path conversion for structured photos
const StructuredPhotoDisplay = ({
  mediaPath,
  alt,
  className = "",
}: {
  mediaPath: string;
  alt: string;
  className?: string;
}) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const convertPath = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        const url = await generateDocumentUrl(mediaPath);
        setImageUrl(url);
      } catch (error) {
        console.error("Error converting S3 path to URL:", error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (mediaPath) {
      convertPath();
    }
  }, [mediaPath]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
      </div>
    );
  }

  if (hasError || !imageUrl) {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <Car size={24} className="text-gray-400" />
      </div>
    );
  }

  return <img src={imageUrl} alt={alt} className={className} />;
};

interface VehicleData {
  listingType: "rental" | "fractional" | "";
  rentalPurpose?: "business" | "individual";
  rentalPeriods?: Array<"daily" | "weekly" | "monthly">;
  ownershipPercentage?: string;
  allowPartialPurchase?: boolean;
  minimumPurchasePercentage?: string;

  // Vehicle selection fields
  makeId?: number;
  modelId?: number;
  variantId?: number;
  make: string;
  model: string;
  year: string;

  // Vehicle details from schema
  color: string;
  mileage: string;
  condition: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  countryId?: number;

  // Media and documents
  images: string[]; // Store S3 paths instead of File objects
  documents: DocumentUpload[];
  selectedVehicleId?: number; // Optional field for when existing vehicle is selected

  // Required fields
  vinNumber: string;
  vehicleRegistration?: string;
}

interface DocumentUpload {
  name: string;
  uploaded: boolean;
  required: boolean;
  file?: File; // Keep for UI preview
  s3Path?: string; // Store S3 path for server action
}

interface VehicleListingDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (vehicleData: VehicleData) => void;
}

export default function VehicleListingDrawer({
  isOpen,
  onClose,
  onNext,
}: VehicleListingDrawerProps) {
  // Add state for submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [currentStep, setCurrentStep] = useState<
    "listing-type" | "vehicle-selection" | "details" | "photos" | "documents"
  >("listing-type");

  const [vehicleData, setVehicleData] = useState<VehicleData>({
    listingType: "",
    rentalPurpose: undefined,
    rentalPeriods: [],
    ownershipPercentage: "",
    allowPartialPurchase: false,
    minimumPurchasePercentage: "",
    makeId: undefined,
    modelId: undefined,
    variantId: undefined,
    make: "",
    model: "",
    year: "",
    color: "",
    mileage: "",
    condition: "excellent",
    manufacturingYear: undefined,
    purchaseDate: undefined,
    countryId: 1,
    images: [],
    documents: [],
    vinNumber: "",
    vehicleRegistration: undefined,
  });

  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [documents, setDocuments] = useState<DocumentUpload[]>([
    { name: "Vehicle Registration Document", uploaded: false, required: false },
    { name: "Vehicle License Document", uploaded: false, required: false },
    { name: "DEKRA Inspection Certificate", uploaded: false, required: false },
    { name: "Insurance Certificate", uploaded: false, required: false },
    { name: "Service History", uploaded: false, required: false },
  ]);
  const [isUploadingImages, setIsUploadingImages] = useState(false);
  const [isUploadingDocument, setIsUploadingDocument] = useState<number | null>(
    null
  );

  // Add a state for selecting existing vehicles
  const [userVehicles, setUserVehicles] = useState<any[]>([]);
  const [selectedVehicleId, setSelectedVehicleId] = useState<number | null>(
    null
  );
  const [isLoadingVehicles, setIsLoadingVehicles] = useState(false);

  // States for vehicle creation form (moved to AddVehicleDrawer)
  // These can be removed as they're now handled by the separate drawer

  // ListingTermsDrawer state
  const [showListingTermsDrawer, setShowListingTermsDrawer] = useState(false);

  // AddVehicleDrawer state
  const [showAddVehicleDrawer, setShowAddVehicleDrawer] = useState(false);

  // Existing vehicle documents
  const [existingDocuments, setExistingDocuments] = useState<DocumentUpload[]>(
    []
  );

  // Simplified state management for new vehicle photos
  const [newVehicleConfigured, setNewVehicleConfigured] = useState(false);
  const [addVehicleStep, setAddVehicleStep] = useState<"form" | "photos">(
    "form"
  );

  // Structured photo categories for better admin approval
  const [structuredPhotos, setStructuredPhotos] = useState({
    front: null as string | null,
    sideRight: null as string | null,
    sideLeft: null as string | null,
    rear: null as string | null,
    interior1: null as string | null,
    interior2: null as string | null,
    interior3: null as string | null,
    extras1: null as string | null,
    extras2: null as string | null,
    extras3: null as string | null,
    extras4: null as string | null,
  });
  const [photoUploadError, setPhotoUploadError] = useState<string | null>(null);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState<string | null>(null);

  // Function to load existing documents for selected vehicle
  const loadExistingDocuments = async (vehicleId: number) => {
    try {
      // Find the selected vehicle from userVehicles
      const selectedVehicle = userVehicles.find((v) => v.id === vehicleId);
      if (selectedVehicle && selectedVehicle.vehicle_documents) {
        // Convert vehicle documents to DocumentUpload format
        const convertedDocs: DocumentUpload[] =
          selectedVehicle.vehicle_documents.map((doc: any) => ({
            name: doc.document_type || doc.name || "Document",
            uploaded: true,
            required: true,
            s3Path: doc.media_path,
          }));
        setExistingDocuments(convertedDocs);
      } else {
        // If no documents exist, set up default required documents
        setExistingDocuments([
          {
            name: "Vehicle Registration Document",
            uploaded: false,
            required: false,
          },
          {
            name: "Vehicle License Document",
            uploaded: false,
            required: false,
          },
          {
            name: "DEKRA Inspection Certificate",
            uploaded: false,
            required: false,
          },
          { name: "Insurance Certificate", uploaded: false, required: false },
          { name: "Service History", uploaded: false, required: false },
        ]);
      }
    } catch (error) {
      console.error("Error loading existing documents:", error);
      // Set default documents on error
      setExistingDocuments([
        {
          name: "Vehicle Registration Document",
          uploaded: false,
          required: false,
        },
        { name: "Vehicle License Document", uploaded: false, required: false },
        {
          name: "DEKRA Inspection Certificate",
          uploaded: false,
          required: false,
        },
        { name: "Insurance Certificate", uploaded: false, required: false },
        { name: "Service History", uploaded: false, required: false },
      ]);
    }
  };

  useEffect(() => {
    if (isOpen && currentStep === "vehicle-selection") {
      loadUserVehicles();
    }
  }, [isOpen, currentStep]);

  // Load models/variants hooks moved to AddVehicleDrawer

  const handleVehicleDataChange = (
    field: keyof VehicleData,
    value: string | string[]
  ) => {
    setVehicleData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      setIsUploadingImages(true);
      try {
        // Upload files to S3 and get paths
        const uploadPromises = files.map(async (file) => {
          const uploadResult = await DocumentUpload(file, "vehicleMedia");
          if (uploadResult && uploadResult.path) {
            return uploadResult.path;
          }
          throw new Error("Upload failed");
        });

        const uploadedPaths = await Promise.all(uploadPromises);
        const newImages = [...vehicleData.images, ...uploadedPaths].slice(0, 6); // Max 6 images
        const newPreviews = files.map((file) => URL.createObjectURL(file));

        setVehicleData((prev) => ({ ...prev, images: newImages }));
        setImagePreviews((prev) => [...prev, ...newPreviews].slice(0, 6));
      } catch (error) {
        console.error("Error uploading images:", error);
        alert("Failed to upload images. Please try again.");
      } finally {
        setIsUploadingImages(false);
      }
    }
  };

  const removeImage = (index: number) => {
    const newImages = vehicleData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);

    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(imagePreviews[index]);

    setVehicleData((prev) => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const handleDocumentUpload = async (index: number, file: File) => {
    setIsUploadingDocument(index);
    try {
      // Upload to S3 immediately
      const uploadResult = await DocumentUpload(file, "applications");

      if (uploadResult && uploadResult.path) {
        if (selectedVehicleId) {
          // Update existing documents for selected vehicle
          const updatedDocuments = [...existingDocuments];
          updatedDocuments[index] = {
            ...updatedDocuments[index],
            uploaded: true,
            file, // Keep for UI preview
            s3Path: uploadResult.path, // Store S3 path
          };
          setExistingDocuments(updatedDocuments);
        } else {
          // Update documents for new vehicle
          const updatedDocuments = [...documents];
          updatedDocuments[index] = {
            ...updatedDocuments[index],
            uploaded: true,
            file, // Keep for UI preview
            s3Path: uploadResult.path, // Store S3 path
          };
          setDocuments(updatedDocuments);
        }
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Error uploading document:", error);
      alert("Failed to upload document. Please try again.");
    } finally {
      setIsUploadingDocument(null);
    }
  };

  const canProceedFromDetails = () => {
    return (
      vehicleData.make &&
      vehicleData.model &&
      vehicleData.year &&
      vehicleData.color &&
      vehicleData.mileage
    );
  };

  const canProceedFromPhotos = () => {
    // For new vehicles, check structured photos - require front, both sides, rear, and interior1
    if (!selectedVehicleId) {
      return (
        structuredPhotos.front &&
        structuredPhotos.sideRight &&
        structuredPhotos.sideLeft &&
        structuredPhotos.rear &&
        structuredPhotos.interior1
      );
    }
    // For existing vehicles, check vehicleData.images
    return vehicleData.images.length > 0;
  };

  const canProceedFromDocuments = () => {
    const docsToCheck = selectedVehicleId ? existingDocuments : documents;
    const requiredDocs = docsToCheck.filter((doc) => doc.required);
    return requiredDocs.every((doc) => doc.uploaded);
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setError(null);

      let vehicleId: number | undefined = selectedVehicleId || undefined;

      // If this is a new vehicle (no selectedVehicleId), create it first
      if (!selectedVehicleId && newVehicleConfigured) {
        console.log("Creating new vehicle before listing...");

        // Get the model ID from vehicle data
        const modelId = vehicleData.modelId;

        if (!modelId || !vehicleData.vinNumber) {
          throw new Error(
            "Model ID and VIN are required to create vehicle. Please make sure you selected a model and entered a VIN number."
          );
        }

        const result = await createVehicleForListing({
          vinNumber: vehicleData.vinNumber,
          modelId: modelId,
          vehicleRegistration: vehicleData.vehicleRegistration,
          manufacturingYear:
            vehicleData.manufacturingYear ||
            parseInt(vehicleData.year) ||
            undefined,
          purchaseDate: vehicleData.purchaseDate,
          color: vehicleData.color,
          countryId: vehicleData.countryId || 1,
          images: vehicleData.images,
        });

        if (!result.success || !result.vehicleId) {
          throw new Error(result.error || "Failed to create vehicle");
        }

        vehicleId = result.vehicleId;
        console.log("Vehicle created successfully with ID:", vehicleId);
      }

      // Prepare the final data with vehicle selection and documents
      const finalData = {
        ...vehicleData,
        selectedVehicleId: vehicleId,
        images: vehicleData.images,
        documents: selectedVehicleId ? existingDocuments : documents,
      };

      // Pass the vehicle data to the next step (ListingTermsDrawer)
      // The actual listing creation will happen after financial terms are collected
      onNext(finalData);
    } catch (error) {
      console.error("Error preparing vehicle data:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to prepare vehicle data. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    console.log("handleNext called, currentStep:", currentStep); // Debug log
    console.log(
      "selectedVehicleId:",
      selectedVehicleId,
      "newVehicleConfigured:",
      newVehicleConfigured
    ); // Debug log

    if (currentStep === "listing-type" && vehicleData.listingType) {
      setCurrentStep("vehicle-selection");
    } else if (currentStep === "vehicle-selection") {
      if (selectedVehicleId) {
        // Existing vehicle selected
        setCurrentStep("documents");
      } else if (newVehicleConfigured) {
        // New vehicle configured
        setCurrentStep("details");
      } else {
        // No vehicle selected yet
        console.log("No vehicle selected or configured yet");
        return;
      }
    } else if (currentStep === "details" && canProceedFromDetails()) {
      // From details, always go to photos when adding new vehicle
      setCurrentStep("photos");
    } else if (currentStep === "photos" && canProceedFromPhotos()) {
      // From photos, go to documents
      setCurrentStep("documents");
    } else if (currentStep === "documents" && canProceedFromDocuments()) {
      // Call handleSubmit instead of showing ListingTermsDrawer
      handleSubmit();
    }
  };

  const handleBack = () => {
    if (currentStep === "vehicle-selection") {
      setCurrentStep("listing-type");
    } else if (currentStep === "details") {
      // Reset the new vehicle flag when going back to vehicle selection
      setNewVehicleConfigured(false);
      setCurrentStep("vehicle-selection");
    } else if (currentStep === "photos") {
      // If user selected existing vehicle, go back to vehicle-selection
      // If user is adding new vehicle, go back to details
      if (selectedVehicleId) {
        setCurrentStep("vehicle-selection");
      } else {
        setCurrentStep("details");
      }
    } else if (currentStep === "documents") {
      // If user selected existing vehicle, go back to vehicle-selection
      // If user is adding new vehicle, go back to photos
      if (selectedVehicleId) {
        setCurrentStep("vehicle-selection");
      } else {
        setCurrentStep("photos");
      }
    } else {
      onClose();
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "listing-type":
        return "Choose Monetization Method";
      case "vehicle-selection":
        return "Select a Vehicle";
      case "details":
        return "Vehicle Details";
      case "photos":
        return "Vehicle Photos";
      case "documents":
        return "Required Documents";
      default:
        return "List Your Vehicle";
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case "listing-type":
        return "How do you want to earn from your vehicle?";
      case "vehicle-selection":
        return "Select an existing vehicle or add a new one";
      case "details":
        return "Tell us about your vehicle";
      case "photos":
        return "Upload clear photos of your vehicle";
      case "documents":
        return selectedVehicleId
          ? "Review and manage your vehicle documents"
          : "Upload required documentation";
      default:
        return "Start listing your vehicle";
    }
  };

  // handleCreateVehicle moved to AddVehicleDrawer

  // Function to load user vehicles
  const loadUserVehicles = async () => {
    setIsLoadingVehicles(true);
    try {
      const vehicles = await getUserVehiclesForListing();
      setUserVehicles(vehicles);
    } catch (error) {
      console.error("Error fetching user vehicles:", error);
    } finally {
      setIsLoadingVehicles(false);
    }
  };

  // Structured photo upload handler for specific categories
  const handleStructuredPhotoUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    category:
      | "front"
      | "sideRight"
      | "sideLeft"
      | "rear"
      | "interior1"
      | "interior2"
      | "interior3"
      | "extras1"
      | "extras2"
      | "extras3"
      | "extras4"
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setPhotoUploadError(null);
    setIsUploadingPhoto(category);

    try {
      const file = files[0]; // Only take first file for each category
      const uploadResult = await DocumentUpload(file, "vehicleMedia");

      if (!uploadResult?.path) {
        throw new Error("Upload failed");
      }

      // Store S3 path directly (not signed URL)
      setStructuredPhotos((prev) => ({
        ...prev,
        [category]: uploadResult.path,
      }));

      // Update vehicleData with all photos
      const allPhotos = Object.values({
        ...structuredPhotos,
        [category]: uploadResult.path,
      }).filter(Boolean) as string[];

      setVehicleData((prev) => ({
        ...prev,
        images: allPhotos,
      }));
    } catch (error) {
      console.error("Error uploading photo:", error);
      setPhotoUploadError("Failed to upload photo. Please try again.");
    } finally {
      setIsUploadingPhoto(null);
    }
  };

  const removeStructuredPhoto = (
    category:
      | "front"
      | "sideRight"
      | "sideLeft"
      | "rear"
      | "interior1"
      | "interior2"
      | "interior3"
      | "extras1"
      | "extras2"
      | "extras3"
      | "extras4"
  ) => {
    setStructuredPhotos((prev) => ({
      ...prev,
      [category]: null,
    }));

    // Update vehicleData with remaining photos
    const updatedPhotos = { ...structuredPhotos, [category]: null };
    const allPhotos = Object.values(updatedPhotos).filter(Boolean) as string[];
    setVehicleData((prev) => ({
      ...prev,
      images: allPhotos,
    }));
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full max-w-md p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
              <div className="flex items-center">
                <button
                  title="Back"
                  onClick={handleBack}
                  className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                >
                  <ArrowLeft size={24} />
                </button>
                <div>
                  <SheetTitle className="text-xl font-bold text-white">
                    {getStepTitle()}
                  </SheetTitle>
                  <SheetDescription className="text-sm text-green-100">
                    {getStepDescription()}
                  </SheetDescription>
                </div>
              </div>
            </SheetHeader>

            {/* Progress Indicator */}
            <div className="px-6 py-3 bg-gray-50 border-b">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span
                  className={
                    currentStep === "listing-type"
                      ? "text-[#009639] font-medium"
                      : ""
                  }
                >
                  Type
                </span>
                <span
                  className={
                    currentStep === "vehicle-selection" ||
                    currentStep === "details" ||
                    currentStep === "photos"
                      ? "text-[#009639] font-medium"
                      : ""
                  }
                >
                  Vehicle
                </span>
                <span
                  className={
                    currentStep === "documents"
                      ? "text-[#009639] font-medium"
                      : ""
                  }
                >
                  Documents
                </span>
              </div>
              <div className="mt-2 h-1 bg-gray-200 rounded-full">
                <div
                  className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                  style={{
                    width:
                      currentStep === "listing-type"
                        ? "33%"
                        : currentStep === "vehicle-selection" ||
                            currentStep === "details" ||
                            currentStep === "photos"
                          ? "66%"
                          : "100%",
                  }}
                />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              {currentStep === "listing-type" && (
                <div className="space-y-6">
                  {/* Listing Type Selection */}
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <h4 className="font-semibold text-[#333333] mb-4">
                      Choose Your Monetization Method
                    </h4>
                    <p className="text-sm text-[#797879] mb-6">
                      Select how you want to earn money from your vehicle
                    </p>

                    {/* Tab Navigation */}
                    <div className="flex bg-gray-100 rounded-full p-1 mb-6">
                      <button
                        title="Rent Out"
                        onClick={() =>
                          handleVehicleDataChange("listingType", "rental")
                        }
                        className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
                          vehicleData.listingType === "rental"
                            ? "bg-[#009639] text-white shadow-md"
                            : "text-[#797879] hover:text-[#333333]"
                        }`}
                      >
                        Rent Out
                      </button>
                      <button
                        title="Sell Fraction"
                        onClick={() =>
                          handleVehicleDataChange("listingType", "fractional")
                        }
                        className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
                          vehicleData.listingType === "fractional"
                            ? "bg-[#009639] text-white shadow-md"
                            : "text-[#797879] hover:text-[#333333]"
                        }`}
                      >
                        Sell Fraction
                      </button>
                    </div>

                    {/* Tab Content */}
                    {vehicleData.listingType === "rental" && (
                      <div className="space-y-4">
                        <div>
                          <h5 className="font-semibold text-[#333333] mb-2">
                            Rent Out Your Vehicle
                          </h5>
                          <p className="text-sm text-[#797879] mb-4">
                            List your car for rental income. Set your own rates
                            and terms.
                          </p>
                          <div className="space-y-3">
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>Flexible rental periods</span>
                            </div>
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>You retain full ownership</span>
                            </div>
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>Regular rental income</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {vehicleData.listingType === "fractional" && (
                      <div className="space-y-4">
                        <div>
                          <h5 className="font-semibold text-[#333333] mb-2">
                            Fractional Ownership
                          </h5>
                          <p className="text-sm text-[#797879] mb-4">
                            Sell co-ownership shares of your vehicle to other
                            users.
                          </p>
                          <div className="space-y-3">
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>Sell 1-99% ownership</span>
                            </div>
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>Immediate lump sum payment</span>
                            </div>
                            <div className="flex items-center text-sm text-[#797879]">
                              <CheckCircle
                                size={16}
                                className="text-[#009639] mr-2"
                              />
                              <span>Shared usage and costs</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {!vehicleData.listingType && (
                      <div className="text-center py-8">
                        <p className="text-sm text-[#797879]">
                          Select a monetization method above to see details
                        </p>
                      </div>
                    )}
                  </div>

                  {/* Additional Configuration for Selected Type */}
                  {vehicleData.listingType === "rental" && (
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <h4 className="font-semibold text-[#333333] mb-4">
                        Rental Configuration
                      </h4>

                      <div className="space-y-4">
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Rental Purpose
                          </label>
                          <select
                            title="Rental Purpose"
                            value={vehicleData.rentalPurpose || ""}
                            onChange={(e) =>
                              handleVehicleDataChange(
                                "rentalPurpose",
                                e.target.value as "business" | "individual"
                              )
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          >
                            <option value="">Select purpose</option>
                            <option value="business">Business Use</option>
                            <option value="individual">Individual Use</option>
                          </select>
                        </div>

                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Rental Periods (Select all that apply)
                          </label>
                          <div className="space-y-3">
                            {["daily", "weekly", "monthly"].map((period) => (
                              <div
                                key={period}
                                className="flex items-center justify-between p-3 bg-white rounded-lg border border-gray-100"
                              >
                                <div>
                                  <h3 className="text-sm font-medium text-[#333333] capitalize">
                                    {period} Rental
                                  </h3>
                                  <p className="text-xs text-[#797879]">
                                    {period === "daily" &&
                                      "Perfect for short trips and quick needs"}
                                    {period === "weekly" &&
                                      "Ideal for business trips and extended use"}
                                    {period === "monthly" &&
                                      "Best value for long-term rentals"}
                                  </p>
                                </div>
                                <label className="relative inline-flex items-center cursor-pointer">
                                  <input
                                    type="checkbox"
                                    className="sr-only peer"
                                    title={`Enable ${period} rental option`}
                                    checked={
                                      vehicleData.rentalPeriods?.includes(
                                        period as "daily" | "weekly" | "monthly"
                                      ) || false
                                    }
                                    onChange={(e) => {
                                      const currentPeriods =
                                        vehicleData.rentalPeriods || [];
                                      const newPeriods = e.target.checked
                                        ? [
                                            ...currentPeriods,
                                            period as
                                              | "daily"
                                              | "weekly"
                                              | "monthly",
                                          ]
                                        : currentPeriods.filter(
                                            (p) => p !== period
                                          );
                                      handleVehicleDataChange(
                                        "rentalPeriods",
                                        newPeriods
                                      );
                                    }}
                                  />
                                  <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                                </label>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {vehicleData.listingType === "fractional" && (
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <h4 className="font-semibold text-[#333333] mb-4">
                        Ownership Configuration
                      </h4>

                      <div className="space-y-4">
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Ownership Percentage to Sell (1-99%)
                          </label>
                          <input
                            type="number"
                            value={vehicleData.ownershipPercentage}
                            onChange={(e) =>
                              handleVehicleDataChange(
                                "ownershipPercentage",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            placeholder="e.g. 50"
                            min="1"
                            max="99"
                          />
                        </div>

                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div>
                            <p className="text-sm font-medium text-[#333333]">
                              Allow Partial Purchases
                            </p>
                            <p className="text-xs text-[#797879]">
                              Let buyers purchase smaller portions of your
                              offered fraction
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              title="Allow Partial Purchases"
                              type="checkbox"
                              className="sr-only peer"
                              checked={vehicleData.allowPartialPurchase}
                              onChange={(e) =>
                                handleVehicleDataChange(
                                  "allowPartialPurchase",
                                  e.target.checked.toString()
                                )
                              }
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                          </label>
                        </div>

                        {vehicleData.allowPartialPurchase && (
                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Minimum Purchase Percentage
                            </label>
                            <input
                              type="number"
                              value={vehicleData.minimumPurchasePercentage}
                              onChange={(e) =>
                                handleVehicleDataChange(
                                  "minimumPurchasePercentage",
                                  e.target.value
                                )
                              }
                              className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                              placeholder="e.g. 10"
                              min="1"
                              max={vehicleData.ownershipPercentage || "99"}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {currentStep === "vehicle-selection" && (
                <div className="space-y-6">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Select a Vehicle to list
                  </h4>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    {isLoadingVehicles ? (
                      <div className="flex justify-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                      </div>
                    ) : userVehicles.length > 0 ? (
                      <div className="space-y-3">
                        {userVehicles.map((vehicle) => (
                          <div
                            key={vehicle.id}
                            onClick={() => {
                              setSelectedVehicleId(vehicle.id);
                              loadExistingDocuments(vehicle.id);
                              // Update vehicleData with selected vehicle info
                              setVehicleData((prev) => ({
                                ...prev,
                                make: vehicle.model.make.name,
                                model: vehicle.model.model,
                                year:
                                  vehicle.manufacturingYear?.toString() || "",
                                color: vehicle.color,
                                mileage: vehicle.mileage?.toString() || "0",
                                condition: vehicle.condition || "good",
                              }));
                            }}
                            className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                              selectedVehicleId === vehicle.id
                                ? "border-[#009639] bg-green-50"
                                : "border-gray-200 hover:border-[#009639] hover:bg-green-50"
                            }`}
                          >
                            <div className="flex items-center">
                              <div className="w-16 h-16 bg-gray-200 rounded-md overflow-hidden mr-3">
                                {vehicle.media && vehicle.media.length > 0 ? (
                                  <VehicleThumbnail
                                    mediaPath={vehicle.media[0].mediaPath}
                                    alt={`${vehicle.model.make.name} ${vehicle.model.model}`}
                                  />
                                ) : (
                                  <div className="flex items-center justify-center h-full">
                                    <Car size={24} className="text-gray-400" />
                                  </div>
                                )}
                              </div>
                              <div>
                                <p className="font-medium text-[#333333]">
                                  {vehicle.model.make.name}{" "}
                                  {vehicle.model.model} (
                                  {vehicle.manufacturingYear})
                                </p>
                                <p className="text-xs text-[#797879]">
                                  {vehicle.color} • Reg:{" "}
                                  {vehicle.vehicleRegistration}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}

                        <div className="mt-4 pt-4 border-t border-gray-200">
                          <button
                            type="button"
                            onClick={() => setShowAddVehicleDrawer(true)}
                            className="text-[#009639] text-sm font-medium flex items-center"
                          >
                            <PlusCircle size={16} className="mr-1" />
                            Add a new vehicle
                          </button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Car size={48} className="text-gray-300 mx-auto mb-3" />
                        <p className="text-[#797879] mb-4">
                          You don't have any vehicles yet
                        </p>
                        <button
                          type="button"
                          onClick={() => setShowAddVehicleDrawer(true)}
                          className="bg-[#009639] text-white px-4 py-2  hover:bg-[#007A2F] transition-colors rounded-full"
                        >
                          Add a new vehicle
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {currentStep === "details" && (
                <div className="space-y-6">
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <h4 className="font-semibold text-[#333333] mb-4">
                      Vehicle Information
                    </h4>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Make
                          </label>
                          <input
                            type="text"
                            value={vehicleData.make}
                            onChange={(e) =>
                              handleVehicleDataChange("make", e.target.value)
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            placeholder="e.g. Toyota"
                          />
                        </div>
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Model
                          </label>
                          <input
                            type="text"
                            value={vehicleData.model}
                            onChange={(e) =>
                              handleVehicleDataChange("model", e.target.value)
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            placeholder="e.g. Corolla"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Year
                          </label>
                          <input
                            type="number"
                            value={vehicleData.year}
                            onChange={(e) =>
                              handleVehicleDataChange("year", e.target.value)
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            placeholder="2020"
                            min="2000"
                            max="2024"
                          />
                        </div>
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Color
                          </label>
                          <input
                            type="text"
                            value={vehicleData.color}
                            onChange={(e) =>
                              handleVehicleDataChange("color", e.target.value)
                            }
                            className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                            placeholder="e.g. White"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Mileage (km)
                        </label>
                        <input
                          type="number"
                          value={vehicleData.mileage}
                          onChange={(e) =>
                            handleVehicleDataChange("mileage", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 50000"
                        />
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Condition
                        </label>
                        <select
                          title="Condition"
                          value={vehicleData.condition}
                          onChange={(e) =>
                            handleVehicleDataChange("condition", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        >
                          <option value="used">Used</option>
                          <option value="new">New</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {currentStep === "photos" && (
                <div className="space-y-6">
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <h4 className="font-semibold text-[#333333] mb-4">
                      Vehicle Photos
                    </h4>
                    <p className="text-sm text-[#797879] mb-4">
                      Upload clear photos of your vehicle to attract potential
                      renters/buyers
                    </p>

                    {/* Simple Photo Upload for existing vehicle listing */}
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg border-2 border-dashed border-gray-300">
                        <div className="text-center">
                          <Camera className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                          <p className="text-sm font-medium text-gray-600 mb-1">
                            Add Listing Photos
                          </p>
                          <p className="text-xs text-gray-500 mb-3">
                            Upload clear photos for your listing (max 6 photos)
                          </p>
                          <input
                            type="file"
                            accept="image/*"
                            multiple
                            onChange={handleImageUpload}
                            className="hidden"
                            id="listing-photo-upload"
                            disabled={isUploadingImages}
                          />
                          <label
                            htmlFor="listing-photo-upload"
                            className={`inline-block px-4 py-2 rounded-full text-sm font-medium cursor-pointer ${
                              isUploadingImages
                                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                                : "bg-[#009639] text-white hover:bg-[#007A2F]"
                            }`}
                          >
                            {isUploadingImages
                              ? "Uploading..."
                              : "Choose Photos"}
                          </label>
                        </div>
                      </div>

                      {/* Photo Preview Grid */}
                      {vehicleData.images.length > 0 && (
                        <div className="grid grid-cols-2 gap-3">
                          {vehicleData.images.map((image, index) => (
                            <div key={index} className="relative">
                              <img
                                src={image}
                                alt={`Vehicle photo ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg border border-gray-200"
                              />
                              <button
                                title="Remove photo"
                                onClick={() => removeImage(index)}
                                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                              >
                                <X size={12} />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {currentStep === "documents" && (
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <FileText size={18} className="mr-2 text-[#009639]" />
                      <h4 className="font-semibold text-[#333333]">
                        Upload Documents
                      </h4>
                    </div>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-6">
                        {(selectedVehicleId
                          ? existingDocuments
                          : documents
                        ).map((doc, index) => (
                          <div key={index} className="rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-[#333333]">
                                  {doc.name}
                                </span>
                                {doc.required ? (
                                  <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                                    Required
                                  </span>
                                ) : (
                                  <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                    Optional
                                  </span>
                                )}
                              </div>
                              {doc.uploaded && (
                                <CheckCircle
                                  size={16}
                                  className="text-green-500"
                                />
                              )}
                            </div>
                            {!doc.uploaded ? (
                              <label
                                className={`flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg transition-colors ${
                                  isUploadingDocument === index
                                    ? "cursor-not-allowed bg-gray-50"
                                    : "cursor-pointer hover:border-[#009639]"
                                }`}
                              >
                                <div className="text-center">
                                  {isUploadingDocument === index ? (
                                    <>
                                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-[#009639] mx-auto mb-1"></div>
                                      <span className="text-xs text-gray-500">
                                        Uploading...
                                      </span>
                                    </>
                                  ) : (
                                    <>
                                      <Upload
                                        size={20}
                                        className="mx-auto mb-1 text-gray-400"
                                      />
                                      <span className="text-xs text-gray-500">
                                        Click to upload
                                      </span>
                                    </>
                                  )}
                                </div>
                                <input
                                  type="file"
                                  className="hidden"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  disabled={isUploadingDocument === index}
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      handleDocumentUpload(index, file);
                                    }
                                  }}
                                />
                              </label>
                            ) : (
                              <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-2">
                                <span className="text-xs text-green-700">
                                  {doc.file?.name || "Document uploaded"}
                                </span>
                                <button
                                  onClick={() => {
                                    const updatedDocuments = [...documents];
                                    updatedDocuments[index] = {
                                      ...updatedDocuments[index],
                                      uploaded: false,
                                      file: undefined,
                                    };
                                    setDocuments(updatedDocuments);
                                  }}
                                  className="text-xs text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="border-t border-gray-200 p-4">
              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-600 rounded-lg text-sm">
                  {error}
                </div>
              )}
              <button
                onClick={() => {
                  console.log("Main continue button clicked"); // Debug log
                  handleNext();
                }}
                disabled={
                  isSubmitting ||
                  (currentStep === "listing-type" &&
                    !vehicleData.listingType) ||
                  (currentStep === "vehicle-selection" &&
                    !selectedVehicleId &&
                    !newVehicleConfigured) ||
                  (currentStep === "details" && !canProceedFromDetails()) ||
                  (currentStep === "photos" && !canProceedFromPhotos()) ||
                  (currentStep === "documents" && !canProceedFromDocuments())
                }
                className={`w-full py-3 rounded-full font-semibold transition-all ${
                  isSubmitting
                    ? "bg-gray-200 text-gray-400 cursor-not-allowed"
                    : (currentStep === "listing-type" &&
                          vehicleData.listingType) ||
                        (currentStep === "vehicle-selection" &&
                          (selectedVehicleId || newVehicleConfigured)) ||
                        (currentStep === "details" &&
                          canProceedFromDetails()) ||
                        (currentStep === "photos" && canProceedFromPhotos()) ||
                        (currentStep === "documents" &&
                          canProceedFromDocuments())
                      ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                      : "bg-gray-200 text-gray-400 cursor-not-allowed"
                }`}
              >
                {/* Debug: Show button state */}
                {currentStep === "vehicle-selection" && newVehicleConfigured ? (
                  "Continue with New Vehicle"
                ) : isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                    Processing...
                  </span>
                ) : currentStep === "documents" ? (
                  "Submit Listing"
                ) : (
                  "Continue"
                )}
              </button>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* ListingTermsDrawer */}
      <ListingTermsDrawer
        isOpen={showListingTermsDrawer}
        onClose={() => setShowListingTermsDrawer(false)}
        onSubmit={(financialTerms, applicantPreferences) => {
          const finalData = {
            ...vehicleData,
            documents: selectedVehicleId ? existingDocuments : documents,
            selectedVehicleId: selectedVehicleId || undefined,
            financialTerms,
            applicantPreferences,
          };
          onNext(finalData);
          setShowListingTermsDrawer(false);
        }}
        vehicleData={{
          listingType: vehicleData.listingType as "rental" | "fractional",
          rentalPurpose: vehicleData.rentalPurpose,
          rentalPeriods: vehicleData.rentalPeriods,
          ownershipPercentage: vehicleData.ownershipPercentage,
          allowPartialPurchase: vehicleData.allowPartialPurchase,
          minimumPurchasePercentage: vehicleData.minimumPurchasePercentage,
          make: vehicleData.make,
          model: vehicleData.model,
          year: vehicleData.year,
          color: vehicleData.color,
          mileage: vehicleData.mileage,
          condition: vehicleData.condition,
          images: [], // ListingTermsDrawer expects File[], but we have string[] paths
          documents: selectedVehicleId ? existingDocuments : documents,
        }}
      />

      {/* AddVehicleDrawer */}
      <AddVehicleDrawer
        isOpen={showAddVehicleDrawer}
        onClose={() => setShowAddVehicleDrawer(false)}
        onVehicleCreated={() => {
          // Reload user vehicles after a new vehicle is created
          loadUserVehicles();
          setShowAddVehicleDrawer(false);
        }}
      />
    </>
  );
}
