"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Shield,
  Lock,
  Eye,
  EyeOff,
  Smartphone,
  Key,
  AlertTriangle,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface PrivacySecuritySheetProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PrivacySecuritySheet({
  isOpen,
  onClose,
}: PrivacySecuritySheetProps) {
  const [settings, setSettings] = useState({
    twoFactorAuth: false,
    biometricLogin: true,
    dataSharing: false,
    locationTracking: true,
    activityLogging: true,
  });

  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const handleToggle = (setting: keyof typeof settings) => {
    setSettings({
      ...settings,
      [setting]: !settings[setting],
    });
  };

  const handlePasswordChange = () => {
    console.log("Password change requested:", passwordData);
    // In a real app, you would handle password change
    setShowPasswordChange(false);
    setPasswordData({
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    });
  };

  const securityOptions = [
    {
      id: "password",
      title: "Change Password",
      description: "Update your account password",
      icon: <Lock size={18} className="text-[#009639]" />,
      action: () => setShowPasswordChange(true),
    },
    {
      id: "sessions",
      title: "Active Sessions",
      description: "Manage your active login sessions",
      icon: <Smartphone size={18} className="text-[#009639]" />,
      action: () => console.log("View active sessions"),
    },
    {
      id: "recovery",
      title: "Account Recovery",
      description: "Set up account recovery options",
      icon: <Key size={18} className="text-[#009639]" />,
      action: () => console.log("Setup account recovery"),
    },
  ];

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Privacy & Security
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Manage your privacy and security settings
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            {/* Security Settings */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">
                Security Settings
              </h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Shield size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Two-Factor Authentication
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Add an extra layer of security to your account
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.twoFactorAuth}
                        onChange={() => handleToggle("twoFactorAuth")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Smartphone size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Biometric Login
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Use fingerprint or face recognition to login
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.biometricLogin}
                        onChange={() => handleToggle("biometricLogin")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Privacy Settings */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">
                Privacy Settings
              </h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Eye size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Data Sharing
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Share anonymized data to improve services
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.dataSharing}
                        onChange={() => handleToggle("dataSharing")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4 border-b border-[#f2f2f2]">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Smartphone size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Location Tracking
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Allow location tracking for better service
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.locationTracking}
                        onChange={() => handleToggle("locationTracking")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <AlertTriangle size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        Activity Logging
                      </h3>
                      <p className="text-xs text-[#797879]">
                        Log app activity for security purposes
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={settings.activityLogging}
                        onChange={() => handleToggle("activityLogging")}
                      />
                      <div className="w-11 h-6 bg-[#d6d9dd] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Security Actions */}
            <div className="p-4">
              <h3 className="text-[#333333] font-medium mb-3">
                Security Actions
              </h3>
              <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
                {securityOptions.map((option, index) => (
                  <div
                    key={option.id}
                    className={`p-4 flex items-center cursor-pointer hover:bg-gray-50 transition-colors ${
                      index < securityOptions.length - 1
                        ? "border-b border-[#f2f2f2]"
                        : ""
                    }`}
                    onClick={option.action}
                  >
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      {option.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-[#333333] font-medium">
                        {option.title}
                      </h3>
                      <p className="text-xs text-[#797879]">
                        {option.description}
                      </p>
                    </div>
                    <ChevronRight size={20} className="text-[#797879]" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Save Button */}
          <div className="border-t border-gray-200 p-4">
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white w-full py-4 rounded-full text-lg font-semibold shadow-md"
              onClick={onClose}
            >
              Save Changes
            </button>
          </div>
        </div>

        {/* Password Change Modal */}
        {showPasswordChange && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 px-4">
            <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-lg border border-gray-100">
              <h3 className="text-[#333333] font-bold text-lg mb-4">
                Change Password
              </h3>
              <div className="space-y-4">
                <input
                  type="password"
                  placeholder="Current Password"
                  value={passwordData.currentPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      currentPassword: e.target.value,
                    })
                  }
                  className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639]"
                />
                <input
                  type="password"
                  placeholder="New Password"
                  value={passwordData.newPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      newPassword: e.target.value,
                    })
                  }
                  className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639]"
                />
                <input
                  type="password"
                  placeholder="Confirm New Password"
                  value={passwordData.confirmPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      confirmPassword: e.target.value,
                    })
                  }
                  className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639]"
                />
              </div>
              <div className="flex space-x-3 mt-6">
                <button
                  className="flex-1 py-3 px-4 border border-[#d6d9dd] rounded-full text-[#333333] font-medium"
                  onClick={() => setShowPasswordChange(false)}
                >
                  Cancel
                </button>
                <button
                  className="flex-1 py-3 px-4 bg-[#009639] text-white rounded-full font-medium"
                  onClick={handlePasswordChange}
                >
                  Update
                </button>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
}
