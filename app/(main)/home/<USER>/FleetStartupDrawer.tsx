"use client";

import { useState } from "react";
import Image from "next/image";
import { Building, Users, ChevronRight } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface FleetStartupDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (fleetType: "company" | "individual") => void;
}

export default function FleetStartupDrawer({
  isOpen,
  onClose,
  onNext,
}: FleetStartupDrawerProps) {
  const [selectedFleetType, setSelectedFleetType] = useState<
    "company" | "individual" | null
  >(null);

  const handleContinue = () => {
    if (selectedFleetType) {
      onNext(selectedFleetType);
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                title="Close"
              >
                <ChevronRight size={24} className="rotate-180" />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Fleet Management
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Choose how you want to set up your fleet
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Fleet Image */}
              <div className="relative h-32 bg-[#f2f2f2] rounded-xl overflow-hidden">
                <Image
                  src="/images/cars/fleet-card-3.png?height=160&width=320"
                  alt="Fleet Management Dashboard"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Start as Section */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-4">Start as:</h4>

                {/* Rounded Tab Toggle */}
                <div className="flex bg-gray-100 rounded-full p-1 mb-6">
                  <button
                    className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
                      selectedFleetType === "company"
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setSelectedFleetType("company")}
                  >
                    Company Fleet
                  </button>
                  <button
                    className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
                      selectedFleetType === "individual"
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setSelectedFleetType("individual")}
                  >
                    Individual Fleet
                  </button>
                </div>
              </div>

              {/* Description based on selection */}
              {selectedFleetType && (
                <div className="space-y-4">
                  {selectedFleetType === "company" ? (
                    <div>
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                          <Building size={20} className="text-[#009639]" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-[#333333]">
                            Company Owned Fleet
                          </h3>
                          <p className="text-xs text-[#009639] font-medium">
                            Professional • Scalable • Compliant
                          </p>
                        </div>
                      </div>

                      {/* Hero Description Card */}
                      <div className="rounded-xl bg-gradient-to-br from-[#e6ffe6] to-[#f0fff0] border border-[#009639]/20 p-4 mb-4">
                        <p className="text-sm text-[#007A2F] font-medium mb-2">
                          Scale your business with enterprise-grade fleet
                          management
                        </p>
                        <p className="text-sm text-[#333333] leading-relaxed">
                          Perfect for registered businesses ready to streamline
                          operations, ensure compliance, and maximize efficiency
                          across multiple vehicles.
                        </p>
                      </div>

                      {/* Features Grid */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mb-2">
                            <Building size={12} className="text-white" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Multi-Company
                          </p>
                          <p className="text-xs text-[#797879]">
                            Single or multiple business entities
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#007A2F] rounded-full flex items-center justify-center mb-2">
                            <Building size={12} className="text-white" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Compliance
                          </p>
                          <p className="text-xs text-[#797879]">
                            Registration & legal tracking
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#FFD700] rounded-full flex items-center justify-center mb-2">
                            <Building size={12} className="text-[#333333]" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Analytics
                          </p>
                          <p className="text-xs text-[#797879]">
                            Advanced reporting tools
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mb-2">
                            <Building size={12} className="text-white" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Simple
                          </p>
                          <p className="text-xs text-[#797879]">
                            Easy to use interface
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex items-center mb-4">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                          <Users size={20} className="text-[#009639]" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-[#333333]">
                            Individual Owned Fleet
                          </h3>
                          <p className="text-xs text-[#009639] font-medium">
                            Personal • Flexible • Simple
                          </p>
                        </div>
                      </div>

                      {/* Hero Description Card */}
                      <div className="rounded-xl bg-[#e6ffe6] border border-[#009639]/30 p-4 mb-4">
                        <p className="text-sm text-[#009639] font-medium mb-2">
                          Start your fleet journey with personal ownership
                        </p>
                        <p className="text-sm text-[#333333] leading-relaxed">
                          Ideal for individuals or small groups who want
                          professional fleet management without the complexity
                          of corporate structures.
                        </p>
                      </div>

                      {/* Features Grid */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#FFD700] rounded-full flex items-center justify-center mb-2">
                            <Users size={12} className="text-[#333333]" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Multi-Owner
                          </p>
                          <p className="text-xs text-[#797879]">
                            Single or multiple individuals
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mb-2">
                            <Users size={12} className="text-white" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Verification
                          </p>
                          <p className="text-xs text-[#797879]">
                            Personal ID confirmation
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#007A2F] rounded-full flex items-center justify-center mb-2">
                            <Users size={12} className="text-white" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Flexible
                          </p>
                          <p className="text-xs text-[#797879]">
                            Adaptable ownership rules
                          </p>
                        </div>

                        <div className="rounded-lg bg-white border border-gray-100 p-3 shadow-sm">
                          <div className="w-6 h-6 bg-[#FFD700] rounded-full flex items-center justify-center mb-2">
                            <Users size={12} className="text-[#333333]" />
                          </div>
                          <p className="text-xs font-medium text-[#333333] mb-1">
                            Simple
                          </p>
                          <p className="text-xs text-[#797879]">
                            Easy-to-use interface
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleContinue}
              disabled={!selectedFleetType}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Continue
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
