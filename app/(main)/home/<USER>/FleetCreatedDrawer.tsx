"use client";

import { useState } from "react";
import {
  CheckCircle,
  Users,
  Car,
  Mail,
  Building,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface FleetCreatedDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onContinueToFleet: () => void;
  fleetData: {
    companyName?: string;
    isPoollyManaged: boolean;
    memberCount: number;
    ownerCount?: number;
  };
}

export default function FleetCreatedDrawer({
  isOpen,
  onClose,
  onContinueToFleet,
  fleetData,
}: FleetCreatedDrawerProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success Header */}
          <div className="px-6 py-8 flex flex-col items-center bg-[#009639]">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <CheckCircle size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Fleet Created Successfully!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              Your fleet management system is ready to use
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Fleet Summary */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Fleet Details
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Building size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Fleet Type</p>
                        <p className="text-[#333333] font-medium">
                          {fleetData.isPoollyManaged ? "Poolly Managed Fleet" : fleetData.companyName}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Users size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Team Members</p>
                        <p className="text-[#333333] font-medium">
                          {fleetData.memberCount} member{fleetData.memberCount !== 1 ? 's' : ''} invited
                          {!fleetData.isPoollyManaged && fleetData.ownerCount && (
                            <span className="text-[#009639] text-xs ml-1">
                              ({fleetData.ownerCount} owner{fleetData.ownerCount !== 1 ? 's' : ''})
                            </span>
                          )}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Mail size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Invitations</p>
                        <p className="text-[#333333] font-medium">
                          Email invitations sent
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* What Happens Next */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  What Happens Next?
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mr-3 text-xs font-medium text-white">
                        1
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Member Invitations Sent
                        </p>
                        <p className="text-xs text-[#797879]">
                          Invited members will receive email invitations to join your fleet
                        </p>
                        <p className="text-xs text-[#009639] font-medium mt-1">
                          Immediate
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mr-3 text-xs font-medium text-white">
                        2
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Fleet Dashboard Ready
                        </p>
                        <p className="text-xs text-[#797879]">
                          Access your fleet management dashboard to add vehicles and start operations
                        </p>
                        <p className="text-xs text-[#009639] font-medium mt-1">
                          Available now
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mr-3 text-xs font-medium text-white">
                        3
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Add Your Vehicles
                        </p>
                        <p className="text-xs text-[#797879]">
                          Start adding vehicles to your fleet and configure management settings
                        </p>
                        <p className="text-xs text-[#009639] font-medium mt-1">
                          Next step
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Fleet Features */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-semibold text-green-900 mb-2 flex items-center">
                  <Car size={16} className="mr-2" />
                  Fleet Management Features
                </h5>
                <div className="space-y-2 text-sm text-green-800">
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Digital vehicle handovers</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Smart maintenance tracking</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Live GPS vehicle tracking</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Secure document management</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={onContinueToFleet}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors flex items-center justify-center"
            >
              Go to Fleet Dashboard
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
