"use client";

import { useState } from "react";
import {
  CheckCircle,
  XCircle,
  Clock,
  MessageSquare,
  FileText,
  Calendar,
  Users,
  Car,
  ArrowRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface ApplicationDecisionConfirmationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  decision: "approved" | "rejected";
  applicantName: string;
  vehicleName: string;
  weeklyRate: number;
  reason?: string;
}

export default function ApplicationDecisionConfirmationDrawer({
  isOpen,
  onClose,
  decision,
  applicantName,
  vehicleName,
  weeklyRate,
  reason,
}: ApplicationDecisionConfirmationDrawerProps) {
  const approvedSteps = [
    {
      icon: <MessageSquare size={16} className="text-[#009639]" />,
      title: "Applicant Notified",
      description: `${applicantName} will receive an email confirmation of approval`,
      timeframe: "Immediate",
    },
    {
      icon: <FileText size={16} className="text-[#009639]" />,
      title: "Lease Agreement Preparation",
      description: "Legal documents will be prepared and sent for review",
      timeframe: "1-2 business days",
    },
    {
      icon: <Users size={16} className="text-[#009639]" />,
      title: "Meet & Greet Scheduled",
      description: "Coordinate vehicle handover and final documentation",
      timeframe: "3-5 business days",
    },
    {
      icon: <Car size={16} className="text-[#009639]" />,
      title: "Vehicle Handover",
      description: "Complete the lease agreement and hand over vehicle keys",
      timeframe: "Within 1 week",
    },
  ];

  const rejectedSteps = [
    {
      icon: <MessageSquare size={16} className="text-red-600" />,
      title: "Applicant Notified",
      description: `${applicantName} will receive an email with feedback`,
      timeframe: "Immediate",
    },
    {
      icon: <FileText size={16} className="text-red-600" />,
      title: "Application Archived",
      description: "Application moved to your history for future reference",
      timeframe: "Immediate",
    },
    {
      icon: <Users size={16} className="text-red-600" />,
      title: "Listing Remains Active",
      description: "Your vehicle listing continues to receive new applications",
      timeframe: "Ongoing",
    },
  ];

  const steps = decision === "approved" ? approvedSteps : rejectedSteps;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success/Decision Header */}
          <div
            className={`px-6 py-8 flex flex-col items-center ${
              decision === "approved" ? "bg-[#009639]" : "bg-red-600"
            }`}
          >
            <div
              className={`w-20 h-20 rounded-full flex items-center justify-center mb-4 shadow-md ${
                decision === "approved" ? "bg-[#e6ffe6]" : "bg-red-50"
              }`}
            >
              {decision === "approved" ? (
                <CheckCircle size={40} className="text-[#009639]" />
              ) : (
                <XCircle size={40} className="text-red-600" />
              )}
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Application {decision === "approved" ? "Approved!" : "Rejected"}
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              Decision has been made for {applicantName}'s application
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Decision Summary */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Decision Details
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div
                        className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 shadow-sm ${
                          decision === "approved" ? "bg-[#e6ffe6]" : "bg-red-50"
                        }`}
                      >
                        {decision === "approved" ? (
                          <CheckCircle size={12} className="text-[#009639]" />
                        ) : (
                          <XCircle size={12} className="text-red-600" />
                        )}
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Applicant</p>
                        <p className="text-[#333333] font-medium">
                          {applicantName}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Car size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Vehicle</p>
                        <p className="text-[#333333] font-medium">
                          {vehicleName}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <span className="text-[#009639] font-bold text-xs">
                          R
                        </span>
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Weekly Rate</p>
                        <p className="text-[#333333] font-medium">
                          R{weeklyRate.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    {reason && (
                      <div className="flex items-start">
                        <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                          <MessageSquare size={12} className="text-[#009639]" />
                        </div>
                        <div>
                          <p className="text-[#797879] text-xs">
                            Decision Reason
                          </p>
                          <p className="text-[#333333] font-medium">{reason}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* What Happens Next */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  What Happens Next?
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    {steps.map((step, index) => (
                      <div key={index} className="flex items-start">
                        <div
                          className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 text-xs font-medium ${
                            decision === "approved"
                              ? "bg-[#009639] text-white"
                              : "bg-red-600 text-white"
                          }`}
                        >
                          {index + 1}
                        </div>
                        <div>
                          <p className="text-sm font-medium text-[#333333]">
                            {step.title}
                          </p>
                          <p className="text-xs text-[#797879]">
                            {step.description}
                          </p>
                          <p className="text-xs text-[#009639] font-medium mt-1">
                            {step.timeframe}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Email Confirmation */}
              <div
                className={`rounded-xl p-4 ${
                  decision === "approved"
                    ? "bg-green-50 border border-green-200"
                    : "bg-red-50 border border-red-200"
                }`}
              >
                <div className="flex items-center mb-2">
                  <MessageSquare
                    size={16}
                    className={`mr-2 ${
                      decision === "approved"
                        ? "text-green-600"
                        : "text-red-600"
                    }`}
                  />
                  <h4
                    className={`font-semibold ${
                      decision === "approved"
                        ? "text-green-900"
                        : "text-red-900"
                    }`}
                  >
                    {decision === "approved"
                      ? "Applicant Notified"
                      : "Decision Communicated"}
                  </h4>
                </div>
                <p
                  className={`text-sm ${
                    decision === "approved" ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {decision === "approved"
                    ? `${applicantName} will receive an email confirmation with next steps and your contact information for coordination.`
                    : `${applicantName} has been notified of the decision${reason ? " with your feedback" : ""}.`}
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={onClose}
              className={`w-full py-3 rounded-full font-semibold transition-colors ${
                decision === "approved"
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-red-600 text-white hover:bg-red-700"
              }`}
            >
              Continue to Management
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
