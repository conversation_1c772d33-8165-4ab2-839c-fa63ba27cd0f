"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Filter,
  Search,
  Calendar,
  FileText,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Application {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  vehicleId: string;
  vehicleName: string;
  experience: {
    hasExperience: boolean;
    company?: string;
    duration?: string;
    profileNumber?: string;
  };
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  weeklyRate: number;
}

interface ApplicationManagementDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onReviewApplication: (applicationId: string) => void;
}

export default function ApplicationManagementDrawer({
  isOpen,
  onClose,
  onReviewApplication,
}: ApplicationManagementDrawerProps) {
  const [currentFilter, setCurrentFilter] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual data fetching
  const [applications] = useState<Application[]>([
    {
      id: "1",
      applicantName: "John Doe",
      applicantEmail: "<EMAIL>",
      applicationDate: "2024-01-15",
      status: "pending",
      vehicleId: "v1",
      vehicleName: "Toyota Corolla 2020",
      experience: {
        hasExperience: true,
        company: "Uber",
        duration: "2-5-years",
        profileNumber: "4.8/5",
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "PrDP", uploaded: true, verified: false },
      ],
      weeklyRate: 2700,
    },
    {
      id: "2",
      applicantName: "Sarah Smith",
      applicantEmail: "<EMAIL>",
      applicationDate: "2024-01-14",
      status: "under_review",
      vehicleId: "v1",
      vehicleName: "Toyota Corolla 2020",
      experience: {
        hasExperience: false,
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
      weeklyRate: 2700,
    },
  ]);

  const getStatusColor = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "under_review":
        return "bg-blue-100 text-blue-800";
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return <Clock size={12} />;
      case "under_review":
        return <Eye size={12} />;
      case "approved":
        return <CheckCircle size={12} />;
      case "rejected":
        return <XCircle size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  const filteredApplications = applications.filter((app) => {
    const matchesFilter =
      currentFilter === "all" || app.status === currentFilter;
    const matchesSearch =
      app.applicantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getFilterCount = (filter: typeof currentFilter) => {
    if (filter === "all") return applications.length;
    return applications.filter((app) => app.status === filter).length;
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                title="Back"
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Manage Applications
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Review and manage lessee applications
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Filters */}
          <div className="px-6 py-4 bg-gray-50 border-b">
            <div className="flex items-center space-x-2 mb-3">
              <Search size={16} className="text-gray-400" />
              <input
                type="text"
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
              />
            </div>

            <div className="flex space-x-2 overflow-x-auto">
              {[
                { key: "all", label: "All" },
                { key: "pending", label: "Pending" },
                { key: "under_review", label: "Review" },
                { key: "approved", label: "Approved" },
                { key: "rejected", label: "Rejected" },
              ].map((filter) => (
                <button
                  key={filter.key}
                  onClick={() =>
                    setCurrentFilter(filter.key as typeof currentFilter)
                  }
                  className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors ${
                    currentFilter === filter.key
                      ? "bg-[#009639] text-white"
                      : "bg-white text-gray-600 border border-gray-200"
                  }`}
                >
                  <span>{filter.label}</span>
                  <span className="bg-white bg-opacity-20 px-1 rounded-full text-xs">
                    {getFilterCount(filter.key as typeof currentFilter)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {filteredApplications.length === 0 ? (
              <div className="text-center py-8">
                <Users size={48} className="mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  No Applications Found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchQuery
                    ? "Try adjusting your search"
                    : "No applications match the selected filter"}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredApplications.map((application) => (
                  <div
                    key={application.id}
                    className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                  >
                    {/* Application Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-[#333333]">
                          {application.applicantName}
                        </h4>
                        <p className="text-xs text-[#797879]">
                          {application.vehicleName}
                        </p>
                      </div>
                      <div
                        className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}
                      >
                        {getStatusIcon(application.status)}
                        <span className="capitalize">
                          {application.status.replace("_", " ")}
                        </span>
                      </div>
                    </div>

                    {/* Application Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-xs text-[#797879]">
                        <Calendar size={12} className="mr-2" />
                        Applied:{" "}
                        {new Date(
                          application.applicationDate
                        ).toLocaleDateString()}
                      </div>
                      <div className="flex items-center text-xs text-[#797879]">
                        <FileText size={12} className="mr-2" />
                        Documents:{" "}
                        {application.documents.filter((d) => d.uploaded).length}
                        /{application.documents.length}
                      </div>
                      {application.experience.hasExperience && (
                        <div className="flex items-center text-xs text-[#797879]">
                          <CheckCircle
                            size={12}
                            className="mr-2 text-green-500"
                          />
                          Experience: {application.experience.company} (
                          {application.experience.duration})
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <button
                      onClick={() => onReviewApplication(application.id)}
                      className="w-full bg-[#009639] text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors"
                    >
                      Review Application
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
