"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { Car, FileText, DollarSign, Loader2, Eye, EyeOff } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import ListingStatusTimeline from "@/components/ListingStatusTimeline";
import VehicleImage from "@/components/vehicle-image";
import { toggleListingPublishStatus } from "@/actions/listing-publish";

interface Listing {
  id: number;
  partyId: number;
  listingType: string;
  sourceType: string;
  sourceId: number;
  effectiveFrom: string;
  effectiveTo?: string | null;
  listingDetails: any; // JSON object containing pricing and other details
  vehicle?: {
    id: number;
    vinNumber?: string;
    vehicleRegistration?: string;
    color?: string;
    manufacturingYear?: number;
    make?: string;
    model?: string;
  };
  media: {
    id: number;
    vehicle_id: number;
    media_path: string;
    created_at: string;
  }[];
  latestDecision?: {
    decision: string;
    reason?: string | null;
    decisionAt: string;
  };
  documents: any[];
  approvalStatus?: string | null;
  statusAt?: string | null;
  statusReason?: string | null;
  publishStatus?: string | null;
  publishStatusAt?: string | null;
}

interface TimelineItem {
  id: number;
  status: string;
  reason?: string | null;
  timestamp: Date;
  reviewerName?: string | null;
}

interface ListingStatusSheetProps {
  isOpen: boolean;
  onClose: () => void;
  listing: Listing | null;
}

export default function ListingStatusSheet({
  isOpen,
  onClose,
  listing,
}: ListingStatusSheetProps) {
  const [timeline, setTimeline] = useState<TimelineItem[]>([]);
  const [currentStatus, setCurrentStatus] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [publishLoading, setPublishLoading] = useState(false);
  const [currentPublishStatus, setCurrentPublishStatus] = useState<
    string | null
  >(null);

  useEffect(() => {
    if (listing && isOpen) {
      // Set initial status from approval status or latest decision
      if (listing.approvalStatus) {
        setCurrentStatus(listing.approvalStatus);
      } else if (listing.latestDecision) {
        setCurrentStatus(listing.latestDecision.decision);
      } else {
        setCurrentStatus("pending");
      }

      // Set publish status
      setCurrentPublishStatus(listing.publishStatus || null);

      // For now, create mock timeline - in real implementation, this would fetch from API
      createMockTimeline();
    }
  }, [listing, isOpen]);

  const createMockTimeline = () => {
    if (!listing) return;

    setLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      const mockTimeline: TimelineItem[] = [
        {
          id: 1,
          status: "pending",
          reason: "Listing submitted and awaiting admin review",
          timestamp: new Date(listing.effectiveFrom),
          reviewerName: null,
        },
      ];

      // Add latest decision if it exists
      if (
        listing.latestDecision &&
        listing.latestDecision.decision !== "pending"
      ) {
        mockTimeline.push({
          id: 2,
          status: listing.latestDecision.decision,
          reason: listing.latestDecision.reason || null,
          timestamp: new Date(listing.latestDecision.decisionAt),
          reviewerName: null,
        });
      }

      setTimeline(mockTimeline);
      setLoading(false);
    }, 500);
  };

  if (!listing) return null;

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-100";
      case "under_review":
        return "text-blue-600 bg-blue-100";
      case "approved":
        return "text-green-600 bg-green-100";
      case "rejected":
        return "text-red-600 bg-red-100";
      case "withdrawn":
        return "text-gray-600 bg-gray-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "under_review":
        return "Under Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "withdrawn":
        return "Withdrawn";
      default:
        return status;
    }
  };

  const getVehicleMedia = () => {
    if (listing.media && listing.media.length > 0) {
      // The media is already in VehicleMediaRead format
      return listing.media;
    }
    return [];
  };

  const getListingTitle = () => {
    if (listing.vehicle) {
      return `${listing.vehicle.make || "Unknown"} ${listing.vehicle.model || "Vehicle"} ${listing.vehicle.manufacturingYear || ""}`.trim();
    }
    return `Listing #${listing.id}`;
  };

  const getListingPrice = () => {
    return listing.listingDetails?.amount || listing.listingDetails?.rate || 0;
  };

  const getVehicleImage = () => {
    if (listing.media && listing.media.length > 0) {
      return listing.media[0].media_path;
    }
    return "/images/cars/default-car.webp";
  };

  const handlePublishToggle = async () => {
    if (!listing) return;

    setPublishLoading(true);
    try {
      const result = await toggleListingPublishStatus(listing.id);

      if (result.success) {
        setCurrentPublishStatus(result.newStatus || null);
        // You could show a success message here
      } else {
        // You could show an error message here
        console.error("Failed to toggle publish status:", result.error);
      }
    } catch (error) {
      console.error("Error toggling publish status:", error);
    } finally {
      setPublishLoading(false);
    }
  };

  const canPublish = () => {
    return currentStatus === "approved";
  };

  const getPublishButtonText = () => {
    if (!currentPublishStatus || currentPublishStatus === "pending") {
      return "Publish Listing";
    } else if (currentPublishStatus === "published") {
      return "Unpublish Listing";
    } else {
      return "Publish Listing";
    }
  };

  const getPublishButtonIcon = () => {
    if (!currentPublishStatus || currentPublishStatus === "pending") {
      return <Eye size={16} />;
    } else if (currentPublishStatus === "published") {
      return <EyeOff size={16} />;
    } else {
      return <Eye size={16} />;
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              {loading ? (
                <Loader2 size={40} className="text-[#009639] animate-spin" />
              ) : (
                <Car size={40} className="text-[#009639]" />
              )}
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Listing Status
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              Track your listing approval progress
            </SheetDescription>
          </div>

          {/* Content */}
          <div
            className={`flex-1 overflow-y-auto p-4 ${loading ? "opacity-50 pointer-events-none" : ""}`}
          >
            <div className="space-y-6">
              {/* Listing Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md overflow-hidden">
                {/* Vehicle Image */}
                <div className="h-52 bg-gray-100 relative overflow-hidden">
                  <VehicleImage
                    media={getVehicleMedia()}
                    alt={getListingTitle()}
                    className="object-cover"
                    fallbackSrc="/images/cars/default-car.webp"
                  />
                  <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                    <span className="text-xs font-medium text-white">
                      {listing.listingType.charAt(0).toUpperCase() +
                        listing.listingType.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-start gap-3 mb-2">
                    <h3 className="text-lg font-bold text-[#333333] flex-1 min-w-0">
                      {getListingTitle()}
                    </h3>
                    <span
                      className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${getStatusColor(currentStatus)}`}
                    >
                      {getStatusText(currentStatus)}
                    </span>
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <span className="text-[#009639] font-bold text-xs">
                          #
                        </span>
                      </div>
                      <span className="text-[#797879]">Listing ID: </span>
                      <span className="text-[#333333] font-medium">
                        {listing.id}
                      </span>
                    </div>
                    {getListingPrice() > 0 && (
                      <div className="flex items-center text-sm">
                        <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                          <DollarSign size={10} className="text-[#009639]" />
                        </div>
                        <span className="text-[#797879]">Price: </span>
                        <span className="text-[#333333] font-medium">
                          R {getListingPrice().toLocaleString()}
                        </span>
                      </div>
                    )}
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <FileText size={10} className="text-[#009639]" />
                      </div>
                      <span className="text-[#797879]">Listed: </span>
                      <span className="text-[#333333] font-medium">
                        {formatDate(new Date(listing.effectiveFrom))}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Timeline */}
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">
                    Loading timeline...
                  </p>
                </div>
              ) : (
                <ListingStatusTimeline
                  timeline={timeline}
                  currentStatus={currentStatus}
                />
              )}

              {/* Publish/Unpublish Button */}
              {canPublish() && (
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-semibold text-[#333333] mb-1">
                        Listing Visibility
                      </h4>
                      <p className="text-sm text-[#797879]">
                        {currentPublishStatus === "published"
                          ? "Your listing is live and visible to users"
                          : "Your listing is ready to be published"}
                      </p>
                    </div>
                    {currentPublishStatus && (
                      <span
                        className={`text-xs px-2 py-1 rounded-full font-medium ${
                          currentPublishStatus === "published"
                            ? "text-green-600 bg-green-100"
                            : currentPublishStatus === "pending"
                              ? "text-yellow-600 bg-yellow-100"
                              : "text-gray-600 bg-gray-100"
                        }`}
                      >
                        {currentPublishStatus === "published"
                          ? "Live"
                          : currentPublishStatus === "pending"
                            ? "Ready"
                            : "Archived"}
                      </span>
                    )}
                  </div>
                  <Button
                    onClick={handlePublishToggle}
                    disabled={publishLoading}
                    className={`w-full rounded-full ${
                      currentPublishStatus === "published"
                        ? "bg-red-600 hover:bg-red-700"
                        : "bg-[#009639] hover:bg-[#007A2F]"
                    } text-white`}
                  >
                    {publishLoading ? (
                      <Loader2 size={16} className="animate-spin mr-2" />
                    ) : (
                      <span className="mr-2">{getPublishButtonIcon()}</span>
                    )}
                    {publishLoading ? "Updating..." : getPublishButtonText()}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
