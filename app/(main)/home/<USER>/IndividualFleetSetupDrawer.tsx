"use client";

import { useState } from "react";
import { ArrowLeft, User, Plus, X, ChevronRight } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Individual {
  id: string;
  firstName: string;
  lastName: string;
  idNumber: string;
}

interface IndividualFleetData {
  groupName: string;
  isMultipleIndividuals: boolean;
  individuals: Individual[];
}

interface IndividualFleetSetupDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onNext: (data: IndividualFleetData) => void;
}

export default function IndividualFleetSetupDrawer({
  isOpen,
  onClose,
  onBack,
  onNext,
}: IndividualFleetSetupDrawerProps) {
  const [groupName, setGroupName] = useState("");
  const [isMultipleIndividuals, setIsMultipleIndividuals] = useState(false); // Default to single individual
  const [individuals, setIndividuals] = useState<Individual[]>([
    { id: "1", firstName: "", lastName: "", idNumber: "" },
  ]);

  const addIndividual = () => {
    const newIndividual: Individual = {
      id: Date.now().toString(),
      firstName: "",
      lastName: "",
      idNumber: "",
    };
    setIndividuals([...individuals, newIndividual]);
  };

  const removeIndividual = (id: string) => {
    if (individuals.length > 1) {
      setIndividuals(individuals.filter((i) => i.id !== id));
    }
  };

  const updateIndividual = (
    id: string,
    field: keyof Individual,
    value: string
  ) => {
    setIndividuals(
      individuals.map((i) => (i.id === id ? { ...i, [field]: value } : i))
    );
  };

  const handleContinue = () => {
    if (
      groupName.trim() &&
      individuals.every(
        (i) => i.firstName.trim() && i.lastName.trim() && i.idNumber.trim()
      )
    ) {
      onNext({
        groupName: groupName.trim(),
        isMultipleIndividuals,
        individuals: individuals.map((i) => ({
          ...i,
          firstName: i.firstName.trim(),
          lastName: i.lastName.trim(),
          idNumber: i.idNumber.trim(),
        })),
      });
    }
  };

  const isValid =
    groupName.trim() &&
    individuals.every(
      (i) => i.firstName.trim() && i.lastName.trim() && i.idNumber.trim()
    );

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                title="Back"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Individual Fleet Setup
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Enter individual owner details
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Group Name */}
              <div>
                <label className="text-[#797879] text-xs mb-2 block">
                  Fleet Group Name *
                </label>
                <input
                  type="text"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="Enter fleet group name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                />
              </div>

              {/* Individual Structure Toggle */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-4">
                  Ownership Structure:
                </h4>

                <div className="flex bg-gray-100 rounded-full p-1 mb-4">
                  <button
                    className={`flex-1 py-2 px-3 rounded-full font-medium text-sm transition-all duration-300 ${
                      !isMultipleIndividuals
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setIsMultipleIndividuals(false)}
                  >
                    Single Individual
                  </button>
                  <button
                    className={`flex-1 py-2 px-3 rounded-full font-medium text-sm transition-all duration-300 ${
                      isMultipleIndividuals
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setIsMultipleIndividuals(true)}
                  >
                    Multiple Individuals
                  </button>
                </div>
              </div>

              {/* Individuals Section */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold text-[#333333]">
                    {isMultipleIndividuals
                      ? "Individual Owners"
                      : "Owner Details"}
                  </h4>
                  {isMultipleIndividuals && (
                    <button
                      onClick={addIndividual}
                      className="flex items-center text-[#009639] text-sm font-medium"
                    >
                      <Plus size={16} className="mr-1" />
                      Add Individual
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {individuals.map((individual, index) => (
                    <div
                      key={individual.id}
                      className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                    >
                      {isMultipleIndividuals && individuals.length > 1 && (
                        <div className="flex justify-between items-center mb-3">
                          <h5 className="font-medium text-[#333333]">
                            Individual {index + 1}
                          </h5>
                          <button
                            onClick={() => removeIndividual(individual.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      )}

                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              First Name *
                            </label>
                            <input
                              type="text"
                              value={individual.firstName}
                              onChange={(e) =>
                                updateIndividual(
                                  individual.id,
                                  "firstName",
                                  e.target.value
                                )
                              }
                              placeholder="First name"
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                            />
                          </div>

                          <div>
                            <label className="text-[#797879] text-xs mb-1 block">
                              Last Name *
                            </label>
                            <input
                              type="text"
                              value={individual.lastName}
                              onChange={(e) =>
                                updateIndividual(
                                  individual.id,
                                  "lastName",
                                  e.target.value
                                )
                              }
                              placeholder="Last name"
                              className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                            />
                          </div>
                        </div>

                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            ID Number *
                          </label>
                          <input
                            type="text"
                            value={individual.idNumber}
                            onChange={(e) =>
                              updateIndividual(
                                individual.id,
                                "idNumber",
                                e.target.value
                              )
                            }
                            placeholder="Enter ID number"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleContinue}
              disabled={!isValid}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Continue
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onBack}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Back
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
