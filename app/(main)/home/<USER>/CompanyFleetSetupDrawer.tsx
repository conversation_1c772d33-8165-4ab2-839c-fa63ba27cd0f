"use client";

import { useState } from "react";
import { ArrowLeft, Building, Plus, X, ChevronRight } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Company {
  id: string;
  name: string;
  registrationNumber: string;
}

interface CompanyFleetData {
  groupName: string;
  isMultipleCompanies: boolean;
  companies: Company[];
}

interface CompanyFleetSetupDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onNext: (data: CompanyFleetData) => void;
}

export default function CompanyFleetSetupDrawer({
  isOpen,
  onClose,
  onBack,
  onNext,
}: CompanyFleetSetupDrawerProps) {
  const [groupName, setGroupName] = useState("");
  const [isMultipleCompanies, setIsMultipleCompanies] = useState(false); // Default to single company
  const [companies, setCompanies] = useState<Company[]>([
    { id: "1", name: "", registrationNumber: "" },
  ]);

  const addCompany = () => {
    const newCompany: Company = {
      id: Date.now().toString(),
      name: "",
      registrationNumber: "",
    };
    setCompanies([...companies, newCompany]);
  };

  const removeCompany = (id: string) => {
    if (companies.length > 1) {
      setCompanies(companies.filter((c) => c.id !== id));
    }
  };

  const updateCompany = (id: string, field: keyof Company, value: string) => {
    setCompanies(
      companies.map((c) => (c.id === id ? { ...c, [field]: value } : c))
    );
  };

  const handleContinue = () => {
    if (
      groupName.trim() &&
      companies.every((c) => c.name.trim() && c.registrationNumber.trim())
    ) {
      onNext({
        groupName: groupName.trim(),
        isMultipleCompanies,
        companies: companies.map((c) => ({
          ...c,
          name: c.name.trim(),
          registrationNumber: c.registrationNumber.trim(),
        })),
      });
    }
  };

  const isValid =
    groupName.trim() &&
    companies.every((c) => c.name.trim() && c.registrationNumber.trim());

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                title="Back"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Company Fleet Setup
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Enter your company details
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Group Name */}
              <div>
                <label className="text-[#797879] text-xs mb-2 block">
                  Fleet Group Name *
                </label>
                <input
                  type="text"
                  value={groupName}
                  onChange={(e) => setGroupName(e.target.value)}
                  placeholder="Enter fleet group name"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                />
              </div>

              {/* Company Structure Toggle */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-4">
                  Company Structure:
                </h4>

                <div className="flex bg-gray-100 rounded-full p-1 mb-4">
                  <button
                    className={`flex-1 py-2 px-3 rounded-full font-medium text-sm transition-all duration-300 ${
                      !isMultipleCompanies
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setIsMultipleCompanies(false)}
                  >
                    Single Company
                  </button>
                  <button
                    className={`flex-1 py-2 px-3 rounded-full font-medium text-sm transition-all duration-300 ${
                      isMultipleCompanies
                        ? "bg-[#009639] text-white shadow-md"
                        : "text-[#797879] hover:text-[#333333]"
                    }`}
                    onClick={() => setIsMultipleCompanies(true)}
                  >
                    Multiple Companies
                  </button>
                </div>
              </div>

              {/* Companies Section */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold text-[#333333]">
                    {isMultipleCompanies ? "Companies" : "Company Details"}
                  </h4>
                  {isMultipleCompanies && (
                    <button
                      onClick={addCompany}
                      className="flex items-center text-[#009639] text-sm font-medium"
                    >
                      <Plus size={16} className="mr-1" />
                      Add Company
                    </button>
                  )}
                </div>

                <div className="space-y-4">
                  {companies.map((company, index) => (
                    <div
                      key={company.id}
                      className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                    >
                      {isMultipleCompanies && companies.length > 1 && (
                        <div className="flex justify-between items-center mb-3">
                          <h5 className="font-medium text-[#333333]">
                            Company {index + 1}
                          </h5>
                          <button
                            onClick={() => removeCompany(company.id)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      )}

                      <div className="space-y-3">
                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Company Name *
                          </label>
                          <input
                            type="text"
                            value={company.name}
                            onChange={(e) =>
                              updateCompany(company.id, "name", e.target.value)
                            }
                            placeholder="Enter company name"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                          />
                        </div>

                        <div>
                          <label className="text-[#797879] text-xs mb-1 block">
                            Registration Number *
                          </label>
                          <input
                            type="text"
                            value={company.registrationNumber}
                            onChange={(e) =>
                              updateCompany(
                                company.id,
                                "registrationNumber",
                                e.target.value
                              )
                            }
                            placeholder="Enter registration number"
                            className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleContinue}
              disabled={!isValid}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Continue
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onBack}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Back
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
