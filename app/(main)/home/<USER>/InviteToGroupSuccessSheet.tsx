"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  Users,
  UserPlus,
  FileText,
  ArrowLeft,
  Calendar,
} from "lucide-react";

interface InviteToGroupSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicantName: string;
  vehicleName: string;
  existingMembersCount?: number;
}

export default function InviteToGroupSuccessSheet({
  isOpen,
  onClose,
  onBack,
  applicantName,
  vehicleName,
  existingMembersCount = 1,
}: InviteToGroupSuccessSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* <PERSON> Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <UserPlus size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Member Added!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              {applicantName} has been invited to join the co-ownership group
              for your {vehicleName}
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Group Status */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3 flex items-center">
                  <Users size={16} className="mr-2 text-[#009639]" />
                  Group Status
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Vehicle:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {vehicleName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">New Member:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {applicantName.replace(/\*/g, "")}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">
                      Total Members:
                    </span>
                    <span className="text-sm font-medium text-[#009639]">
                      {existingMembersCount + 1} co-owners
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">
                      Group Status:
                    </span>
                    <span className="text-sm font-medium text-[#009639]">
                      Active
                    </span>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                <h4 className="font-semibold text-[#007A2F] mb-3 flex items-center">
                  <Calendar size={16} className="mr-2" />
                  What Happens Next
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      1
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Member Onboarding
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        {applicantName} will complete their co-ownership
                        agreement
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      2
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Update Usage Schedule
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Coordinate vehicle usage times with all group members
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      3
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Group Communication
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        All members will be added to the group chat for
                        coordination
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Group Benefits */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Expanded Group Benefits
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Reduced individual ownership costs</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>More flexible usage scheduling</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Shared maintenance responsibilities</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Group communication tools</span>
                  </div>
                </div>
              </div>

              {/* Current Members */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Group Members
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-[#797879]">You (Owner)</span>
                    <span className="text-xs bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded">
                      Primary
                    </span>
                  </div>
                  {Array.from({ length: existingMembersCount }, (_, i) => (
                    <div
                      key={i}
                      className="flex items-center justify-between text-sm"
                    >
                      <span className="text-[#797879]">Member {i + 1}</span>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        Co-owner
                      </span>
                    </div>
                  ))}
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-[#333333] font-medium">
                      {applicantName.replace(/\*/g, "")}
                    </span>
                    <span className="text-xs bg-[#ffd700] text-[#b8860b] px-2 py-1 rounded">
                      New
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={onClose}
              className="w-full bg-[#009639] text-white hover:bg-[#007A2F] py-3 rounded-full font-semibold transition-colors"
            >
              <Users size={16} className="mr-2 inline" />
              Manage Group
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              <Calendar size={16} className="mr-2 inline" />
              Update Schedule
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
