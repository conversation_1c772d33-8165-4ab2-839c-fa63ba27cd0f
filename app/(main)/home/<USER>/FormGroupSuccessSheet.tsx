"use client";

import React from "react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  Users,
  UserPlus,
  FileText,
  ArrowLeft,
  DollarSign,
} from "lucide-react";

interface FormGroupSuccessSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicantName: string;
  vehicleName: string;
}

export default function FormGroupSuccessSheet({
  isOpen,
  onClose,
  onBack,
  applicantName,
  vehicleName,
}: FormGroupSuccessSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <Users size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Group Created!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              {applicantName} is now the first co-owner of your {vehicleName}
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Group Details */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3 flex items-center">
                  <DollarSign size={16} className="mr-2 text-[#009639]" />
                  Co-ownership Details
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">Vehicle:</span>
                    <span className="text-sm font-medium text-[#333333]">
                      {vehicleName}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">
                      First Co-owner:
                    </span>
                    <span className="text-sm font-medium text-[#333333]">
                      {applicantName.replace(/\*/g, "")}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">
                      Group Status:
                    </span>
                    <span className="text-sm font-medium text-[#009639]">
                      Active
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-[#797879]">
                      Available Shares:
                    </span>
                    <span className="text-sm font-medium text-[#333333]">
                      Open for more members
                    </span>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-[#e6ffe6] border border-[#009639] rounded-lg p-4">
                <h4 className="font-semibold text-[#007A2F] mb-3 flex items-center">
                  <UserPlus size={16} className="mr-2" />
                  What Happens Next
                </h4>
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      1
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Finalize Ownership Terms
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Complete the co-ownership agreement with {applicantName}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      2
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Accept More Co-owners
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Review additional applications and invite more members
                        to the group
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium flex-shrink-0">
                      3
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#007A2F]">
                        Manage Group Activities
                      </p>
                      <p className="text-xs text-[#007A2F]">
                        Coordinate usage schedules and shared responsibilities
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Benefits */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Group Benefits
                </h4>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Shared ownership costs and responsibilities</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Coordinated usage scheduling</span>
                  </div>
                  <div className="flex items-center text-sm text-[#797879]">
                    <CheckCircle size={12} className="text-green-500 mr-2" />
                    <span>Platform-managed legal agreements</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={onClose}
              className="w-full bg-[#009639] text-white hover:bg-[#007A2F] py-3 rounded-full font-semibold transition-colors"
            >
              <Users size={16} className="mr-2 inline" />
              Manage Group
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
