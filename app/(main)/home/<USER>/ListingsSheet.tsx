"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  FileText,
  Calendar,
  Clock,
  ChevronRight,
  Car,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import ListingStatusSheet from "./ListingStatusSheet";
import VehicleImage from "@/components/vehicle-image";
import { FetchUserAttributesOutput } from "aws-amplify/auth";
import { getUserListingsAction } from "@/actions/listings";

interface Listing {
  id: number;
  partyId: number;
  listingType: string;
  sourceType: string;
  sourceId: number;
  effectiveFrom: string;
  effectiveTo?: string | null;
  listingDetails: any; // JSON object containing pricing and other details
  vehicle?: {
    id: number;
    vinNumber?: string;
    vehicleRegistration?: string;
    color?: string;
    manufacturingYear?: number;
    make?: string;
    model?: string;
  };
  media: {
    id: number;
    vehicle_id: number;
    media_path: string;
    created_at: string;
  }[];
  latestDecision?: {
    decision: string;
    reason?: string | null;
    decisionAt: string;
  };
  documents: any[];
  publishStatus?: string | null;
  publishStatusAt?: string | null;
}

interface ListingsSheetProps {
  isOpen: boolean;
  onClose: () => void;
  attributes: FetchUserAttributesOutput;
}

export default function ListingsSheet({
  isOpen,
  onClose,
  attributes,
}: ListingsSheetProps) {
  const [selectedListing, setSelectedListing] = useState<Listing | null>(null);
  const [showStatusSheet, setShowStatusSheet] = useState(false);
  const [listings, setListings] = useState<Listing[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchListings();
    }
  }, [isOpen]);

  const fetchListings = async () => {
    setLoading(true);
    try {
      const result = await getUserListingsAction();
      if (result.success && result.listings) {
        setListings(result.listings);
      }
    } catch (error) {
      console.error("Error fetching listings:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          text: "text-yellow-800",
          color: "#ffd700",
        };
      case "under_review":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          text: "text-blue-800",
          color: "#3b82f6",
        };
      case "approved":
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          text: "text-green-800",
          color: "#10b981",
        };
      case "rejected":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          text: "text-red-800",
          color: "#ef4444",
        };
      case "withdrawn":
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          color: "#6b7280",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          color: "#6b7280",
        };
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "under_review":
        return "Under Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      case "withdrawn":
        return "Withdrawn";
      default:
        return status;
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleListingClick = (listing: Listing) => {
    setSelectedListing(listing);
    setShowStatusSheet(true);
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full max-w-md p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
              <div className="flex items-center">
                <button
                  title="Back"
                  onClick={onClose}
                  className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                >
                  <ArrowLeft size={24} />
                </button>
                <div>
                  <SheetTitle className="text-xl font-bold text-white">
                    My Listings
                  </SheetTitle>
                  <SheetDescription className="text-sm text-green-100">
                    Track your listing status
                  </SheetDescription>
                </div>
              </div>
            </SheetHeader>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-4">
                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
                    <p className="text-sm text-gray-500">Loading listings...</p>
                  </div>
                ) : listings.length === 0 ? (
                  <div className="text-center py-12">
                    <Car size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Listings Yet
                    </h3>
                    <p className="text-gray-500">
                      Your vehicle listings will appear here once you submit
                      them.
                    </p>
                  </div>
                ) : (
                  listings.map((listing) => {
                    const getListingTitle = () => {
                      if (!listing.vehicle) return `Listing #${listing.id}`;
                      return `${listing.vehicle.make || "Unknown"} ${listing.vehicle.model || "Vehicle"} ${listing.vehicle.manufacturingYear || ""}`;
                    };

                    const getListingSubtitle = () => {
                      const listingType = listing.listingType;
                      if (!listingType) return "Vehicle Listing";
                      return (
                        listingType.charAt(0).toUpperCase() +
                        listingType.slice(1) +
                        " Listing"
                      );
                    };

                    // Get current status from latest decision or default to pending
                    const currentStatus =
                      listing.latestDecision?.decision || "pending";

                    const statusStyle = getStatusColor(currentStatus);
                    return (
                      <div
                        key={listing.id}
                        className="bg-white rounded-xl border border-gray-100 drop-shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                        onClick={() => handleListingClick(listing)}
                      >
                        {/* Vehicle Image */}
                        {listing.vehicle && (
                          <div className="h-48 bg-gray-100 relative overflow-hidden">
                            <VehicleImage
                              media={listing.media}
                              alt={`${listing.vehicle.make || "Vehicle"} ${listing.vehicle.model || ""}`}
                              className="object-cover"
                              fallbackSrc="/images/cars/default-car.webp"
                            />
                            <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                              <span className="text-xs font-medium text-white">
                                {listing.listingType
                                  ? listing.listingType
                                      .charAt(0)
                                      .toUpperCase() +
                                    listing.listingType.slice(1)
                                  : "Listing"}
                              </span>
                            </div>
                          </div>
                        )}

                        <div className="p-4">
                          <div className="flex justify-between items-start gap-3 mb-3">
                            <div className="flex-1 min-w-0">
                              <h3 className="text-lg font-bold text-[#333333] mb-1">
                                {getListingTitle()}
                              </h3>
                              <p className="text-[#797879] text-sm">
                                {getListingSubtitle()}
                              </p>
                            </div>
                            <div className="flex flex-col gap-1 items-end">
                              {/* Show single status based on priority */}
                              {(() => {
                                // If approved and published, show only "Published"
                                if (
                                  currentStatus === "approved" &&
                                  listing.publishStatus === "published"
                                ) {
                                  return (
                                    <span
                                      className="text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 text-green-600 bg-green-100 border-green-200"
                                      style={{ borderWidth: "1px" }}
                                    >
                                      Published
                                    </span>
                                  );
                                }

                                // If approved but not published, show both statuses
                                else if (currentStatus === "approved") {
                                  return (
                                    <>
                                      <span
                                        className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${statusStyle.bg} ${statusStyle.border} ${statusStyle.text}`}
                                        style={{ borderWidth: "1px" }}
                                      >
                                        {getStatusText(currentStatus)}
                                      </span>
                                      <span
                                        className={`text-xs px-2 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${
                                          listing.publishStatus === "pending"
                                            ? "text-yellow-600 bg-yellow-100 border-yellow-200"
                                            : "text-gray-600 bg-gray-100 border-gray-200"
                                        }`}
                                        style={{ borderWidth: "1px" }}
                                      >
                                        {listing.publishStatus === "pending"
                                          ? "Ready to Publish"
                                          : listing.publishStatus ||
                                            "Not Published"}
                                      </span>
                                    </>
                                  );
                                }

                                // For all other statuses, show approval status only
                                else {
                                  return (
                                    <span
                                      className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${statusStyle.bg} ${statusStyle.border} ${statusStyle.text}`}
                                      style={{ borderWidth: "1px" }}
                                    >
                                      {getStatusText(currentStatus)}
                                    </span>
                                  );
                                }
                              })()}
                            </div>
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <span className="text-[#009639] font-bold text-xs">
                                  #
                                </span>
                              </div>
                              <span className="text-[#797879]">ID: </span>
                              <span className="text-[#333333] font-medium">
                                {listing.id}
                              </span>
                            </div>
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <Calendar
                                  size={10}
                                  className="text-[#009639]"
                                />
                              </div>
                              <span className="text-[#797879]">Listed: </span>
                              <span className="text-[#333333] font-medium">
                                {formatDate(new Date(listing.effectiveFrom))}
                              </span>
                            </div>
                            {(listing.listingDetails?.amount ||
                              listing.listingDetails?.rate) && (
                              <div className="flex items-center text-sm">
                                <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                  <span className="text-[#009639] font-bold text-xs">
                                    $
                                  </span>
                                </div>
                                <span className="text-[#797879]">Price: </span>
                                <span className="text-[#333333] font-medium">
                                  R{" "}
                                  {(
                                    listing.listingDetails?.amount ||
                                    listing.listingDetails?.rate ||
                                    0
                                  ).toLocaleString()}
                                </span>
                              </div>
                            )}
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-[#009639]">
                              <Clock size={16} className="mr-1" />
                              <span>View Status Timeline</span>
                            </div>
                            <ChevronRight
                              size={20}
                              className="text-[#797879]"
                            />
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Listing Status Sheet */}
      <ListingStatusSheet
        isOpen={showStatusSheet}
        onClose={() => setShowStatusSheet(false)}
        listing={selectedListing}
      />
    </>
  );
}
