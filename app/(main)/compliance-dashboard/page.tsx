"use server";
import ComplianceDashboardScreen from "./compliance-dashboard";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import { getComplianceRequirementByPartyId } from "@/actions/compliance-requirement";
import MissingParty from "@/components/missing-party";
import { getCompanies } from "@/actions/company";
import {
  Document,
  ComplianceItem,
  Entity,
  ComplianceRequirementRead,
  ComplianceRequirementReadWithRelations,
} from "@/types/compliance";
import { CompanyRead } from "@/types/company";
import { formatDateForInput } from "@/lib/utils";

function convertCompaniesToEntities(companies: CompanyRead[]): Entity[] {
  return companies.map((company) => ({
    id: String(company.party_id),
    name: company.name ?? "Unknown",
    type: "Private Company", // Default assumption
    status: "active", // Default assumption
    province: company.registration_country ?? "",
    formationDate: company.registration_date
      ? formatDateForInput(company.registration_date)
      : null,
  }));
}

function convertCompliancesToComplianceItems(
  compliances: ComplianceRequirementReadWithRelations[]
): ComplianceItem[] {
  return compliances.map((compliance) => ({
    id: compliance.id,
    entityId: String(compliance.party_id),
    type: compliance.reference_type!,
    description: compliance.compliance_set?.description ?? "",
    dueDate: compliance.submitted_at ?? "",
    status: compliance.status!,
    priority: "medium",
    fee: 0,
    documentRequired: true,
    notes: compliance.notes ?? "",
  }));
}
function convertCompliancesToDocuments(
  compliances: ComplianceRequirementRead[]
): Document[] {
  return compliances.map((compliance) => ({
    id: compliance.id,
    entityId: String(compliance.party_id),
    name: compliance.reference,
    type: "Uploaded Document",
    date: formatDateForInput(compliance.issue_date),
    status: "valid",
    expirationDate: compliance.expiry_date
      ? formatDateForInput(compliance.expiry_date)
      : null,
  }));
}

export default async function ComplianceDashboard() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  const [compliances, companies] = await Promise.all([
    getComplianceRequirementByPartyId(+dbId),
    getCompanies(),
  ]);

  const entities = convertCompaniesToEntities(companies);
  const complianceItems = convertCompliancesToComplianceItems(compliances);
  const documents = convertCompliancesToDocuments(compliances);

  console.log(documents);
  return (
    <ComplianceDashboardScreen
      entities={entities}
      complianceItems={complianceItems}
      documents={documents}
    />
  );
}
