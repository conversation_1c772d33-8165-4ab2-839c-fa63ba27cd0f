"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Calendar,
  Clock,
  MessageSquare,
  ChevronRight,
  Edit,
  X,
  Phone,
  Mail,
  MapPin,
} from "lucide-react";
import type { ContactPointRead } from "@/types/contact-points";
import type { VehicleMediaRead } from "@/types/vehicles";
import { deprecated_BookingRead } from "@/types/bookings";
import { IndividualRead } from "@/types/individuals";
import { VehicleReadWithModelAndParty } from "@/types/vehicles";
import { deprecated_BookingStatus } from "@/types/bookings";
import { getUrl } from "aws-amplify/storage";
import { formatDateForInput } from "@/lib/utils";
import type { PartyIdentificationRead } from "@/types/party-identifications";

const contactActions = {
  phone: {
    label: "Call",
    icon: <Phone size={18} className="mr-2" />,
    getUrl: (value: string) => `tel:${value}`,
  },
  email: {
    label: "Email",
    icon: <Mail size={18} className="mr-2" />,
    getUrl: (value: string) => `mailto:${value}`,
  },
  address: {
    label: "View Address",
    icon: <MapPin size={18} className="mr-2" />,
    getUrl: (value: string) =>
      `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(value)}`,
  },
};

export default function BookingDetailsScreen({
  booking,
  individual,
  vehicle,
  contacts,
  partyIdentifications,
}: {
  booking: deprecated_BookingRead;
  individual: IndividualRead;
  vehicle: VehicleReadWithModelAndParty;
  contacts: ContactPointRead[];
  partyIdentifications: PartyIdentificationRead[];
}) {
  const router = useRouter();
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [profile, setProfile] = useState<string | null>(null);
  const profilePic = partyIdentifications
    .filter((id) => id.identification_type.name === "ProfilePicture")
    .sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0]?.document_image_url;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };
  console.log("Booking Details:", partyIdentifications);
  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length) return;

      const urls: string[] = await Promise.all(
        vehicle.media.map(async (item: VehicleMediaRead) => {
          const result = await getUrl({ path: item.media_path });
          return result.url.toString();
        })
      );

      setImageUrls(urls);
    }

    loadImages();
  }, [vehicle]);

  useEffect(() => {
    async function loadProfile() {
      if (!profilePic) return;
      const result = await getUrl({ path: profilePic });
      setProfile(result.url.toString());
    }

    loadProfile();
  }, [profilePic]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Confirmed
          </span>
        );
      case "active":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Active
          </span>
        );
      case "completed":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#f2f2f2] text-[#333333] font-medium">
            Completed
          </span>
        );
      case "cancelled":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#ffe6e6] text-[#7A0000] font-medium">
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  const handleCancelBooking = () => {
    console.log("Cancelling booking:", booking.reference);
    // In a real app, you would make an API call here
    setShowCancelModal(false);
    router.push("/my-bookings");
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Booking Details</h1>
        </div>
        {booking.status === deprecated_BookingStatus.CONFIRMED && (
          <button
            className="text-white text-sm font-medium"
            onClick={() => router.push(`/edit-booking/${booking.id}`)}
          >
            <Edit size={18} />
          </button>
        )}
      </div>

      {/* Booking Status */}
      <div className="bg-white px-6 py-4 flex items-center justify-between border-b border-[#f2f2f2]">
        <div>
          <div className="flex items-center">
            <h2 className="text-[#333333] font-medium mr-2">
              Booking : {booking.reference}
            </h2>
            {getStatusBadge(booking.status)}
          </div>
          <p className="text-xs text-[#797879] mt-1">
            Created on {formatDateForInput(booking.created_at)}
          </p>
        </div>
        {booking.status === deprecated_BookingStatus.CONFIRMED && (
          <button
            className="text-red-500 text-sm font-medium"
            onClick={() => setShowCancelModal(true)}
          >
            Cancel
          </button>
        )}
      </div>

      {/* Vehicle Info */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-start">
            <div className="w-20 h-16 bg-[#f2f2f2] rounded-lg overflow-hidden mr-3 flex-shrink-0">
              <Image
                src={imageUrls?.[0] || "/placeholder.svg"}
                alt={vehicle?.model?.model}
                width={80}
                height={64}
                className="object-cover"
              />
            </div>
            <div className="flex-1">
              <h3 className="text-[#333333] font-medium">
                {vehicle?.model?.model}
              </h3>
              <button
                className="text-[#0286ff] text-sm flex items-center mt-1"
                onClick={() => router.push(`/vehicle-status/${vehicle?.id}`)}
              >
                View Vehicle{" "}
                <ChevronRight size={16} className="text-[#009639]" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Booking Details */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Booking Details</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <Calendar size={18} className="text-[#009639] mr-3 mt-0.5" />
              <div>
                <p className="text-[#797879] text-xs">Dates</p>
                <p className="text-[#333333]">
                  {formatDate(booking.start_datetime)} -{" "}
                  {formatDate(booking.end_datetime)}
                </p>
              </div>
            </div>

            {/* <div className="flex items-start">
              <Clock size={18} className="text-[#009639] mr-3 mt-0.5" />
              <div>
                <p className="text-[#797879] text-xs">Times</p>
                <p className="text-[#333333]">
                  {booking.start_datetime} - {booking.end_datetime}
                </p>
              </div>
            </div> */}

            {/* <div className="flex items-start">
              <MapPin size={18} className="text-[#009639] mr-3 mt-0.5" />
              <div>
                <p className="text-[#797879] text-xs">Pickup Location</p>
                <p className="text-[#333333]">{booking.pickupLocation}</p>
              </div>
            </div> */}
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">Booked By</h3>
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full overflow-hidden mr-3 border-2 border-[#e6ffe6] shadow-sm">
              <Image
                src={profile || "/placeholder-profile.svg"}
                alt={individual?.first_name}
                width={40}
                height={40}
                className="object-cover"
              />
            </div>
            <div className="flex-1">
              <h4 className="text-[#333333] font-medium">
                {individual?.first_name} {individual?.last_name}
              </h4>
              <button
                className="text-[#009639] text-sm flex items-center mt-1"
                onClick={() => router.push(`/member-details/${individual?.id}`)}
              >
                View Profile{" "}
                <ChevronRight size={16} className="text-[#009639]" />
              </button>
            </div>
          </div>

          <div className="flex mt-4 space-x-3">
            <button
              className="flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full flex items-center justify-center shadow-md"
              onClick={() => router.push(`/chat/${individual.id}`)}
            >
              <MessageSquare size={18} className="mr-2" /> Message
            </button>
          </div>
        </div>
        <div className="flex gap-3">
          {contacts?.map((contact) => {
            const type = contact.contact_point_type
              .name as keyof typeof contactActions;
            const action = contactActions[type];

            if (!action) return null;

            return (
              <button
                key={contact.id}
                className="flex-1 border border-[#009639] text-[#009639] py-3 rounded-full flex items-center justify-center shadow-sm"
                onClick={() => window.open(action.getUrl(contact.value))}
              >
                {action.icon}
                {action.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Cancel Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-lg border border-gray-100">
            <div className="flex justify-end">
              <button onClick={() => setShowCancelModal(false)}>
                <X size={20} className="text-[#333333]" />
              </button>
            </div>
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-[#ffe6e6] rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                <X size={32} className="text-[#7A0000]" />
              </div>
              <h3 className="text-lg font-bold text-[#333333] mb-2">
                Cancel Booking
              </h3>
              <p className="text-[#797879]">
                Are you sure you want to cancel this booking? This action cannot
                be undone.
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                className="flex-1 py-3 border border-[#d6d9dd] rounded-full text-[#333333] shadow-sm"
                onClick={() => setShowCancelModal(false)}
              >
                Keep Booking
              </button>
              <button
                className="flex-1 py-3 bg-gradient-to-r from-[#FF3B30] to-[#7A0000] text-white rounded-full shadow-md"
                onClick={handleCancelBooking}
              >
                Cancel Booking
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
