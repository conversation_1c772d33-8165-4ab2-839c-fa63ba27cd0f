"use server";

import { getListingByIdDrizzle } from "@/drizzle-actions/listings";
import { getVehicleByIdWithListings } from "@/drizzle-actions/vehicle-dashboard";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import ListingManagementScreen from "@/components/screens/ListingManagementScreen";

export default async function EditListingPage({
  params,
}: {
  params: Promise<{ listingId: string }>;
}) {
  try {
    const { listingId } = await params;
    console.log("Edit Listing - Loading listing ID:", listingId);
    
    // Fetch user attributes first
    const userAttributes = await getUserAttributes().catch(error => {
      console.error("Failed to get user attributes:", error);
      return null;
    });
    console.log("User attributes loaded:", !!userAttributes);

    // Fetch listing data first to ensure it exists
    const listing = await getListingByIdDrizzle(+listingId).catch(error => {
      console.error("Failed to get listing:", error);
      return null;
    });
    
    if (!listing) {
      return <p>Listing not found.</p>;
    }
    console.log("Listing loaded:", listing.id);

    // Fetch vehicle data
    const vehicle = await getVehicleByIdWithListings(listing.vehicle_id).catch(error => {
      console.error("Failed to get vehicle:", error);
      return null;
    });

    if (!vehicle) {
      return <p>Vehicle not found.</p>;
    }
    console.log("Vehicle loaded:", vehicle.id);

    return (
      <ListingManagementScreen 
        listing={listing}
        vehicle={vehicle}
        params={{ listingId }}
      />
    );
  } catch (error) {
    console.error("Edit Listing page error:", error);
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-500 mb-4">Error loading listing management</p>
          <button
            onClick={() => window.history.back()}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
} 