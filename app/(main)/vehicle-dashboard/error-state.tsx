"use client";

import BackElement from "@/app/components/back-element";

export default function VehicleDashboardError() {
  return (
    <div className="min-h-screen p-4">
      <BackElement title="Vehicle Dashboard" />
      <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
        <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mb-4">
          <span className="text-red-600 text-2xl">⚠️</span>
        </div>
        <h3 className="text-lg font-semibold text-red-600 mb-2">
          Something went wrong
        </h3>
        <p className="text-gray-600 mb-4">
          There was an error loading the vehicle dashboard. Please try refreshing the page.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
        >
          Refresh Page
        </button>
      </div>
    </div>
  );
} 