import BackElement from "@/app/components/back-element";
import VehicleDashboardTabs from "./vehicle-tabs";
import VehicleDashboardError from "./error-state";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import { getCompanyOwnershipByParty, getVehiclesByParties } from "@/drizzle-actions/vehicle-dashboard";

export default async function VehicleDashboardScreen() {
  try {
    const attributes = await getUserAttributes();
    console.log("User attributes:", attributes);
    
    const { ["custom:db_id"]: dbId, sub: externalId, email } = attributes || {};
    console.log("Extracted values:", { dbId, externalId, email });
    
    if (!dbId || !externalId) {
      console.log("Missing dbId or externalId, showing MissingParty");
      return <MissingParty />;
    }

    const ownerships = await getCompanyOwnershipByParty(+dbId);
    console.log("Ownerships:", ownerships);
    
    // Always include user's own party ID for directly owned vehicles
    const userPartyId = +dbId;
    const companyPartyIds = ownerships
      .map((ownership) => ownership.company?.party_id)
      .filter((id): id is number => typeof id === "number");
    
    // Combine user's party ID with company party IDs (remove duplicates)
    const partyIds: number[] = [...new Set([userPartyId, ...companyPartyIds])];
    
    console.log("Party IDs:", partyIds);
    console.log("User Party ID:", userPartyId);
    console.log("Company Party IDs:", companyPartyIds);

    const vehicles = await getVehiclesByParties(partyIds);
    console.log("Vehicles:", vehicles);

    // Add additional debugging for empty results
    if (vehicles.length === 0) {
      console.log("No vehicles found for party IDs:", partyIds);
      console.log("Ownerships data:", JSON.stringify(ownerships, null, 2));
    }

    return (
      <div className="min-h-screen ">
        <VehicleDashboardTabs vehicles={vehicles} ownerships={ownerships} partyId={+dbId} />
      </div>
    );
  } catch (error) {
    console.error("Error in VehicleDashboardScreen:", error);
    return <VehicleDashboardError />;
  }
}
