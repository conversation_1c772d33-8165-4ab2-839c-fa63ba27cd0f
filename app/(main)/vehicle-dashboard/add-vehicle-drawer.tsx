"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { VehicleWizard } from '../list-vehicle';

interface AddVehicleDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  partyId: number;
  groupId?: number;
  fleetId?: number;
}

export default function AddVehicleDrawer({ 
  isOpen, 
  onClose, 
  partyId, 
  groupId, 
  fleetId 
}: AddVehicleDrawerProps) {
  const router = useRouter();

  const handleComplete = () => {
    onClose();
    // Refresh the page to show the new vehicle
    router.refresh();
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Content */}
          <div className="h-full overflow-y-auto">
            <VehicleWizard 
              partyId={partyId} 
              groupId={groupId} 
              fleetId={fleetId}
              onComplete={handleComplete}
              isInDrawer={true}
            />
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
} 