"use client";

import { use } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { useUserAttributes } from "@/hooks/useUserAttributes";
import VehicleToGroupForm from "@/components/vehicle-to-group-form";

export default function AddVehicleToGroupPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const groupId = parseInt(use(params).id);
  const { navigateToGroupDetails } = useNavigation();
  const { attributes } = useUserAttributes();

  // Get current user party ID from auth context
  const currentUserPartyId = parseInt(attributes?.["custom:db_id"] || "1");

  return (
    <div className="min-h-screen bg-[#f5f5f5] py-8">
      <div className="container mx-auto px-4">
        <VehicleToGroupForm
          currentUserPartyId={currentUserPartyId}
          preselectedCompanyId={groupId}
          onSuccess={(data) => {
            // Navigate back to group details on success using SPA navigation
            navigateToGroupDetails(groupId.toString());
          }}
          onCancel={() => {
            // Go back to group details using SPA navigation
            navigateToGroupDetails(groupId.toString());
          }}
        />
      </div>
    </div>
  );
} 