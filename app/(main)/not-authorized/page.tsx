"use client";

import { useRouter } from "next/navigation";

export default function Unauthorized() {
  const router = useRouter();

  return (
    <div className="h-screen flex flex-col items-center justify-center px-6 relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-[#009639] to-[#007A2F] z-0"></div>
      <div className="absolute inset-0 opacity-5 z-0">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage:
              "url('data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M15 0C6.716 0 0 6.716 0 15c0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm0 5c5.514 0 10 4.486 10 10s-4.486 10-10 10S5 20.514 5 15 9.486 5 15 5z' fill='%23ffffff' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E')",
            backgroundSize: "30px 30px",
          }}
        ></div>
      </div>
      <div className="absolute top-1/4 left-1/4 w-40 h-40 rounded-full bg-white/5 z-0"></div>
      <div className="absolute bottom-1/4 right-1/4 w-60 h-60 rounded-full bg-white/3 z-0"></div>
      <div className="flex flex-col items-center justify-center h-full w-full max-w-md z-10 relative">
        <h1 className="text-5xl md:text-6xl font-bold drop-shadow-lg animate-fadeIn mb-6">
          Unauthorized
        </h1>
        <button
          className="w-full bg-white text-[#009639] py-4 rounded-full text-xl font-semibold shadow-md transition-transform duration-300 hover:scale-105 animate-fadeIn mt-8"
          onClick={() => router.push("/home")}
        >
          Go Home
        </button>
      </div>
    </div>
  );
}
