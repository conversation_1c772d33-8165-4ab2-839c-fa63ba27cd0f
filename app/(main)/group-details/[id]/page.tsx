"use client";

import { useState, use, useEffect } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import {
  ArrowLeft,
  Scale,
  Speech,
  Calendar,
  Settings,
  MessageSquare,
  ChevronRight,
  Plus,
  Mail,
} from "lucide-react";
import {
  getGroupDetails,
  getGroupVehiclesWithStatus,
  getGroupInvitations,
  cancelInvitation,
} from "../../../../drizzle-actions/groups-community";
import {
  GroupDetails,
  GroupMember,
  GroupVehicle,
  GroupBooking,
} from "../../../../types/community";

interface GroupInvitation {
  id: number;
  email: string;
  name: string;
  firstName: string | null;
  lastName: string | null;
  fraction: number;
  status: string;
  sentAt: string | null;
}

export default function GroupDetailsScreen({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const router = useRouter();
  const groupId = parseInt(use(params).id);
  const [activeTab, setActiveTab] = useState("members");
  const [group, setGroup] = useState<GroupDetails | null>(null);
  const [vehicles, setVehicles] = useState<GroupVehicle[]>([]);
  const [invitations, setInvitations] = useState<GroupInvitation[]>([]);
  const [loading, setLoading] = useState(true);

  const refreshGroupData = async () => {
    try {
      const groupDetails = await getGroupDetails(groupId);
      if (groupDetails) {
        console.log(
          "Refreshed group details - bookings count:",
          groupDetails.upcomingBookings.length
        );
        console.log("Upcoming bookings:", groupDetails.upcomingBookings);
        setGroup(groupDetails);
        setVehicles(groupDetails.vehicles);
      }
    } catch (error) {
      console.error("Failed to refresh group details:", error);
    }
  };

  useEffect(() => {
    const fetchGroupData = async () => {
      try {
        setLoading(true);
        const [groupDetails, groupInvitations] = await Promise.all([
          getGroupDetails(groupId),
          getGroupInvitations(groupId),
        ]);

        if (groupDetails) {
          console.log(
            "Initial fetch - bookings count:",
            groupDetails.upcomingBookings.length
          );
          console.log("Initial bookings:", groupDetails.upcomingBookings);
          setGroup(groupDetails);
          setVehicles(groupDetails.vehicles);
        }

        setInvitations(groupInvitations);
      } catch (error) {
        console.error("Failed to fetch group details:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchGroupData();
  }, [groupId]);

  // Refresh data when the component comes back into focus (e.g., navigating back from booking)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Re-fetch group details when page becomes visible
        refreshGroupData();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [groupId]);

  useEffect(() => {
    const fetchVehiclesWithStatus = async () => {
      if (activeTab === "vehicles") {
        try {
          const vehiclesWithStatus = await getGroupVehiclesWithStatus(groupId);
          // Transform to match GroupVehicle interface and remove duplicates
          const transformedVehicles = vehiclesWithStatus.map((vehicle) => ({
            ...vehicle,
            registration: vehicle.registration || "Unknown",
            status: vehicle.status as "available" | "in-use" | "maintenance",
            year: vehicle.year || undefined,
            color: vehicle.color || undefined,
          }));

          // Remove duplicates based on vehicle ID
          const uniqueVehicles = transformedVehicles.filter(
            (vehicle, index, self) =>
              index === self.findIndex((v) => v.id === vehicle.id)
          );

          setVehicles(uniqueVehicles);
        } catch (error) {
          console.error("Failed to fetch vehicles with status:", error);
        }
      }
    };

    fetchVehiclesWithStatus();
  }, [activeTab, groupId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-[#e6ffe6] text-[#007A2F] font-medium";
      case "in-use":
        return "bg-[#fff8e6] text-[#7A6500] font-medium";
      case "maintenance":
        return "bg-[#ffe6e6] text-[#7A0000] font-medium";
      default:
        return "bg-[#f9f9f9] text-[#797879] font-medium";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "available":
        return "Available";
      case "in-use":
        return "In Use";
      case "maintenance":
        return "Maintenance";
      default:
        return status;
    }
  };

  const formatBookingDate = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const startFormatted = start.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    const endFormatted = end.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });

    if (start.toDateString() === end.toDateString()) {
      return startFormatted;
    }
    return `${startFormatted} - ${endFormatted}`;
  };

  const handleCancelInvitation = async (invitationId: number) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    try {
      const result = await cancelInvitation(invitationId);
      if (result.success) {
        setInvitations(invitations.filter((inv) => inv.id !== invitationId));
        alert("Invitation cancelled successfully");
      } else {
        alert(result.message || "Failed to cancel invitation");
      }
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      alert("Failed to cancel invitation");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen">
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button
            className="mr-4"
            onClick={() => router.back()}
            aria-label="Go back"
          >
            <ArrowLeft size={24} className="text-white" />
          </button>
          <div className="h-6 bg-white/20 rounded w-32 animate-pulse"></div>
        </div>

        {/* Loading Content */}
        <div className="p-6">
          <div className="bg-white rounded-xl p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded mb-4 w-3/4"></div>
            <div className="flex space-x-4">
              <div className="flex-1 h-16 bg-gray-200 rounded"></div>
              <div className="flex-1 h-16 bg-gray-200 rounded"></div>
              <div className="flex-1 h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!group) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-center">
          <p className="text-[#797879] mb-4">Group not found</p>
          <button
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            onClick={() => router.back()}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen ">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button
          className="mr-4"
          onClick={() => router.back()}
          aria-label="Go back"
        >
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">{group.name}</h1>
      </div>

      {/* Group Info */}
      <div className="bg-white px-6 py-4 shadow-sm">
        <p className="text-[#333333] mb-4">{group.description}</p>
        <div className="flex space-x-4">
          <div className="text-center bg-[#e6ffe6] p-3 rounded-lg shadow-sm flex-1">
            <p className="text-2xl font-bold text-[#009639]">
              {group.members.length}
            </p>
            <p className="text-xs text-[#007A2F] font-medium">Members</p>
          </div>
          <div className="text-center bg-[#e6ffe6] p-3 rounded-lg shadow-sm flex-1">
            <p className="text-2xl font-bold text-[#009639]">
              {vehicles.length}
            </p>
            <p className="text-xs text-[#007A2F] font-medium">Vehicles</p>
          </div>
          <div className="text-center bg-[#e6ffe6] p-3 rounded-lg shadow-sm flex-1">
            <p className="text-2xl font-bold text-[#009639]">
              {group.upcomingBookings.length}
            </p>
            <p className="text-xs text-[#007A2F] font-medium">Bookings</p>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4 mt-4">
        <div className="flex bg-white rounded-t-xl shadow-sm mb-0">
          <button
            className={`py-4 px-4 text-sm font-medium relative flex-1 ${
              activeTab === "members" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("members")}
          >
            Members
            {activeTab === "members" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`py-4 px-4 text-sm font-medium relative flex-1 ${
              activeTab === "vehicles" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("vehicles")}
          >
            Vehicles
            {activeTab === "vehicles" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
          <button
            className={`py-4 px-4 text-sm font-medium relative flex-1 ${
              activeTab === "bookings" ? "text-[#009639]" : "text-[#797879]"
            }`}
            onClick={() => {
              setActiveTab("bookings");
              // Refresh booking data when switching to bookings tab
              refreshGroupData();
            }}
          >
            Bookings
            {activeTab === "bookings" && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-[#009639] rounded-full"></div>
            )}
          </button>
        </div>
      </div>

      {/* Members Tab */}
      {activeTab === "members" && (
        <div className="px-4 py-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-[#333333] font-medium">Group Members</h3>
            <button
              className="bg-[#009639] text-white text-sm font-medium flex items-center px-3 py-1.5 rounded-full shadow-sm"
              onClick={() => router.push(`/add-members/${groupId}`)}
            >
              <Plus size={14} className="mr-1" /> Manage Invites
            </button>
          </div>

          {/* Current Members */}
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100 mb-6">
            <div className="px-4 py-3 border-b border-[#f2f2f2]">
              <h4 className="text-sm font-semibold text-[#333333]">
                Current Members ({group?.members.length || 0})
              </h4>
            </div>
            {group?.members.map((member, index) => (
              <div
                key={member.id}
                className={`p-4 flex items-center cursor-pointer hover:bg-gray-50 transition-colors ${
                  index < group.members.length - 1
                    ? "border-b border-[#f2f2f2]"
                    : ""
                }`}
                onClick={() => router.push(`/member-details/${member.id}`)}
              >
                <div className="w-10 h-10 ride-avatar mr-3">
                  <Image
                    src={member.avatar}
                    alt={member.name}
                    width={40}
                    height={40}
                    className="object-cover rounded-full"
                  />
                </div>
                <div className="flex-1">
                  <div className="flex items-center">
                    <h3 className="text-[#333333] font-medium">
                      {member.name}
                    </h3>
                    <span className="ml-2 text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#009639] font-medium">
                      {member.role}
                    </span>
                  </div>
                </div>
                <div className="text-[#009639]">
                  <ChevronRight size={20} />
                </div>
              </div>
            ))}
          </div>

          {/* Pending Invitations */}
          {invitations.length > 0 && (
            <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
              <div className="px-4 py-3 border-b border-[#f2f2f2]">
                <h4 className="text-sm font-semibold text-[#333333]">
                  Pending Invitations ({invitations.length})
                </h4>
              </div>
              {invitations.map((invitation, index) => (
                <div
                  key={invitation.id}
                  className={`p-4 flex items-center ${
                    index < invitations.length - 1
                      ? "border-b border-[#f2f2f2]"
                      : ""
                  }`}
                >
                  <div className="w-10 h-10 bg-[#fff8e6] rounded-full flex items-center justify-center mr-3">
                    <Mail size={18} className="text-[#7A6500]" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#333333] font-medium">
                      {invitation.name}
                    </h3>
                    <p className="text-xs text-[#797879]">
                      {invitation.email} • {invitation.fraction * 100}%
                      ownership
                    </p>
                    <p className="text-xs text-[#7A6500]">
                      Status: {invitation.status}
                      {invitation.sentAt &&
                        ` • Sent ${new Date(invitation.sentAt).toLocaleDateString()}`}
                    </p>
                  </div>
                  <button
                    className="text-red-500 text-sm px-3 py-1 rounded hover:bg-red-50"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleCancelInvitation(invitation.id);
                    }}
                  >
                    Cancel
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Vehicles Tab */}
      {activeTab === "vehicles" && (
        <div className="px-4 py-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-[#333333] font-medium">Group Vehicles</h3>
            <button
              className="bg-[#009639] text-white text-sm font-medium flex items-center px-3 py-1.5 rounded-full shadow-sm"
              onClick={() => router.push(`/add-vehicle-to-group/${groupId}`)}
            >
              <Plus size={14} className="mr-1" /> Add Vehicle
            </button>
          </div>

          <div className="space-y-3">
            {vehicles.map((vehicle) => (
              <div
                key={vehicle.id}
                className="bg-white rounded-xl shadow-md p-4 border border-gray-100 cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => router.push(`/vehicle-status/${vehicle.id}`)}
              >
                <div className="flex items-center">
                  <div className="w-16 h-12 bg-[#f2f2f2] rounded-lg overflow-hidden mr-3 flex-shrink-0">
                    <Image
                      src={vehicle.image}
                      alt={vehicle.name}
                      width={64}
                      height={48}
                      className="object-cover"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-[#333333] font-medium">
                          {vehicle.name}
                        </h3>
                        {vehicle.registration && (
                          <p className="text-xs text-[#797879]">
                            {vehicle.registration}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(
                            vehicle.status
                          )}`}
                        >
                          {getStatusLabel(vehicle.status)}
                        </span>
                        <button
                          className="text-[#009639] text-xs font-medium px-2 py-1 border border-[#009639] rounded-full hover:bg-[#009639] hover:text-white transition-colors"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/booking-calendar/${vehicle.id}`);
                          }}
                        >
                          Book
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="text-[#009639] ml-2">
                    <ChevronRight size={20} />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Bookings Tab */}
      {activeTab === "bookings" && (
        <div className="px-4 py-8">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-[#333333] font-medium">Upcoming Bookings</h3>
            <div className="flex space-x-2">
              <button
                className="bg-gray-100 text-[#333333] text-sm font-medium flex items-center px-3 py-1.5 rounded-full shadow-sm hover:bg-gray-200"
                onClick={refreshGroupData}
              >
                Refresh
              </button>
              <button
                className="bg-[#009639] text-white text-sm font-medium flex items-center px-3 py-1.5 rounded-full shadow-sm"
                onClick={() => router.push(`/calendar-view`)}
              >
                <Calendar size={14} className="mr-1" /> Calendar
              </button>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            {group.upcomingBookings.length === 0 ? (
              <div className="p-8 text-center">
                <p className="text-[#797879] mb-4">No upcoming bookings</p>
                <button
                  className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
                  onClick={() => setActiveTab("vehicles")}
                >
                  Book a Vehicle
                </button>
              </div>
            ) : (
              group.upcomingBookings.map((booking, index) => (
                <div
                  key={booking.id}
                  className={`p-4 flex items-start cursor-pointer hover:bg-gray-50 transition-colors ${
                    index < group.upcomingBookings.length - 1
                      ? "border-b border-[#f2f2f2]"
                      : ""
                  }`}
                  onClick={() => router.push(`/booking-details/${booking.id}`)}
                >
                  <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                    <Calendar size={18} className="text-[#009639]" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-[#333333] font-medium">
                      {booking.vehicle}
                    </h3>
                    <p className="text-xs text-[#797879]">
                      Booked by {booking.member}
                    </p>
                    <p className="text-xs text-[#009639] mt-1 font-medium">
                      {formatBookingDate(booking.startDate, booking.endDate)}
                    </p>
                  </div>
                  <div className="text-[#009639]">
                    <ChevronRight size={20} />
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] shadow-lg flex flex-col space-y-3 md:flex-row md:space-y-0 md:space-x-3">
        <button
          className="w-full md:flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3.5 rounded-full flex items-center justify-center shadow-md"
          onClick={() => router.push(`/group-chat/${groupId}`)}
        >
          <MessageSquare size={18} className="mr-2" /> Chat
        </button>
        <button
          className="w-full md:flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3.5 rounded-full flex items-center justify-center shadow-md"
          onClick={() => router.push(`/group-settings/${groupId}`)}
        >
          <Settings size={18} className="mr-2" /> Settings
        </button>
        <button
          className="w-full md:flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3.5 rounded-full flex items-center justify-center shadow-md"
          onClick={() => router.push(`/dispute-resolution`)}
        >
          <Speech size={18} className="mr-2" /> Disputes
        </button>
        <button
          className="w-full md:flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3.5 rounded-full flex items-center justify-center shadow-md"
          onClick={() => router.push(`/compliance-dashboard`)}
        >
          <Scale size={18} className="mr-2" /> Compliance
        </button>
      </div>
    </div>
  );
}
