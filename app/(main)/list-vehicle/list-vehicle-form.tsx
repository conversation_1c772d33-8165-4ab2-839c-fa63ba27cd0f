"use client";

import React from "react";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  CreditCard,
  FileText,
  ChevronDown,
  ChevronUp,
} from "lucide-react";
import { createVehicleAndRentalOrCoOwnershipListingDrizzle } from "@/drizzle-actions/listings";
import { getAllVehicleMakes, getVehicleModelsByMakeId, getVehicleVariantsByModelId } from "@/drizzle-actions/vehicle-domain";
import { getCities } from "@/drizzle-actions/community";
import ImageUploader, { UploadedImage } from "@/app/components/ImageUploader";
import VehicleDetailsSection from "./components/VehicleDetailsSection";
import OfferDetailSection from "./components/MonthlyRentalSection";
import { VehicleIntentEnum } from "./vehicle-intent";
import { useVehicleIntent } from "./vehicle-intent-context";
import { ListingTypeEnum } from "@/types/listings";
import GroupSelection from "./group-selection";
import { CompanyPurposeEnum } from "@/types/company";

import { GroupCreate, GroupCreationProps, GroupRoleEnum, GroupMembershipInvitationCreate } from "@/types/groups";
import { createGroup } from "@/drizzle-actions/groups";
import { db } from "@/db";

interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface ListVehicleFormProps {
  partyId: number;
}

export default function ListVehicleForm({ partyId }: ListVehicleFormProps) {
  const router = useRouter();
  const [vehicleImages, setVehicleImages] = useState<UploadedImage[]>([]);
  const [showTerms, setShowTerms] = useState(false);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Vehicle dropdown data
  const [makes, setMakes] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [variants, setVariants] = useState<any[]>([]);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingVariants, setLoadingVariants] = useState(false);
  
  // Cities data
  const [cities, setCities] = useState<City[]>([]);
  const [loadingCities, setLoadingCities] = useState(false);
  
  const { intent, onComplete, isInDrawer } = useVehicleIntent();

  const [formData, setFormData] = useState({
    make: "",
    model: "",
    year: "",
    color: "",
    mileage: "",
    condition: "used",
    listingType: intent == VehicleIntentEnum.RENTAL_INCOME ? ListingTypeEnum.ShortTermLeaseOut : intent == VehicleIntentEnum.CO_OWNERSHIP ? ListingTypeEnum.CoOwnershipSale : ListingTypeEnum.LongTermLeaseOut,
    description: "",
    fraction: 0,
    pricePerFraction: 0,
    monthlyRentalPrice: 0,
    termsAccepted: false,
    makeId: 0,
    modelId: 0,
    variantId: 0,
    vinNumber: "",
    vehicleRegistration: "",

  });

  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [showNewGroupForm, setShowNewGroupForm] = useState(false);
  const [newGroupData, setNewGroupData] = useState<GroupCreate>({
    name: '',
    description: '',
    cityId: 0,
    countryId: 1,
    InitialPurpose: CompanyPurposeEnum.OTHER,
    isManaged: false,
    createdBy: partyId,
  });
  const [memberInvitations, setMemberInvitations] = useState<GroupMembershipInvitationCreate[]>([]);

  // Load makes on component mount
  useEffect(() => {
    const loadMakes = async () => {
      setLoadingMakes(true);
      try {
        const makesData = await getAllVehicleMakes();
        setMakes(makesData);
      } catch (error) {
        console.error("Error loading makes:", error);
        setError("Failed to load vehicle makes");
      } finally {
        setLoadingMakes(false);
      }
    };

    const loadCities = async () => {
      setLoadingCities(true);
      try {
        const citiesData = await getCities();
        setCities(citiesData);
      } catch (error) {
        console.error("Error loading cities:", error);
        setError("Failed to load cities");
      } finally {
        setLoadingCities(false);
      }
    };

    loadMakes();
    loadCities();
  }, []);

  // Load models when make is selected
  useEffect(() => {
    if (formData.makeId > 0) {
      const loadModels = async () => {
        setLoadingModels(true);
        setModels([]);
        setVariants([]);
        setFormData(prev => ({ ...prev, modelId: 0, variantId: 0 }));

        try {
          const modelsData = await getVehicleModelsByMakeId(formData.makeId);
          setModels(modelsData);
        } catch (error) {
          console.error("Error loading models:", error);
          setError("Failed to load vehicle models");
        } finally {
          setLoadingModels(false);
        }
      };

      loadModels();
    }
  }, [formData.makeId]);

  // Load variants when model is selected
  useEffect(() => {
    if (formData.modelId > 0) {
      const loadVariants = async () => {
        setLoadingVariants(true);
        setVariants([]);
        setFormData(prev => ({ ...prev, variantId: 0 }));

        try {
          const variantsData = await getVehicleVariantsByModelId(formData.modelId);
          setVariants(variantsData);
        } catch (error) {
          console.error("Error loading variants:", error);
          setError("Failed to load vehicle variants");
        } finally {
          setLoadingVariants(false);
        }
      };

      loadVariants();
    }
  }, [formData.modelId]);

  const handleGroupChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewGroupData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;

    setFormData({
      ...formData,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (vehicleImages.length < 3) {
      setError("Please upload at least 3 images of your vehicle.");
      return;
    }

    // Validate vehicle selection
    if (formData.makeId === 0) {
      setError("Please select a vehicle make.");
      return;
    }

    if (formData.modelId === 0) {
      setError("Please select a vehicle model.");
      return;
    }

    if (formData.variantId === 0) {
      setError("Please select a vehicle variant.");
      return;
    }

    // Validate rental price
    if (intent == VehicleIntentEnum.RENTAL_INCOME) {
      if (!formData.monthlyRentalPrice) {
        setError("Please enter a valid monthly rental price.");
        return;
      }
    }

    if (intent == VehicleIntentEnum.CO_OWNERSHIP) {
      if (!formData.pricePerFraction && !formData.fraction) {
        setError("Please enter valid offer details.");
        return;
      }
    }

    if (intent == VehicleIntentEnum.ADD_TO_GROUP) {
      if (!selectedGroupId && !showNewGroupForm) {
        setError("Please select a group or create a new one.");
        return;
      }

      if (showNewGroupForm && !newGroupData.name.trim()) {
        setError("Please enter a group name.");
        return;
      }
    }

    // Validate VIN number
    if (!formData.vinNumber || formData.vinNumber.trim().length === 0) {
      setError("Please enter the vehicle VIN number.");
      return;
    }

    // Validate registration number
    if (!formData.vehicleRegistration || formData.vehicleRegistration.trim().length === 0) {
      setError("Please enter the vehicle registration number.");
      return;
    }

    try {
      setError("");
      setIsSubmitting(true);

      // Prepare form data for the drizzle action
      const listingFormData = {
        make: formData.make,
        model: formData.model,
        year: formData.year,
        color: formData.color,
        mileage: formData.mileage,
        condition: formData.condition,
        location: "", // Removed location from form
        description: formData.description,
        vinNumber: formData.vinNumber.trim(), // Use actual VIN from form
        vehicleRegistration: formData.vehicleRegistration.trim(),
        // Include the new ID fields for proper database relations
        makeId: formData.makeId,
        modelId: formData.modelId,
        variantId: formData.variantId,
        fraction: formData.fraction,
        pricePerFraction: formData.pricePerFraction,
        monthlyRentalPrice: formData.monthlyRentalPrice,
        listingType: intent == VehicleIntentEnum.RENTAL_INCOME ? ListingTypeEnum.ShortTermLeaseOut : intent == VehicleIntentEnum.CO_OWNERSHIP ? ListingTypeEnum.CoOwnershipSale : ListingTypeEnum.LongTermLeaseOut,
        audience: "CONSUMER",
        effectiveFrom: new Date().toISOString().split('T')[0],
        effectiveTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        vehicleImages: vehicleImages.map(img => img.s3Path), // Use S3 paths instead of preview URLs
      };

      console.log("Creating rental listing with price:", formData.pricePerFraction);

      // Create vehicle and listing using drizzle actions (rental-specific)
      let vehicleListingResult;
      if (intent === VehicleIntentEnum.RENTAL_INCOME || intent === VehicleIntentEnum.CO_OWNERSHIP) {
        vehicleListingResult = await createVehicleAndRentalOrCoOwnershipListingDrizzle(listingFormData, partyId);
      }

      console.log("Vehicle and listing created:", vehicleListingResult);

      // Add vehicle to group if ADD_TO_GROUP intent
      let finalGroupId = selectedGroupId;

      let groupResult;
      if (intent === VehicleIntentEnum.ADD_TO_GROUP && showNewGroupForm) {
        groupResult = await createGroupWithMembership();
        console.log("Group created:", groupResult);
        if (!groupResult) {
          setError("Failed to create group. Please try again.");
          return;
        }
        finalGroupId = groupResult.group.id;
      }

      // Handle completion based on whether we're in a drawer or not
      if (isInDrawer && onComplete) {
        onComplete();
      } else {
        router.push("/vehicle-dashboard");
      }
    } catch (error) {
      console.error("Error creating vehicle listing:", error);
      setError("Failed to create listing. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNewGroupChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewGroupData(prev => ({
      ...prev,
      [name]: name === 'cityId' ? (value ? parseInt(value) : 0) : value
    }));
  };

  const createGroupWithMembership = async () => {
    if (!showNewGroupForm || !newGroupData.name.trim()) {
      return null;
    }

    try {
      // Generate invitation tokens and expiry dates for member invitations

      const groupCreateResponse = await createGroup({
        groupCreate: {
          name: newGroupData.name,
          description: newGroupData.description,
          cityId: newGroupData.cityId,
          countryId: newGroupData.countryId,
          InitialPurpose: newGroupData.InitialPurpose,
          isManaged: newGroupData.isManaged,
          createdBy: newGroupData.createdBy
        },
        memberInvitations: memberInvitations,
        vehicleCreate: {
        
          model_id: formData.modelId,
          vin_number: formData.vinNumber,
          vehicle_registration: formData.vehicleRegistration,
          manufacturing_year: parseInt(formData.year),
          color: formData.color,
          is_active: true

        }

      });

      return groupCreateResponse;
    } catch (error) {
      console.error("Error creating group:", error);
      setError('Failed to create group. Please try again.');
      return null;
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!selectedGroupId && !showNewGroupForm) {
      errors.group = 'Please select an existing group or create a new one';
    }

    if (showNewGroupForm && !newGroupData.name.trim()) {
      errors.newGroup = 'Group name is required';
    }

    return errors;
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">List Vehicle</h1>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-4 space-y-4">
        {/* Vehicle Images */}
        <ImageUploader
          images={vehicleImages}
          onImagesChange={setVehicleImages}
          uploadPath="listingsMedia"
          minImages={3}
          maxImages={10}
          title="Vehicle Photos"
          onError={setError}
        />

        {/* Vehicle Details */}
        <VehicleDetailsSection
          formData={formData}
          makes={makes}
          models={models}
          variants={variants}
          loadingMakes={loadingMakes}
          loadingModels={loadingModels}
          loadingVariants={loadingVariants}
          setFormData={setFormData}
          handleChange={handleChange}
        />
        {intent === VehicleIntentEnum.ADD_TO_GROUP && (
          <GroupSelection
            selectedGroupId={selectedGroupId}
            setSelectedGroupId={setSelectedGroupId}
            showNewGroupForm={showNewGroupForm}
            setShowNewGroupForm={setShowNewGroupForm}
            newGroupData={newGroupData}
            handleNewGroupChange={handleNewGroupChange}
            memberInvitations={memberInvitations}
            setMemberInvitations={setMemberInvitations}
            cities={cities}
            loadingCities={loadingCities}
          />
        )}
        {/* Monthly Rental Pricing */}
        <OfferDetailSection
          formData={formData}
          handleChange={handleChange}
        />

        {/* Terms and Conditions */}
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <button
            type="button"
            className="w-full flex items-center justify-between"
            onClick={() => setShowTerms(!showTerms)}
          >
            <div className="flex items-center">
              <FileText size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Terms and Conditions
              </h3>
            </div>
            {showTerms ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          </button>

          {showTerms && (
            <div className="mt-3 p-3 bg-[#f9f9f9] rounded-lg max-h-40 overflow-y-auto">
              <p className="text-sm text-[#797879]">
                By listing your vehicle on Poolly, you agree to the following
                terms:
                <br />
                <br />
                1. You confirm that all information provided about the vehicle
                is accurate and complete.
                <br />
                <br />
                2. You have the legal right to sell or offer fractions of this
                vehicle in accordance with South African law.
                <br />
                <br />
                3. You agree to the standard Poolly co-ownership agreement that
                will govern the relationship between all co-owners in South
                Africa.
                <br />
                <br />
                4. Poolly will charge a 5% service fee on the total transaction
                value when fractions are sold in Rand (ZAR).
                <br />
                <br />
                5. You agree to maintain the vehicle in good condition according
                to South African roadworthiness standards until all fractions
                are sold.
              </p>
            </div>
          )}

          <div className="mt-3 flex items-start">
            <input
              type="checkbox"
              id="termsAccepted"
              name="termsAccepted"
              checked={formData.termsAccepted}
              onChange={(e) =>
                setFormData({ ...formData, termsAccepted: e.target.checked })
              }
              className="mt-1 h-4 w-4 rounded border-[#d6d9dd] text-[#009639] focus:ring-[#009639]"
              required
            />
            <label
              htmlFor="termsAccepted"
              className="ml-2 text-sm text-[#797879]"
            >
              I agree to the terms and conditions for listing my vehicle on
              Poolly
            </label>
          </div>
        </div>
      </form>

      {/* List Vehicle Button */}
      <div className="bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          type="submit"
          className="w-full bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-4 rounded-full text-xl font-semibold shadow-md flex justify-center items-center"
          onClick={handleSubmit}
          disabled={!formData.termsAccepted || isSubmitting}
        >
          {isSubmitting ? (
            <>
              <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin mr-2"></div>
              Creating Listing...
            </>
          ) : (
            "List Vehicle"
          )}
        </button>
      </div>
    </div>
  );
} 