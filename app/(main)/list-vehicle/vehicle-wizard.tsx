"use client";

import React from 'react';
import { VehicleIntentProvider, useVehicleIntent } from './vehicle-intent-context';
import IntentSelection from './intent-selection';
import ListVehicleForm from './list-vehicle-form';

interface VehicleWizardProps {
  partyId: number;
  groupId?: number;
  fleetId?: number;
  onComplete?: () => void;
  isInDrawer?: boolean;
}

function VehicleWizardContent({ partyId }: { partyId: number }) {
  const { intent } = useVehicleIntent();

  // Show intent selection if no intent is selected
  if (!intent) {
    return <IntentSelection />;
  }

  // Show the appropriate form based on intent
  return <ListVehicleForm partyId={partyId} />;
}

export default function VehicleWizard({ 
  partyId, 
  groupId, 
  fleetId, 
  onComplete,
  isInDrawer = false
}: VehicleWizardProps) {
  return (
    <VehicleIntentProvider 
      partyId={partyId} 
      groupId={groupId} 
      fleetId={fleetId}
      onComplete={onComplete}
      isInDrawer={isInDrawer}
    >
      <VehicleWizardContent partyId={partyId} />
    </VehicleIntentProvider>
  );
} 