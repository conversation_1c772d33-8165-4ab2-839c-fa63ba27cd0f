import { getUserAttributes } from "@/lib/serverUserAttributes";
import MissingParty from "@/components/missing-party";
import VehicleWizard from "./vehicle-wizard";


export default async function ListVehiclePage() {
  const attributes = await getUserAttributes();
  const { ["custom:db_id"]: dbId } = attributes || {};
  
  if (!dbId) return <MissingParty />;

  return <VehicleWizard partyId={+dbId} />;
}

