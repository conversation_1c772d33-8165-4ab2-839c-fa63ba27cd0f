"use client";

import React from 'react';
import { 
  DollarSign, 
  Users, 
  UserPlus, 
  Truck,
  ChevronRight,
  Car
} from 'lucide-react';
import {
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { useVehicleIntent, VehicleIntent } from './vehicle-intent-context';

interface IntentCard {
  intent: VehicleIntent;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
}

export default function IntentSelection() {
  const { setIntent, isInDrawer } = useVehicleIntent();

  const intentCards: IntentCard[] = [
    {
      intent: 'rental-income',
      title: 'List for Rental Income',
      description: 'Rent out your vehicle to earn income',
      icon: <DollarSign size={18} />,
      color: 'text-white',
      bgColor: 'bg-[#009639]'
    },
    {
      intent: 'co-ownership',
      title: 'Find Co-ownership Partners',
      description: 'Share ownership and costs with others',
      icon: <Users size={18} />,
      color: 'text-white',
      bgColor: 'bg-[#007A2F]'
    },
    {
      intent: 'add-to-group',
      title: 'Add to Group',
      description: 'Add vehicle to an existing group',
      icon: <UserPlus size={18} />,
      color: 'text-[#333333]',
      bgColor: 'bg-[#FFD700]'
    },
    {
      intent: 'add-to-fleet',
      title: 'Add to Fleet',
      description: 'Add to business fleet management',
      icon: <Truck size={18} />,
      color: 'text-white',
      bgColor: 'bg-[#009639]'
    }
  ];

  const handleIntentSelection = (intent: VehicleIntent) => {
    setIntent(intent);
  };

  // For drawer mode, use a different layout
  if (isInDrawer) {
    return (
      <div className="flex h-full flex-col">
        {/* Header */}
        <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
          <div className="flex items-center">
            <div>
              <SheetTitle className="text-xl font-bold text-white flex items-center">
                <Car size={24} className="mr-2" />
                Add Vehicle
              </SheetTitle>
              <SheetDescription className="text-sm text-green-100">
                Choose how you'd like to use your vehicle
              </SheetDescription>
            </div>
          </div>
        </SheetHeader>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-[#333333] mb-2">
              What would you like to do?
            </h2>
            <p className="text-[#797879] text-sm">
              Select the option that best describes your goal for adding this vehicle.
            </p>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4 mb-6">
            <div className="flex items-center mb-4">
              <Car size={20} className="text-[#009639] mr-2" />
              <h4 className="text-[#333333] font-semibold">
                Choose your vehicle journey
              </h4>
            </div>
            <div className="grid grid-cols-1 gap-3">
              {intentCards.map((card) => (
                <button
                  key={card.intent}
                  onClick={() => handleIntentSelection(card.intent)}
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                >
                  <div className={`${card.bgColor} w-10 h-10 rounded-full flex items-center justify-center mr-3`}>
                    <div className={card.color}>
                      {card.icon}
                    </div>
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      {card.title}
                    </p>
                    <p className="text-[#797879] text-xs">
                      {card.description}
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>
              ))}
            </div>
          </div>

          {/* Info Section */}
          <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 rounded-full bg-[#009639] mt-2 flex-shrink-0"></div>
              <div>
                <h4 className="font-medium text-[#333333] mb-1">Need Help Deciding?</h4>
                <p className="text-[#797879] text-sm leading-relaxed">
                  Don't worry! You can always change how you use your vehicle later. 
                  Start with the option that feels right for your current situation.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For page mode, use the original layout
  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-6 border-b border-[#007A2F]">
        <div className="flex items-center justify-center mb-2">
          <Car size={32} className="text-white mr-3" />
          <h1 className="text-2xl font-bold text-white">Add Vehicle</h1>
        </div>
        <p className="text-green-100 text-center text-sm">
          Choose how you'd like to use your vehicle
        </p>
      </div>

      {/* Intent Cards */}
      <div className="p-4">
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-2">
            What would you like to do?
          </h2>
          <p className="text-[#797879] text-sm">
            Select the option that best describes your goal for adding this vehicle.
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
          <div className="flex items-center mb-4">
            <Car size={20} className="text-[#009639] mr-2" />
            <h4 className="text-[#333333] font-semibold">
              Choose your vehicle journey
            </h4>
          </div>
          <div className="grid grid-cols-1 gap-3">
            {intentCards.map((card) => (
              <button
                key={card.intent}
                onClick={() => handleIntentSelection(card.intent)}
                className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
              >
                <div className={`${card.bgColor} w-10 h-10 rounded-full flex items-center justify-center mr-3`}>
                  <div className={card.color}>
                    {card.icon}
                  </div>
                </div>
                <div className="flex-1 text-left">
                  <p className="text-[#333333] font-medium text-sm">
                    {card.title}
                  </p>
                  <p className="text-[#797879] text-xs">
                    {card.description}
                  </p>
                </div>
                <ChevronRight size={16} className="text-[#009639]" />
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Info Section */}
      <div className="mx-4 mt-8 bg-white rounded-xl shadow-md p-4 border border-gray-100">
        <div className="flex items-start space-x-3">
          <div className="w-2 h-2 rounded-full bg-[#009639] mt-2 flex-shrink-0"></div>
          <div>
            <h4 className="font-medium text-[#333333] mb-1">Need Help Deciding?</h4>
            <p className="text-[#797879] text-sm leading-relaxed">
              Don't worry! You can always change how you use your vehicle later. 
              Start with the option that feels right for your current situation.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
} 