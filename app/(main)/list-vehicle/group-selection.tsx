"use client";

import React, { useState } from 'react';
import { Building, Plus, X, Users, Mail, UserPlus } from 'lucide-react';
import { useVehicleIntent } from './vehicle-intent-context';
import { GroupRequest, GroupRoleEnum, GroupMembershipInvitationCreate } from '@/types/groups';
import { company } from '@/drizzle/schema';

interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface MemberInvitation {
  firstName: string;
  lastName: string;
  email: string;
  role: GroupRoleEnum;
}

interface GroupSelectionProps {
  userGroups?: GroupRequest[];
  selectedGroupId: number | null;
  setSelectedGroupId: (id: number | null) => void;
  showNewGroupForm: boolean;
  setShowNewGroupForm: (show: boolean) => void;
  newGroupData: {
    name: string;
    description: string;
    cityId?: number;
  };
  handleNewGroupChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  memberInvitations: GroupMembershipInvitationCreate[];
  setMemberInvitations: (invitations: GroupMembershipInvitationCreate[]) => void;
  cities: City[];
  loadingCities: boolean;
}

export default function GroupSelection({
  userGroups,
  selectedGroupId,
  setSelectedGroupId,
  showNewGroupForm,
  setShowNewGroupForm,
  newGroupData,
  handleNewGroupChange,
  memberInvitations,
  setMemberInvitations,
  cities,
  loadingCities,
}: GroupSelectionProps) {

  const { intent } = useVehicleIntent();
  const [showMemberForm, setShowMemberForm] = useState(false);
  const [newMemberData, setNewMemberData] = useState<MemberInvitation>({
    firstName: '',
    lastName: '',
    email: '',
    role: GroupRoleEnum.MEMBER,
  });

  const handleAddMember = () => {
    if (newMemberData.firstName && newMemberData.lastName && newMemberData.email) {
      setMemberInvitations([...memberInvitations, newMemberData]);
      setNewMemberData({
        firstName: '',
        lastName: '',
        email: '',
        role: GroupRoleEnum.MEMBER,
      });
      setShowMemberForm(false);
    }
  };

  const handleRemoveMember = (index: number) => {
    setMemberInvitations(memberInvitations.filter((_, i) => i !== index));
  };

  const handleMemberChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewMemberData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  return (
    <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <div className="flex items-center mb-4">
        <Building size={20} className="text-[#009639] mr-2" />
        <h3 className="text-[#333333] font-medium">
          {intent === 'add-to-group' ? 'Select Group' : 'Select Fleet'}

        </h3>
      </div>

      {userGroups && userGroups?.length > 0 && !showNewGroupForm && (
        <div className="space-y-3 mb-4">
          <label className="block text-sm text-[#797879] mb-2">
            Choose existing {intent === 'add-to-group' ? 'group' : 'fleet'}:
          </label>
          {userGroups.map((group) => (
            <div
              key={group.id}
              className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                selectedGroupId === group.id
                  ? 'border-[#009639] bg-[#f8fff8]'
                  : 'border-[#d6d9dd] hover:border-[#009639]'
              }`}
              onClick={() => setSelectedGroupId(group.id)}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-[#333333]">{group.name}</h4>
                  {group.description && (
                    <p className="text-sm text-[#797879]">{group.description}</p>
                  )}
                  {/* <p className="text-xs text-[#009639]">
                    Your ownership: {(group.user_fraction * 100).toFixed(1)}%
                  </p> */}
                </div>
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedGroupId === group.id
                    ? 'border-[#009639] bg-[#009639]'
                    : 'border-[#d6d9dd]'
                }`}>
                  {selectedGroupId === group.id && (
                    <div className="w-2 h-2 bg-white rounded-full m-0.5"></div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="border-t border-[#f2f2f2] pt-4">
        {!showNewGroupForm ? (
          <button
            type="button"
            onClick={() => setShowNewGroupForm(true)}
            className="flex items-center text-[#009639] hover:text-[#007A2F] transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Create new {intent === 'add-to-group' ? 'group' : 'fleet'}
          </button>
        ) : (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-[#333333]">
                Create New {intent === 'add-to-group' ? 'Group' : 'Fleet'}
              </h4>
              <button
                type="button"
                onClick={() => setShowNewGroupForm(false)}
                className="text-[#797879] hover:text-[#333333]"
              >
                <X size={16} />
              </button>
            </div>
            
            <div>
              <label
                htmlFor="groupName"
                className="block text-sm text-[#797879] mb-1"
              >
                {intent === 'add-to-group' ? 'Group' : 'Fleet'} Name *
              </label>
              <input
                type="text"
                id="groupName"
                name="name"
                value={newGroupData.name}
                onChange={handleNewGroupChange}
                className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder={`Enter ${intent === 'add-to-group' ? 'group' : 'fleet'} name`}
                required={showNewGroupForm}
              />
            </div>
            
            <div>
              <label
                htmlFor="groupDescription"
                className="block text-sm text-[#797879] mb-1"
              >
                Description (Optional)
              </label>
              <textarea
                id="groupDescription"
                name="description"
                value={newGroupData.description}
                onChange={handleNewGroupChange}
                className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm min-h-[80px]"
                placeholder={`Describe this ${intent === 'add-to-group' ? 'group' : 'fleet'}...`}
              />
            </div>

            {/* City Selection */}
            <div>
              <label
                htmlFor="groupCity"
                className="block text-sm text-[#797879] mb-1"
              >
                City (Optional)
              </label>
              {loadingCities ? (
                <div className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] bg-gray-100 animate-pulse">
                  Loading cities...
                </div>
              ) : (
                <select
                  id="groupCity"
                  name="cityId"
                  value={newGroupData.cityId || ""}
                  onChange={handleNewGroupChange}
                  className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                >
                  <option value="">Select a city (optional)</option>
                  {cities.map((city) => (
                    <option key={city.id} value={city.id}>
                      {city.name}, {city.province}, {city.country}
                    </option>
                  ))}
                </select>
              )}
            </div>

            {/* Member Invitations Section */}
            <div className="border-t border-[#f2f2f2] pt-4 mt-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Users size={16} className="text-[#009639] mr-2" />
                  <h5 className="font-medium text-[#333333]">Invite Members</h5>
                </div>
                <button
                  type="button"
                  onClick={() => setShowMemberForm(true)}
                  className="flex items-center text-[#009639] hover:text-[#007A2F] transition-colors text-sm"
                >
                  <UserPlus size={14} className="mr-1" />
                  Add Member
                </button>
              </div>

              {/* Display invited members */}
              {memberInvitations.length > 0 && (
                <div className="space-y-2 mb-3">
                  {memberInvitations.map((member, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-[#f8fff8] rounded-lg border border-[#e6f7e6]">
                      <div className="flex items-center">
                        <Mail size={14} className="text-[#009639] mr-2" />
                        <div>
                          <span className="text-sm font-medium text-[#333333]">
                            {member.firstName} {member.lastName}
                          </span>
                          <div className="text-xs text-[#797879]">
                            {member.email} • {member.role}
                          </div>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => handleRemoveMember(index)}
                        className="text-[#797879] hover:text-[#333333]"
                      >
                        <X size={14} />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Add member form */}
              {showMemberForm && (
                <div className="space-y-3 p-3 bg-[#f9f9f9] rounded-lg border">
                  <div className="flex items-center justify-between">
                    <h6 className="font-medium text-[#333333]">Invite New Member</h6>
                    <button
                      type="button"
                      onClick={() => setShowMemberForm(false)}
                      className="text-[#797879] hover:text-[#333333]"
                    >
                      <X size={16} />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm text-[#797879] mb-1">
                        First Name *
                      </label>
                      <input
                        type="text"
                        name="firstName"
                        value={newMemberData.firstName}
                        onChange={handleMemberChange}
                        className="w-full px-3 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] text-sm"
                        placeholder="Enter first name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm text-[#797879] mb-1">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        name="lastName"
                        value={newMemberData.lastName}
                        onChange={handleMemberChange}
                        className="w-full px-3 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] text-sm"
                        placeholder="Enter last name"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm text-[#797879] mb-1">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={newMemberData.email}
                      onChange={handleMemberChange}
                      className="w-full px-3 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] text-sm"
                      placeholder="Enter email address"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm text-[#797879] mb-1">
                      Role
                    </label>
                    <select
                      name="role"
                      value={newMemberData.role}
                      onChange={handleMemberChange}
                      className="w-full px-3 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] text-sm"
                    >
                      <option value={GroupRoleEnum.MEMBER}>Member</option>
                      <option value={GroupRoleEnum.ADMIN}>Admin</option>
                    </select>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <button
                      type="button"
                      onClick={() => setShowMemberForm(false)}
                      className="px-3 py-1 text-sm text-[#797879] hover:text-[#333333] transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="button"
                      onClick={handleAddMember}
                      className="px-3 py-1 text-sm bg-[#009639] text-white rounded-lg hover:bg-[#007A2F] transition-colors"
                    >
                      Add Member
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 