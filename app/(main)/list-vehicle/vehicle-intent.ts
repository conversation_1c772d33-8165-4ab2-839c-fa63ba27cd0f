export type VehicleIntent = 
  | 'rental-income'
  | 'co-ownership' 
  | 'add-to-group'
  | 'add-to-fleet';

export enum VehicleIntentEnum {
  RENTAL_INCOME = 'rental-income',
  CO_OWNERSHIP = 'co-ownership',
  ADD_TO_GROUP = 'add-to-group',
  ADD_TO_FLEET = 'add-to-fleet'
}

// Helper function to validate if a string is a valid VehicleIntent
export function isValidVehicleIntent(intent: string): intent is VehicleIntent {
  return Object.values(VehicleIntentEnum).includes(intent as VehicleIntentEnum);
} 