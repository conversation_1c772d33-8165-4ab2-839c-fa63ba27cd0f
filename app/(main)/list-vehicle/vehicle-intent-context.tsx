"use client";

import React, { createContext, useContext, useState, ReactNode } from 'react';

export type VehicleIntent = 
  | 'rental-income'
  | 'co-ownership' 
  | 'add-to-group'
  | 'add-to-fleet';

export const enum VehicleIntentEnum {
  RENTAL_INCOME = 'rental-income',
  CO_OWNERSHIP = 'co-ownership',
  ADD_TO_GROUP = 'add-to-group',
  ADD_TO_FLEET = 'add-to-fleet'
}

interface VehicleIntentContextType {
  intent: VehicleIntent | null;
  setIntent: (intent: VehicleIntent | null) => void;
  partyId: number;
  groupId?: number;
  fleetId?: number;
  onComplete?: () => void;
  isInDrawer?: boolean;
}

const VehicleIntentContext = createContext<VehicleIntentContextType | undefined>(undefined);

interface VehicleIntentProviderProps {
  children: ReactNode;
  partyId: number;
  groupId?: number;
  fleetId?: number;
  onComplete?: () => void;
  isInDrawer?: boolean;
}

export function VehicleIntentProvider({ 
  children, 
  partyId, 
  groupId, 
  fleetId,
  onComplete,
  isInDrawer = false
}: VehicleIntentProviderProps) {
  const [intent, setIntent] = useState<VehicleIntent | null>(null);

  return (
    <VehicleIntentContext.Provider value={{
      intent,
      setIntent,
      partyId,
      groupId,
      fleetId,
      onComplete,
      isInDrawer
    }}>
      {children}
    </VehicleIntentContext.Provider>
  );
}

export function useVehicleIntent() {
  const context = useContext(VehicleIntentContext);
  if (context === undefined) {
    throw new Error('useVehicleIntent must be used within a VehicleIntentProvider');
  }
  return context;
} 