import React from "react";

interface VehicleDetailsSectionProps {
  formData: {
    makeId: number;
    make: string;
    modelId: number;
    model: string;
    variantId: number;
    year: string;
    color: string;
    mileage: string;
    condition: string;
    description: string;
    vinNumber: string;
    vehicleRegistration: string;
  };
  makes: any[];
  models: any[];
  variants: any[];
  loadingMakes: boolean;
  loadingModels: boolean;
  loadingVariants: boolean;
  setFormData: (formData: any) => void;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export default function VehicleDetailsSection({
  formData,
  makes,
  models,
  variants,
  loadingMakes,
  loadingModels,
  loadingVariants,
  setFormData,
  handleChange,
}: VehicleDetailsSectionProps) {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <h3 className="text-[#333333] font-medium mb-4">Vehicle Details</h3>

      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div>
            <label
              htmlFor="makeId"
              className="block text-sm text-[#797879] mb-1"
            >
              Make
            </label>
            <select
              id="makeId"
              name="makeId"
              value={formData.makeId}
              onChange={(e) => {
                const makeId = parseInt(e.target.value);
                const selectedMake = makes.find(make => make.id === makeId);
                setFormData({
                  ...formData,
                  makeId: makeId,
                  make: selectedMake?.name || "",
                  modelId: 0,
                  model: "",
                  variantId: 0,
                  year: ""
                });
              }}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
              required
              disabled={loadingMakes}
            >
              <option value={0}>
                {loadingMakes ? "Loading makes..." : "Select Make"}
              </option>
              {makes.map(make => (
                <option key={make.id} value={make.id}>
                  {make.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label
              htmlFor="modelId"
              className="block text-sm text-[#797879] mb-1"
            >
              Model
            </label>
            <select
              id="modelId"
              name="modelId"
              value={formData.modelId}
              onChange={(e) => {
                const modelId = parseInt(e.target.value);
                const selectedModel = models.find(model => model.id === modelId);
                setFormData({
                  ...formData,
                  modelId: modelId,
                  model: selectedModel?.model || "",
                  variantId: 0,
                  year: ""
                });
              }}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
              required
              disabled={loadingModels || formData.makeId === 0}
            >
              <option value={0}>
                {loadingModels ? "Loading models..." : 
                 formData.makeId === 0 ? "Select Make First" : "Select Model"}
              </option>
              {models.map(model => (
                <option key={model.id} value={model.id}>
                  {model.model}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label
              htmlFor="variantId"
              className="block text-sm text-[#797879] mb-1"
            >
              Variant
            </label>
            <select
              id="variantId"
              name="variantId"
              value={formData.variantId}
              onChange={(e) => {
                const variantId = parseInt(e.target.value);
                const selectedVariant = variants.find(variant => variant.id === variantId);
                setFormData({
                  ...formData,
                  variantId: variantId,
                  year: selectedVariant?.year?.toString() || ""
                });
              }}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
              required
              disabled={loadingVariants || formData.modelId === 0}
            >
              <option value={0}>
                {loadingVariants ? "Loading variants..." : 
                 formData.modelId === 0 ? "Select Model First" : "Select Variant"}
              </option>
              {variants.map(variant => (
                <option key={variant.id} value={variant.id}>
                  {variant.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label
              htmlFor="year"
              className="block text-sm text-[#797879] mb-1"
            >
              Year
            </label>
            <input
              type="text"
              id="year"
              name="year"
              value={formData.year}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-gray-100"
              placeholder="Auto-filled from variant"
              readOnly
              required
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label
              htmlFor="color"
              className="block text-sm text-[#797879] mb-1"
            >
              Color
            </label>
            <input
              type="text"
              id="color"
              name="color"
              value={formData.color}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              placeholder="e.g. Blue"
              required
            />
          </div>
          <div>
            <label
              htmlFor="mileage"
              className="block text-sm text-[#797879] mb-1"
            >
              Mileage
            </label>
            <input
              type="text"
              id="mileage"
              name="mileage"
              value={formData.mileage}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              placeholder="e.g. 25,000 km"
              required
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="condition"
            className="block text-sm text-[#797879] mb-1"
          >
            Condition
          </label>
          <select
            id="condition"
            name="condition"
            value={formData.condition}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm bg-white"
            required
          >
            <option value="new">New</option>
            <option value="used">Used</option>
          </select>
        </div>

        <div>
          <label
            htmlFor="description"
            className="block text-sm text-[#797879] mb-1"
          >
            Description
          </label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm min-h-[100px]"
            placeholder="Describe your vehicle..."
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label
              htmlFor="vinNumber"
              className="block text-sm text-[#797879] mb-1"
            >
              VIN Number
            </label>
            <input
              type="text"
              id="vinNumber"
              name="vinNumber"
              value={formData.vinNumber}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              placeholder="e.g. 1HGBH41JXMN109186"
              required
            />
          </div>
          <div>
            <label
              htmlFor="vehicleRegistration"
              className="block text-sm text-[#797879] mb-1"
            >
              Registration Number
            </label>
            <input
              type="text"
              id="vehicleRegistration"
              name="vehicleRegistration"
              value={formData.vehicleRegistration}
              onChange={handleChange}
              className="w-full px-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
              placeholder="e.g. ABC 123 GP"
              required
            />
          </div>
        </div>
      </div>
    </div>
  );
} 