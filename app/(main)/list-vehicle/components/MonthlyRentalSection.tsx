import React from "react";
import { CreditCard } from "lucide-react";
import { useVehicleIntent } from "../vehicle-intent-context";
import { VehicleIntentEnum } from "../vehicle-intent-context";

interface OfferDetailSectionProps {
  formData: {
    pricePerFraction: number;
    fraction: number;
    monthlyRentalPrice: number;
  };
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
}

export default function OfferDetailSection({
  formData,
  handleChange,
}: OfferDetailSectionProps) {
  const { intent } = useVehicleIntent();
 
  if (intent == VehicleIntentEnum.RENTAL_INCOME) {


    return (
      <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
        <div className="flex items-center mb-4">
          <CreditCard size={20} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Monthly Rental Price</h3>
        </div>

        <div className="space-y-3">
          <div>
            <label
              htmlFor="monthlyRentalPrice"
              className="block text-sm text-[#797879] mb-1"
            >
              Monthly Rate
            </label>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-[#797879] font-medium">R</span>
              </div>
              <input
                type="number"
                id="monthlyRentalPrice"
                name="monthlyRentalPrice"
                value={formData.monthlyRentalPrice}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="Monthly rental price in Rand"
                min="0"
                step="100"
                required
              />
            </div>
            <p className="text-xs text-[#797879] mt-2">
              Set your monthly rental rate. This amount will be charged to renters per month.
            </p>
          </div>
        </div>
      </div>
    );
  }
  if (intent == VehicleIntentEnum.CO_OWNERSHIP) {
    return (
      <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
        <div className="flex items-center mb-4">
          <CreditCard size={20} className="text-[#009639] mr-2" />
          <h3 className="text-[#333333] font-medium">Offer Details</h3>
        </div>

        <div className="space-y-3">
          <div>
            <label
              htmlFor="fraction"
              className="block text-sm text-[#797879] mb-1"
            >
              Fraction Size
            </label>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-[#797879] font-medium">%</span>
              </div>
              <input
                type="number"
                id="fraction"
                name="fraction"
                value={formData.fraction}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="Percentage of vehicle ownership"
                min="1"
                max="100"
                step="1"
                required
              />
            </div>
            <p className="text-xs text-[#797879] mt-2">
              Specify the percentage of vehicle ownership you're offering for sale.
            </p>
          </div>

          <div>
            <label
              htmlFor="pricePerFraction"
              className="block text-sm text-[#797879] mb-1"
            >
              Price
            </label>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
                <span className="text-[#797879] font-medium">R</span>
              </div>
              <input
                type="number"
                id="pricePerFraction"
                name="pricePerFraction"
                value={formData.pricePerFraction}
                onChange={handleChange}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-[#d6d9dd] focus:outline-none focus:border-[#009639] shadow-sm"
                placeholder="Price for the offered fraction"
                min="0"
                step="100"
                required
              />
            </div>
            <p className="text-xs text-[#797879] mt-2">
              Set the price for your offered fraction of vehicle ownership.
            </p>
          </div>
        </div>
      </div>
    );
  }
  return null;
} 