"use client";

import { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, UserPlus, Trash2, Edit, Mail, Phone, Crown, Users, X, Clock, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { GroupRoleEnum, InvitationStatusEnum, GroupMembershipInvitationCreate } from "@/types/groups";
import { createGroupInvitation, cancelGroupInvitation } from "@/actions/member-management";

interface ContactPoint {
  partyId: number;
  contactValue: string;
  isPrimary: boolean;
  contactPointTypeId: number;
}

interface Member {
  id: number;
  partyId: number;
  firstName: string;
  lastName: string;
  name: string;
  role: GroupRoleEnum;
  joinedAt: string;
  isActive: boolean;
  contacts: ContactPoint[];
}

interface Invitation {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  role: GroupRoleEnum;
  status: InvitationStatusEnum;
  sentAt: string;
  expiresAt: string;
  invitedBy: string;
}

interface MemberManagementData {
  success: boolean;
  error?: string;
  isAdmin: boolean;
  members: Member[];
  invitations: Invitation[];
}

interface MemberManagementScreenProps {
  groupId: number;
  managementData: MemberManagementData;
}

export default function MemberManagementScreen({
  groupId,
  managementData,
}: MemberManagementScreenProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [newInvitation, setNewInvitation] = useState<GroupMembershipInvitationCreate>({
    firstName: "",
    lastName: "",
    email: "",
    role: GroupRoleEnum.MEMBER
  });

  // Show access denied if not admin
  if (!managementData.success || !managementData.isAdmin) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Member Management</h1>
        </div>
        
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <AlertCircle size={48} className="text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-[#333333] mb-2">Access Denied</h2>
            <p className="text-[#797879] mb-4">
              {managementData.error || "You need admin permissions to access member management."}
            </p>
            <Button 
              onClick={() => router.back()}
              className="bg-[#009639] hover:bg-[#007A2F] text-white"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const { members, invitations } = managementData;

  const handleInviteMember = () => {
    setShowInviteModal(true);
  };

  const handleCreateInvitation = () => {
    if (!newInvitation.firstName.trim() || !newInvitation.lastName.trim() || !newInvitation.email.trim()) {
      alert("Please fill in all fields");
      return;
    }

    startTransition(async () => {
      try {
        const result = await createGroupInvitation(groupId, newInvitation);
        if (result.success) {
          setShowInviteModal(false);
          setNewInvitation({
            firstName: "",
            lastName: "",
            email: "",
            role: GroupRoleEnum.MEMBER
          });
          // Refresh the page to show updated data
          router.refresh();
        } else {
          alert(result.error || "Failed to create invitation");
        }
      } catch (error) {
        console.error("Error creating invitation:", error);
        alert("Failed to create invitation");
      }
    });
  };

  const handleCancelInvitation = (invitationId: number) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    startTransition(async () => {
      try {
        const result = await cancelGroupInvitation(invitationId);
        if (result.success) {
          // Refresh the page to show updated data
          router.refresh();
        } else {
          alert(result.error || "Failed to cancel invitation");
        }
      } catch (error) {
        console.error("Error cancelling invitation:", error);
        alert("Failed to cancel invitation");
      }
    });
  };

  const getRoleIcon = (role: GroupRoleEnum) => {
    switch (role) {
      case GroupRoleEnum.ADMIN: return <Crown size={14} className="text-yellow-500" />;
      default: return <Users size={14} className="text-gray-500" />;
    }
  };

  const getRoleBadge = (role: GroupRoleEnum) => {
    switch (role) {
      case GroupRoleEnum.ADMIN: return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadge = (status: InvitationStatusEnum) => {
    switch (status) {
      case InvitationStatusEnum.SENT: return 'bg-blue-100 text-blue-800';
      case InvitationStatusEnum.PENDING: return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getContactDisplay = (contacts: ContactPoint[]) => {
    const emailContact = contacts.find(c => c.contactPointTypeId === 1); // Assuming 1 is email
    const phoneContact = contacts.find(c => c.contactPointTypeId === 2); // Assuming 2 is phone
    
    return {
      email: emailContact?.contactValue,
      phone: phoneContact?.contactValue
    };
  };

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Member Management</h1>
        </div>
        <Button
          onClick={handleInviteMember}
          className="bg-white text-[#009639] hover:bg-gray-100 flex items-center gap-2"
          disabled={isPending}
        >
          <UserPlus size={20} />
          Invite Member
        </Button>
      </div>

      {/* Summary */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-[#333333]">Group Overview</h2>
            <Badge className="bg-blue-100 text-blue-800">
              {members.length} Members
            </Badge>
          </div>
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{members.length}</p>
              <p className="text-xs text-[#797879]">Active Members</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{members.filter(m => m.role === GroupRoleEnum.ADMIN).length}</p>
              <p className="text-xs text-[#797879]">Admins</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{invitations.length}</p>
              <p className="text-xs text-[#797879]">Pending Invites</p>
            </div>
          </div>
        </div>
      </div>

      {/* Current Members */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
          <div className="p-4 border-b border-gray-100">
            <h3 className="font-semibold text-[#333333]">Current Members ({members.length})</h3>
          </div>
          
          {members.map((member, index) => {
            const contact = getContactDisplay(member.contacts);
            
            return (
              <div
                key={member.id}
                className={`p-4 ${
                  index < members.length - 1 ? "border-b border-[#f2f2f2]" : ""
                }`}
              >
                <div className="flex items-center mb-3">
                  <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                    <span className="text-[#009639] font-medium text-lg">
                      {member.firstName.charAt(0)}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="text-[#333333] font-semibold">
                        {member.firstName} {member.lastName}
                      </h3>
                      {getRoleIcon(member.role)}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getRoleBadge(member.role)}>
                        {member.role.toLowerCase()}
                      </Badge>
                      <span className="text-xs text-[#797879]">
                        Joined {new Date(member.joinedAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Contact Info */}
                {(contact.email || contact.phone) && (
                  <div className="flex items-center gap-4 text-sm text-[#797879] mb-3">
                    {contact.email && (
                      <div className="flex items-center gap-1">
                        <Mail size={14} />
                        <span>{contact.email}</span>
                      </div>
                    )}
                    {contact.phone && (
                      <div className="flex items-center gap-1">
                        <Phone size={14} />
                        <span>{contact.phone}</span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Pending Invitations */}
      {invitations.length > 0 && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md overflow-hidden mb-4 border border-gray-100">
            <div className="p-4 border-b border-gray-100">
              <h3 className="font-semibold text-[#333333]">Pending Invitations ({invitations.length})</h3>
            </div>
            
            {invitations.map((invitation, index) => (
              <div
                key={invitation.id}
                className={`p-4 ${
                  index < invitations.length - 1 ? "border-b border-[#f2f2f2]" : ""
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#f0f0f0] rounded-full flex items-center justify-center mr-3 shadow-sm">
                      <span className="text-[#666666] font-medium text-lg">
                        {invitation.firstName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h3 className="text-[#333333] font-semibold">
                          {invitation.firstName} {invitation.lastName}
                        </h3>
                        <Badge className={getStatusBadge(invitation.status)}>
                          {invitation.status.toLowerCase()}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-[#797879]">
                        <div className="flex items-center gap-1">
                          <Mail size={14} />
                          <span>{invitation.email}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={14} />
                          <span>Sent {new Date(invitation.sentAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleCancelInvitation(invitation.id)}
                    disabled={isPending}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <X size={16} className="mr-1" />
                    Cancel
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Invite Member Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-5/6 max-w-md shadow-lg border border-gray-100">
            <h3 className="text-[#333333] font-semibold mb-4">
              Invite New Member
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-[#797879] text-sm mb-1">
                  First Name *
                </label>
                <input
                  type="text"
                  value={newInvitation.firstName}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, firstName: e.target.value }))}
                  className="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  placeholder="Enter first name"
                />
              </div>

              <div>
                <label className="block text-[#797879] text-sm mb-1">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={newInvitation.lastName}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, lastName: e.target.value }))}
                  className="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  placeholder="Enter last name"
                />
              </div>

              <div>
                <label className="block text-[#797879] text-sm mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={newInvitation.email}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <label className="block text-[#797879] text-sm mb-1">
                  Role
                </label>
                <select
                  value={newInvitation.role}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, role: e.target.value as GroupRoleEnum }))}
                  className="w-full px-4 py-3 rounded-lg border border-gray-200 focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                >
                  <option value={GroupRoleEnum.MEMBER}>Member</option>
                  <option value={GroupRoleEnum.ADMIN}>Admin</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <Button
                variant="outline"
                className="flex-1"
                onClick={() => setShowInviteModal(false)}
                disabled={isPending}
              >
                Cancel
              </Button>
              <Button
                className="flex-1 bg-[#009639] hover:bg-[#007A2F] text-white"
                onClick={handleCreateInvitation}
                disabled={isPending}
              >
                {isPending ? "Sending..." : "Send Invitation"}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
