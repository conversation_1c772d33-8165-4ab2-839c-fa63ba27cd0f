"use server";
import MemberManagementScreen from "./member-management";
import { getGroupMemberManagementData } from "@/actions/member-management";

export default async function MemberManagement({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const groupId = +id;
  
  // Get member management data with admin access check
  const managementData = await getGroupMemberManagementData(groupId);

  return (
    <MemberManagementScreen
      groupId={groupId}
      managementData={managementData}
    />
  );
}
