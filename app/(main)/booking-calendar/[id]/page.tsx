"use server";
import { deprecated_getVehicleBookingsWithDetails, deprecated_addBookingDrizzle } from "@/drizzle-actions/bookings";
import { getVehicleByIdDrizzle } from "@/drizzle-actions/vehicle-dashboard";
import BookingCalendarScreen from "./booking-calender";
import MissingParty from "@/components/missing-party";
import { getUserAttributes } from "@/lib/serverUserAttributes";

export default async function BookingCalendar({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const attributes = await getUserAttributes();

  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  if (!dbId || !externalId) return <MissingParty />;

  // Use drizzle for all operations
  const vehicleId = +id;
  const bookings = await deprecated_getVehicleBookingsWithDetails(vehicleId);
  const vehicle = await getVehicleByIdDrizzle(vehicleId);
  
  if (!vehicle) {
    return <p>Vehicle not found.</p>;
  }

  return (
    <BookingCalendarScreen
      bookingsx={bookings}
      vehicle={vehicle}
      action={deprecated_addBookingDrizzle} // Now using drizzle action
      party_id={+dbId}
    />
  );
}
