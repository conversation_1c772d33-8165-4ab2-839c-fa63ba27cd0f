import webpush from 'web-push';

if (!process.env.VAPID_EMAIL || !process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || !process.env.VAPID_PRIVATE_KEY) {
  console.warn('VAPID environment variables not found. Push notifications will not work.');
} else {
  webpush.setVapidDetails(
    process.env.VAPID_EMAIL,
    process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  );
}

export { webpush };

export interface PoollyNotificationPayload {
  title: string;
  body: string;
  type: 'booking' | 'group' | 'maintenance' | 'financial' | 'compliance' | 'general';
  icon?: string;
  badge?: string;
  tag?: string;
  url?: string;
  groupId?: string;
  vehicleId?: string;
  bookingId?: string;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  data?: Record<string, any>;
}

export interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}

export const NOTIFICATION_TYPES = {
  BOOKING: 'booking',
  GROUP: 'group', 
  MAINTENANCE: 'maintenance',
  FINANCIAL: 'financial',
  COMPLIANCE: 'compliance',
  GENERAL: 'general'
} as const;

export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];

// Default notification configurations
export const NOTIFICATION_DEFAULTS = {
  [NOTIFICATION_TYPES.BOOKING]: {
    icon: '/icon-192x192.png',
    badge: '/booking-badge.png',
    requireInteraction: true,
    vibrate: [200, 100, 200]
  },
  [NOTIFICATION_TYPES.GROUP]: {
    icon: '/icon-192x192.png', 
    badge: '/group-badge.png',
    requireInteraction: false,
    vibrate: [100]
  },
  [NOTIFICATION_TYPES.MAINTENANCE]: {
    icon: '/icon-192x192.png',
    badge: '/maintenance-badge.png', 
    requireInteraction: true,
    vibrate: [300, 100, 300]
  },
  [NOTIFICATION_TYPES.FINANCIAL]: {
    icon: '/icon-192x192.png',
    badge: '/financial-badge.png',
    requireInteraction: true,
    vibrate: [200, 100, 200, 100, 200]
  },
  [NOTIFICATION_TYPES.COMPLIANCE]: {
    icon: '/icon-192x192.png',
    badge: '/compliance-badge.png',
    requireInteraction: true,
    vibrate: [400]
  },
  [NOTIFICATION_TYPES.GENERAL]: {
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png',
    requireInteraction: false,
    vibrate: [100]
  }
} as const; 