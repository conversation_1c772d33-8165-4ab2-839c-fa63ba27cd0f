"use server";

import { SQSClient, SendMessageCommand } from "@aws-sdk/client-sqs";
import { fetchAuthSession } from "aws-amplify/auth/server";
import outputs from "@/amplify_outputs.json";
import { runWithAmplifyServerContext } from "./amplifyServerUtils";
import { cookies } from "next/headers";

const REGION = outputs.auth.aws_region;

async function _sendMessageToQueue(
  cookiesSnapshot: any,
  QUEUE_URL: string | null,
  messageBody: Record<string, any>
) {
  return await runWithAmplifyServerContext({
    nextServerContext: { cookies: () => cookiesSnapshot },
    operation: async (contextSpec) => {
      if (!QUEUE_URL) throw new Error("QUEUE_URL not set in environment");

      try {
        const session = await fetchAuthSession(contextSpec);
        if (!session.credentials) {
          throw new Error(
            "No credentials available. User may not be authenticated."
          );
        }

        const sqsClient = new SQSClient({
          credentials: session.credentials,
          region: REGION,
        });

        const command = new SendMessageCommand({
          QueueUrl: QUEUE_URL,
          MessageBody: JSON.stringify(messageBody),
        });

        const response = await sqsClient.send(command);
        return response;
      } catch (error) {
        console.error("Failed to send message to SQS:", error);
        throw error;
      }
    },
  });
}

export async function sendMessageToQueue(
  QUEUE_URL: string | null,
  messageBody: Record<string, any>
) {
  const cookiesSnapshot = await cookies();
  _sendMessageToQueue(cookiesSnapshot, QUEUE_URL, messageBody);
}
