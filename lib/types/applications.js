"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FRACTIONAL_DOCUMENT_TYPES = exports.RENTAL_DOCUMENT_TYPES = exports.EHAILING_DOCUMENT_TYPES = exports.ApplicationStatsResponseSchema = exports.ApplicationsListResponseSchema = exports.ApplicationResponseSchema = exports.ApplicationStatsSchema = exports.UpdateDocumentStatusSchema = exports.MakeDecisionSchema = exports.FractionalApplicationFormSchema = exports.RentalApplicationFormSchema = exports.EhailingApplicationFormSchema = exports.ApplicationWithDetailsSchema = exports.EnhancedApplicationDecisionSchema = exports.EnhancedApplicationDocumentSchema = exports.EnhancedApplicantSchema = exports.CatalogDetailsSchema = exports.VehicleDetailsSchema = exports.EnhancedApplicationListingSchema = exports.ApplicationDecisionSchema = exports.ApplicationListingSchema = exports.ApplicantSchema = exports.ApplicationDocumentSchema = exports.CreateApplicationSchema = exports.DocumentUploadSchema = exports.ApplicationDetailsSchema = exports.RentalFractionalDetailsSchema = exports.ApplicantPreferencesSchema = exports.EhailingExperienceSchema = exports.ListingTypeEnum = exports.DocumentStatusEnum = exports.ApplicationStatusEnum = void 0;
const zod_1 = require("zod");
// Base application status enum
exports.ApplicationStatusEnum = zod_1.z.enum([
    "pending",
    "under_review",
    "approved",
    "rejected",
    "withdrawn",
]);
// Document status enum
exports.DocumentStatusEnum = zod_1.z.enum([
    "pending",
    "uploaded",
    "verified",
    "rejected",
    "superseded",
]);
// Listing type enum for applications
exports.ListingTypeEnum = zod_1.z.enum([
    "rental",
    "fractional",
    "ehailing-platform",
]);
// E-hailing experience schema
exports.EhailingExperienceSchema = zod_1.z.object({
    hasEhailingExperience: zod_1.z.boolean(),
    ehailingCompany: zod_1.z.string().optional(),
    ehailingProfileNumber: zod_1.z.string().optional(),
    ehailingWorkType: zod_1.z.enum(["full-time", "part-time", "weekend"]).optional(),
    drivingExperienceYears: zod_1.z.string().optional(),
    arrangementRequested: zod_1.z.boolean().optional(),
});
// Applicant preferences for rental/fractional
exports.ApplicantPreferencesSchema = zod_1.z.object({
    minAge: zod_1.z.number().min(18).max(100).optional(),
    drivingExperienceYears: zod_1.z.string().optional(),
    gender: zod_1.z.enum(["male", "female", "any"]).optional(),
});
// Rental/Fractional application details
exports.RentalFractionalDetailsSchema = zod_1.z.object({
    purpose: zod_1.z.string().optional(),
    applicantPreferences: exports.ApplicantPreferencesSchema.optional(),
});
// Combined application details schema
exports.ApplicationDetailsSchema = zod_1.z.union([
    // E-hailing application
    exports.EhailingExperienceSchema,
    // Rental/Fractional application
    exports.RentalFractionalDetailsSchema,
    // Generic object for flexibility
    zod_1.z.record(zod_1.z.any()),
]);
// Document upload schema
exports.DocumentUploadSchema = zod_1.z.object({
    documentType: zod_1.z.string().min(1, "Document type is required"),
    documentUrl: zod_1.z.string().url("Valid document URL is required"),
});
// Application creation schema
exports.CreateApplicationSchema = zod_1.z.object({
    listingId: zod_1.z.number().positive("Valid listing ID is required"),
    applicationDetails: exports.ApplicationDetailsSchema,
});
// Document with status
exports.ApplicationDocumentSchema = zod_1.z.object({
    id: zod_1.z.number(),
    documentType: zod_1.z.string(),
    documentUrl: zod_1.z.string(),
    uploadedAt: zod_1.z.string(),
    status: exports.DocumentStatusEnum.optional(),
});
// Applicant information
exports.ApplicantSchema = zod_1.z.object({
    id: zod_1.z.number(),
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    email: zod_1.z.string().email(),
    phone: zod_1.z.string().optional(),
});
// Listing information for applications
exports.ApplicationListingSchema = zod_1.z.object({
    id: zod_1.z.number(),
    listingType: exports.ListingTypeEnum,
    listingDetails: zod_1.z.record(zod_1.z.any()),
});
// Application decision
exports.ApplicationDecisionSchema = zod_1.z.object({
    decision: exports.ApplicationStatusEnum,
    reason: zod_1.z.string().optional(),
    decisionAt: zod_1.z.string(),
    reviewerName: zod_1.z.string().optional(),
});
// Enhanced listing schema for applications
exports.EnhancedApplicationListingSchema = zod_1.z.object({
    id: zod_1.z.number(),
    listingType: exports.ListingTypeEnum,
    listingDetails: zod_1.z.record(zod_1.z.any()),
    sourceType: zod_1.z.string().optional(),
    sourceId: zod_1.z.number().optional(),
});
// Vehicle details schema
exports.VehicleDetailsSchema = zod_1.z.object({
    make: zod_1.z.string(),
    model: zod_1.z.string(),
    year: zod_1.z.number(),
    registration: zod_1.z.string().optional(),
});
// Catalog details schema
exports.CatalogDetailsSchema = zod_1.z.object({
    make: zod_1.z.string(),
    model: zod_1.z.string(),
    year: zod_1.z.number(),
    category: zod_1.z.string(),
    weeklyFeeTarget: zod_1.z.number().optional(),
});
// Enhanced applicant schema
exports.EnhancedApplicantSchema = zod_1.z.object({
    id: zod_1.z.number(),
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    email: zod_1.z.string().email(),
    phone: zod_1.z.string().optional(),
});
// Enhanced document schema
exports.EnhancedApplicationDocumentSchema = zod_1.z.object({
    id: zod_1.z.number(),
    documentType: zod_1.z.string(),
    documentUrl: zod_1.z.string(),
    uploadedAt: zod_1.z.string(),
    status: zod_1.z.string().optional(),
    statusAt: zod_1.z.string().optional(),
});
// Enhanced application decision schema
exports.EnhancedApplicationDecisionSchema = zod_1.z.object({
    decision: zod_1.z.string(),
    reason: zod_1.z.string().optional(),
    decisionAt: zod_1.z.string(),
    reviewerName: zod_1.z.string().optional(),
});
// Full application with all related data
exports.ApplicationWithDetailsSchema = zod_1.z.object({
    id: zod_1.z.number(),
    applicantId: zod_1.z.number(),
    listingId: zod_1.z.number(),
    applicationDetails: zod_1.z.record(zod_1.z.any()),
    createdAt: zod_1.z.string(),
    // Related data
    applicant: exports.EnhancedApplicantSchema,
    listing: exports.EnhancedApplicationListingSchema,
    // Optional vehicle/catalog details
    vehicle: exports.VehicleDetailsSchema.optional(),
    catalog: exports.CatalogDetailsSchema.optional(),
    documents: zod_1.z.array(exports.EnhancedApplicationDocumentSchema),
    latestDecision: exports.EnhancedApplicationDecisionSchema.optional(),
});
// Application form data for different types
exports.EhailingApplicationFormSchema = zod_1.z.object({
    listingId: zod_1.z.number().positive(),
    hasEhailingExperience: zod_1.z.boolean(),
    ehailingCompany: zod_1.z.string().optional(),
    ehailingProfileNumber: zod_1.z.string().optional(),
    ehailingWorkType: zod_1.z.enum(["full-time", "part-time", "weekend"]).optional(),
    drivingExperienceYears: zod_1.z.string().optional(),
    arrangementRequested: zod_1.z.boolean().optional(),
    documents: zod_1.z.array(exports.DocumentUploadSchema).optional(),
});
exports.RentalApplicationFormSchema = zod_1.z.object({
    listingId: zod_1.z.number().positive(),
    purpose: zod_1.z.string().min(1, "Purpose is required"),
    documents: zod_1.z.array(exports.DocumentUploadSchema).optional(),
});
exports.FractionalApplicationFormSchema = zod_1.z.object({
    listingId: zod_1.z.number().positive(),
    documents: zod_1.z.array(exports.DocumentUploadSchema).optional(),
});
// Admin decision schema
exports.MakeDecisionSchema = zod_1.z.object({
    applicationId: zod_1.z.number().positive(),
    decision: zod_1.z.enum(["approved", "rejected"]),
    reason: zod_1.z.string().optional(),
});
// Document status update schema
exports.UpdateDocumentStatusSchema = zod_1.z.object({
    documentId: zod_1.z.number().positive(),
    status: zod_1.z.enum(["verified", "rejected"]),
});
// Application statistics
exports.ApplicationStatsSchema = zod_1.z.object({
    total: zod_1.z.number(),
    pending: zod_1.z.number(),
    approved: zod_1.z.number(),
    rejected: zod_1.z.number(),
    byListingType: zod_1.z.record(zod_1.z.number()),
});
// API Response types
exports.ApplicationResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    applicationId: zod_1.z.number().optional(),
    error: zod_1.z.string().optional(),
});
exports.ApplicationsListResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    applications: zod_1.z.array(exports.ApplicationWithDetailsSchema).optional(),
    error: zod_1.z.string().optional(),
});
exports.ApplicationStatsResponseSchema = zod_1.z.object({
    success: zod_1.z.boolean(),
    stats: exports.ApplicationStatsSchema.optional(),
    error: zod_1.z.string().optional(),
});
// Document types for different application types
exports.EHAILING_DOCUMENT_TYPES = [
    "ID Document",
    "Driver's license",
    "Bank Statement - 3 months",
    "Proof of residence",
    "Selfie",
    "PrDP (Professional driving permit)",
    "Police clearance certificate",
];
exports.RENTAL_DOCUMENT_TYPES = [
    "ID Document",
    "Driver's license",
    "Bank Statement - 3 months",
    "Proof of residence",
    "Selfie",
];
exports.FRACTIONAL_DOCUMENT_TYPES = [
    "ID Document",
    "Driver's license",
    "Bank Statement - 3 months",
    "Proof of residence",
    "Selfie",
    "Proof of income",
];
