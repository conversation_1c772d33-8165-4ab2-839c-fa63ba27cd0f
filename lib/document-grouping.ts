/**
 * Document Grouping Utilities
 *
 * This module provides utility functions for grouping documents by type,
 * determining latest versions, and calculating document status for the
 * admin application review interface.
 */

import type { ApplicationDocument } from "../types/applications";

// Enhanced document interface with grouping metadata
export interface DocumentWithStatus extends ApplicationDocument {
  // Existing fields from ApplicationDocument
  id: number;
  documentType: string;
  documentUrl: string;
  uploadedAt: string;
  status?: "pending" | "uploaded" | "verified" | "rejected" | "superseded";
  statusAt?: string;
  statusBy?: number;

  // Enhanced fields for grouping
  isLatest: boolean;
  version: number; // 1, 2, 3, etc.
}

// Document group interface
export interface DocumentGroup {
  documentType: string;
  documents: DocumentWithStatus[];
  latestDocument: DocumentWithStatus;
  hasMultipleVersions: boolean;
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

/**
 * Groups documents by their document type and sorts them chronologically
 * @param documents - Array of application documents to group
 * @returns Array of document groups with metadata
 */
export function groupDocumentsByType(
  documents: ApplicationDocument[]
): DocumentGroup[] {
  if (!documents || documents.length === 0) {
    return [];
  }

  // Group documents by type
  const grouped = documents.reduce(
    (acc, doc) => {
      if (!acc[doc.documentType]) {
        acc[doc.documentType] = [];
      }
      acc[doc.documentType].push(doc);
      return acc;
    },
    {} as Record<string, ApplicationDocument[]>
  );

  // Process each group
  return Object.entries(grouped).map(([type, docs]) => {
    // Sort documents by upload date (newest first)
    const sortedDocs = sortDocumentsByUploadDate(docs);

    // Enhance documents with grouping metadata
    const enhancedDocs: DocumentWithStatus[] = sortedDocs.map(
      (doc, index) =>
        ({
          ...doc,
          isLatest: index === 0,
          version: sortedDocs.length - index, // Latest gets highest version number
        }) as DocumentWithStatus
    );

    return {
      documentType: type,
      documents: enhancedDocs,
      latestDocument: enhancedDocs[0],
      hasMultipleVersions: enhancedDocs.length > 1,
    };
  });
}

/**
 * Sorts documents by upload date in descending order (newest first)
 * @param documents - Array of documents to sort
 * @returns Sorted array of documents
 */
export function sortDocumentsByUploadDate(
  documents: ApplicationDocument[]
): ApplicationDocument[] {
  return [...documents].sort(
    (a, b) =>
      new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
  );
}

/**
 * Determines the latest document from a group of documents of the same type
 * @param documents - Array of documents of the same type
 * @returns The latest document or null if no documents provided
 */
export function determineLatestDocument(
  documents: ApplicationDocument[]
): ApplicationDocument | null {
  if (!documents || documents.length === 0) {
    return null;
  }

  const sorted = sortDocumentsByUploadDate(documents);
  return sorted[0];
}

/**
 * Calculates the effective status of a document based on its current status
 * and position in the document history
 * @param document - Document to calculate status for
 * @param isLatest - Whether this is the latest version of the document type
 * @returns Calculated document status
 */
export function calculateDocumentStatus(
  document: ApplicationDocument,
  isLatest: boolean
): "pending" | "uploaded" | "verified" | "rejected" | "superseded" {
  // If this is not the latest document and has been superseded by a newer upload
  if (
    !isLatest &&
    document.status !== "verified" &&
    document.status !== "rejected"
  ) {
    return "superseded";
  }

  // If document has an explicit status, use it
  if (document.status) {
    return document.status as
      | "pending"
      | "uploaded"
      | "verified"
      | "rejected"
      | "superseded";
  }

  // Default to pending if no status is set
  return "pending";
}

/**
 * Validates document status and metadata consistency
 * @param document - Document to validate
 * @returns Validation result with any errors found
 */
export function validateDocumentStatus(
  document: DocumentWithStatus
): ValidationResult {
  const errors: string[] = [];

  // Check if document has URL
  if (!document.documentUrl) {
    errors.push("Document URL is missing");
  }

  // Check if latest document can be superseded
  if (document.isLatest && document.status === "superseded") {
    errors.push("Latest document cannot be superseded");
  }

  // Check status consistency
  if (
    document.status &&
    !document.statusAt &&
    (document.status === "verified" || document.status === "rejected")
  ) {
    errors.push("Status timestamp is missing for verified/rejected document");
  }

  // Check version numbering
  if (document.version < 1) {
    errors.push("Document version must be positive");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Checks if a document can be verified or rejected
 * @param document - Document to check
 * @returns Whether the document can be acted upon
 */
export function canVerifyDocument(document: DocumentWithStatus): boolean {
  // Only latest documents can be verified/rejected
  if (!document.isLatest) {
    return false;
  }

  // Cannot verify superseded documents
  if (document.status === "superseded") {
    return false;
  }

  // Can verify pending, uploaded, or previously verified/rejected documents
  return true;
}

/**
 * Gets the display status text for a document
 * @param document - Document to get status text for
 * @returns Human-readable status text
 */
export function getDocumentStatusText(document: DocumentWithStatus): string {
  const status = calculateDocumentStatus(document, document.isLatest);

  switch (status) {
    case "verified":
      return "Verified";
    case "rejected":
      return "Rejected";
    case "superseded":
      return "Superseded";
    case "uploaded":
      return "Uploaded";
    case "pending":
    default:
      return "Pending Review";
  }
}

/**
 * Gets the appropriate CSS classes for document status styling
 * @param document - Document to get styling for
 * @returns CSS class string for status styling
 */
export function getDocumentStatusClasses(document: DocumentWithStatus): string {
  const status = calculateDocumentStatus(document, document.isLatest);

  switch (status) {
    case "verified":
      return "text-green-700 bg-green-100 border-green-200";
    case "rejected":
      return "text-red-700 bg-red-100 border-red-200";
    case "superseded":
      return "text-gray-700 bg-gray-100 border-gray-200";
    case "uploaded":
      return "text-blue-700 bg-blue-100 border-blue-200";
    case "pending":
    default:
      return "text-yellow-700 bg-yellow-100 border-yellow-200";
  }
}

/**
 * Filters documents to only include those that need admin attention
 * @param groups - Array of document groups
 * @returns Groups that contain documents requiring review
 */
export function getDocumentsRequiringReview(
  groups: DocumentGroup[]
): DocumentGroup[] {
  return groups.filter((group) => {
    const latestDoc = group.latestDocument;
    const status = calculateDocumentStatus(latestDoc, true);
    return status === "pending" || status === "uploaded";
  });
}

/**
 * Gets summary statistics for document groups
 * @param groups - Array of document groups to analyze
 * @returns Summary statistics object
 */
export function getDocumentGroupStats(groups: DocumentGroup[]) {
  const stats = {
    totalGroups: groups.length,
    totalDocuments: 0,
    verified: 0,
    rejected: 0,
    pending: 0,
    superseded: 0,
    hasMultipleVersions: 0,
  };

  groups.forEach((group) => {
    stats.totalDocuments += group.documents.length;

    if (group.hasMultipleVersions) {
      stats.hasMultipleVersions++;
    }

    group.documents.forEach((doc) => {
      const status = calculateDocumentStatus(doc, doc.isLatest);
      switch (status) {
        case "verified":
          stats.verified++;
          break;
        case "rejected":
          stats.rejected++;
          break;
        case "superseded":
          stats.superseded++;
          break;
        case "pending":
        case "uploaded":
        default:
          stats.pending++;
          break;
      }
    });
  });

  return stats;
}
