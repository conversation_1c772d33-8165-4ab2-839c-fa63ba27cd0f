/**
 * Unified User Attributes API
 * 
 * This file provides the correct cached user attributes function based on context.
 * 
 * ⚠️  IMPORTANT: Always import from this file instead of using fetchUserAttributes directly!
 * 
 * USAGE GUIDE:
 * 
 * 🖥️  SERVER-SIDE (Server Components, Actions, API Routes):
 * ```typescript
 * import { getUserAttributes } from "@/lib/userAttributes";
 * 
 * export default async function MyServerPage() {
 *   const attributes = await getUserAttributes();
 *   // ... use attributes
 * }
 * ```
 * 
 * 🌐 CLIENT-SIDE (Client Components, Hooks, Event Handlers):
 * ```typescript
 * import { getCachedUserAttributes } from "@/lib/userAttributes";
 * 
 * export default function MyClientComponent() {
 *   const [attributes, setAttributes] = useState(null);
 *   
 *   useEffect(() => {
 *     const fetchAttributes = async () => {
 *       const attrs = await getCachedUserAttributes();
 *       setAttributes(attrs);
 *     };
 *     fetchAttributes();
 *   }, []);
 * }
 * ```
 * 
 * 🚫 LOGOUT/CACHE CLEARING:
 * ```typescript
 * import { clearUserAttributesCache } from "@/lib/userAttributes";
 * 
 * const handleLogout = () => {
 *   clearUserAttributesCache(); // Clear client cache
 *   // ... perform logout
 * };
 * ```
 * 
 * CACHING BEHAVIOR:
 * - ⏱️  Both server and client versions cache for 1 hour
 * - 🖥️  Server: Uses Next.js unstable_cache (shared across requests)
 * - 🌐 Client: Uses localStorage (persists across browser sessions)
 * - 🔄 Cache automatically clears on authentication errors
 * - 🧹 Cache manually clears on logout
 */

/**
 * Client-Side User Attributes API
 * 
 * ⚠️  CLIENT COMPONENTS ONLY
 * 
 * For server components, import from "@/lib/serverUserAttributes" instead
 * 
 * Usage:
 * ```typescript
 * import { getCachedUserAttributes, clearUserAttributesCache } from "@/lib/userAttributes";
 * ```
 */

// Client-side cached function (for client components and hooks)
export { getCachedUserAttributes, clearUserAttributesCache } from "./clientUserAttributes";

/**
 * @deprecated DO NOT USE - Use getCachedUserAttributes for client or getUserAttributes from @/lib/serverUserAttributes for server
 */
// Intentionally not exporting fetchUserAttributes to prevent direct usage 