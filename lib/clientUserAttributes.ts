"use client";

import { fetchUserAttributes } from "aws-amplify/auth";

/**
 * Client-side cached version of fetchUserAttributes
 * 
 * ⚠️  CLIENT COMPONENTS ONLY - Use this for:
 * - Client Components (pages/components with "use client")
 * - React Hooks
 * - Event Handlers
 * - Browser-only code
 * 
 * For server components, use getUserAttributes from @/lib/userAttributes instead
 * 
 * Caches results in localStorage for 1 hour
 * 
 * @returns User attributes or throws error if user is not authenticated
 */
export const getCachedUserAttributes = async () => {
  const cacheKey = 'user_attributes_cache';
  const cacheExpiry = 'user_attributes_expiry';
  
  // Check if we have cached data that's still valid
  if (typeof window !== 'undefined') {
    const cached = localStorage.getItem(cacheKey);
    const expiry = localStorage.getItem(cacheExpiry);
    
    if (cached && expiry && Date.now() < parseInt(expiry)) {
      return JSON.parse(cached);
    }
  }
  
  // Fetch fresh data
  try {
    const attributes = await fetchUserAttributes();
    
    // Cache the result for 1 hour
    if (typeof window !== 'undefined') {
      localStorage.setItem(cacheKey, JSON.stringify(attributes));
      localStorage.setItem(cacheExpiry, (Date.now() + 3600000).toString()); // 1 hour
    }
    
    return attributes;
  } catch (error) {
    // Clear invalid cache on error
    if (typeof window !== 'undefined') {
      localStorage.removeItem(cacheKey);
      localStorage.removeItem(cacheExpiry);
    }
    throw error;
  }
};

/**
 * Clear the cached user attributes
 * Useful when user data changes or on logout
 */
export const clearUserAttributesCache = () => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('user_attributes_cache');
    localStorage.removeItem('user_attributes_expiry');
  }
}; 