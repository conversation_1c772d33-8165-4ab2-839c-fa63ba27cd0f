// Background job processor for word cloud analysis
// This would integrate with your job queue system (AWS SQS, Redis Queue, etc.)

import { generateWordCloudAnalysis, type WordCloudAnalysis } from '@/actions/word-cloud-processor';
import type { FormType } from '@/db/queries';

export interface WordCloudJob {
  id: string;
  type: 'word_cloud_analysis';
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: Date;
  processingStartedAt?: Date;
  completedAt?: Date;
  data: {
    formTypes?: FormType[] | 'all';
    fieldNames?: string[];
    dateRangeStart?: Date;
    dateRangeEnd?: Date;
    maxWords?: number;
    includesPhrases?: boolean;
    presetName?: string;
    requestedBy?: string; // User ID who requested the analysis
  };
  result?: WordCloudAnalysis;
  error?: string;
}

// Job queue interface (implement with your preferred queue system)
interface JobQueue {
  enqueue(job: Omit<WordCloudJob, 'id' | 'status' | 'createdAt'>): Promise<string>;
  dequeue(): Promise<WordCloudJob | null>;
  updateJob(jobId: string, updates: Partial<WordCloudJob>): Promise<void>;
  getJob(jobId: string): Promise<WordCloudJob | null>;
  listJobs(status?: WordCloudJob['status']): Promise<WordCloudJob[]>;
}

// In-memory job queue for development (replace with Redis/SQS in production)
class InMemoryJobQueue implements JobQueue {
  private jobs: Map<string, WordCloudJob> = new Map();
  private jobCounter = 0;

  async enqueue(job: Omit<WordCloudJob, 'id' | 'status' | 'createdAt'>): Promise<string> {
    const id = `job_${++this.jobCounter}_${Date.now()}`;
    const fullJob: WordCloudJob = {
      ...job,
      id,
      status: 'pending',
      createdAt: new Date()
    };
    
    this.jobs.set(id, fullJob);
    console.log(`[WordCloud] Enqueued job ${id} with priority ${job.priority}`);
    return id;
  }

  async dequeue(): Promise<WordCloudJob | null> {
    // Get highest priority pending job
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

    const nextJob = pendingJobs[0];
    if (nextJob) {
      nextJob.status = 'processing';
      nextJob.processingStartedAt = new Date();
      this.jobs.set(nextJob.id, nextJob);
    }

    return nextJob || null;
  }

  async updateJob(jobId: string, updates: Partial<WordCloudJob>): Promise<void> {
    const job = this.jobs.get(jobId);
    if (job) {
      Object.assign(job, updates);
      this.jobs.set(jobId, job);
    }
  }

  async getJob(jobId: string): Promise<WordCloudJob | null> {
    return this.jobs.get(jobId) || null;
  }

  async listJobs(status?: WordCloudJob['status']): Promise<WordCloudJob[]> {
    const allJobs = Array.from(this.jobs.values());
    return status ? allJobs.filter(job => job.status === status) : allJobs;
  }
}

// Job processor class
export class WordCloudJobProcessor {
  private queue: JobQueue;
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  constructor(queue?: JobQueue) {
    this.queue = queue || new InMemoryJobQueue();
  }

  // Start processing jobs
  start(intervalMs = 5000): void {
    if (this.isProcessing) {
      console.log('[WordCloud] Job processor already running');
      return;
    }

    this.isProcessing = true;
    console.log('[WordCloud] Starting job processor');

    this.processingInterval = setInterval(async () => {
      await this.processNextJob();
    }, intervalMs);
  }

  // Stop processing jobs
  stop(): void {
    if (!this.isProcessing) return;

    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    console.log('[WordCloud] Stopped job processor');
  }

  // Process the next job in the queue
  private async processNextJob(): Promise<void> {
    try {
      const job = await this.queue.dequeue();
      if (!job) return;

      console.log(`[WordCloud] Processing job ${job.id}`);
      
      try {
        // Generate the word cloud analysis
        const result = await generateWordCloudAnalysis(job.data);
        
        // Update job with result
        await this.queue.updateJob(job.id, {
          status: 'completed',
          completedAt: new Date(),
          result
        });

        console.log(`[WordCloud] Completed job ${job.id} - found ${result.words.length} words from ${result.totalSubmissions} submissions`);

        // Optional: Save to database here
        await this.saveAnalysisToDatabase(result, job);

      } catch (error) {
        console.error(`[WordCloud] Job ${job.id} failed:`, error);
        
        await this.queue.updateJob(job.id, {
          status: 'failed',
          completedAt: new Date(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('[WordCloud] Error processing job queue:', error);
    }
  }

  // Save analysis to database (implement based on your database setup)
  private async saveAnalysisToDatabase(analysis: WordCloudAnalysis, job: WordCloudJob): Promise<void> {
    // This would insert into your word_cloud_analysis and word_cloud_words tables
    // Implementation depends on your database client (Drizzle, Prisma, etc.)
    
    console.log(`[WordCloud] Saving analysis ${analysis.id} to database (${analysis.words.length} words, ${analysis.phrases.length} phrases)`);
    
    // Example implementation:
    /*
    const db = await getDatabase();
    
    // Insert main analysis record
    const [analysisRecord] = await db.insert(wordCloudAnalysisTable).values({
      formType: analysis.formType,
      analysisType: analysis.analysisType,
      fieldName: analysis.fieldName,
      totalSubmissionsAnalyzed: analysis.totalSubmissions,
      status: analysis.status,
      metadata: {
        presetName: job.data.presetName,
        requestedBy: job.data.requestedBy,
        jobId: job.id
      }
    }).returning();

    // Insert word records
    if (analysis.words.length > 0) {
      await db.insert(wordCloudWordsTable).values(
        analysis.words.map(word => ({
          analysisId: analysisRecord.id,
          word: word.word,
          frequency: word.frequency,
          weight: word.weight,
          category: word.category
        }))
      );
    }

    // Insert phrase records
    if (analysis.phrases.length > 0) {
      await db.insert(wordCloudPhrasesTable).values(
        analysis.phrases.map(phrase => ({
          analysisId: analysisRecord.id,
          phrase: phrase.phrase,
          wordCount: phrase.wordCount,
          frequency: phrase.frequency,
          weight: phrase.weight
        }))
      );
    }
    */
  }

  // Queue a new word cloud analysis job
  async queueAnalysis(options: {
    formTypes?: FormType[] | 'all';
    fieldNames?: string[];
    dateRangeStart?: Date;
    dateRangeEnd?: Date;
    maxWords?: number;
    includesPhrases?: boolean;
    presetName?: string;
    priority?: 'low' | 'medium' | 'high';
    requestedBy?: string;
  }): Promise<string> {
    const {
      priority = 'medium',
      ...data
    } = options;

    return await this.queue.enqueue({
      type: 'word_cloud_analysis',
      priority,
      data
    });
  }

  // Get job status
  async getJobStatus(jobId: string): Promise<WordCloudJob | null> {
    return await this.queue.getJob(jobId);
  }

  // List all jobs
  async listJobs(status?: WordCloudJob['status']): Promise<WordCloudJob[]> {
    return await this.queue.listJobs(status);
  }

  // Queue preset analysis
  async queuePresetAnalysis(presetName: string, priority: 'low' | 'medium' | 'high' = 'medium'): Promise<string> {
    const presets = {
      'all_leads': {
        formTypes: 'all' as const,
        maxWords: 50
      },
      'business_focus': {
        formTypes: ['business'] as FormType[],
        maxWords: 75
      },
      'investment_interest': {
        formTypes: ['co-own'] as FormType[],
        maxWords: 60
      },
      'service_requests': {
        formTypes: ['management', 'rideshare'] as FormType[],
        maxWords: 40
      }
    };

    const preset = presets[presetName as keyof typeof presets];
    if (!preset) {
      throw new Error(`Unknown preset: ${presetName}`);
    }

    return await this.queueAnalysis({
      ...preset,
      presetName,
      priority
    });
  }
}

// Global job processor instance
let globalProcessor: WordCloudJobProcessor | null = null;

export function getWordCloudProcessor(): WordCloudJobProcessor {
  if (!globalProcessor) {
    globalProcessor = new WordCloudJobProcessor();
    // Auto-start in development
    if (process.env.NODE_ENV === 'development') {
      globalProcessor.start();
    }
  }
  return globalProcessor;
}

// Server actions for interacting with the job processor
export async function queueWordCloudAnalysis(options: Parameters<WordCloudJobProcessor['queueAnalysis']>[0]): Promise<string> {
  const processor = getWordCloudProcessor();
  return await processor.queueAnalysis(options);
}

export async function getWordCloudJobStatus(jobId: string): Promise<WordCloudJob | null> {
  const processor = getWordCloudProcessor();
  return await processor.getJobStatus(jobId);
}

export async function listWordCloudJobs(status?: WordCloudJob['status']): Promise<WordCloudJob[]> {
  const processor = getWordCloudProcessor();
  return await processor.listJobs(status);
} 