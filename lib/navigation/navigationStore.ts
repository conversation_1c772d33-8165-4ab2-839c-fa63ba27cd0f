import { create } from 'zustand';

interface NavigationState {
  currentScreen: string;
  params: Record<string, any>;
  history: Array<{ screen: string; params: Record<string, any> }>;
  navigationDirection: 'forward' | 'back' | 'replace';
}

interface NavigationActions {
  navigate: (screen: string, params?: Record<string, any>) => void;
  goBack: () => void;
  replace: (screen: string, params?: Record<string, any>) => void;
  reset: (screen: string, params?: Record<string, any>) => void;
  canGoBack: () => boolean;
}

export const useNavigationStore = create<NavigationState & NavigationActions>((set, get) => ({
  currentScreen: 'home',
  params: {},
  history: [{ screen: 'home', params: {} }],
  navigationDirection: 'forward',

  navigate: (screen: string, params = {}) => {
    const { history } = get();
    set({
      currentScreen: screen,
      params,
      history: [...history, { screen, params }],
      navigationDirection: 'forward'
    });
  },

  goBack: () => {
    const { history } = get();
    if (history.length > 1) {
      const newHistory = history.slice(0, -1);
      const previous = newHistory[newHistory.length - 1];
      set({
        currentScreen: previous.screen,
        params: previous.params,
        history: newHistory,
        navigationDirection: 'back'
      });
    }
  },

  replace: (screen: string, params = {}) => {
    const { history } = get();
    const newHistory = [...history.slice(0, -1), { screen, params }];
    set({
      currentScreen: screen,
      params,
      history: newHistory,
      navigationDirection: 'replace'
    });
  },

  reset: (screen: string, params = {}) => {
    set({
      currentScreen: screen,
      params,
      history: [{ screen, params }],
      navigationDirection: 'replace'
    });
  },

  canGoBack: () => {
    const { history } = get();
    return history.length > 1;
  }
})); 