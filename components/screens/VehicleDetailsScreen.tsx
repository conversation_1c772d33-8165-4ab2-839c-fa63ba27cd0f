"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  Share,
  Heart,
  Car,
  Calendar,
  MapPin,
  DollarSign,
  Info,
  Clock,
  Check,
  ChevronRight,
  MessageSquare,
  Phone,
  ChevronLeft,
  User,
  Mail,
  AlertTriangle,
  Shield,
  Wrench,
  Users,
} from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { fetchUserAttributes } from "aws-amplify/auth";
import { getUrl } from "aws-amplify/storage";
import VehicleImage from "@/components/vehicle-image";
import { getVehicleByIdDrizzle } from "@/drizzle-actions/vehicle-dashboard";
import { deprecated_getCurrentVehiclePossession } from "@/drizzle-actions/bookings";
import { getVehicleMaintenanceByVehicleIdDrizzle } from "@/drizzle-actions/vehicle-maintenance";
import { deprecated_getVehicleBookingsWithDetails } from "@/drizzle-actions/bookings";
import type { VehicleReadWithModelAndParty, VehicleReadWithListings } from "@/types/vehicles";
import type { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import type { VehicleMaintenanceRead } from "@/drizzle-actions/vehicle-maintenance";
import type { deprecated_BookingRead } from "@/types/bookings";
import {
  getStatusColor,
  getStatusLabel,
  formatDateForInput,
  calculateTimeRemaining,
} from "@/lib/utils";

interface VehicleDetailsScreenProps {
  params?: { vehicleId?: string };
}

export default function VehicleDetailsScreen({ params }: VehicleDetailsScreenProps) {
  const { goBack, navigateToBookingCalendar, navigateToVehicleHandover, navigateToMaintenanceDetails } = useNavigation();
  const { user } = useAuthenticator((context) => [context.user]);
  const vehicleId = params?.vehicleId || "1";

  // Data state
  const [vehicle, setVehicle] = useState<VehicleReadWithModelAndParty | null>(null);
  const [possessions, setPossessions] = useState<any[]>([]);
  const [maintenanceRecords, setMaintenanceRecords] = useState<VehicleMaintenanceRead[]>([]);
  const [bookings, setBookings] = useState<deprecated_BookingRead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [activeTab, setActiveTab] = useState("details");
  const [isFavorite, setIsFavorite] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [currentPossession, setCurrentPossession] = useState<any | null>(null);
  const [timeRemaining, setTimeRemaining] = useState({ hours: 5, minutes: 45 });

  // Data fetching
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        const [vehicleData, possessionData, maintenanceData, bookingsData] = await Promise.all([
          getVehicleByIdDrizzle(parseInt(vehicleId)),
          deprecated_getCurrentVehiclePossession(parseInt(vehicleId)),
          getVehicleMaintenanceByVehicleIdDrizzle(parseInt(vehicleId)),
          deprecated_getVehicleBookingsWithDetails(parseInt(vehicleId))
        ]);

        setVehicle(vehicleData);
        setPossessions(possessionData ? [possessionData] : []);
        setMaintenanceRecords(maintenanceData || []);
        setBookings(bookingsData || []);
      } catch (error) {
        console.error("Error fetching vehicle data:", error);
        setError("Failed to load vehicle data");
      } finally {
        setLoading(false);
      }
    };

    if (user && vehicleId) {
      fetchData();
    }
  }, [user, vehicleId]);

  // Load images
  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length || !Array.isArray(vehicle.media)) return;

      try {
        const urls: string[] = await Promise.all(
          vehicle.media.map(async (item: any) => {
            const result = await getUrl({ path: item.media_path });
            return result.url.toString();
          })
        );
        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading vehicle images:", error);
        setImageUrls([]);
      }
    }

    if (vehicle) {
      loadImages();
    }
  }, [vehicle]);

  // Find current possession
  useEffect(() => {
    if (!vehicle?.id || !Array.isArray(possessions)) return;
    
    try {
      const latest = possessions
        .filter((p) => p?.status === "PENDING")
        .sort(
          (a, b) =>
            new Date(b?.handoverExpectedDatetime || 0).getTime() -
            new Date(a?.handoverExpectedDatetime || 0).getTime()
        )[0];
      setCurrentPossession(latest || null);
    } catch (error) {
      console.error("Error processing possessions:", error);
      setCurrentPossession(null);
    }
  }, [vehicle?.id, possessions]);

  // Time remaining calculation
  useEffect(() => {
    if (!currentPossession?.handoverExpectedDatetime) return;

    const updateTime = () => {
      const remaining = calculateTimeRemaining(
        currentPossession.handoverExpectedDatetime
      );
      setTimeRemaining(remaining);
    };

    updateTime();
    const intervalId = setInterval(updateTime, 60 * 1000);
    return () => clearInterval(intervalId);
  }, [currentPossession?.handoverExpectedDatetime]);

  const handleNextImage = () => {
    const totalImages = imageUrls.length || 1;
    setCurrentImageIndex((prev) => (prev === totalImages - 1 ? 0 : prev + 1));
  };

  const handlePrevImage = () => {
    const totalImages = imageUrls.length || 1;
    setCurrentImageIndex((prev) => (prev === 0 ? totalImages - 1 : prev - 1));
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
      </div>
    );
  }

  if (error || !vehicle) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Vehicle</h3>
          <p className="text-gray-600">{error || "Vehicle not found"}</p>
          <button
            onClick={goBack}
            className="mt-4 bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-[#eef1f9]">
      {/* Fixed Header */}
      <div className="flex-shrink-0">
        {/* Status Bar */}
        <div className="bg-white px-4 py-2 flex justify-between items-center text-[#333333]">
          <div className="font-semibold">9:41</div>
          <div className="flex items-center gap-1">
            <div className="h-3 w-4">
              <svg viewBox="0 0 20 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1H19" stroke="#333333" strokeWidth="2" strokeLinecap="round" />
                <path d="M4 5H16" stroke="#333333" strokeWidth="2" strokeLinecap="round" />
                <path d="M7 9H13" stroke="#333333" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </div>
            <div className="h-3 w-4">
              <svg viewBox="0 0 16 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 0.5C0 0.223858 0.223858 0 0.5 0H15.5C15.7761 0 16 0.223858 16 0.5V11.5C16 11.7761 15.7761 12 15.5 12H0.5C0.223858 12 0 11.7761 0 11.5V0.5Z" fill="#333333" />
                <path d="M2 2.5C2 2.22386 2.22386 2 2.5 2H13.5C13.7761 2 14 2.22386 14 2.5V9.5C14 9.77614 13.7761 10 13.5 10H2.5C2.22386 10 2 9.77614 2 9.5V2.5Z" fill="#eef1f9" />
              </svg>
            </div>
          </div>
        </div>

        {/* Header */}
        <div className="bg-white px-6 py-4 flex items-center justify-between border-b border-[#f2f2f2]">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-[#333333]" />
        </button>
        <h1 className="text-xl font-bold text-[#333333] flex-1">Vehicle Details</h1>
        <div className="flex space-x-3">
          <button
            className="w-10 h-10 bg-[#f2f2f2] rounded-full flex items-center justify-center"
            onClick={() => setIsFavorite(!isFavorite)}
          >
            <Heart
              size={20}
              className={isFavorite ? "fill-red-500 text-red-500" : "text-[#333333]"}
            />
          </button>
          <button className="w-10 h-10 bg-[#f2f2f2] rounded-full flex items-center justify-center">
            <Share size={20} className="text-[#333333]" />
          </button>
        </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        {/* Image Gallery */}
      <div className="relative bg-[#f2f2f2] h-64">
        {imageUrls.length > 0 ? (
          <Image
            src={imageUrls[currentImageIndex]}
            alt={`${vehicle.model?.make?.name} ${vehicle.model?.model}`}
            fill
            className="object-cover"
          />
        ) : (
          <VehicleImage
            media={vehicle.media}
            alt={`${vehicle.model?.make?.name} ${vehicle.model?.model}`}
            className="object-cover"
          />
        )}
        
        {imageUrls.length > 1 && (
          <>
            <div className="absolute bottom-4 left-0 right-0 flex justify-center">
              <div className="flex space-x-1 px-2 py-1 bg-black bg-opacity-50 rounded-full">
                {imageUrls.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full cursor-pointer ${
                      index === currentImageIndex ? "bg-white" : "bg-white bg-opacity-50"
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  />
                ))}
              </div>
            </div>
            <button
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center"
              onClick={handlePrevImage}
            >
              <ChevronLeft size={20} className="text-[#333333]" />
            </button>
            <button
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center"
              onClick={handleNextImage}
            >
              <ChevronRight size={20} className="text-[#333333]" />
            </button>
          </>
        )}
      </div>

      {/* Vehicle Title and Status */}
      <div className="bg-white p-4 border-b border-[#f2f2f2]">
        <h2 className="text-xl font-bold text-[#333333]">
          {vehicle.manufacturing_year} {vehicle.model?.make?.name} {vehicle.model?.model}
        </h2>
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-3">
            <span className={`px-3 py-1 rounded-full text-xs font-medium shadow-sm ${
              currentPossession 
                ? "bg-[#FFD700] text-[#333333]" 
                : "bg-[#e6ffe6] text-[#007A2F]"
            }`}>
              {currentPossession ? "In Use" : "Available"}
            </span>
            {vehicle.vehicle_registration && (
              <span className="text-sm text-[#797879]">
                Reg: {vehicle.vehicle_registration}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Current User Section */}
      {currentPossession && (
        <div className="bg-white p-4 border-b border-[#f2f2f2]">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <User size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Current User</h3>
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-[#333333] font-medium">
                {currentPossession?.to_party?.individual
                  ? `${currentPossession.to_party.individual.first_name} ${
                      currentPossession.to_party.individual?.middle_name ?? ""
                    } ${currentPossession.to_party.individual.last_name}`
                  : "Unknown user"}
              </p>
              <div className="flex items-center mt-1">
                <Calendar size={14} className="text-[#797879] mr-1" />
                <p className="text-xs text-[#797879]">
                  Return: {new Date(currentPossession?.handover_expected_datetime || "").toLocaleString()}
                </p>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                onClick={() => {
                  const phoneNumber = currentPossession?.to_party?.contact_points?.find(
                    (cp: any) => cp.contact_point_type?.name === "phone"
                  )?.value || "0000";
                  window.open(`tel:${phoneNumber}`);
                }}
              >
                <Phone size={20} />
              </button>
              <button
                className="bg-[#009639] text-white p-2 rounded-full shadow-sm"
                onClick={() => {
                  const emailAddress = currentPossession?.to_party?.contact_points?.find(
                    (cp: any) => cp.contact_point_type?.name === "email"
                  )?.value || "<EMAIL>";
                  window.open(`mailto:${emailAddress}`);
                }}
              >
                <Mail size={20} />
              </button>
            </div>
          </div>
          
          {/* Time Remaining Progress */}
          <div className="mt-4">
            <div className="flex justify-between text-xs text-[#797879] mb-1">
              <span>Time Remaining</span>
              <span>{timeRemaining.hours}h {timeRemaining.minutes}m</span>
            </div>
            <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
              <div
                className="h-full bg-[#009639] rounded-full"
                style={{
                  width: `${((timeRemaining.hours * 60 + timeRemaining.minutes) / (6 * 60)) * 100}%`,
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Emergency Contacts */}
      <div className="bg-white p-4 border-b border-[#f2f2f2]">
        <div className="flex items-center mb-3">
          <AlertTriangle size={20} className="text-red-500 mr-2" />
          <h3 className="text-[#333333] font-medium">Emergency Contacts</h3>
        </div>
        <div className="grid grid-cols-1 gap-2">
          <div className="flex items-center justify-between p-2 bg-red-50 rounded-lg border border-red-100">
            <div className="flex items-center">
              <Shield size={14} className="text-red-600 mr-2" />
              <span className="text-sm text-[#333333]">Police: 10111</span>
            </div>
            <button
              className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium"
              onClick={() => window.open('tel:10111')}
            >
              Call
            </button>
          </div>
          <div className="flex items-center justify-between p-2 bg-yellow-50 rounded-lg border border-yellow-100">
            <div className="flex items-center">
              <Car size={14} className="text-yellow-600 mr-2" />
              <span className="text-sm text-[#333333]">Roadside: AA</span>
            </div>
            <button
              className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium"
              onClick={() => window.open('tel:0861102872')}
            >
              Call
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white border-b border-[#f2f2f2]">
        <div className="flex">
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "details"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Details
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "maintenance"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("maintenance")}
          >
            Maintenance
          </button>
          <button
            className={`flex-1 py-3 text-sm font-medium ${
              activeTab === "bookings"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => setActiveTab("bookings")}
          >
            Bookings
          </button>
        </div>
      </div>

      {/* Tab Content */}
      <div className="bg-white">
        {/* Details Tab */}
        {activeTab === "details" && (
          <div className="p-4">
            <div className="ride-card p-4 mb-4">
              <h3 className="text-[#333333] font-medium mb-3">Vehicle Specifications</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-[#797879]">Make</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.model?.make?.name || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-[#797879]">Model</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.model?.model || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-[#797879]">Year</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.manufacturing_year || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-[#797879]">Color</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.color || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-[#797879]">Registration</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.vehicle_registration || "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-xs text-[#797879]">Country</p>
                  <p className="text-sm text-[#333333] font-medium">
                    {vehicle.country_id || "N/A"}
                  </p>
                </div>
                <div className="col-span-2">
                  <p className="text-xs text-[#797879]">VIN</p>
                  <p className="text-sm text-[#333333] font-medium font-mono">
                    {vehicle.vin_number || "N/A"}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Maintenance Tab */}
        {activeTab === "maintenance" && (
          <div className="p-4">
            <div className="space-y-3">
              {maintenanceRecords.length > 0 ? (
                maintenanceRecords.map((item) => (
                  <div
                    key={item.id}
                    className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                  >
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                        <Wrench size={18} className="text-[#009639]" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{item.name}</h3>
                            <p className="text-xs text-[#797879]">{item.description}</p>
                          </div>
                          <div className="flex items-center gap-2 ml-auto">
                            <span
                              className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(item.status)}`}
                            >
                              {getStatusLabel(item.status)}
                            </span>
                            <button
                              className="text-[#009639] text-sm flex items-center"
                              onClick={() => navigateToMaintenanceDetails(item.id.toString())}
                            >
                              <ChevronRight size={20} className="text-[#797879]" />
                            </button>
                          </div>
                        </div>
                        <div className="flex items-center mt-2">
                          <Clock size={14} className="text-[#797879] mr-1" />
                          <span className="text-xs text-[#797879]">
                            {formatDateForInput(item.dueDate)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-[#797879] text-sm text-center py-8">
                  No maintenance records available.
                </p>
              )}
            </div>
          </div>
        )}

        {/* Bookings Tab */}
        {activeTab === "bookings" && (
          <div className="p-4">
            <div className="space-y-3">
              {bookings.length > 0 ? (
                bookings.map((booking) => (
                  <div
                    key={booking.id}
                    className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                  >
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                        <Calendar size={18} className="text-[#009639]" />
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div>
                            <h3 className="font-medium">{booking.reference}</h3>
                            <p className="text-xs text-[#797879]">{booking.notes}</p>
                          </div>
                          <span
                            className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(booking.status)}`}
                          >
                            {getStatusLabel(booking.status)}
                          </span>
                        </div>
                        <div className="flex items-center mt-2">
                          <Clock size={14} className="text-[#797879] mr-1" />
                          <span className="text-xs text-[#797879]">
                            {formatDateForInput(booking.start_datetime)} - {formatDateForInput(booking.end_datetime)}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-[#797879] text-sm text-center py-8">
                  No bookings available.
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      <div className="bg-white p-4 border-t border-[#f2f2f2] space-y-3">
        {/* Book Vehicle Button - only show if vehicle is available */}
        {!currentPossession && (
          <button
            className="w-full py-3 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
            onClick={() => navigateToBookingCalendar(vehicleId)}
          >
            Book This Vehicle
          </button>
        )}
        
        {/* Handover Button */}
        <button
          className="w-full py-3 bg-[#007A2F] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={() => navigateToVehicleHandover(vehicleId)}
        >
          Initiate Vehicle Handover
        </button>
      </div>
      </div>
    </div>
  );
} 