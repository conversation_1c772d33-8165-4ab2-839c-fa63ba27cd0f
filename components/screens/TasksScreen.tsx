"use client";

import { useState, useEffect } from "react";
import { 
  CheckS<PERSON>re, 
  AlertCircle, 
  UserPlus, 
  FileText, 
  Wrench, 
  CreditCard, 
  Shield,
  CheckCircle,
  X,
  Calendar,
  Timer,
  ChevronRight,
  ArrowLeft,
  Car
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { useNavigation } from "@/hooks/useNavigation";
import { useUserAttributes } from "@/hooks/useUserAttributes";
import { checkPendingTasks } from "@/actions/tasks";
import { TaskTypeEnum, TaskStatusEnum, TaskPriorityEnum } from "@/types/tasks";

// Task Types
type TaskType = 
  | 'onboarding'
  | 'compliance'
  | 'invitations'
  | 'approvals'
  | 'maintenance'
  | 'financial'
  | 'handover'
  | 'GROUP_INVITATION'

type TaskPriority = 
  | 'blocking'
  | 'urgent'
  | 'normal'
  | 'optional'
  | 'BLOCKING'
  | 'URGENT'
  | 'NORMAL'
  | 'OPTIONAL'

type TaskStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'dismissed'
  | 'expired'
  | 'PENDING'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'DISMISSED'
  | 'EXPIRED'

interface UserTask {
  id: string
  userId: string
  type: TaskType
  priority: TaskPriority
  status: TaskStatus
  title: string
  description: string
  actionText: string
  completionText?: string
  dueDate?: Date
  estimatedMinutes?: number
  metadata: {
    groupId?: string
    vehicleId?: string
    documentType?: string
    paymentId?: string
    termsVersion?: string
    relatedEntityId?: string
    completionPercentage?: number
    inviterName?: string
    paymentType?: string
    // Handover-related fields
    handoverId?: string
    handoverType?: string
    scheduledTime?: Date
    fromPartyId?: string
    toPartyId?: string
    bookingReference?: string
    isInspection?: boolean
    vehicleDetails?: {
      make: string
      model: string
      year: number
      registration: string
      color: string
    }
    location?: string
    inspectionType?: string
  }
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  dismissedAt?: Date
  expiresAt?: Date
}

type EmptyStateType = 
  | 'all_complete'
  | 'loading'
  | 'error'

export default function TasksScreen() {
  const { goBack, navigateToTaskDetail } = useNavigation();
  const { attributes, isAuthenticated } = useUserAttributes();
  const [tasks, setTasks] = useState<UserTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data replaced with real database loading below

  // Load real tasks data
  useEffect(() => {
    const loadTasks = async () => {
      if (!isAuthenticated || !attributes?.email) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const email = attributes.email;
        const partyId = attributes["custom:db_id"] ? parseInt(attributes["custom:db_id"]) : undefined;
        
        const result = await checkPendingTasks(email, partyId);
        
        if (result.success) {
          // Convert database tasks to UI format
          const uiTasks: UserTask[] = (result.tasks || []).map(task => ({
            id: task.id.toString(),
            userId: task.partyId?.toString() || email,
            type: task.type as TaskType,
            priority: task.priority as TaskPriority,
            status: task.status as TaskStatus,
            title: task.title,
            description: task.description || '',
            actionText: task.type === TaskTypeEnum.GROUP_INVITATION ? 'Review Invitation' : 'View Details',
            estimatedMinutes: task.estimatedMinutes || undefined,
            dueDate: task.expiresAt ? new Date(task.expiresAt) : undefined,
            metadata: task.metadata || {},
            createdAt: new Date(task.createdAt),
            updatedAt: new Date(task.createdAt)
          }));
          
          setTasks(uiTasks);
        } else {
          setError(result.error || 'Failed to load tasks');
        }
      } catch (err) {
        console.error('Error loading tasks:', err);
        setError('Failed to load tasks');
      } finally {
        setLoading(false);
      }
    };

    loadTasks();
  }, [isAuthenticated, attributes]);

  // Helper function to normalize priority values
  const normalizePriority = (priority: TaskPriority): 'blocking' | 'urgent' | 'normal' | 'optional' => {
    if (priority === 'BLOCKING') return 'blocking';
    if (priority === 'URGENT') return 'urgent';
    if (priority === 'NORMAL') return 'normal';
    if (priority === 'OPTIONAL') return 'optional';
    return priority as 'blocking' | 'urgent' | 'normal' | 'optional';
  };

  // Helper function to normalize status values
  const normalizeStatus = (status: TaskStatus): 'pending' | 'in_progress' | 'completed' | 'dismissed' | 'expired' => {
    if (status === 'PENDING') return 'pending';
    if (status === 'IN_PROGRESS') return 'in_progress';
    if (status === 'COMPLETED') return 'completed';
    if (status === 'DISMISSED') return 'dismissed';
    if (status === 'EXPIRED') return 'expired';
    return status as 'pending' | 'in_progress' | 'completed' | 'dismissed' | 'expired';
  };

  // Filter and sort tasks - only show PENDING and IN_PROGRESS
  const getFilteredTasks = () => {
    let filteredTasks = tasks.filter(task => {
      const normalizedStatus = normalizeStatus(task.status);
      return normalizedStatus === 'pending' || normalizedStatus === 'in_progress';
    });

    const priorityOrder = { blocking: 0, urgent: 1, normal: 2, optional: 3 };
    return filteredTasks.sort((a, b) => {
      const normalizedPriorityA = normalizePriority(a.priority);
      const normalizedPriorityB = normalizePriority(b.priority);
      
      const priorityDiff = priorityOrder[normalizedPriorityA] - priorityOrder[normalizedPriorityB];
      if (priorityDiff !== 0) return priorityDiff;
      
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  };

  const filteredTasks = getFilteredTasks();
  const blockingTasks = tasks.filter(task => normalizePriority(task.priority) === 'blocking' && (normalizeStatus(task.status) === 'pending' || normalizeStatus(task.status) === 'in_progress'));

  // Task action handlers
  const handleTaskAction = (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;

    console.log(`Navigating to task detail: ${task.title}`);
    navigateToTaskDetail(taskId);
  };

  const handleTaskDismiss = (taskId: string) => {
    setTasks(prevTasks =>
      prevTasks.map(t =>
        t.id === taskId
          ? {
              ...t,
              status: 'dismissed' as TaskStatus,
              dismissedAt: new Date()
            }
          : t
      )
    );
  };

  // Get task icon
  const getTaskIcon = (type: TaskType, priority: TaskPriority) => {
    const normalizedPriority = normalizePriority(priority);
    const iconProps = {
      size: 20,
      className: normalizedPriority === 'blocking' ? 'text-[#009639]' : 
                normalizedPriority === 'urgent' ? 'text-[#009639]' :
                normalizedPriority === 'normal' ? 'text-[#009639]' : 'text-[#009639]'
    };

    switch (type) {
      case 'onboarding':
        return <UserPlus {...iconProps} />;
      case 'compliance':
        return <Shield {...iconProps} />;
      case 'invitations':
        return <UserPlus {...iconProps} />;
      case 'GROUP_INVITATION':
        return <UserPlus {...iconProps} />;
      case 'approvals':
        return <CheckSquare {...iconProps} />;
      case 'maintenance':
        return <Wrench {...iconProps} />;
      case 'financial':
        return <CreditCard {...iconProps} />;
      case 'handover':
        return <Car {...iconProps} />;
      default:
        return <FileText {...iconProps} />;
    }
  };

  const getPriorityBadge = (priority: TaskPriority) => {
    const variants = {
      blocking: { variant: "destructive" as const, text: "Required", className: "" },
      urgent: { variant: "default" as const, text: "Urgent", className: "bg-orange-500" },
      normal: { variant: "secondary" as const, text: "Normal", className: "" },
      optional: { variant: "outline" as const, text: "Optional", className: "" }
    };

    const normalizedPriority = normalizePriority(priority);
    const config = variants[normalizedPriority];
    return (
      <Badge 
        variant={config.variant} 
        className={config.className}
      >
        {config.text}
      </Badge>
    );
  };

  const formatTimeEstimate = (minutes?: number) => {
    if (!minutes) return null;
    return minutes < 60 ? `${minutes} min` : `${Math.round(minutes / 60)}h ${minutes % 60}m`;
  };

  const formatDueDate = (dueDate?: Date) => {
    if (!dueDate) return null;
    const now = new Date();
    const diffInDays = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return "Due today";
    if (diffInDays === 1) return "Due tomorrow";
    if (diffInDays > 1) return `Due in ${diffInDays} days`;
    return "Overdue";
  };

  // Empty states
  const renderEmptyState = (type: EmptyStateType) => {
    const states = {
      loading: (
        <div className="flex flex-col items-center justify-center py-12 px-6">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mb-4"></div>
          <p className="text-[#797879] text-center">Loading your tasks...</p>
        </div>
      ),
      all_complete: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <CheckCircle size={48} className="text-[#009639] mb-4" />
          <h3 className="text-xl font-semibold text-[#333333] mb-2">All caught up! 🎉</h3>
          <p className="text-[#797879]">No pending or active tasks right now.</p>
          <p className="text-[#797879]">We'll notify you when new tasks arrive.</p>
        </div>
      ),
      error: (
        <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
          <AlertCircle size={48} className="text-red-500 mb-4" />
          <h3 className="text-lg font-semibold text-[#333333] mb-2">Unable to load tasks</h3>
          <p className="text-[#797879] mb-4">Please try again or contact support if the problem persists.</p>
        </div>
      )
    };

    return states[type];
  };

  // Header component
  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Tasks</h1>
      </div>
      
    </div>
  );

  // Determine which empty state to show
  const getEmptyStateType = (): EmptyStateType | null => {
    if (loading) return 'loading';
    if (error) return 'error';
    if (filteredTasks.length === 0) return 'all_complete';
    return null;
  };

  const emptyStateType = getEmptyStateType();

  if (emptyStateType) {
    return (
      <PageWithScroll
        header={header}
        className="bg-[#f5f5f5]"
        paddingBottom="pb-32"
      >
        {renderEmptyState(emptyStateType)}
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll
      header={header}
      className="bg-[#f5f5f5]"
      paddingBottom="pb-32"
    >
      {/* Summary Section */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-semibold text-[#333333]">Task Overview</h2>
            <div className="flex items-center gap-2">
              {blockingTasks.length > 0 && (
                <Badge className="bg-white text-[#009639] border border-[#009639]">
                  {blockingTasks.length} Required
                </Badge>
              )}
              <Badge className="bg-white text-[#009639] border border-[#009639]">
                {filteredTasks.length} Active
              </Badge>
            </div>
          </div>
          
          {blockingTasks.length > 0 && (
            <div className="bg-[#009639] border border-[#009639] rounded-lg p-3 mb-3">
              <div className="flex items-center gap-2 mb-1">
                <AlertCircle size={16} className="text-white" />
                <h3 className="font-semibold text-white">Action Required</h3>
              </div>
              <p className="text-white text-sm">
                You have {blockingTasks.length} required task{blockingTasks.length > 1 ? 's' : ''} that must be completed.
              </p>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <div className="font-semibold text-[#333333]">{filteredTasks.length}</div>
              <div className="text-[#797879]">Active Tasks</div>
            </div>
            <div className="text-center p-2 bg-green-50 rounded-lg">
              <div className="font-semibold text-green-700">
                {blockingTasks.length}
              </div>
              <div className="text-green-600">Required</div>
            </div>
          </div>
        </div>
      </div>

      {/* Tasks List */}
      <div className="p-4">
        {filteredTasks.length > 0 || blockingTasks.length > 0 ? (
          <div className="space-y-4">
            {filteredTasks.map((task) => {
              const normalizedStatus = normalizeStatus(task.status);
              const normalizedPriority = normalizePriority(task.priority);
              
              return (
              <div
                key={task.id}
                className={`bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden ${
                  normalizedStatus === 'completed' ? "opacity-75" : ""
                } ${normalizedStatus === 'pending' || normalizedStatus === 'in_progress' ? "hover:shadow-lg cursor-pointer transition-shadow duration-200" : ""}`}
                onClick={() => (normalizedStatus === 'pending' || normalizedStatus === 'in_progress') && handleTaskAction(task.id)}
              >
                
                <div className="p-4">
                  <div className="flex">
                    <div
                      className={`w-10 h-10 border border-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm ${
                        normalizedStatus === 'completed' ? "bg-white" : 
                        normalizedPriority === 'blocking' ? "bg-white" :
                        normalizedPriority === 'urgent' ? "bg-white" :
                        normalizedPriority === 'normal' ? "bg-white" : "bg-white"
                      }`}
                    >
                      {normalizedStatus === 'completed' ? (
                        <CheckCircle size={20} className="text-[#009639]" />
                      ) : (
                        getTaskIcon(task.type, task.priority)
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3
                          className={`font-semibold ${
                            normalizedStatus === 'completed' ? "text-gray-600 line-through" : "text-[#333333]"
                          }`}
                        >
                          {task.title}
                        </h3>
                      </div>
                      
                      <p className={`text-sm mb-3 ${normalizedStatus === 'completed' ? 'text-gray-500' : 'text-[#797879]'}`}>
                        {normalizedStatus === 'completed' && task.completionText ? task.completionText : task.description}
                      </p>
                      
                      {/* Task metadata */}
                      <div className="flex items-center gap-4 text-xs text-[#797879] mb-3">
                        {task.estimatedMinutes && (
                          <div className="flex items-center gap-1">
                            <Timer size={12} />
                            {formatTimeEstimate(task.estimatedMinutes)}
                          </div>
                        )}
                        {task.dueDate && (normalizedStatus === 'pending' || normalizedStatus === 'in_progress') && (
                          <div className="flex items-center gap-1">
                            <Calendar size={12} />
                            <span className={formatDueDate(task.dueDate)?.includes('Overdue') ? 'text-red-500' : ''}>
                              {formatDueDate(task.dueDate)}
                            </span>
                          </div>
                        )}
                        {normalizedStatus === 'completed' && task.completedAt && (
                          <div className="flex items-center gap-1">
                            <CheckCircle size={12} />
                            Completed {new Date(task.completedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>

                      {/* Action buttons below content */}
                      {(normalizedStatus === 'pending' || normalizedStatus === 'in_progress') && (
                        <div className="flex items-center gap-2">
                        
                          <Button
                            className="ride-primary-btn w-full py-2 text-sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTaskAction(task.id);
                            }}
                          >
                            {task.actionText}
                            <ChevronRight size={16} className="ml-1" />
                          </Button>
                          {normalizedPriority === 'optional' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTaskDismiss(task.id);
                              }}
                            >
                              <X size={16} />
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              );
            })}
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <CheckSquare size={40} className="text-[#d6d9dd] mx-auto mb-3" />
            <p className="text-[#333333] font-medium">No active tasks</p>
            <p className="text-[#797879] text-sm mt-1">
              All your tasks are completed or no new tasks have been assigned.
            </p>
          </div>
        )}
      </div>
    </PageWithScroll>
  );
}
