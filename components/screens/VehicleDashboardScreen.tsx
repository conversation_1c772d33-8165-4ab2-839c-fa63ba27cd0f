"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  Calendar,
  Search,
  Filter,
  ChevronRight,
  Clock,
  Wrench,
  RefreshCw,
  Plus,
  Car,
  FileText,
  Camera,
} from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { useVehicleDashboard } from "@/hooks/useVehicleDashboard";
import VehicleImage from "@/components/vehicle-image";
import { useVehicleMaintenance } from "@/hooks/use-vehicle-maintenance";
import { getStatusColor, getStatusLabel, formatDateForInput } from "@/lib/utils";
import { addVehicleDocument } from "@/actions/vehicle-documents";
import { addVehicleMedia } from "@/actions/vehicle-media";
import type { VehicleReadWithModelAndParty, VehicleReadWithListings } from "@/types/vehicles";
import type { CompanyOwnershipReadWithRelations } from "@/types/company-ownerships";
import { VehicleMaintenanceRead } from "@/types/maintanance";
import DocPostForm from "@/app/(main)/vehicle-dashboard/post-form-doc";
import ImagePostForm from "@/app/(main)/vehicle-dashboard/post-form-image";
import AddVehicleDrawer from "@/app/(main)/vehicle-dashboard/add-vehicle-drawer";

interface VehicleMaintenanceReadWithVehicleName extends VehicleMaintenanceRead {
  vehicleName: string;
}

interface VehicleDashboardScreenProps {
  params?: Record<string, any>;
}

export default function VehicleDashboardScreen({ params }: VehicleDashboardScreenProps) {
  const { navigateToVehicleStatus, navigateToBookingCalendar, navigateToVehicleHandover, navigateToMaintenanceDetails, navigateToMaintenanceSchedule, navigateToVehicleDetails } = useNavigation();
  const { user } = useAuthenticator((context) => [context.user]);
  
  // Use SWR for data fetching
  const { vehicles, ownerships, partyId, isLoading: loading, error, refetch, isAuthenticated } = useVehicleDashboard();
  
  // UI state
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("owned");
  const [filteredItems, setFilteredItems] = useState<VehicleMaintenanceReadWithVehicleName[]>([]);
  const [listDrawerOpen, setListDrawerOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    make: "",
    model: "",
    year: "",
    condition: "",
  });
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");

  const maintenanceItems = useVehicleMaintenance(vehicles);

  useEffect(() => {
    const filtered = maintenanceItems.filter(
      (item) => item.status === "Scheduled" || item.status === "Pending"
    );
    setFilteredItems(filtered);
  }, [maintenanceItems]);

  const getOwnership = (
    vehicle: VehicleReadWithListings,
    ownerships: CompanyOwnershipReadWithRelations[]
  ) => {
    const ownership = ownerships.find(
      (o) => o.company?.party_id === vehicle.party_id
    );
    return ownership?.fraction ?? null;
  };

  const isActiveDate = (dateString: string): boolean => {
    if (!dateString) {
      return false;
    }
    
    const dateStr = dateString.toString().toLowerCase();
    if (dateStr === 'infinity' || dateStr === '+infinity') {
      return true;
    }
    if (dateStr === '-infinity') {
      return false;
    }
    
    try {
      const effectiveToDate = new Date(dateString);
      if (isNaN(effectiveToDate.getTime())) {
        return false;
      }
      const now = new Date();
      return effectiveToDate > now;
    } catch (error) {
      console.warn('Failed to parse effective_to date:', dateString, error);
      return false;
    }
  };

  const getListingStatus = (vehicle: VehicleReadWithListings) => {
    if (!vehicle.listings || vehicle.listings.length === 0) {
      return { status: "Not Listed", type: null, color: "bg-gray-100 text-gray-800" };
    }

    const activeListing = vehicle.listings
      .filter(listing => isActiveDate(listing.effective_to))
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];

    if (!activeListing) {
      return { status: "Not Listed", type: null, color: "bg-gray-100 text-gray-800" };
    }

    switch (activeListing.listing_type) {
      case "SHORT_TERM_LEASE_OUT":
        return { status: "Listed for Rental", type: "rental", color: "bg-blue-100 text-blue-800" };
      case "LONG_TERM_LEASE_OUT":
        return { status: "Listed for Lease", type: "lease", color: "bg-purple-100 text-purple-800" };
      case "CO_OWNERSHIP_SALE":
        return { status: "Listed for Co-ownership", type: "co-ownership", color: "bg-green-100 text-green-800" };
      default:
        return { status: "Listed", type: "unknown", color: "bg-gray-100 text-gray-800" };
    }
  };

  const filteredVehicles = vehicles.filter((vehicle) => {
    const query = searchQuery.toLowerCase();
    const matchesSearch = !query || (
      vehicle.model?.make?.name?.toLowerCase().includes(query) ||
      vehicle.model?.model?.toLowerCase().includes(query) ||
      vehicle.vehicle_registration?.toLowerCase().includes(query) ||
      vehicle.vin_number?.toLowerCase().includes(query) ||
      vehicle.color?.toLowerCase().includes(query) ||
      vehicle.manufacturing_year?.toString().includes(query)
    );

    const matchesMake = !filters.make || vehicle.model?.make?.name === filters.make;
    const matchesModel = !filters.model || vehicle.model?.model === filters.model;
    const matchesYear = !filters.year || vehicle.manufacturing_year?.toString() === filters.year;
    const matchesCondition = !filters.condition || (
      filters.condition === "new" ? vehicle.manufacturing_year && vehicle.manufacturing_year >= new Date().getFullYear() - 1 :
      filters.condition === "used" ? vehicle.manufacturing_year && vehicle.manufacturing_year < new Date().getFullYear() - 1 :
      true
    );

    return matchesSearch && matchesMake && matchesModel && matchesYear && matchesCondition;
  }).sort((a, b) => {
    let aValue, bValue;
    
    switch (sortBy) {
      case "createdAt":
        aValue = new Date(a.created_at || "").getTime();
        bValue = new Date(b.created_at || "").getTime();
        break;
      case "year":
        aValue = a.manufacturing_year || 0;
        bValue = b.manufacturing_year || 0;
        break;
      case "make":
        aValue = a.model?.make?.name || "";
        bValue = b.model?.make?.name || "";
        break;
      case "model":
        aValue = a.model?.model || "";
        bValue = b.model?.model || "";
        break;
      default:
        aValue = new Date(a.created_at || "").getTime();
        bValue = new Date(b.created_at || "").getTime();
    }
    
    if (sortOrder === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const uniqueMakes = [...new Set(vehicles.map(v => v.model?.make?.name).filter(Boolean))];
  const uniqueModels = [...new Set(vehicles.filter(v => !filters.make || v.model?.make?.name === filters.make).map(v => v.model?.model).filter(Boolean))];
  const uniqueYears = [...new Set(vehicles.map(v => v.manufacturing_year?.toString()).filter(Boolean))]
    .sort((a, b) => parseInt(b || "0") - parseInt(a || "0"));

  // Show loading only when authenticated and actually loading
  if (isAuthenticated && loading && !vehicles.length) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
      </div>
    );
  }

  // Show error only when authenticated and there's an actual error
  if (isAuthenticated && error && !vehicles.length) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Data</h3>
          <p className="text-gray-600">{error.message || "Failed to load data"}</p>
          <button
            onClick={() => refetch()}
            className="mt-4 bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Show loading when not authenticated
  if (!isAuthenticated) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 border-b border-[#007A2F]">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-bold text-white">Vehicle Dashboard</h1>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => setListDrawerOpen(true)}
          >
            <Plus size={20} className="text-white" />
          </button>
        </div>
      </div>

      {/* Fixed Content */}
      <div className="bg-white shadow-sm">
        {/* Search and Filter */}
        <div className="p-4">
          <div className="flex space-x-2 mb-3">
            <div className="flex-1 bg-gray-50 rounded-full px-4 py-2 flex items-center">
              <Search size={18} className="text-[#797879] mr-2" />
              <input
                type="text"
                placeholder="Search by make, model, year, registration, VIN..."
                className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium flex items-center shadow-sm hover:bg-gray-200 transition-colors"
            >
              <Filter size={16} className="mr-1" />
              Filters
            </button>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Make</label>
                  <select
                    value={filters.make}
                    onChange={(e) => setFilters({...filters, make: e.target.value, model: ""})}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                  >
                    <option value="">All Makes</option>
                    {uniqueMakes.map(make => (
                      <option key={make} value={make}>{make}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Model</label>
                  <select
                    value={filters.model}
                    onChange={(e) => setFilters({...filters, model: e.target.value})}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                    disabled={!filters.make}
                  >
                    <option value="">All Models</option>
                    {uniqueModels.map(model => (
                      <option key={model} value={model}>{model}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Year</label>
                  <select
                    value={filters.year}
                    onChange={(e) => setFilters({...filters, year: e.target.value})}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                  >
                    <option value="">All Years</option>
                    {uniqueYears.map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Condition</label>
                  <select
                    value={filters.condition}
                    onChange={(e) => setFilters({...filters, condition: e.target.value})}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                  >
                    <option value="">All Conditions</option>
                    <option value="new">New</option>
                    <option value="used">Used</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Sort By</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                  >
                    <option value="createdAt">Date Added</option>
                    <option value="year">Year</option>
                    <option value="make">Make</option>
                    <option value="model">Model</option>
                  </select>
                </div>
                <div>
                  <label className="block text-xs text-[#797879] mb-1">Sort Order</label>
                  <select
                    value={sortOrder}
                    onChange={(e) => setSortOrder(e.target.value)}
                    className="w-full px-3 py-2 text-sm bg-white border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
                  >
                    <option value="desc">Newest First</option>
                    <option value="asc">Oldest First</option>
                  </select>
                </div>
                <div className="md:col-span-2"></div>
              </div>
              
              <div className="flex justify-between items-center pt-3 border-t border-gray-200">
                <span className="text-xs text-[#797879]">
                  {filteredVehicles.length} vehicle{filteredVehicles.length !== 1 ? 's' : ''} found
                </span>
                <button
                  onClick={() => {
                    setFilters({make: "", model: "", year: "", condition: ""});
                    setSortBy("createdAt");
                    setSortOrder("desc");
                  }}
                  className="text-xs text-[#009639] hover:text-[#007A2F] font-medium"
                >
                  Reset All
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="px-4">
          <div className="flex border-b border-[#f2f2f2]">
            <button
              className={`py-3 px-4 text-sm font-medium whitespace-nowrap ${
                activeTab === "owned"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("owned")}
            >
              Vehicles
            </button>
            <button
              className={`py-3 px-4 text-sm font-medium whitespace-nowrap ${
                activeTab === "maintenance"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("maintenance")}
            >
              Maintenance
            </button>
            <button
              className={`py-3 px-4 text-sm font-medium whitespace-nowrap ${
                activeTab === "documents"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("documents")}
            >
              Docs
            </button>
            <button
              className={`py-3 px-4 text-sm font-medium whitespace-nowrap ${
                activeTab === "images"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("images")}
            >
              Images
            </button>
          </div>
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="pb-24"> {/* Extra padding for bottom navigation */}
          {/* Vehicle List */}
          {activeTab === "owned" && (
            <div className="p-4">
            {filteredVehicles.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
                <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4">
                  <Car size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-semibold text-[#333333] mb-2">
                  {searchQuery ? "No vehicles found" : "No vehicles yet"}
                </h3>
                <p className="text-[#797879] max-w-sm">
                  {searchQuery
                    ? "Try adjusting your search terms to find vehicles."
                    : "Get started by adding your first vehicle to the platform using the + button above."}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredVehicles.map((vehicle) => (
                  <div
                    key={vehicle.id}
                    className="ride-card overflow-hidden drop-shadow-sm rounded-xl border border-gray-100 cursor-pointer hover:shadow-lg transition-shadow"
                    onClick={() => navigateToVehicleDetails(vehicle.id.toString())}
                  >
                    <div className="h-40 bg-[#f2f2f2] relative">
                      <VehicleImage
                        media={vehicle.media}
                        alt={`${vehicle.model?.make?.name} ${vehicle.model?.model}`}
                        className="object-cover"
                      />
                      
                      <div className="absolute top-3 left-3">
                        <div className={`px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getListingStatus(vehicle).color}`}>
                          {getListingStatus(vehicle).status}
                        </div>
                      </div>

                      <button
                        className="absolute top-3 right-3 bg-white p-2 rounded-full shadow-sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigateToVehicleStatus(vehicle.id.toString());
                        }}
                      >
                        <ChevronRight size={18} className="text-[#009639]" />
                      </button>
                    </div>

                    <div className="p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-[#333333] font-semibold">
                          {vehicle?.model?.make?.name} {vehicle.model?.model}
                        </h3>
                        <span className="text-xs text-[#797879]">
                          {vehicle?.manufacturing_year}
                        </span>
                      </div>

                      <div className="space-y-1 mb-3">
                        <div className="flex items-center text-sm text-[#797879]">
                          <span className="font-medium text-[#333333] mr-1">Reg:</span>
                          {vehicle?.vehicle_registration || "Not specified"}
                        </div>
                        <div className="flex items-center text-sm text-[#797879]">
                          <span className="font-medium text-[#333333] mr-1">VIN:</span>
                          <span className="font-mono text-xs">
                            {vehicle?.vin_number ? 
                              `${vehicle.vin_number.substring(0, 8)}...${vehicle.vin_number.substring(vehicle.vin_number.length - 4)}` 
                              : "Not specified"}
                          </span>
                        </div>
                        {vehicle?.color && (
                          <div className="flex items-center text-sm text-[#797879]">
                            <span className="font-medium text-[#333333] mr-1">Color:</span>
                            {vehicle.color}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center mb-3">
                        <Calendar size={16} className="text-[#009639] mr-1" />
                        <span className="text-sm text-[#333333]">
                          <span className="font-medium">0</span> days remaining this month
                        </span>
                      </div>

                      <div className="flex justify-start gap-4 pl-1">
                        <button
                          className="bg-[#009639] text-white py-1 px-4 rounded-full text-sm font-medium flex items-center h-9 shadow-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToBookingCalendar(vehicle.id.toString());
                          }}
                        >
                          <Calendar size={18} className="mr-2" />
                          <span>Book</span>
                        </button>
                        <button
                          className="bg-[#e6ffe6] text-[#009639] py-1 px-4 rounded-full text-sm font-medium flex items-center h-9 shadow-sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            navigateToVehicleHandover(vehicle.id.toString());
                          }}
                        >
                          <RefreshCw size={18} className="mr-2" />
                          <span>Handover</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Maintenance Tab */}
        {activeTab === "maintenance" && (
          <div className="p-4">
            {filteredItems.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
                <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4">
                  <Wrench size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-semibold text-[#333333] mb-2">
                  No upcoming maintenance
                </h3>
                <p className="text-[#797879] mb-6 max-w-sm">
                  All your vehicles are up to date with their maintenance schedule.
                </p>
                <button
                  className="bg-[#009639] text-white py-3 px-6 rounded-full font-medium flex items-center shadow-md hover:bg-[#00732e] transition-colors"
                  onClick={() => console.log("Navigate to add maintenance")}
                >
                  <Wrench size={20} className="mr-2" />
                  Log Maintenance
                </button>
              </div>
            ) : (
              <>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-[#333333] font-medium">Upcoming Maintenance</h3>
                  <button
                    className="text-[#009639] text-sm font-medium hover:underline"
                    onClick={() => navigateToMaintenanceSchedule()}
                  >
                    View All
                  </button>
                </div>

                <div className="space-y-3">
                  {filteredItems.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{item.vehicleName}</h3>
                              <h5>{item.name}</h5>
                              <p className="text-xs text-[#797879]">
                                {item.description}
                              </p>
                            </div>
                            <div className="flex items-center space-x-2 ml-auto">
                              <span
                                className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(item.status)}`}
                              >
                                {getStatusLabel(item.status)}
                              </span>
                              <button
                                className="text-[#009639] text-sm font-medium flex items-center rounded-full px-3 py-1"
                                onClick={() => navigateToMaintenanceDetails(item.id.toString())}
                              >
                                <ChevronRight size={18} />
                              </button>
                            </div>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput(item.due_date)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}

        {/* Documents Tab */}
        {activeTab === "documents" && (
          <div className="p-4">
            {vehicles.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
                <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4">
                  <FileText size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-semibold text-[#333333] mb-2">
                  No vehicles to manage documents for
                </h3>
                <p className="text-[#797879] mb-6 max-w-sm">
                  Add vehicles to your fleet first before you can upload and manage documents.
                </p>
                <button
                  className="bg-[#009639] text-white py-3 px-6 rounded-full font-medium flex items-center shadow-md hover:bg-[#00732e] transition-colors"
                  onClick={() => setListDrawerOpen(true)}
                >
                  <Plus size={20} className="mr-2" />
                  Add Vehicle
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-[#333333] mb-4">
                    Upload Vehicle Documents
                  </h3>
                  <DocPostForm action={addVehicleDocument} vehicles={vehicles} />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Images Tab */}
        {activeTab === "images" && (
          <div className="p-4">
            {vehicles.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full min-h-[400px] text-center">
                <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4">
                  <Camera size={32} className="text-[#009639]" />
                </div>
                <h3 className="text-lg font-semibold text-[#333333] mb-2">
                  No vehicles to manage images for
                </h3>
                <p className="text-[#797879] mb-6 max-w-sm">
                  Add vehicles to your fleet first before you can upload and manage images.
                </p>
                <button
                  className="bg-[#009639] text-white py-3 px-6 rounded-full font-medium flex items-center shadow-md hover:bg-[#00732e] transition-colors"
                  onClick={() => setListDrawerOpen(true)}
                >
                  <Plus size={20} className="mr-2" />
                  Add Vehicle
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
                  <h3 className="text-lg font-semibold text-[#333333] mb-4">
                    Upload Vehicle Images
                  </h3>
                  <ImagePostForm action={addVehicleMedia} vehicles={vehicles} />
                </div>
              </div>
            )}
          </div>
        )}
        </div> {/* End of pb-24 wrapper */}
      </div>

      {/* Add Vehicle Drawer */}
      <AddVehicleDrawer
        isOpen={listDrawerOpen}
        onClose={() => setListDrawerOpen(false)}
        partyId={partyId}
      />
    </div>
  );
} 