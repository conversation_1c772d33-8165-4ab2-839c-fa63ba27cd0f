"use client";

import React, { useState, useEffect } from 'react';
import { 
  ArrowLeft, 
  Car, 
  DollarSign, 
  Settings, 
  Eye, 
  Save, 
  X, 
  Loader,
  CheckCircle,
  AlertTriangle,
  Plus,
  Upload,
  Maximize2,
  Calendar as CalendarIcon
} from 'lucide-react';
import { useNavigation } from '@/hooks/useNavigation';
import { PageWithScroll } from '@/components/ui/scroll-container';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { getListingByIdDrizzle, type ListingRead } from '@/drizzle-actions/listings';
import { getVehicleByIdWithListings } from '@/drizzle-actions/vehicle-dashboard';
import { addVehicleMedia, deleteVehicleMedia } from '@/actions/vehicle-media';
import { updateListing } from '@/actions/listing-management';
import { getUrl, uploadData, remove } from 'aws-amplify/storage';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { DocumentUpload, DocumentDelete } from '@/lib/utils';
import type { VehicleReadWithListings, VehicleMediaRead } from '@/types/vehicles';
import type { ConditionEnum, AudienceEnum, ListingTypeEnum } from '@/types/listings';

// Custom DatePicker component with "Now" link
function DatePickerWithNow({
  value,
  onChange,
  label,
  showNowButton = true,
  minDate,
  maxDate
}: {
  value: string;
  onChange: (value: string) => void;
  label: string;
  showNowButton?: boolean;
  minDate?: Date;
  maxDate?: Date;
}) {
  const [open, setOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    value ? new Date(value) : undefined
  );

  // Update selectedDate when value prop changes
  useEffect(() => {
    if (value) {
      setSelectedDate(new Date(value));
    } else {
      setSelectedDate(undefined);
    }
  }, [value]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onChange(date.toISOString().split('T')[0]);
      setOpen(false);
    }
  };

  const handleNowClick = () => {
    const today = new Date();
    setSelectedDate(today);
    onChange(today.toISOString().split('T')[0]);
    setOpen(false);
  };

  return (
    <div>
      <label className="block text-sm font-medium text-[#333333] mb-1">
        {label}
      </label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start text-left font-normal border-gray-300 hover:border-[#009639] focus:border-[#009639] focus:ring-2 focus:ring-[#009639] focus:ring-opacity-50"
          >
            <CalendarIcon className="mr-2 h-4 w-4 text-gray-400" />
            {selectedDate ? (
              selectedDate.toLocaleDateString()
            ) : (
              <span className="text-gray-500">Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="p-3">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateSelect}
              initialFocus
              fromDate={minDate}
              toDate={maxDate}
              disabled={(date) => {
                if (minDate && date < minDate) return true;
                if (maxDate && date > maxDate) return true;
                return false;
              }}
            />
            {showNowButton && (
              <div className="border-t border-gray-200 pt-2 mt-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleNowClick}
                  className="w-full text-[#009639] hover:text-[#007A2F] hover:bg-[#e6ffe6] font-medium"
                >
                  Now
                </Button>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

// ListingImageCarousel component
function ListingImageCarousel({ 
  images, 
  vehicle, 
  onImagesUpdate 
}: { 
  images: string[]; 
  vehicle?: VehicleReadWithListings;
  onImagesUpdate?: (newImages: string[], newMediaRecords: VehicleMediaRead[]) => void;
}) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [fullScreenIndex, setFullScreenIndex] = useState(0);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => setTouchEnd(e.targetTouches[0].clientX);

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && currentImageIndex < images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
    if (isRightSwipe && currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  };

  useEffect(() => {
    if (isFullScreen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeFullScreen();
      } else if (event.key === 'ArrowLeft' && fullScreenIndex > 0) {
        setFullScreenIndex(fullScreenIndex - 1);
      } else if (event.key === 'ArrowRight' && fullScreenIndex < images.length - 1) {
        setFullScreenIndex(fullScreenIndex + 1);
      }
    };

    if (isFullScreen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFullScreen, fullScreenIndex, images.length]);

  const openFullScreen = (index: number) => {
    setIsFullScreen(true);
    setFullScreenIndex(index);
  };

  const closeFullScreen = () => {
    setIsFullScreen(false);
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !vehicle || !onImagesUpdate) return;
    
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const uploadPromises = files.map(async (file, index) => {
        const uploadResult = await DocumentUpload(file, "vehicleMedia");
        
        if (uploadResult && uploadResult.path) {
          const formData = new FormData();
          formData.append("vehicleID", vehicle.id.toString());
          formData.append("documentUrl", uploadResult.path);
          
          const result = await addVehicleMedia(null, formData);
          if ('success' in result && result.success) {
            setUploadProgress((index + 1) / files.length * 100);
            return uploadResult.path;
          }
        }
        throw new Error("Upload failed");
      });
      
      const uploadedPaths = await Promise.all(uploadPromises);
      
      const newImageUrls = await Promise.all(
        uploadedPaths.map(async (path) => {
          const urlResult = await getUrl({ path });
          return urlResult.url.toString();
        })
      );
      
      onImagesUpdate([...images, ...newImageUrls], []);
      
    } catch (error) {
      console.error("Error uploading images:", error);
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
      e.target.value = "";
    }
  };

  const handleImageDelete = async (index: number) => {
    if (!vehicle?.media?.[index] || !onImagesUpdate) return;
    
    const confirmed = window.confirm("Are you sure you want to delete this image?");
    if (!confirmed) return;

    try {
      const mediaRecord = vehicle.media[index];
      await DocumentDelete(mediaRecord.media_path);
      await deleteVehicleMedia(mediaRecord.id);
      
      const updatedImages = images.filter((_, i) => i !== index);
      const updatedMedia = vehicle.media.filter((_, i) => i !== index);
      onImagesUpdate(updatedImages, updatedMedia);
      
      if (currentImageIndex >= updatedImages.length) {
        setCurrentImageIndex(Math.max(0, updatedImages.length - 1));
      }
      
    } catch (error) {
      console.error("Error deleting image:", error);
    }
  };

  if (images.length === 0) {
    return (
      <div className="h-48 bg-[#f2f2f2] rounded-xl flex flex-col items-center justify-center relative">
        <Upload size={32} className="text-[#797879] mb-2" />
        <p className="text-[#333333] font-medium mb-1">No Vehicle Images</p>
        <p className="text-[#797879] text-sm mb-4">Add images to showcase your vehicle</p>
        
        <input
          type="file"
          className="hidden"
          accept="image/*"
          multiple
          onChange={handleImageUpload}
          id="upload-first-images"
          disabled={isUploading}
        />
        <label 
          htmlFor="upload-first-images"
          className="bg-[#009639] text-white px-4 py-2 rounded-full text-sm cursor-pointer hover:bg-[#007A2F] transition-colors flex items-center disabled:opacity-50"
        >
          <Plus size={16} className="mr-1" />
          Add Images
        </label>

        {isUploading && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white rounded-lg p-2 shadow-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-[#333333]">Uploading...</span>
                <span className="text-xs text-[#333333]">{Math.round(uploadProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-[#009639] h-1 rounded-full transition-all duration-300" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Main carousel */}
      <div className="relative h-48 bg-[#f2f2f2] rounded-xl overflow-hidden">
        <div 
          className="relative h-full cursor-pointer"
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          onClick={() => openFullScreen(currentImageIndex)}
        >
          <img
            src={images[currentImageIndex]}
            alt={`Vehicle image ${currentImageIndex + 1}`}
            className="w-full h-full object-cover"
          />
          
          {/* Edit mode overlay */}
          <div className="absolute top-2 right-2 flex space-x-2">
            <input
              type="file"
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              id="add-images"
              disabled={isUploading}
            />
            <label 
              htmlFor="add-images"
              className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 cursor-pointer transition-all shadow-sm"
            >
              <Plus size={16} className="text-[#009639]" />
            </label>
            
            {images.length > 0 && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleImageDelete(currentImageIndex);
                }}
                className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all shadow-sm"
              >
                <X size={16} className="text-red-500" />
              </button>
            )}
          </div>

          {/* Fullscreen button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              openFullScreen(currentImageIndex);
            }}
            className="absolute bottom-2 right-2 bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 transition-all shadow-sm"
          >
            <Maximize2 size={16} className="text-[#333333]" />
          </button>
        </div>

        {/* Progress indicators */}
        {images.length > 1 && (
          <div className="absolute bottom-2 left-2 flex space-x-1">
            {images.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentImageIndex(index)}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentImageIndex ? 'bg-white' : 'bg-white bg-opacity-50'
                }`}
              />
            ))}
          </div>
        )}

        {/* Upload progress overlay */}
        {isUploading && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white rounded-lg p-2 shadow-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-xs text-[#333333]">Uploading...</span>
                <span className="text-xs text-[#333333]">{Math.round(uploadProgress)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div 
                  className="bg-[#009639] h-1 rounded-full transition-all duration-300" 
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Thumbnail grid */}
      {images.length > 1 && (
        <div className="mt-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-[#333333] font-medium text-sm">All Images ({images.length})</h4>
            <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center">
              <Plus size={14} className="text-white" />
            </div>
          </div>
          
          <div className="grid grid-cols-4 gap-2">
            {images.map((imageUrl, index) => (
              <div 
                key={index} 
                className={`relative aspect-square bg-[#f2f2f2] rounded-lg overflow-hidden cursor-pointer transition-all ${
                  index === currentImageIndex ? 'ring-2 ring-[#009639]' : ''
                }`}
                onClick={() => setCurrentImageIndex(index)}
              >
                <img
                  src={imageUrl}
                  alt={`Vehicle thumbnail ${index + 1}`}
                  className="w-full h-full object-cover"
                />
                <button
                  className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleImageDelete(index);
                  }}
                >
                  <X size={8} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Full-screen modal */}
      {isFullScreen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center"
          onClick={closeFullScreen}
        >
          <div className="relative max-w-screen-lg max-h-screen-lg">
            <img
              src={images[fullScreenIndex]}
              alt={`Vehicle image ${fullScreenIndex + 1}`}
              className="max-w-full max-h-full object-contain"
              onClick={(e) => e.stopPropagation()}
            />
            
            {/* Close button */}
            <button
              onClick={closeFullScreen}
              className="absolute top-4 right-4 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all"
            >
              <X size={24} className="text-white" />
            </button>
            
            {/* Navigation buttons */}
            {images.length > 1 && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (fullScreenIndex > 0) {
                      setFullScreenIndex(fullScreenIndex - 1);
                    }
                  }}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all"
                  disabled={fullScreenIndex === 0}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                    <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    if (fullScreenIndex < images.length - 1) {
                      setFullScreenIndex(fullScreenIndex + 1);
                    }
                  }}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all"
                  disabled={fullScreenIndex === images.length - 1}
                >
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-white">
                    <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
}

// Skeleton components
function VehicleInfoSkeleton() {
  return (
    <div className="space-y-4">
      <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
        <div className="h-4 bg-gray-200 rounded animate-pulse mb-3"></div>
        <div className="space-y-2">
          {[1,2,3,4,5].map(i => (
            <div key={i} className="flex justify-between">
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3"></div>
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function ListingDetailsSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <div className="h-4 bg-gray-200 rounded animate-pulse mb-4"></div>
      <div className="space-y-4">
        {[1,2,3,4,5].map(i => (
          <div key={i}>
            <div className="h-3 bg-gray-200 rounded animate-pulse mb-2 w-1/4"></div>
            <div className="h-8 bg-gray-200 rounded animate-pulse w-full"></div>
          </div>
        ))}
      </div>
    </div>
  );
}

interface ListingManagementScreenProps {
  listing?: ListingRead;
  vehicle?: VehicleReadWithListings;
  params?: { listingId?: string };
}

export default function ListingManagementScreen({ 
  listing: listingProp,
  vehicle: vehicleProp,
  params 
}: ListingManagementScreenProps) {
  const { goBack } = useNavigation();
  
  // State management
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isEditingListing, setIsEditingListing] = useState(false);
  
  // Data state
  const [listing, setListing] = useState<ListingRead | null>(listingProp || null);
  const [vehicle, setVehicle] = useState<VehicleReadWithListings | null>(vehicleProp || null);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  // Form state for listing details
  const [listingForm, setListingForm] = useState({
    askingPrice: "",
    fraction: "",
    effectiveFrom: "",
    effectiveTo: "",
    condition: "used" as ConditionEnum,
    mileage: "",
    audience: "CONSUMER" as AudienceEnum,
    listingType: "CO_OWNERSHIP_SALE" as ListingTypeEnum,
  });

  // Get listing ID from params or props
  const listingId = params?.listingId ? parseInt(params.listingId) : listing?.id;

  // Initial data loading
  useEffect(() => {
    if (!listingProp && listingId && typeof listingId === 'number') {
      const listingIdValue = listingId; // Extract the value to ensure type safety
      
      async function fetchData() {
        setLoading(true);
        try {
          const [listingData, userAttributes] = await Promise.all([
            getListingByIdDrizzle(listingIdValue),
            fetchUserAttributes().catch(() => null)
          ]);

          if (!listingData) {
            setError("Listing not found");
            return;
          }

          setListing(listingData);
          setCurrentUser(userAttributes);

          // Fetch vehicle data
          const vehicleData = await getVehicleByIdWithListings(listingData.vehicle_id);
          if (vehicleData) {
            setVehicle(vehicleData);
          }

          // Initialize form with listing data
          setListingForm({
            askingPrice: listingData.asking_price.toString(),
            fraction: (listingData.fraction * 100).toString(),
            effectiveFrom: listingData.effective_from.split('T')[0],
            effectiveTo: listingData.effective_to?.split('T')[0] || "",
            condition: listingData.condition,
            mileage: listingData.mileage?.toString() || "",
            audience: listingData.audience,
            listingType: listingData.listing_type,
          });

        } catch (error) {
          console.error("Error fetching listing data:", error);
          setError("Failed to load listing data");
        } finally {
          setLoading(false);
        }
      }

      fetchData();
    } else if (listingProp) {
      // Initialize form with prop data
      setListingForm({
        askingPrice: listingProp.asking_price.toString(),
        fraction: (listingProp.fraction * 100).toString(),
        effectiveFrom: listingProp.effective_from.split('T')[0],
        effectiveTo: listingProp.effective_to?.split('T')[0] || "",
        condition: listingProp.condition,
        mileage: listingProp.mileage?.toString() || "",
        audience: listingProp.audience,
        listingType: listingProp.listing_type,
      });
    }
  }, [listingId, listingProp]);

  // Load images when vehicle changes
  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length) return;

      try {
        const urls: string[] = await Promise.all(
          vehicle.media.map(async (item: VehicleMediaRead) => {
            const result = await getUrl({ path: item.media_path });
            return result.url.toString();
          })
        );
        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading vehicle images:", error);
        setImageUrls([]);
      }
    }

    if (vehicle) {
      loadImages();
    }
  }, [vehicle]);

  // Form handlers
  const handleListingFormChange = (field: string, value: string) => {
    setListingForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveListing = async () => {
    if (!listing || !listingId || typeof listingId !== 'number' || saving) return;

    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await updateListing({
        listingId,
        askingPrice: parseFloat(listingForm.askingPrice),
        fraction: listing.listing_type === "CO_OWNERSHIP_SALE" ? parseFloat(listingForm.fraction) / 100 : undefined,
        effectiveFrom: listingForm.effectiveFrom,
        effectiveTo: listingForm.effectiveTo,
        condition: listingForm.condition,
        mileage: listingForm.mileage ? parseFloat(listingForm.mileage) : undefined,
        audience: listingForm.audience,
      });

      if (result.success) {
        setSuccess("Listing updated successfully!");
        if (result.data) {
          setListing(result.data as ListingRead);
        }
      } else {
        setError(result.message);
      }

    } catch (error) {
      console.error("Error updating listing:", error);
      setError("Failed to update listing. Please try again.");
    } finally {
      setSaving(false);
    }
  };



  // Get listing type display name
  const getListingTypeDisplay = (type: ListingTypeEnum) => {
    switch (type) {
      case "SHORT_TERM_LEASE_OUT":
        return "Short-term Rental";
      case "LONG_TERM_LEASE_OUT":
        return "Long-term Lease";
      case "CO_OWNERSHIP_SALE":
        return "Co-ownership Sale";
      default:
        return type;
    }
  };

  // Header component
  const header = (
    <div className="bg-[#009639] px-4 py-3 flex items-center justify-between">
      <div className="flex items-center">
        <button onClick={goBack} className="mr-3">
          <ArrowLeft size={24} className="text-white" />
        </button>
        <div>
          <h1 className="text-white text-lg font-semibold">
            {vehicle ? `${vehicle.model?.make?.name || ''} ${vehicle.model?.model || ''}` : 'Listing Management'}
          </h1>
          {listing && (
            <p className="text-white text-sm opacity-90">
              {getListingTypeDisplay(listing.listing_type)}
            </p>
          )}
        </div>
      </div>
    </div>
  );

  // Show error state
  if (error) {
    return (
      <PageWithScroll
        header={header}
        className="bg-white"
        paddingBottom="pb-32"
      >
        <div className="p-4 text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <AlertTriangle size={24} className="text-red-600 mx-auto mb-2" />
            <p className="text-red-700 font-medium mb-2">Error</p>
            <p className="text-red-600 text-sm">{error}</p>
          </div>
          <button
            onClick={goBack}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
          >
            Go Back
          </button>
        </div>
      </PageWithScroll>
    );
  }



  // Show loading state with skeletons
  if (loading || !listing || !vehicle) {
    return (
      <PageWithScroll
        header={header}
        className="bg-white"
        paddingBottom="pb-32"
      >
        <div className="p-4 space-y-4">
          {/* Image skeleton */}
          <div className="h-48 bg-gray-200 rounded-xl animate-pulse"></div>
          
          {/* Vehicle info skeleton */}
          <VehicleInfoSkeleton />
          
          {/* Listing details skeleton */}
          <ListingDetailsSkeleton />
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll
      header={header}
      className="bg-white"
      paddingBottom="pb-32"
    >
      {/* Success message */}
      {success && (
        <div className="p-4">
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-4">
            <div className="flex items-center">
              <CheckCircle size={16} className="text-green-600 mr-2" />
              <p className="text-green-700 text-sm font-medium">{success}</p>
            </div>
          </div>
        </div>
      )}

      {/* Vehicle Images Section */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-[#333333] font-medium">Vehicle Images</h3>
            <span className="text-[#797879] text-sm">
              {imageUrls.length} image{imageUrls.length !== 1 ? 's' : ''}
            </span>
          </div>
          
          <ListingImageCarousel 
            images={imageUrls} 
            vehicle={vehicle}
            onImagesUpdate={async (newImages) => {
              setImageUrls(newImages);
              
              // Refresh vehicle data to get updated media records
              try {
                const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                if (refreshedVehicle) {
                  setVehicle(refreshedVehicle);
                }
              } catch (error) {
                console.error("Error refreshing vehicle data:", error);
              }
            }}
          />
        </div>
      </div>

      {/* Vehicle Details Section */}
      <div className="p-4">
        {/* Vehicle Status */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Vehicle Status</h3>
            </div>
            <span className="text-xs px-2 py-1 rounded-full bg-[#e6ffe6] text-[#007A2F]">
              Listed for {getListingTypeDisplay(listing.listing_type)}
            </span>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Make & Model:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.model?.make?.name} {vehicle.model?.model}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Registration:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.vehicle_registration || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Year:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.manufacturing_year || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Color:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.color || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">VIN:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.vin_number || 'Not specified'}
              </span>
            </div>
          </div>
        </div>

        {/* Listing Summary */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <DollarSign size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Listing Summary</h3>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`text-xs px-2 py-1 rounded-full ${
               listing.effective_to && new Date(listing.effective_to) > new Date()
                  ? "bg-[#e6ffe6] text-[#007A2F]"
                  : "bg-[#ffe6e6] text-[#cc0000]"
              }`}>
                {listing.effective_to && new Date(listing.effective_to) > new Date() ? "Active" : "Ended"}
              </span>
              {!isEditingListing && (
                <button
                  onClick={() => setIsEditingListing(true)}
                  className="text-[#009639] text-sm font-medium hover:text-[#007A2F] transition-colors"
                >
                  Edit Listing
                </button>
              )}
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Type:</span>
              <span className="text-[#333333] text-sm font-medium">
                {getListingTypeDisplay(listing.listing_type)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Price:</span>
              <span className="text-[#333333] text-sm font-medium">
                R{listing.asking_price.toLocaleString()}
              </span>
            </div>
            {listing.listing_type === "CO_OWNERSHIP_SALE" && (
              <div className="flex justify-between">
                <span className="text-[#797879] text-sm">Ownership Share:</span>
                <span className="text-[#333333] text-sm font-medium">
                  {(listing.fraction * 100).toFixed(1)}%
                </span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Available From:</span>
              <span className="text-[#333333] text-sm font-medium">
                {listing.effective_from ? new Date(listing.effective_from).toLocaleDateString() : ""}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Available Until:</span>
              <span className="text-[#333333] text-sm font-medium">
                {listing.effective_to ? new Date(listing.effective_to).toLocaleDateString() : ""}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Listing Configuration Section */}
      {isEditingListing && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center mb-4">
              <Settings size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Listing Configuration</h3>
            </div>

          <div className="space-y-4">
            {/* Listing Type (read-only) */}
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-1">
                Listing Type
              </label>
              <div className="px-3 py-2 bg-gray-100 rounded-lg text-sm text-[#797879]">
                {getListingTypeDisplay(listing.listing_type)}
              </div>
            </div>

            {/* Asking Price */}
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-1">
                Asking Price (R)
              </label>
              <input
                type="number"
                value={listingForm.askingPrice}
                onChange={(e) => handleListingFormChange("askingPrice", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                placeholder="Enter asking price"
              />
            </div>

            {/* Ownership Fraction (for co-ownership only) */}
            {listing.listing_type === "CO_OWNERSHIP_SALE" && (
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-1">
                  Ownership Share (%)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  step="0.1"
                  value={listingForm.fraction}
                  onChange={(e) => handleListingFormChange("fraction", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  placeholder="Enter ownership percentage"
                />
              </div>
            )}

            {/* Effective Dates */}
            <div className="grid grid-cols-2 gap-4">
              <DatePickerWithNow
                value={listingForm.effectiveFrom}
                onChange={(value) => handleListingFormChange("effectiveFrom", value)}
                label="Available From"
                showNowButton={false}
                minDate={new Date()} // No past dates
              />
              <DatePickerWithNow
                value={listingForm.effectiveTo}
                onChange={(value) => handleListingFormChange("effectiveTo", value)}
                label="Available Until"
                showNowButton={true}
                minDate={listingForm.effectiveFrom ? new Date(listingForm.effectiveFrom) : new Date()} // Can't be before start date
              />
            </div>

            {/* Condition */}
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-1">
                Vehicle Condition
              </label>
              <select
                value={listingForm.condition}
                onChange={(e) => handleListingFormChange("condition", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              >
                <option value="used">Used</option>
                <option value="new">New</option>
              </select>
            </div>

            {/* Mileage */}
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-1">
                Mileage (km)
              </label>
              <input
                type="number"
                value={listingForm.mileage}
                onChange={(e) => handleListingFormChange("mileage", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                placeholder="Enter current mileage"
              />
            </div>

            {/* Target Audience */}
            <div>
              <label className="block text-sm font-medium text-[#333333] mb-1">
                Target Audience
              </label>
              <select
                value={listingForm.audience}
                onChange={(e) => handleListingFormChange("audience", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              >
                <option value="CONSUMER">General Consumer</option>
                <option value="BUSINESS">Business</option>
                <option value="E_HAILING">E-Hailing</option>
              </select>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4 border-t border-gray-200">
              <button
                onClick={() => setIsEditingListing(false)}
                disabled={saving}
                className="flex-1 bg-gray-500 text-white px-4 py-3 rounded-full text-sm font-medium hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                <X size={16} className="mr-2" />
                Cancel
              </button>
              <button
                onClick={async () => {
                  await handleSaveListing();
                  if (!error) {
                    setIsEditingListing(false);
                  }
                }}
                disabled={saving}
                className="flex-1 bg-[#009639] text-white px-4 py-3 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {saving ? (
                  <>
                    <Loader size={16} className="mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Listing Performance (placeholder) */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center mb-3">
            <Eye size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">Listing Performance</h3>
          </div>
          <div className="text-center py-6">
            <div className="text-[#797879] mb-2">
              <Eye size={32} className="mx-auto mb-2 opacity-50" />
            </div>
            <p className="text-[#333333] font-medium mb-1">Performance Metrics</p>
            <p className="text-[#797879] text-sm">Analytics and insights coming soon</p>
          </div>
        </div>
      </div>
      )}
    </PageWithScroll>
  );
} 