"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  Phone,
  Mail,
  MessageSquare,
  PieChart,
  Calendar,
  Car,
  ChevronRight,
  Star,
  Clock,
  MapPin,
  Info,
} from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { formatDateForInput } from "@/lib/utils";

interface MemberDetailsScreenProps {
  params?: { memberId?: string };
}

export default function MemberDetailsScreen({ params }: MemberDetailsScreenProps) {
  const { goBack, navigateToBookingDetails } = useNavigation();
  const memberId = params?.memberId || "1";
  
  const [activeTab, setActiveTab] = useState<'activity' | 'bookings'>('bookings');
  const [loading, setLoading] = useState(true);

  // Mock member data
  const member = {
    id: memberId,
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "+27 82 555 0123",
    profilePic: "/placeholder.svg?height=96&width=96",
    memberSince: new Date('2023-06-15'),
    rating: 4.8,
    ownerships: [
      {
        id: "1",
        companyName: "Downtown Commuters Group",
        fraction: 25,
        vehicle: "Tesla Model 3"
      }
    ],
    contacts: [
      { id: "1", type: "email", value: "<EMAIL>" },
      { id: "2", type: "phone", value: "+27 82 555 0123" },
      { id: "3", type: "address", value: "Cape Town, South Africa" }
    ],
    bookings: [
      {
        id: "1",
        vehicleName: "Tesla Model 3",
        startDate: new Date('2024-01-20T09:00:00'),
        endDate: new Date('2024-01-20T17:00:00'),
        status: "upcoming"
      },
      {
        id: "2", 
        vehicleName: "BMW X3",
        startDate: new Date('2024-01-15T14:00:00'),
        endDate: new Date('2024-01-15T18:00:00'),
        status: "completed"
      }
    ],
    activities: [
      {
        id: "1",
        type: "booking",
        title: "Vehicle Booking Completed",
        description: "Returned Tesla Model 3",
        timestamp: new Date('2024-01-15T18:00:00')
      },
      {
        id: "2",
        type: "payment",
        title: "Payment Processed",
        description: "Monthly contribution paid",
        timestamp: new Date('2024-01-01T10:00:00')
      }
    ]
  };

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const contactTypeIcons: Record<string, JSX.Element> = {
    email: <Mail size={16} className="text-[#009639]" />,
    phone: <Phone size={16} className="text-[#009639]" />,
    address: <MapPin size={16} className="text-[#009639]" />,
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "booking":
        return <Calendar size={18} className="text-[#009639]" />;
      case "payment":
        return <PieChart size={18} className="text-[#009639]" />;
      case "maintenance":
        return <Car size={18} className="text-[#009639]" />;
      default:
        return <Clock size={18} className="text-[#009639]" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={goBack}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">Member Details</h1>
    </div>
  );

  const tabs = (
    <div className="bg-white shadow-sm border-b border-gray-100">
      <div className="flex px-4">
        {[
          { key: 'bookings', label: 'Bookings' },
          { key: 'activity', label: 'Activity' }
        ].map(({ key, label }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as any)}
            className={`flex-1 py-3 px-4 font-medium transition-all ${
              activeTab === key
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-gray-600"
            }`}
          >
            {label}
          </button>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading member details...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} tabs={tabs} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Member Profile */}
      <div className="bg-white px-6 py-6 flex flex-col items-center border-b border-[#f2f2f2]">
        <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-[#e6ffe6] shadow-md mb-3">
          <Image
            src={member.profilePic}
            alt={member.firstName}
            width={96}
            height={96}
            className="object-cover"
          />
        </div>
        <h2 className="text-xl font-bold text-[#333333] mb-1">
          {member.firstName} {member.lastName}
        </h2>
        <div className="flex items-center mb-2">
          <Star size={16} className="text-[#FFD700] mr-1" />
          <span className="text-sm text-[#333333]">{member.rating}</span>
        </div>
        <p className="text-sm text-[#797879] mb-3">
          Member since {formatDateForInput(member.memberSince.toISOString())}
        </p>
        <div className="flex space-x-3">
          {member.contacts.map((contact) => (
            <div key={contact.id} className="p-3 bg-[#f9f9f9] rounded-lg">
              {contactTypeIcons[contact.type] || (
                <Info size={16} className="text-[#009639]" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Ownership */}
      <div className="p-4">
        {member.ownerships.map((ownership) => (
          <div
            key={ownership.id}
            className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100"
          >
            <h3 className="text-[#333333] font-medium mb-3">Ownership</h3>
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="h-4 bg-[#f2f2f2] rounded-full overflow-hidden">
                  <div
                    className="h-full bg-[#009639] rounded-full"
                    style={{ width: `${ownership.fraction}%` }}
                  ></div>
                </div>
              </div>
              <span className="text-lg font-bold text-[#333333] ml-3">
                {ownership.fraction}%
              </span>
            </div>
            <div className="mt-3">
              <p className="text-sm text-[#797879]">
                {ownership.companyName}
              </p>
              <p className="text-sm text-[#333333] font-medium">
                {ownership.vehicle}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Tab Content */}
      <div className="p-4">
        {activeTab === 'bookings' && (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            <div className="p-4 border-b border-gray-100">
              <h3 className="font-semibold text-[#333333]">Bookings ({member.bookings.length})</h3>
            </div>
            {member.bookings.length > 0 ? (
              member.bookings.map((booking, index) => (
                <div
                  key={booking.id}
                  onClick={() => navigateToBookingDetails(booking.id, {
                    id: parseInt(booking.id),
                    reference: `BK-${booking.id}`,
                    vehicle: booking.vehicleName,
                    vehicleName: booking.vehicleName,
                    member: `${member.firstName} ${member.lastName}`,
                    memberFirstName: member.firstName,
                    memberLastName: member.lastName,
                    startDate: booking.startDate.toISOString(),
                    endDate: booking.endDate.toISOString(),
                    start_datetime: booking.startDate.toISOString(),
                    end_datetime: booking.endDate.toISOString(),
                    status: booking.status.toUpperCase(),
                  })}
                  className={`p-4 cursor-pointer hover:bg-gray-50 ${
                    index < member.bookings.length - 1 ? "border-b border-[#f2f2f2]" : ""
                  }`}
                >
                  <div className="flex">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                      <Car size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-[#333333] font-medium">{booking.vehicleName}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                          {booking.status}
                        </span>
                      </div>
                      <p className="text-sm text-[#797879]">
                        {booking.startDate.toLocaleDateString()} • {booking.startDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {booking.endDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                    <ChevronRight size={20} className="text-[#797879] ml-2" />
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center">
                <Calendar size={48} className="text-[#797879] mx-auto mb-4" />
                <p className="text-[#797879]">No bookings found</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100">
            <div className="p-4 border-b border-gray-100">
              <h3 className="font-semibold text-[#333333]">Recent Activity</h3>
            </div>
            {member.activities.length > 0 ? (
              member.activities.map((activity, index) => (
                <div
                  key={activity.id}
                  className={`p-4 ${
                    index < member.activities.length - 1 ? "border-b border-[#f2f2f2]" : ""
                  }`}
                >
                  <div className="flex">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <h4 className="text-[#333333] font-medium mb-1">{activity.title}</h4>
                      <p className="text-sm text-[#797879] mb-1">{activity.description}</p>
                      <p className="text-xs text-[#797879]">
                        {activity.timestamp.toLocaleDateString()} at {activity.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </p>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-6 text-center">
                <Clock size={48} className="text-[#797879] mx-auto mb-4" />
                <p className="text-[#797879]">No recent activity</p>
              </div>
            )}
          </div>
        )}
      </div>
    </PageWithScroll>
  );
} 