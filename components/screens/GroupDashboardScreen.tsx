"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { 
  ArrowLeft, 
  Users, 
  Car, 
  DollarSign, 
  Calendar,
  Settings,
  MessageSquare,
  TrendingUp,
  AlertCircle,
  Plus,
  MoreVertical,
  Star,
  MapPin
} from "lucide-react";

interface GroupDashboardScreenProps {
  params?: { id?: string };
}

export default function GroupDashboardScreen({ params }: GroupDashboardScreenProps) {
  const { goBack, navigateToGroupDetails, navigateToGroupFinances, navigateToGroupSettings, navigateToGroupChat, navigateToMemberManagement, navigateToVehicleDetails } = useNavigation();
  const groupId = params?.id || "1";
  
  const [activeTab, setActiveTab] = useState<'overview' | 'vehicles' | 'members'>('overview');

  // Mock group data
  const group = {
    id: groupId,
    name: "Downtown Commuters",
    description: "A group of professionals sharing vehicles for daily commutes",
    totalMembers: 8,
    activeMembers: 6,
    totalVehicles: 3,
    monthlyRevenue: 2450,
    status: "active",
    createdDate: "2023-10-15",
    owner: {
      id: "user1",
      name: "<PERSON>",
      isCurrentUser: true
    },
    recentActivity: [
      { type: "booking", user: "Sarah M.", vehicle: "Tesla Model 3", time: "2 hours ago" },
      { type: "payment", user: "Mike R.", amount: 150, time: "1 day ago" },
      { type: "join", user: "Emma L.", time: "3 days ago" },
      { type: "maintenance", vehicle: "BMW X3", cost: 200, time: "1 week ago" }
    ]
  };

  const vehicles = [
    {
      id: "v1",
      make: "Tesla",
      model: "Model 3",
      year: 2023,
      status: "available",
      currentBookings: 12,
      monthlyEarnings: 1200,
      location: "Downtown Parking Garage",
      rating: 4.8
    },
    {
      id: "v2", 
      make: "BMW",
      model: "X3",
      year: 2022,
      status: "in-use",
      currentBookings: 8,
      monthlyEarnings: 800,
      location: "Union Station",
      rating: 4.6
    },
    {
      id: "v3",
      make: "Honda",
      model: "Civic",
      year: 2023,
      status: "maintenance",
      currentBookings: 0,
      monthlyEarnings: 450,
      location: "Service Center",
      rating: 4.7
    }
  ];

  const members = [
    { id: "m1", name: "Sarah Mitchell", role: "Co-owner", joinDate: "2023-10-15", status: "active", contributions: 850 },
    { id: "m2", name: "Mike Rodriguez", role: "Member", joinDate: "2023-11-01", status: "active", contributions: 650 },
    { id: "m3", name: "Emma Liu", role: "Member", joinDate: "2023-12-10", status: "active", contributions: 400 },
    { id: "m4", name: "David Chen", role: "Member", joinDate: "2023-10-20", status: "inactive", contributions: 300 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800';
      case 'in-use': return 'bg-blue-100 text-blue-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">{group.name}</h1>
          <button
            onClick={() => navigateToGroupSettings(groupId)}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <Settings size={20} className="text-gray-600" />
          </button>
        </div>
      </div>

      {/* Group Info Banner */}
      <div className="bg-white border-b border-gray-100">
        <div className="p-4">
          <p className="text-sm text-gray-600 mb-3">{group.description}</p>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Users size={16} />
                <span>{group.totalMembers} members</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Car size={16} />
                <span>{group.totalVehicles} vehicles</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => navigateToGroupChat(groupId)}
                className="flex items-center justify-center w-8 h-8 rounded-full bg-[#009639] text-white"
              >
                <MessageSquare size={16} />
              </button>
              <button
                onClick={() => navigateToGroupFinances(groupId)}
                className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white"
              >
                <DollarSign size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex px-4">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'vehicles', label: 'Vehicles' },
            { key: 'members', label: 'Members' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`flex-1 py-3 px-4 font-medium transition-all ${
                activeTab === key
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-gray-600"
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'overview' && (
          <div className="p-4 space-y-4">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                <div className="flex items-center space-x-3">
                  <TrendingUp size={24} className="text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">${group.monthlyRevenue}</p>
                    <p className="text-sm text-gray-600">Monthly Revenue</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                <div className="flex items-center space-x-3">
                  <Calendar size={24} className="text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">28</p>
                    <p className="text-sm text-gray-600">Active Bookings</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => navigateToMemberManagement(groupId)}
                    className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-[#009639] transition-all"
                  >
                    <Plus size={20} className="text-gray-600" />
                    <span className="font-medium text-gray-900">Add Member</span>
                  </button>
                  <button className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg hover:border-[#009639] transition-all">
                    <Car size={20} className="text-gray-600" />
                    <span className="font-medium text-gray-900">Add Vehicle</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {group.recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-2 h-2 rounded-full ${
                          activity.type === 'booking' ? 'bg-blue-500' :
                          activity.type === 'payment' ? 'bg-green-500' :
                          activity.type === 'join' ? 'bg-purple-500' : 'bg-yellow-500'
                        }`} />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {activity.type === 'booking' && `${activity.user} booked ${activity.vehicle}`}
                            {activity.type === 'payment' && `${activity.user} made payment of $${activity.amount}`}
                            {activity.type === 'join' && `${activity.user} joined the group`}
                            {activity.type === 'maintenance' && `${activity.vehicle} maintenance completed ($${activity.cost})`}
                          </p>
                          <p className="text-xs text-gray-500">{activity.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'vehicles' && (
          <div className="p-4 space-y-4">
            {vehicles.map((vehicle) => (
              <div key={vehicle.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg flex items-center justify-center">
                        <Car size={20} className="text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {vehicle.year} {vehicle.make} {vehicle.model}
                        </h3>
                        <div className="flex items-center space-x-2 mt-1">
                          <MapPin size={14} className="text-gray-400" />
                          <span className="text-sm text-gray-600">{vehicle.location}</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => navigateToVehicleDetails(vehicle.id)}
                      className="text-sm text-[#009639] font-medium"
                    >
                      View Details
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-xl font-bold text-gray-900">{vehicle.currentBookings}</p>
                      <p className="text-xs text-gray-600">Bookings</p>
                    </div>
                    <div className="text-center">
                      <p className="text-xl font-bold text-green-600">${vehicle.monthlyEarnings}</p>
                      <p className="text-xs text-gray-600">Monthly</p>
                    </div>
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-1">
                        <Star size={14} className="text-yellow-400 fill-current" />
                        <span className="text-sm font-semibold text-gray-900">{vehicle.rating}</span>
                      </div>
                      <p className="text-xs text-gray-600">Rating</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(vehicle.status)}`}>
                      {vehicle.status.charAt(0).toUpperCase() + vehicle.status.slice(1).replace('-', ' ')}
                    </span>
                    <button className="text-gray-400">
                      <MoreVertical size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'members' && (
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Group Members</h3>
              <button
                onClick={() => navigateToMemberManagement(groupId)}
                className="flex items-center space-x-2 px-4 py-2 bg-[#009639] text-white rounded-lg text-sm font-medium"
              >
                <Plus size={16} />
                <span>Add Member</span>
              </button>
            </div>
            
            {members.map((member) => (
              <div key={member.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
                <div className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{member.name}</h3>
                        <p className="text-sm text-gray-600">{member.role}</p>
                        <p className="text-xs text-gray-500">Joined {new Date(member.joinDate).toLocaleDateString()}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">${member.contributions}</p>
                      <p className="text-xs text-gray-600">Contributed</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${getStatusColor(member.status)}`}>
                        {member.status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Floating Action Button */}
      <div className="absolute bottom-20 right-4">
        <button
          onClick={() => navigateToGroupChat(groupId)}
          className="w-14 h-14 bg-[#009639] text-white rounded-full shadow-lg flex items-center justify-center"
        >
          <MessageSquare size={24} />
        </button>
      </div>
    </div>
  );
} 