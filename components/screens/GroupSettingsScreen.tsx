"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { 
  ArrowLeft, 
  Settings,
  Users,
  Shield,
  Bell,
  Trash2,
  Edit,
  Eye,
  EyeOff,
  Globe,
  Lock,
  Crown,
  AlertTriangle,
  Save
} from "lucide-react";

interface GroupSettingsScreenProps {
  params?: { groupId?: string };
}

export default function GroupSettingsScreen({ params }: GroupSettingsScreenProps) {
  const { goBack, navigateToGroupDetails } = useNavigation();
  const groupId = params?.groupId || "1";
  
  const [activeTab, setActiveTab] = useState<'general' | 'privacy' | 'members' | 'danger'>('general');
  
  // Form states
  const [groupName, setGroupName] = useState("Downtown Commuters");
  const [groupDescription, setGroupDescription] = useState("A professional car sharing group for downtown Toronto commuters.");
  const [groupType, setGroupType] = useState<'personal' | 'professional' | 'commercial'>('professional');
  const [privacy, setPrivacy] = useState<'public' | 'private' | 'invite-only'>('private');
  const [allowNewMembers, setAllowNewMembers] = useState(true);
  const [requireApproval, setRequireApproval] = useState(true);
  const [notifications, setNotifications] = useState({
    newBookings: true,
    payments: true,
    maintenance: true,
    chat: false
  });
  
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleSaveSettings = () => {
    // Save settings logic
    console.log("Saving settings...");
  };

  const handleDeleteGroup = () => {
    // Delete group logic
    console.log("Deleting group...");
    navigateToGroupDetails(groupId);
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Group Settings</h1>
          <button
            onClick={handleSaveSettings}
            className="flex items-center space-x-2 px-3 py-2 bg-[#009639] text-white rounded-lg text-sm font-medium"
          >
            <Save size={16} />
            <span>Save</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex px-4">
          {[
            { key: 'general', label: 'General', icon: Settings },
            { key: 'privacy', label: 'Privacy', icon: Shield },
            { key: 'members', label: 'Members', icon: Users },
            { key: 'danger', label: 'Danger Zone', icon: AlertTriangle }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`flex-1 flex items-center justify-center space-x-2 py-3 px-2 font-medium transition-all ${
                activeTab === key
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-gray-600"
              }`}
            >
              <Icon size={16} />
              <span className="text-sm">{label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'general' && (
          <div className="p-4 space-y-4">
            {/* Group Information */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Group Information</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Group Name
                    </label>
                    <input
                      type="text"
                      value={groupName}
                      onChange={(e) => setGroupName(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                      placeholder="Enter group name"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={groupDescription}
                      onChange={(e) => setGroupDescription(e.target.value)}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                      placeholder="Describe your group"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Group Type
                    </label>
                    <select
                      value={groupType}
                      onChange={(e) => setGroupType(e.target.value as any)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                    >
                      <option value="personal">Personal</option>
                      <option value="professional">Professional</option>
                      <option value="commercial">Commercial</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Notifications */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Notifications</h3>
                
                <div className="space-y-4">
                  {Object.entries(notifications).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-sm text-gray-600">
                          Receive notifications for {key.toLowerCase()}
                        </p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={value}
                          onChange={(e) => setNotifications({
                            ...notifications,
                            [key]: e.target.checked
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'privacy' && (
          <div className="p-4 space-y-4">
            {/* Privacy Settings */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Privacy Settings</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Group Visibility
                    </label>
                    <div className="space-y-3">
                      {[
                        { value: 'public', label: 'Public', desc: 'Anyone can find and join this group', icon: Globe },
                        { value: 'private', label: 'Private', desc: 'Only invited members can join', icon: Lock },
                        { value: 'invite-only', label: 'Invite Only', desc: 'Members can only be added by owners', icon: Shield }
                      ].map(({ value, label, desc, icon: Icon }) => (
                        <label key={value} className="flex items-start space-x-3 cursor-pointer">
                          <input
                            type="radio"
                            name="privacy"
                            value={value}
                            checked={privacy === value}
                            onChange={(e) => setPrivacy(e.target.value as any)}
                            className="mt-1"
                          />
                          <div className="flex items-start space-x-3">
                            <Icon size={20} className="text-gray-600 mt-0.5" />
                            <div>
                              <p className="font-medium text-gray-900">{label}</p>
                              <p className="text-sm text-gray-600">{desc}</p>
                            </div>
                          </div>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Member Permissions */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Member Permissions</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Allow New Members</p>
                      <p className="text-sm text-gray-600">Members can invite new people to join</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={allowNewMembers}
                        onChange={(e) => setAllowNewMembers(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">Require Approval</p>
                      <p className="text-sm text-gray-600">New members need owner approval</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={requireApproval}
                        onChange={(e) => setRequireApproval(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="p-4 space-y-4">
            {/* Member Roles */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Member Roles</h3>
                
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    <Crown size={20} className="text-yellow-500" />
                    <div>
                      <p className="font-medium text-gray-900">Owner</p>
                      <p className="text-sm text-gray-600">Full access to all group settings and finances</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    <Crown size={20} className="text-orange-500" />
                    <div>
                      <p className="font-medium text-gray-900">Co-owner</p>
                      <p className="text-sm text-gray-600">Can manage members and vehicles, limited financial access</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                    <Users size={20} className="text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">Member</p>
                      <p className="text-sm text-gray-600">Can book vehicles and participate in group chat</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Pending Invitations */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Pending Invitations</h3>
                
                <div className="text-center py-8">
                  <Users size={48} className="text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">No pending invitations</p>
                  <button className="mt-2 px-4 py-2 bg-[#009639] text-white rounded-lg text-sm font-medium">
                    Invite Members
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'danger' && (
          <div className="p-4 space-y-4">
            {/* Warning */}
            <div className="bg-red-50 border border-red-200 rounded-xl">
              <div className="p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle size={20} className="text-red-600 mt-1" />
                  <div>
                    <h4 className="font-medium text-red-900">Danger Zone</h4>
                    <p className="text-sm text-red-700 mt-1">
                      These actions cannot be undone. Please proceed with caution.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Transfer Ownership */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Transfer Ownership</h3>
                <p className="text-gray-600 mb-4">
                  Transfer ownership of this group to another member. You will become a regular member.
                </p>
                <button className="w-full py-3 bg-yellow-500 text-white rounded-lg font-medium hover:bg-yellow-600 transition-all">
                  Transfer Ownership
                </button>
              </div>
            </div>

            {/* Leave Group */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Leave Group</h3>
                <p className="text-gray-600 mb-4">
                  Leave this group. As the owner, you must transfer ownership first.
                </p>
                <button 
                  disabled
                  className="w-full py-3 bg-gray-300 text-gray-500 rounded-lg font-medium cursor-not-allowed"
                >
                  Leave Group (Transfer ownership first)
                </button>
              </div>
            </div>

            {/* Delete Group */}
            <div className="bg-white rounded-xl shadow-sm border border-red-200">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-red-900 mb-2">Delete Group</h3>
                <p className="text-gray-600 mb-4">
                  Permanently delete this group. All data, messages, and history will be lost forever.
                </p>
                <button 
                  onClick={() => setShowDeleteConfirm(true)}
                  className="w-full py-3 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-all"
                >
                  Delete Group
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-sm w-full p-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Trash2 size={24} className="text-red-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Group?</h3>
              <p className="text-gray-600 mb-6">
                This action cannot be undone. All group data will be permanently deleted.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteGroup}
                  className="flex-1 py-3 bg-red-500 text-white rounded-lg font-medium"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 