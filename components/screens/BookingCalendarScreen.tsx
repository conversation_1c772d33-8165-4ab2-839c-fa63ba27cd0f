"use client";

import React, {
  useState,
  useActionState,
  startTransition,
  useEffect,
} from "react";
import {
  <PERSON>Left,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
} from "lucide-react";
import { deprecated_BookingRead } from "@/types/bookings";
import { VehicleRead } from "@/types/vehicles";
import { v4 as uuidv4 } from "uuid";
import { deprecated_BookingStatus } from "@/types/bookings";
import { useNavigation } from "@/hooks/useNavigation";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { deprecated_getVehicleBookingsWithDetails, deprecated_addBookingDrizzle } from "@/drizzle-actions/bookings";
import { getVehicleByIdDrizzle } from "@/drizzle-actions/vehicle-dashboard";
import { fetchUserAttributes } from "aws-amplify/auth";

interface BookingCalendarScreenProps {
  vehicleId?: string;
  params?: { vehicleId: string };
}

// Form Submit Component
function FormSubmit({ selectedDates }: { selectedDates: Date[] }) {
  return (
    <div className="px-4 pb-6">
      <button
        type="submit"
        disabled={selectedDates.length === 0}
        className={`w-full py-4 rounded-full font-medium text-white transition-colors ${
          selectedDates.length > 0
            ? "bg-[#009639] hover:bg-[#007A2F]"
            : "bg-gray-300 cursor-not-allowed"
        }`}
      >
        {selectedDates.length > 0
          ? `Confirm Booking (${selectedDates.length} ${selectedDates.length === 1 ? 'day' : 'days'})`
          : "Select dates to book"
        }
      </button>
    </div>
  );
}

export default function BookingCalendarScreen({ vehicleId, params }: BookingCalendarScreenProps) {
  const { goBack, navigateToBookingConfirmation } = useNavigation();
  const { user } = useAuthenticator((context) => [context.user]);

  // Extract vehicleId from either direct prop or params
  const actualVehicleId = vehicleId || params?.vehicleId;

  // Early validation
  if (!actualVehicleId) {
    return (
      <div className="h-screen flex flex-col bg-gray-50">
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F] flex-shrink-0">
          <button className="mr-4" onClick={() => goBack()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Booking Calendar</h1>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-600 mb-2">Invalid Vehicle ID</h3>
            <p className="text-gray-600">No vehicle ID provided</p>
          </div>
        </div>
      </div>
    );
  }

  // Local state for fetched data
  const [bookingsx, setBookingsx] = useState<deprecated_BookingRead[]>([]);
  const [vehicle, setVehicle] = useState<VehicleRead | null>(null);
  const [partyId, setPartyId] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [bookingDuration, setBookingDuration] = useState(1);
  const [showConflictWarning, setShowConflictWarning] = useState(false);
  const [state, formAction] = useActionState(deprecated_addBookingDrizzle, { success: false, errors: { general: [] } });
  const [selectionMode, setSelectionMode] = useState<"individual" | "range">("range");
  const [selectedBookingDetails, setSelectedBookingDetails] = useState<deprecated_BookingRead | null>(null);

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get user attributes
        const attributes = await fetchUserAttributes();
        const { ["custom:db_id"]: dbId } = attributes || {};
        
        if (!dbId) {
          setError("User not authenticated");
          return;
        }

        const vehicleIdNum = parseInt(actualVehicleId);
        
        if (isNaN(vehicleIdNum)) {
          setError("Invalid vehicle ID format");
          return;
        }

        setPartyId(parseInt(dbId));

        // Fetch vehicle and bookings data
        const [vehicleData, bookingsData] = await Promise.all([
          getVehicleByIdDrizzle(vehicleIdNum),
          deprecated_getVehicleBookingsWithDetails(vehicleIdNum)
        ]);

        if (!vehicleData) {
          setError("Vehicle not found");
          return;
        }

        setVehicle(vehicleData);
        setBookingsx(bookingsData);
      } catch (err) {
        console.error("Error fetching booking calendar data:", err);
        setError("Failed to load booking data");
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchData();
    }
  }, [actualVehicleId, user]);

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    return new Date(year, month, 1).getDay();
  };

  const daysInMonth = getDaysInMonth(currentMonth);
  const firstDayOfMonth = getFirstDayOfMonth(currentMonth);
  const monthName = currentMonth.toLocaleString("default", { month: "long" });
  const year = currentMonth.getFullYear();

  const bookings = React.useMemo(() => {
    const bookingsMap: Record<number, { status: string; memberId?: string; booking: deprecated_BookingRead }> = {};

    console.log(`Processing ${bookingsx.length} bookings for ${monthName} ${year}:`, bookingsx);

    bookingsx.forEach((booking) => {
      const startDate = new Date(booking.start_datetime);
      const endDate = new Date(booking.end_datetime);

      console.log(`Booking ${booking.reference}:`, {
        status: booking.status,
        statusType: typeof booking.status,
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      });

      // Define current month boundaries
      const monthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
      const monthEnd = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
      monthEnd.setHours(23, 59, 59, 999);

      // Check if booking overlaps with current month
      const overlapsMonth = startDate <= monthEnd && endDate >= monthStart;

      console.log(`Booking ${booking.reference} overlaps ${monthName}:`, overlapsMonth);

      if (overlapsMonth) {
        // Find the range of days in current month that are booked
        const rangeStart = new Date(Math.max(startDate.getTime(), monthStart.getTime()));
        const rangeEnd = new Date(Math.min(endDate.getTime(), monthEnd.getTime()));
        
        console.log(`Marking days from ${rangeStart.getDate()} to ${rangeEnd.getDate()}`);
        
        let date = new Date(rangeStart);
        while (date <= rangeEnd) {
          const day = date.getDate();
          console.log(`Marking day ${day} as ${booking.status}`);
          bookingsMap[day] = {
            status: booking.status,
            memberId: booking.party_id.toString(),
            booking: booking,
          };
          date.setDate(date.getDate() + 1);
        }
      }
    });

    console.log(`Final bookings map for ${monthName}:`, bookingsMap);
    return bookingsMap;
  }, [bookingsx, currentMonth, monthName, year]);
  
  const isPastDate = (day: number) => {
    const today = new Date();
    const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    
    // Set time to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    dayDate.setHours(0, 0, 0, 0);
    
    return dayDate < today;
  };

  const getDayStatus = (day: number) => {
    // First check if there's a booking for this day
    const bookingStatus = bookings[day as keyof typeof bookings];
    
    if (bookingStatus) {
      // If there's a booking, that status takes priority over past date
      return bookingStatus;
    }
    
    // Only check past date if there's no booking
    if (isPastDate(day)) {
      return { status: "past", memberId: undefined, booking: undefined };
    }
    
    return { status: "available", memberId: undefined, booking: undefined };
  };

  // Helper function to check if a day is selected
  const isDaySelected = (day: number) => {
    const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    dayDate.setHours(0, 0, 0, 0); // Normalize time for comparison
    return selectedDates.some(selectedDate => {
      const normalizedSelected = new Date(selectedDate);
      normalizedSelected.setHours(0, 0, 0, 0);
      return normalizedSelected.getTime() === dayDate.getTime();
    });
  };

  // Enhanced day class with range selection visual feedback
  const getDayClass = (day: number) => {
    const status = getDayStatus(day).status;
    const isSelected = isDaySelected(day);
    
    // For range mode, check if day is within the selected range
    const isInRange = selectionMode === "range" && selectedDates.length >= 2 && (() => {
      const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      dayDate.setHours(0, 0, 0, 0);
      const sortedDates = [...selectedDates].sort((a, b) => a.getTime() - b.getTime());
      const startDate = new Date(sortedDates[0]);
      const endDate = new Date(sortedDates[sortedDates.length - 1]);
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0);
      return dayDate >= startDate && dayDate <= endDate;
    })();
    
    const isRangeStart = isSelected && selectedDates.length > 0 && (() => {
      const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      dayDate.setHours(0, 0, 0, 0);
      const sortedDates = [...selectedDates].sort((a, b) => a.getTime() - b.getTime());
      const startDate = new Date(sortedDates[0]);
      startDate.setHours(0, 0, 0, 0);
      return dayDate.getTime() === startDate.getTime();
    })();
    
    const isRangeEnd = isSelected && selectedDates.length > 0 && (() => {
      const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
      dayDate.setHours(0, 0, 0, 0);
      const sortedDates = [...selectedDates].sort((a, b) => a.getTime() - b.getTime());
      const endDate = new Date(sortedDates[sortedDates.length - 1]);
      endDate.setHours(0, 0, 0, 0);
      return dayDate.getTime() === endDate.getTime();
    })();

    const baseClass =
      "h-10 w-10 rounded-full flex items-center justify-center text-sm transition-colors ";

    if (status !== "available") {
      // Unavailable dates - handle both enum and string values
      switch (status) {
        case "past":
          return baseClass + "bg-gray-100 text-gray-400 cursor-not-allowed";
        case "Confirmed":
        case "Pending": 
        case "Completed":
        case "CONFIRMED":
        case "PENDING":
        case "COMPLETED":
          return baseClass + "bg-[#ffe6e6] text-[#d32f2f] font-medium cursor-pointer"; // Changed to pointer for booking details
        case "Cancelled":
        case "CANCELLED":
        default:
          return baseClass + "text-[#333333] bg-white hover:bg-[#e6ffe6] cursor-pointer";
      }
    }

    // Available dates
    if (isRangeStart || isRangeEnd) {
      return baseClass + "bg-[#009639] text-white font-semibold cursor-pointer";
    } else if (isSelected || isInRange) {
      return baseClass + "bg-[#009639] text-white cursor-pointer";
    } else {
      return baseClass + "text-[#333333] bg-white hover:bg-[#e6ffe6] cursor-pointer";
    }
  };

  const handlePrevMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    );
    setShowConflictWarning(false);
  };

  const handleNextMonth = () => {
    setCurrentMonth(
      new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 1)
    );
    setShowConflictWarning(false);
  };

  const handleDayClick = (day: number) => {
    const dayStatus = getDayStatus(day);
    const status = dayStatus.status;
    const dayDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), day);
    dayDate.setHours(0, 0, 0, 0);

    console.log(`Clicked day ${day}, status:`, status, 'dayStatus:', dayStatus);

    // If clicking on a booked date, show booking details
    if (dayStatus.booking && (status === "Confirmed" || status === "Pending" || status === "Completed")) {
      console.log('Showing booking details for:', dayStatus.booking);
      setSelectedBookingDetails(dayStatus.booking);
      return;
    }

    // Prevent clicking on past dates
    if (status !== "available") {
      console.log('Setting conflict warning for status:', status);
      setShowConflictWarning(true);
      return;
    }

    setShowConflictWarning(false);

    if (selectionMode === "individual") {
      // Individual date selection mode
      if (isDaySelected(day)) {
        setSelectedDates(selectedDates.filter((d) => d.getTime() !== dayDate.getTime()));
      } else {
        setSelectedDates([...selectedDates, dayDate].sort((a, b) => a.getTime() - b.getTime()));
      }
    } else {
      // Range selection mode
      if (selectedDates.length === 0) {
        // First click - select start date
        setSelectedDates([dayDate]);
      } else if (selectedDates.length === 1) {
        // Second click - create range
        const startDate = selectedDates[0];
        if (dayDate.getTime() === startDate.getTime()) {
          // Clicking same date - deselect
          setSelectedDates([]);
        } else {
          // Create range from start to end
          const start = new Date(Math.min(startDate.getTime(), dayDate.getTime()));
          const end = new Date(Math.max(startDate.getTime(), dayDate.getTime()));
          const range: Date[] = [];
          
          // Check if any dates in range are unavailable
          let hasConflict = false;
          const current = new Date(start);
          while (current <= end) {
            const currentDay = current.getDate();
            const currentStatus = getDayStatus(currentDay).status;
            
            if (currentStatus !== "available") {
              hasConflict = true;
              break;
            }
            range.push(new Date(current));
            current.setDate(current.getDate() + 1);
          }
          
          if (hasConflict) {
            setShowConflictWarning(true);
            setSelectedDates([startDate]); // Keep only the start date
          } else {
            setSelectedDates(range);
          }
        }
      } else {
        // Already have a range - start new selection
        setSelectedDates([dayDate]);
      }
    }
  };

  const clearSelection = () => {
    setSelectedDates([]);
    setShowConflictWarning(false);
  };

  const handleBookingDurationChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setBookingDuration(Number(e.target.value));
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    if (selectedDates.length === 0) return;

    // Validate that no past dates or already booked dates are selected
    const hasInvalidDates = selectedDates.some(date => {
      const day = date.getDate();
      const status = getDayStatus(day).status;
      return status !== "available";
    });

    if (hasInvalidDates) {
      setShowConflictWarning(true);
      return;
    }

    const sortedDates = [...selectedDates].sort((a, b) => a.getTime() - b.getTime());

    const startDate = new Date(sortedDates[0]);
    const endDate = new Date(sortedDates[sortedDates.length - 1]);

    endDate.setHours(23, 59, 59, 999);
    formData.set("vehicle_id", actualVehicleId);
    formData.set("start_datetime", startDate.toISOString());
    formData.set("end_datetime", endDate.toISOString());
    formData.set("party_id", partyId.toString());
    formData.set("status", deprecated_BookingStatus.CONFIRMED);
    formData.set("reference", uuidv4());

    startTransition(() => {
      formAction(formData);
    });
  };
  
  useEffect(() => {
    if (state?.success && state?.bookingID) {
      navigateToBookingConfirmation(state.bookingID.toString());
    }
  }, [state, navigateToBookingConfirmation]);

  // Show loading state
  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="h-screen flex flex-col bg-gray-50">
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F] flex-shrink-0">
          <button className="mr-4" onClick={() => goBack()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Booking Calendar</h1>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-red-600 mb-2">Error Loading Data</h3>
            <p className="text-gray-600">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F] flex-shrink-0">
        <button className="mr-4" onClick={() => goBack()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">
          Book {vehicle?.model?.model || 'Vehicle'}
        </h1>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="pb-24"> {/* Extra padding for bottom navigation */}
          
          {/* Selection Mode Toggle */}
          <div className="bg-white px-6 py-3 border-b border-[#f2f2f2]">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-[#333333]">Selection Mode:</span>
              <div className="flex bg-[#f2f2f2] rounded-full p-1">
                <button
                  type="button"
                  className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                    selectionMode === "range"
                      ? "bg-[#009639] text-white"
                      : "text-[#797879] hover:text-[#333333]"
                  }`}
                  onClick={() => {
                    setSelectionMode("range");
                    clearSelection();
                  }}
                >
                  Date Range
                </button>
                <button
                  type="button"
                  className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${
                    selectionMode === "individual"
                      ? "bg-[#009639] text-white"
                      : "text-[#797879] hover:text-[#333333]"
                  }`}
                  onClick={() => {
                    setSelectionMode("individual");
                    clearSelection();
                  }}
                >
                  Individual Dates
                </button>
              </div>
            </div>
            {selectionMode === "range" && (
              <p className="text-xs text-[#797879] mt-1">
                Click start date, then end date to select a range
              </p>
            )}
            {selectionMode === "individual" && (
              <p className="text-xs text-[#797879] mt-1">
                Click multiple dates to select individual days
              </p>
            )}
          </div>

          {/* Month Navigation */}
          <div className="p-4 flex items-center justify-between bg-white border-b border-[#f2f2f2]">
            <button
              className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-sm"
              onClick={handlePrevMonth}
            >
              <ChevronLeft size={20} className="text-[#009639]" />
            </button>
            <h2 className="text-lg font-semibold text-[#333333]">
              {monthName} {year}
            </h2>
            <button
              className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center shadow-sm"
              onClick={handleNextMonth}
            >
              <ChevronRight size={20} className="text-[#009639]" />
            </button>
          </div>

          {/* Calendar */}
          <div className="p-4 bg-white ">
            {/* Weekdays */}
            <div className="grid grid-cols-7 mb-2">
              {["S", "M", "T", "W", "T", "F", "S"].map((day, index) => (
                <div
                  key={index}
                  className="text-center text-[#797879] text-sm font-medium"
                >
                  {day}
                </div>
              ))}
            </div>

            {/* Days */}
            <div className="grid grid-cols-7 gap-1">
              {/* Empty cells for days before the first day of month */}
              {Array.from({ length: firstDayOfMonth }).map((_, index) => (
                <div key={`empty-${index}`} className="h-10"></div>
              ))}

              {/* Actual days */}
              {Array.from({ length: daysInMonth }).map((_, index) => {
                const day = index + 1;

                return (
                  <div key={day} className="flex justify-center items-center">
                    <button
                      className={getDayClass(day)}
                      onClick={() => handleDayClick(day)}
                    >
                      {day}
                    </button>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Legend */}
          <div className="p-4 bg-white border-t border-b border-[#f2f2f2] drop-shadow-sm">
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-[#009639] mr-1"></div>
                <span className="text-xs text-[#333333]">Selected</span>
              </div>
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-white border border-[#d6d9dd] mr-1"></div>
                <span className="text-xs text-[#333333]">Available</span>
              </div>
              <div className="flex items-center">
                <div className="h-4 w-4 rounded-full bg-[#ffe6e6] mr-1"></div>
                <span className="text-xs text-[#333333]">Booked</span>
              </div>
            </div>
          </div>

          {/* Conflict Warning */}
          {showConflictWarning && (
            <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg flex items-start shadow-sm">
              <AlertCircle
                size={20}
                className="text-yellow-500 mr-2 flex-shrink-0 mt-0.5"
              />
              <div>
                <p className="text-sm text-yellow-700 font-medium">
                  Booking Conflict
                </p>
                <p className="text-xs text-yellow-600">
                  {selectionMode === "range" 
                    ? "The selected range contains unavailable dates (past dates or already booked). Please select a different range."
                    : "This date is unavailable (past date or already booked). Please select a different date."
                  }
                </p>
              </div>
            </div>
          )}

          {/* Booking Details Form */}
          <form onSubmit={handleSubmit}>
            {state?.errors && (
              <div className="bg-red-100 text-red-700 p-4 rounded mx-4 mt-4">
                <ul>
                  {Object.entries(state.errors).map(([field, messages]) =>
                    Array.isArray(messages)
                      ? messages.map((msg, i) => <li key={`${field}-${i}`}>{msg}</li>)
                      : null
                  )}
                </ul>
              </div>
            )}
            {state?.message && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4 text-sm mx-4 mt-4">
                {state?.message}
              </div>
            )}
            <div className="p-4">
              <div className="ride-card p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-[#333333] font-medium">
                    {selectionMode === "range" ? "Selected Date Range" : "Selected Dates"}
                  </label>
                  {selectedDates.length > 0 && (
                    <button
                      type="button"
                      onClick={clearSelection}
                      className="text-xs text-[#797879] hover:text-[#333333] underline"
                    >
                      Clear Selection
                    </button>
                  )}
                </div>
                
                {selectedDates.length > 0 ? (
                  <div>
                    {selectionMode === "range" && selectedDates.length >= 2 ? (
                      <div className="bg-[#e6ffe6] text-[#009639] px-4 py-3 rounded-lg border border-[#009639]/20">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium">
                              {selectedDates[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - {selectedDates[selectedDates.length - 1].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            </p>
                            <p className="text-xs mt-1">
                              {selectedDates.length} {selectedDates.length === 1 ? 'day' : 'days'} selected
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-2">
                        {selectedDates.map((date, index) => (
                          <div
                            key={date.getTime()}
                            className="bg-[#e6ffe6] text-[#009639] px-3 py-1 rounded-full text-sm shadow-sm border border-gray-100 flex items-center"
                          >
                            {date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                            {selectionMode === "individual" && (
                              <button
                                type="button"
                                onClick={() => setSelectedDates(selectedDates.filter(d => d.getTime() !== date.getTime()))}
                                className="ml-2 text-[#009639] hover:text-[#007A2F]"
                              >
                                ×
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <p className="text-[#797879] text-sm">
                    {selectionMode === "range" 
                      ? "Select start and end dates for your booking"
                      : "Select individual dates for your booking"
                    }
                  </p>
                )}
              </div>
            </div>

            {/* Confirm Button */}
            <FormSubmit selectedDates={selectedDates} />
          </form>

        </div>
      </div>

      {/* Booking Details Modal */}
      {selectedBookingDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full mx-4 shadow-lg">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Booking Details</h3>
                <button
                  onClick={() => setSelectedBookingDetails(null)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>
            
            <div className="p-4 space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <p className={`text-sm font-medium ${
                  selectedBookingDetails.status === deprecated_BookingStatus.CONFIRMED ? 'text-green-600' :
                  selectedBookingDetails.status === deprecated_BookingStatus.PENDING ? 'text-yellow-600' :
                  selectedBookingDetails.status === deprecated_BookingStatus.COMPLETED ? 'text-blue-600' :
                  'text-gray-600'
                }`}>
                  {selectedBookingDetails.status}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Booking Period</label>
                <p className="text-sm text-gray-900">
                  {new Date(selectedBookingDetails.start_datetime).toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    month: 'short', 
                    day: 'numeric',
                    year: 'numeric'
                  })} - {new Date(selectedBookingDetails.end_datetime).toLocaleDateString('en-US', { 
                    weekday: 'short', 
                    month: 'short', 
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Booked by</label>
                <p className="text-sm text-gray-900">User ID: {selectedBookingDetails.party_id}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-500">Reference</label>
                <p className="text-sm text-gray-900 font-mono">{selectedBookingDetails.reference}</p>
              </div>

              {selectedBookingDetails.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Notes</label>
                  <p className="text-sm text-gray-900">{selectedBookingDetails.notes}</p>
                </div>
              )}
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button
                onClick={() => setSelectedBookingDetails(null)}
                className="w-full bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 