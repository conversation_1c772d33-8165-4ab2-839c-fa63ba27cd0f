"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Check, Car, MapPin, Calendar, Clock, CreditCard, Shield, Users } from "lucide-react";

interface BookingConfirmationScreenProps {
  params?: { bookingId?: string, isBookingConfirmed?: boolean };
}

export default function BookingConfirmationScreen({ params }: BookingConfirmationScreenProps) {
  const { goBack, navigateToBookingDetails, navigateToHome } = useNavigation();
  const bookingId = params?.bookingId || "default_booking";
  
  const [isConfirming, setIsConfirming] = useState(false);
  const [isConfirmed, setIsConfirmed] = useState(params?.isBookingConfirmed || true);

  // Parse booking data from ID (in real app, this would come from API)
  const parseBookingData = (id: string) => {
    const parts = id.split('_');
    return {
      vehicleId: parts[0] || "1",
      date: parts[1] || new Date().toISOString().split('T')[0],
      time: parts[2] || "09:00",
      durationType: parts[3] || "hourly",
      duration: parseInt(parts[4]) || 4
    };
  };

  const bookingData = parseBookingData(bookingId);

  // Mock data
  const vehicle = {
    id: bookingData.vehicleId,
    make: "Tesla",
    model: "Model 3",
    year: 2023,
    location: "Downtown Toronto",
    pricePerHour: 25,
    pricePerDay: 180,
    pricePerWeek: 1200,
    image: "/images/cars/tesla-model-3.jpg",
    licensePlate: "ABC 123",
    features: ["Electric", "Autopilot", "Premium Interior"]
  };

  const calculatePrice = () => {
    switch (bookingData.durationType) {
      case 'hourly': return vehicle.pricePerHour * bookingData.duration;
      case 'daily': return vehicle.pricePerDay * bookingData.duration;
      case 'weekly': return vehicle.pricePerWeek * bookingData.duration;
      default: return 0;
    }
  };

  const subtotal = calculatePrice();
  const serviceFee = Math.round(subtotal * 0.1);
  const insurance = Math.round(subtotal * 0.05);
  const total = subtotal + serviceFee + insurance;

  const formatDate = (dateStr: string) => {
    return new Date(dateStr).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getEndTime = () => {
    const startTime = parseInt(bookingData.time.split(':')[0]);
    const endHour = startTime + (bookingData.durationType === 'hourly' ? bookingData.duration : 24);
    return `${endHour.toString().padStart(2, '0')}:00`;
  };

  const handleConfirmBooking = async () => {
    setIsConfirming(true);
    // Skip payment processing for now - directly confirm the booking
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsConfirming(false);
    setIsConfirmed(true);
  };

  const handleViewBooking = () => {
    navigateToBookingDetails(bookingId);
  };

  const handleBackToHome = () => {
    navigateToHome();
  };

  if (isConfirmed) {
    return (
      <div className="h-full bg-white flex flex-col items-center justify-center p-8">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
          <Check size={40} className="text-green-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 text-center mb-2">
          Booking Confirmed!
        </h1>
        <p className="text-gray-600 text-center mb-8">
          Your vehicle is reserved. Check your email for details.
        </p>
        <div className="w-full max-w-sm space-y-3">
          <button
            onClick={handleViewBooking}
            className="w-full py-3 bg-[#009639] text-white rounded-lg font-medium hover:bg-[#007A2F] transition-all"
          >
            View Booking Details
          </button>
          <button
            onClick={handleBackToHome}
            className="w-full py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-all"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Confirm Booking</h1>
          <div className="w-10 h-10" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Vehicle Info */}
        <div className="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                <Car size={24} className="text-blue-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-lg font-semibold text-gray-900">
                  {vehicle.year} {vehicle.make} {vehicle.model}
                </h2>
                <div className="flex items-center text-sm text-gray-600 mt-1">
                  <MapPin size={14} className="mr-1" />
                  {vehicle.location}
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  Plate: {vehicle.licensePlate}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Details */}
        <div className="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Booking Details</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Calendar size={20} className="text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">Date</p>
                    <p className="text-sm text-gray-600">{formatDate(bookingData.date)}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Clock size={20} className="text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">Time</p>
                    <p className="text-sm text-gray-600">
                      {bookingData.time} - {getEndTime()}
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Users size={20} className="text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">Duration</p>
                    <p className="text-sm text-gray-600">
                      {bookingData.duration} {bookingData.durationType === 'hourly' ? 'hour(s)' : bookingData.durationType === 'daily' ? 'day(s)' : 'week(s)'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Price Breakdown */}
        <div className="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Breakdown</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {bookingData.durationType.charAt(0).toUpperCase() + bookingData.durationType.slice(1)} rate × {bookingData.duration}
                </span>
                <span className="font-medium text-gray-900">${subtotal.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Service fee</span>
                <span className="font-medium text-gray-900">${serviceFee}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Insurance</span>
                <span className="font-medium text-gray-900">${insurance}</span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Total</span>
                  <span className="text-xl font-bold text-[#009639]">${total.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Notice */}
        <div className="bg-blue-50 border border-blue-200 mx-4 mt-4 mb-4 rounded-xl">
          <div className="p-4">
            <div className="flex items-start space-x-3">
              <Shield size={20} className="text-blue-600 mt-1" />
              <div>
                <h4 className="font-medium text-blue-900">Free Booking</h4>
                <p className="text-sm text-blue-700 mt-1">
                  This booking is free of charge. Payment processing will be implemented in a future update.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Safety Notice */}
        <div className="bg-blue-50 border border-blue-200 mx-4 mt-4 mb-4 rounded-xl">
          <div className="p-4">
            <div className="flex items-start space-x-3">
              <Shield size={20} className="text-blue-600 mt-1" />
              <div>
                <h4 className="font-medium text-blue-900">Safety First</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Please inspect the vehicle before driving and report any issues immediately.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Action */}
      <div className="bg-white border-t border-gray-100 p-4">
        <button
          onClick={handleConfirmBooking}
          disabled={isConfirming}
          className={`
            w-full py-4 rounded-lg font-medium transition-all
            ${isConfirming
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-[#009639] text-white hover:bg-[#007A2F]'
            }
          `}
        >
          {isConfirming ? (
            <div className="flex items-center justify-center space-x-2">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              <span>Confirming...</span>
            </div>
          ) : (
            `Confirm Booking`
          )}
        </button>
      </div>
    </div>
  );
} 