"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { useCurrentUser } from "@/hooks/use-current-user";
import { useNavigation } from "@/hooks/useNavigation";
import { DocumentUpload } from "@/lib/utils";
import { completeVehicleHandoverTask } from "@/actions/tasks";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import {
  ArrowLeft,
  Camera,
  ChevronDown,
  ChevronUp,
  QrCode,
  Upload,
  AlertCircle,
  Gauge,
  X,
  Plus,
  CheckCircle,
} from "lucide-react";

interface Task {
  id: string;
  type: string;
  metadata?: any;
}

interface VehicleHandoverTaskScreenProps {
  task: Task;
  vehicle: VehicleReadWithModelAndParty;
  onComplete: (result: any) => void;
  onCancel: () => void;
}

export default function VehicleHandoverTaskScreen({
  task,
  vehicle,
  onComplete,
  onCancel,
}: VehicleHandoverTaskScreenProps) {
  const { goBack } = useNavigation();
  const currentUser = useCurrentUser();
  
  const fileInputRefs = {
    front: useRef<HTMLInputElement>(null),
    back: useRef<HTMLInputElement>(null),
    left: useRef<HTMLInputElement>(null),
    right: useRef<HTMLInputElement>(null),
    dashboard: useRef<HTMLInputElement>(null),
    seats: useRef<HTMLInputElement>(null),
    additional: useRef<HTMLInputElement>(null),
  };

  // Determine if this user is handing over or receiving based on task type
  const isHandingOver = task.type === 'VEHICLE_HANDOVER_GIVER';
  const taskTitle = isHandingOver ? 'Hand Over Vehicle' : 'Receive Vehicle';
  
  const [showQrScanner, setShowQrScanner] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [checklist, setChecklist] = useState({
    exterior: {
      scratches: "none",
      dents: "none",
      tires: "good",
      lights: "working",
    },
    interior: {
      cleanliness: "clean",
      seats: "good",
      dashboard: "working",
      odors: "none",
    },
    fuel: 75, // percentage
    odometer: "",
    issues: "",
  });
  
  const [photoSlots, setPhotoSlots] = useState<{
    front: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    back: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    left: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    right: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    dashboard: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    seats: {
      file: File | null;
      previewUrl: string | null;
      s3Url: string | null;
    };
    additional: { file: File; previewUrl: string; s3Url: string }[];
  }>({
    front: { file: null, previewUrl: null, s3Url: null },
    back: { file: null, previewUrl: null, s3Url: null },
    left: { file: null, previewUrl: null, s3Url: null },
    right: { file: null, previewUrl: null, s3Url: null },
    dashboard: { file: null, previewUrl: null, s3Url: null },
    seats: { file: null, previewUrl: null, s3Url: null },
    additional: [],
  });

  type ChecklistCategory = "exterior" | "interior";

  const handleChecklistChange = (
    category: ChecklistCategory,
    item: string,
    value: string
  ) => {
    setChecklist({
      ...checklist,
      [category]: {
        ...checklist[category],
        [item]: value,
      },
    });
  };

  const handleIssuesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setChecklist({
      ...checklist,
      issues: e.target.value,
    });
  };

  const handleOdometerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setChecklist({
      ...checklist,
      odometer: e.target.value,
    });
  };

  const handlePhotoUpload =
    (type: keyof typeof fileInputRefs) =>
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      try {
        const files = e.target.files;
        if (files && files.length > 0) {
          if (type === "additional") {
            const newPhotos = await Promise.all(
              Array.from(files).map(async (file) => {
                const previewUrl = URL.createObjectURL(file);
                const uploadResult = await DocumentUpload(file, "vehicleMedia");
                if (!uploadResult || !uploadResult.path) {
                  URL.revokeObjectURL(previewUrl);
                  throw new Error("Failed to upload additional photo to S3");
                }
                return { file, previewUrl, s3Url: uploadResult.path };
              })
            );
            setPhotoSlots((prev) => ({
              ...prev,
              additional: [...prev.additional, ...newPhotos],
            }));
          } else {
            const file = files[0];
            const previewUrl = URL.createObjectURL(file);
            const uploadResult = await DocumentUpload(file, "vehicleMedia");
            if (!uploadResult || !uploadResult.path) {
              URL.revokeObjectURL(previewUrl);
              throw new Error(`Failed to upload ${type} photo to S3`);
            }
            setPhotoSlots((prev) => ({
              ...prev,
              [type]: { file, previewUrl, s3Url: uploadResult.path },
            }));
          }
          setError(null);
        }
      } catch (error: any) {
        console.error(`Error uploading ${type} photo:`, error);
        setError(
          error.message || "An error occurred while uploading the photo."
        );
      }
    };

  const handleRemovePhoto = (
    type: keyof typeof fileInputRefs,
    index?: number
  ) => {
    if (type === "additional" && index !== undefined) {
      setPhotoSlots((prev) => {
        const updatedAdditional = prev.additional.filter((_, i) => i !== index);
        // Revoke object URLs for removed photos
        prev.additional[index]?.previewUrl &&
          URL.revokeObjectURL(prev.additional[index].previewUrl);
        return {
          ...prev,
          additional: updatedAdditional,
        };
      });
    } else {
      setPhotoSlots((prev) => {
        // Revoke object URL for the removed photo
        const slot = prev[type];
        if ('previewUrl' in slot && slot.previewUrl) {
          URL.revokeObjectURL(slot.previewUrl);
        }
        return {
          ...prev,
          [type]: { file: null, previewUrl: null, s3Url: null },
        };
      });
    }
  };

  // Cleanup object URLs on component unmount
  useEffect(() => {
    return () => {
      // Revoke all preview URLs when component unmounts
      Object.values(photoSlots).forEach((slot) => {
        if ("previewUrl" in slot && slot.previewUrl) {
          URL.revokeObjectURL(slot.previewUrl);
        } else if (Array.isArray(slot)) {
          slot.forEach(
            (photo) => photo.previewUrl && URL.revokeObjectURL(photo.previewUrl)
          );
        }
      });
    };
  }, [photoSlots]);

  const validateRequiredPhotos = () => {
    return (
      photoSlots.front.s3Url &&
      photoSlots.back.s3Url &&
      photoSlots.left.s3Url &&
      photoSlots.right.s3Url &&
      photoSlots.dashboard.s3Url &&
      photoSlots.seats.s3Url
    );
  };

  const handleCompleteHandover = async () => {
    if (!validateRequiredPhotos()) {
      setError("All required photos (front, back, left, right, dashboard, seats) must be uploaded.");
      return;
    }

    if (!checklist.odometer.trim()) {
      setError("Odometer reading is required.");
      return;
    }

    if (!currentUser?.partyId) {
      setError("User information not available.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create handover inspection data
      const handoverData = {
        vehicleId: vehicle.id,
        handingOver: isHandingOver,
        
        // Checklist data
        scratches: checklist.exterior.scratches,
        dents: checklist.exterior.dents,
        tires: checklist.exterior.tires,
        lights: checklist.exterior.lights,
        cleanliness: checklist.interior.cleanliness,
        seats: checklist.interior.seats,
        dashboard_controls: checklist.interior.dashboard,
        odors: checklist.interior.odors,
        
        // Additional data
        odometer: parseInt(checklist.odometer),
        fuel_level: checklist.fuel,
        known_issues: checklist.issues,
        
        // Photo URLs (validated as non-null by validateRequiredPhotos)
        front_view: photoSlots.front.s3Url!,
        rear_view: photoSlots.back.s3Url!,
        left_view: photoSlots.left.s3Url!,
        right_view: photoSlots.right.s3Url!,
        dashboard: photoSlots.dashboard.s3Url!,
        seats_view: photoSlots.seats.s3Url!,
        additional_photos: photoSlots.additional.map(photo => photo.s3Url),
      };

      console.log("📝 Submitting handover inspection:", handoverData);

      // Call the backend API to complete the handover task
      const result = await completeVehicleHandoverTask(
        parseInt(task.id), 
        currentUser.partyId, 
        handoverData
      );

      // Complete the task
      onComplete({
        completed: true,
        timestamp: new Date().toISOString(),
        taskResult: result,
      });

    } catch (error: any) {
      console.error("Error completing handover:", error);
      setError(error.message || "Failed to complete handover inspection.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white pb-24">
      {/* Task Info */}
      <div className="p-4 bg-blue-50 border-b border-blue-200">
        <div className="flex items-center gap-3">
          <CheckCircle size={20} className="text-blue-600" />
          <div>
            <p className="font-medium text-blue-800">Vehicle Handover Task</p>
            <p className="text-sm text-blue-600">
              Complete the inspection to {isHandingOver ? 'hand over' : 'receive'} the vehicle
            </p>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mx-4 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* QR Code Scanner */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
          <button
            className="w-full flex items-center justify-between"
            onClick={() => setShowQrScanner(!showQrScanner)}
          >
            <div className="flex items-center">
              <QrCode size={24} className="text-[#009639] mr-3" />
              <span className="text-[#333333] font-medium">Scan QR Code</span>
            </div>
            {showQrScanner ? (
              <ChevronUp size={20} />
            ) : (
              <ChevronDown size={20} />
            )}
          </button>

          {showQrScanner && (
            <div className="mt-4">
              <div className="bg-[#f2f2f2] rounded-lg h-64 flex items-center justify-center">
                <div className="text-center">
                  <QrCode size={48} className="text-[#797879] mx-auto mb-2" />
                  <p className="text-[#797879]">
                    Point camera at vehicle QR code
                  </p>
                </div>
              </div>
              <p className="text-xs text-[#797879] mt-2 text-center">
                Scan the QR code on the vehicle to automatically fill in
                vehicle details
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Vehicle Condition Checklist */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-[#333333] mb-3">
          Vehicle Condition
        </h2>

        {/* Exterior Condition */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">
            Exterior Condition
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Scratches
              </label>
              <div className="flex space-x-2">
                {["none", "minor", "major"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.exterior.scratches === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("exterior", "scratches", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Dents
              </label>
              <div className="flex space-x-2">
                {["none", "minor", "major"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.exterior.dents === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("exterior", "dents", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Tires
              </label>
              <div className="flex space-x-2">
                {["good", "fair", "poor"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.exterior.tires === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("exterior", "tires", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Lights
              </label>
              <div className="flex space-x-2">
                {["working", "partial", "broken"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.exterior.lights === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("exterior", "lights", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Interior Condition */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <h3 className="text-[#333333] font-medium mb-3">
            Interior Condition
          </h3>

          <div className="space-y-4">
            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Cleanliness
              </label>
              <div className="flex space-x-2">
                {["clean", "acceptable", "dirty"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.interior.cleanliness === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange(
                        "interior",
                        "cleanliness",
                        value
                      )
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Seats
              </label>
              <div className="flex space-x-2">
                {["good", "fair", "poor"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.interior.seats === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("interior", "seats", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Dashboard/Controls
              </label>
              <div className="flex space-x-2">
                {["working", "partial", "issues"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.interior.dashboard === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("interior", "dashboard", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Odors
              </label>
              <div className="flex space-x-2">
                {["none", "mild", "strong"].map((value) => (
                  <button
                    key={value}
                    type="button"
                    className={`px-3 py-1 rounded-full text-sm ${
                      checklist.interior.odors === value
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleChecklistChange("interior", "odors", value)
                    }
                  >
                    {value.charAt(0).toUpperCase() + value.slice(1)}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Mileage */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center mb-3">
            <Gauge size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">Odometer (km)</h3>
          </div>

          <input
            type="number"
            value={checklist.odometer}
            onChange={handleOdometerChange}
            className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639]"
            placeholder="Enter current mileage"
          />
        </div>

        {/* Known Issues */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center mb-3">
            <AlertCircle size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">Known Issues</h3>
          </div>

          <textarea
            value={checklist.issues}
            onChange={handleIssuesChange}
            className="w-full px-4 py-3 rounded-xl border border-[#d6d9dd] focus:outline-none focus:border-[#009639] min-h-[100px]"
            placeholder="Describe any known issues or concerns..."
          />
        </div>
      </div>

      {/* Photo Documentation */}
      <div className="p-4">
        <h2 className="text-lg font-semibold text-[#333333] mb-3">
          Photo Documentation
        </h2>

        {/* Exterior Photos */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Camera size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Exterior Photos (Required)
              </h3>
            </div>
          </div>

          <div className="space-y-3">
            <div>
              <p className="text-sm text-[#797879] mb-2">Front View</p>
              <input
                type="file"
                ref={fileInputRefs.front}
                className="hidden"
                accept="image/*"
                onChange={handlePhotoUpload("front")}
              />
              {photoSlots.front.previewUrl ? (
                <div className="relative h-40 bg-[#f2f2f2] rounded-lg overflow-hidden">
                  <Image
                    src={photoSlots.front.previewUrl}
                    alt="Front view"
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                    onClick={() => handleRemovePhoto("front")}
                  >
                    <X size={16} className="text-red-500" />
                  </button>
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 h-40 flex flex-col items-center justify-center cursor-pointer"
                  onClick={() => fileInputRefs.front.current?.click()}
                >
                  <Upload size={24} className="text-[#797879] mb-2" />
                  <p className="text-[#797879] text-center text-sm">
                    Add front view photo
                  </p>
                </div>
              )}
            </div>

            <div>
              <p className="text-sm text-[#797879] mb-2">Back View</p>
              <input
                type="file"
                ref={fileInputRefs.back}
                className="hidden"
                accept="image/*"
                onChange={handlePhotoUpload("back")}
              />
              {photoSlots.back.previewUrl ? (
                <div className="relative h-40 bg-[#f2f2f2] rounded-lg overflow-hidden">
                  <Image
                    src={photoSlots.back.previewUrl}
                    alt="Back view"
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                    onClick={() => handleRemovePhoto("back")}
                  >
                    <X size={16} className="text-red-500" />
                  </button>
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 h-40 flex flex-col items-center justify-center cursor-pointer"
                  onClick={() => fileInputRefs.back.current?.click()}
                >
                  <Upload size={24} className="text-[#797879] mb-2" />
                  <p className="text-[#797879] text-center text-sm">
                    Add back view photo
                  </p>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <p className="text-sm text-[#797879] mb-2">Left Side</p>
                <input
                  type="file"
                  ref={fileInputRefs.left}
                  className="hidden"
                  accept="image/*"
                  onChange={handlePhotoUpload("left")}
                />
                {photoSlots.left.previewUrl ? (
                  <div className="relative h-32 bg-[#f2f2f2] rounded-lg overflow-hidden">
                    <Image
                      src={photoSlots.left.previewUrl}
                      alt="Left side"
                      fill
                      className="object-cover"
                    />
                    <button
                      type="button"
                      className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                      onClick={() => handleRemovePhoto("left")}
                    >
                      <X size={16} className="text-red-500" />
                    </button>
                  </div>
                ) : (
                  <div
                    className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-4 h-32 flex flex-col items-center justify-center cursor-pointer"
                    onClick={() => fileInputRefs.left.current?.click()}
                  >
                    <Upload size={20} className="text-[#797879] mb-1" />
                    <p className="text-[#797879] text-center text-xs">
                      Add left side
                    </p>
                  </div>
                )}
              </div>

              <div>
                <p className="text-sm text-[#797879] mb-2">Right Side</p>
                <input
                  type="file"
                  ref={fileInputRefs.right}
                  className="hidden"
                  accept="image/*"
                  onChange={handlePhotoUpload("right")}
                />
                {photoSlots.right.previewUrl ? (
                  <div className="relative h-32 bg-[#f2f2f2] rounded-lg overflow-hidden">
                    <Image
                      src={photoSlots.right.previewUrl}
                      alt="Right side"
                      fill
                      className="object-cover"
                    />
                    <button
                      type="button"
                      className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                      onClick={() => handleRemovePhoto("right")}
                    >
                      <X size={16} className="text-red-500" />
                    </button>
                  </div>
                ) : (
                  <div
                    className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-4 h-32 flex flex-col items-center justify-center cursor-pointer"
                    onClick={() => fileInputRefs.right.current?.click()}
                  >
                    <Upload size={20} className="text-[#797879] mb-1" />
                    <p className="text-[#797879] text-center text-xs">
                      Add right side
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Interior Photos */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Camera size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Interior Photos (Required)
              </h3>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-3">
            <div>
              <p className="text-sm text-[#797879] mb-2">Dashboard</p>
              <input
                type="file"
                ref={fileInputRefs.dashboard}
                className="hidden"
                accept="image/*"
                onChange={handlePhotoUpload("dashboard")}
              />
              {photoSlots.dashboard.previewUrl ? (
                <div className="relative h-32 bg-[#f2f2f2] rounded-lg overflow-hidden">
                  <Image
                    src={photoSlots.dashboard.previewUrl}
                    alt="Dashboard"
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                    onClick={() => handleRemovePhoto("dashboard")}
                  >
                    <X size={16} className="text-red-500" />
                  </button>
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-4 h-32 flex flex-col items-center justify-center cursor-pointer"
                  onClick={() => fileInputRefs.dashboard.current?.click()}
                >
                  <Upload size={20} className="text-[#797879] mb-1" />
                  <p className="text-[#797879] text-center text-xs">
                    Add dashboard
                  </p>
                </div>
              )}
            </div>

            <div>
              <p className="text-sm text-[#797879] mb-2">Seats</p>
              <input
                type="file"
                ref={fileInputRefs.seats}
                className="hidden"
                accept="image/*"
                onChange={handlePhotoUpload("seats")}
              />
              {photoSlots.seats.previewUrl ? (
                <div className="relative h-32 bg-[#f2f2f2] rounded-lg overflow-hidden">
                  <Image
                    src={photoSlots.seats.previewUrl}
                    alt="Seats"
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm"
                    onClick={() => handleRemovePhoto("seats")}
                  >
                    <X size={16} className="text-red-500" />
                  </button>
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-4 h-32 flex flex-col items-center justify-center cursor-pointer"
                  onClick={() => fileInputRefs.seats.current?.click()}
                >
                  <Upload size={20} className="text-[#797879] mb-1" />
                  <p className="text-[#797879] text-center text-xs">
                    Add seats
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Additional Photos */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Camera size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                Additional Photos (Optional)
              </h3>
            </div>
            <input
              type="file"
              ref={fileInputRefs.additional}
              className="hidden"
              accept="image/*"
              multiple
              onChange={handlePhotoUpload("additional")}
            />
            <button
              type="button"
              className="bg-[#009639] text-white p-2 rounded-full"
              onClick={() => fileInputRefs.additional.current?.click()}
            >
              <Camera size={20} />
            </button>
          </div>

          {photoSlots.additional.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {photoSlots.additional.map((photo, index) => (
                <div
                  key={index}
                  className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
                >
                  <Image
                    src={photo.previewUrl}
                    alt={`Additional photo ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  <button
                    type="button"
                    className="absolute top-1 right-1 bg-white rounded-full p-1"
                    onClick={() => handleRemovePhoto("additional", index)}
                  >
                    <X size={14} className="text-red-500" />
                  </button>
                </div>
              ))}
              <div
                className="border-2 border-dashed border-[#d6d9dd] rounded-lg h-24 flex flex-col items-center justify-center cursor-pointer"
                onClick={() => fileInputRefs.additional.current?.click()}
              >
                <Plus size={24} className="text-[#797879]" />
              </div>
            </div>
          ) : (
            <div
              className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
              onClick={() => fileInputRefs.additional.current?.click()}
            >
              <Upload size={32} className="text-[#797879] mb-2" />
              <p className="text-[#797879] text-center">
                Add additional photos (damages, etc.)
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2]">
        <button
          type="button"
          onClick={handleCompleteHandover}
          disabled={isSubmitting || !validateRequiredPhotos() || !checklist.odometer.trim()}
          className="w-full bg-[#009639] text-white py-4 rounded-full text-xl font-semibold shadow-sm disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center">
              <div className="border-t-4 border-b-4 border-white w-6 h-6 border-solid rounded-full animate-spin mr-2"></div>
              Processing...
            </div>
          ) : (
            `Complete ${taskTitle}`
          )}
        </button>
      </div>
    </div>
  );
} 