"use client";

import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, User, Settings, Bell, CreditCard, Shield, HelpCircle } from "lucide-react";

interface ProfileScreenProps {
  params?: { section?: string };
}

export default function ProfileScreen({ params }: ProfileScreenProps) {
  const { goBack, canGoBack } = useNavigation();

  const profileOptions = [
    { icon: User, label: "Personal Information", action: () => {} },
    { icon: CreditCard, label: "Payment Methods", action: () => {} },
    { icon: Bell, label: "Notifications", action: () => {} },
    { icon: Shield, label: "Privacy & Security", action: () => {} },
    { icon: HelpCircle, label: "Help & Support", action: () => {} },
    { icon: Settings, label: "App Settings", action: () => {} },
  ];

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        {canGoBack() ? (
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
        ) : (
          <div className="w-10 h-10"></div>
        )}
        <h1 className="text-lg font-semibold text-gray-900">Profile</h1>
        <div className="w-10 h-10"></div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto scrollbar-hide p-4">
        {/* Profile Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gray-200 rounded-full mx-auto mb-4 flex items-center justify-center">
            <User size={32} className="text-gray-400" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900">John Doe</h2>
          <p className="text-gray-600"><EMAIL></p>
        </div>

        {/* Profile Options */}
        <div className="space-y-2">
          {profileOptions.map((option, index) => (
            <button
              key={index}
              onClick={option.action}
              className="w-full flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <option.icon size={20} className="text-gray-600" />
              </div>
              <span className="flex-1 text-left font-medium text-gray-900">
                {option.label}
              </span>
              <ArrowLeft size={16} className="text-gray-400 rotate-180" />
            </button>
          ))}
        </div>

        {/* Sign Out */}
        <div className="mt-8 pt-4 border-t border-gray-100">
          <button className="w-full py-3 text-red-600 font-medium text-center hover:bg-red-50 rounded-lg transition-colors">
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
} 