"use client";

import React, { useState, useEffect, useRef } from "react";
import { getUrl, uploadData, remove } from "aws-amplify/storage";
import type { VehicleMediaRead } from "@/types/vehicles";
import type { VehiclePossessionWithContactRead } from "@/types/vehicle-possessions";
import type { VehicleMaintenanceRead } from "@/drizzle-actions/vehicle-maintenance";
import type { deprecated_BookingRead } from "@/types/bookings";
import {
  ArrowLeft,
  Phone,
  User,
  Calendar,
  ChevronRight,
  Car,
  Wrench,
  Clock,
  Mail,
  ChevronLeft,
  ChevronRight as ChevronRightPagination,
  AlertTriangle,
  Shield,
  MapPin,
  Plus,
  X,
  Upload,
  Trash2,
  Edit3,
  Check,
  Loader,
} from "lucide-react";
import type { VehicleReadWithListings } from "@/types/vehicles";
import { useNavigation } from "@/hooks/useNavigation";
import {
  getStatusColor,
  getStatusLabel,
  formatDateForInput,
  calculateTimeRemaining,
} from "@/lib/utils";
// Import the database actions for data fetching
import { getVehicleByIdWithListings } from "@/drizzle-actions/vehicle-dashboard";
import { getVehicleMaintenanceByVehicleIdDrizzle } from "@/drizzle-actions/vehicle-maintenance";
import { deprecated_getVehicleBookingsWithDetails } from "@/drizzle-actions/bookings";
import { fetchUserAttributes } from "aws-amplify/auth";

// Import the real actions for vehicle media management
import { addVehicleMedia, deleteVehicleMedia } from '@/actions/vehicle-media';
import { DocumentUpload, DocumentDelete } from '@/lib/utils';
import { PageWithScroll } from '@/components/ui/scroll-container';

// Simple image carousel component with full-screen functionality
function VehicleImageCarousel({ 
  images, 
  vehicle, 
  onImagesUpdate 
}: { 
  images: string[]; 
  vehicle?: VehicleReadWithListings;
  onImagesUpdate?: (newImages: string[], newMediaRecords: VehicleMediaRead[]) => void;
}) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [fullScreenIndex, setFullScreenIndex] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<number | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Touch handling for swipe gestures
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;
    
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe && images.length > 1) {
      setFullScreenIndex((prev) => (prev + 1) % images.length);
    }
    if (isRightSwipe && images.length > 1) {
      setFullScreenIndex((prev) => (prev - 1 + images.length) % images.length);
    }
  };

  // Handle keyboard events for full-screen mode
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isFullScreen) return;
      
      switch (event.key) {
        case 'Escape':
          setIsFullScreen(false);
          break;
        case 'ArrowLeft':
          setFullScreenIndex((prev) => (prev - 1 + images.length) % images.length);
          break;
        case 'ArrowRight':
          setFullScreenIndex((prev) => (prev + 1) % images.length);
          break;
      }
    };

    if (isFullScreen) {
      document.addEventListener('keydown', handleKeyDown);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isFullScreen, images.length]);

  const openFullScreen = (index: number) => {
    setFullScreenIndex(index);
    setIsFullScreen(true);
  };

  const closeFullScreen = () => {
    setIsFullScreen(false);
  };

  // Handle image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0 || !vehicle) return;

    setIsUploading(true);
    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Upload to S3 using DocumentUpload
        const uploadResult = await DocumentUpload(file, "vehicleMedia");
        
        if (uploadResult && uploadResult.path) {
          // Add to database using the existing action
          const formData = new FormData();
          formData.append("vehicleID", vehicle.id.toString());
          formData.append("documentUrl", uploadResult.path);
          
          const result = await addVehicleMedia(null, formData);
          if ('success' in result && result.success) {
            return uploadResult.path;
          }
        }
        throw new Error("Upload failed");
      });
      
      const uploadedPaths = await Promise.all(uploadPromises);
      
      // Generate signed URLs for the new images
      const newImageUrls = await Promise.all(
        uploadedPaths.map(async (path) => {
          const urlResult = await getUrl({ path });
          return urlResult.url.toString();
        })
      );
      
      // Update images via callback
      if (onImagesUpdate) {
        const updatedImages = [...images, ...newImageUrls];
        onImagesUpdate(updatedImages, []); // We don't have the full media records here
      }
      
    } catch (error) {
      console.error("Error uploading images:", error);
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle image deletion
  const handleImageDelete = async (index: number) => {
    if (!vehicle || !vehicle.media) return;
    
    const mediaRecord = vehicle.media[index];
    if (!mediaRecord) return;

    const confirmed = window.confirm("Are you sure you want to delete this image?");
    if (!confirmed) return;

    setIsUploading(true);
    try {
      // Delete from S3
      await DocumentDelete(mediaRecord.media_path);
      
      // Delete from database
      await deleteVehicleMedia(mediaRecord.id);
      
      // Update images by removing the deleted one
      if (onImagesUpdate) {
        const updatedImages = images.filter((_, i) => i !== index);
        const updatedMediaRecords = vehicle.media.filter((_, i) => i !== index);
        onImagesUpdate(updatedImages, updatedMediaRecords);
      }
      
      // Adjust current index if needed
      if (index === currentIndex && currentIndex > 0) {
        setCurrentIndex(currentIndex - 1);
      }
      
    } catch (error) {
      console.error("Error deleting image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  if (!images || images.length === 0) {
    return (
      <div className="relative h-48 bg-[#f2f2f2] rounded-xl flex items-center justify-center">
        <Car size={48} className="text-[#797879]" />
        {vehicle && (
          <>
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
            />
            <button
              className="absolute bottom-4 right-4 bg-[#009639] text-white p-3 rounded-full shadow-lg hover:bg-[#007A2F] transition-colors"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? <Loader size={20} className="animate-spin" /> : <Upload size={20} />}
            </button>
          </>
        )}
      </div>
    );
  }

  return (
    <>
      {/* Regular Carousel */}
      <div 
        className="relative h-48 bg-[#f2f2f2] rounded-xl overflow-hidden"
        onTouchStart={(e) => {
          setTouchEnd(null);
          setTouchStart(e.targetTouches[0].clientX);
        }}
        onTouchMove={(e) => {
          setTouchEnd(e.targetTouches[0].clientX);
        }}
        onTouchEnd={() => {
          if (!touchStart || !touchEnd) return;
          
          const distance = touchStart - touchEnd;
          const isLeftSwipe = distance > minSwipeDistance;
          const isRightSwipe = distance < -minSwipeDistance;

          if (isLeftSwipe && images.length > 1) {
            setCurrentIndex((prev) => (prev + 1) % images.length);
          }
          if (isRightSwipe && images.length > 1) {
            setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
          }
        }}
      >
        <img
          src={images[currentIndex]}
          alt="Vehicle"
          className="w-full h-full object-cover cursor-pointer"
          onClick={() => openFullScreen(currentIndex)}
        />
        {images.length > 1 && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                }`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        )}
        
        {/* Management buttons */}
        {vehicle && (
          <div className="absolute top-2 right-2 flex gap-2">
            {/* Edit mode toggle */}
            <button
              className="bg-black/50 text-white p-2 rounded hover:bg-black/70 transition-colors"
              onClick={() => setIsEditMode(!isEditMode)}
            >
              {isEditMode ? <Check size={16} /> : <Edit3 size={16} />}
            </button>
            
            {/* Fullscreen expand icon */}
            <button
              className="bg-black/50 text-white p-2 rounded hover:bg-black/70 transition-colors"
              onClick={() => openFullScreen(currentIndex)}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>
        )}

        {/* Edit mode overlay */}
        {isEditMode && vehicle && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center gap-4">
            {/* Hidden file input */}
            <input
              type="file"
              ref={fileInputRef}
              className="hidden"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
            />
            
            {/* Upload button */}
            <button
              className="bg-[#009639] text-white p-3 rounded-full shadow-lg hover:bg-[#007A2F] transition-colors"
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading}
            >
              {isUploading ? <Loader size={20} className="animate-spin" /> : <Upload size={20} />}
            </button>
            
            {/* Delete current image button */}
            <button
              className="bg-red-600 text-white p-3 rounded-full shadow-lg hover:bg-red-700 transition-colors"
              onClick={() => handleImageDelete(currentIndex)}
              disabled={isUploading}
            >
              <Trash2 size={20} />
            </button>
          </div>
        )}
      </div>

      {/* Full-Screen Modal */}
      {isFullScreen && (
        <div 
          className="fixed inset-0 bg-black z-50 flex items-center justify-center"
          onClick={closeFullScreen}
        >
          {/* Exit fullscreen button */}
          <button
            className="absolute top-4 right-4 text-white bg-black/50 p-2 rounded-full hover:bg-black/70 transition-colors z-10"
            onClick={(e) => {
              e.stopPropagation();
              closeFullScreen();
            }}
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 3v3a2 2 0 0 1-2 2H3m18 0h-3a2 2 0 0 1-2-2V3m0 18v-3a2 2 0 0 1 2-2h3M3 16h3a2 2 0 0 1 2 2v3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>

          {/* Image counter */}
          <div 
            className="absolute top-4 left-4 text-white bg-black/50 px-3 py-1 rounded text-sm z-10"
            onClick={(e) => e.stopPropagation()}
          >
            {fullScreenIndex + 1} of {images.length}
          </div>

          {/* Main image */}
          <div 
            className="relative w-full h-full flex items-center justify-center"
            onTouchStart={onTouchStart}
            onTouchMove={onTouchMove}
            onTouchEnd={onTouchEnd}
            onClick={(e) => e.stopPropagation()}
          >
            <img
              src={images[fullScreenIndex]}
              alt={`Vehicle image ${fullScreenIndex + 1}`}
              className="w-full h-full object-contain"
              style={{ maxWidth: '100vw', maxHeight: '100vh' }}
              onClick={(e) => e.stopPropagation()}
            />
            

          </div>

          {/* Scalable thumbnail navigation */}
          {images.length > 1 && (
            <div 
              className="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-full max-w-sm px-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div 
                className="flex space-x-1.5 overflow-x-auto scrollbar-hide"
                style={{
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none'
                }}
              >
                {images.map((image, index) => {
                  // Dynamic thumbnail size based on number of images
                  const thumbnailSize = images.length <= 4 ? 'w-12 h-12' : 
                                       images.length <= 8 ? 'w-10 h-10' : 
                                       'w-8 h-8';
                  
                  return (
                    <button
                      key={index}
                      className={`flex-shrink-0 ${thumbnailSize} rounded-md overflow-hidden border-2 transition-all ${
                        index === fullScreenIndex ? "border-white scale-110 shadow-lg" : "border-white/30"
                      }`}
                      onClick={(e) => {
                        e.stopPropagation();
                        setFullScreenIndex(index);
                        
                        // Scroll the selected thumbnail into view
                        e.currentTarget.scrollIntoView({
                          behavior: 'smooth',
                          block: 'nearest',
                          inline: 'center'
                        });
                      }}
                    >
                      <img
                        src={image}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  );
                })}
              </div>
              
              {/* Scroll indicators for many images */}
              {images.length > 6 && (
                <div className="flex justify-center mt-2 space-x-1">
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                  <div className="w-1 h-1 bg-white/30 rounded-full"></div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </>
  );
}

// Individual skeleton components for better granular loading states
function VehicleImageSkeleton() {
  return (
    <div className="p-4">
      <div className="h-48 bg-gray-200 rounded-xl animate-pulse"></div>
    </div>
  );
}

function VehicleInfoSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-6 mb-8 border border-gray-100">
      <div className="flex items-center mb-3 animate-pulse">
        <div className="w-5 h-5 bg-gray-200 rounded mr-2"></div>
        <div className="h-4 bg-gray-200 rounded w-32"></div>
      </div>
      <div className="space-y-2 animate-pulse">
        <div className="h-3 bg-gray-200 rounded w-full"></div>
        <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        <div className="h-3 bg-gray-200 rounded w-4/5"></div>
      </div>
    </div>
  );
}

function CurrentUserSkeleton() {
  return (
    <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
      <div className="flex items-center justify-between mb-3 animate-pulse">
        <div className="flex items-center">
          <div className="w-5 h-5 bg-gray-200 rounded mr-2"></div>
          <div className="h-4 bg-gray-200 rounded w-24"></div>
        </div>
        <div className="w-16 h-6 bg-gray-200 rounded-full"></div>
      </div>
      <div className="space-y-2 animate-pulse">
        <div className="h-3 bg-gray-200 rounded w-full"></div>
        <div className="h-3 bg-gray-200 rounded w-2/3"></div>
      </div>
      <div className="h-3 bg-gray-200 rounded w-full"/>
      <div className="space-y-2 animate-pulse">
        <div className="h-3 bg-gray-200 rounded w-full"></div>
        <div className="h-6 bg-gray-200 rounded w-2/3"></div>
        
      </div>
    </div>
  );
}

interface VehicleStatusScreenProps {
  vehicle?: VehicleReadWithListings;
  possessions?: VehiclePossessionWithContactRead[];
  maintenanceRecords?: VehicleMaintenanceRead[];
  bookings?: deprecated_BookingRead[];
  currentUser?: any;
  params?: { vehicleId?: string };
}

export default function VehicleStatusScreen({ 
  vehicle: vehicleProp,
  possessions: possessionsProp = [],
  maintenanceRecords: maintenanceRecordsProp = [],
  bookings: bookingsProp = [],
  currentUser: currentUserProp,
  params 
}: VehicleStatusScreenProps) {
  const { goBack, navigate, navigateToBookingCalendar, navigateToVehicleHandover, navigateToMaintenanceDetails, navigateToListingManagement } = useNavigation();
  const vehicleId = params?.vehicleId || vehicleProp?.id?.toString() || "1";

  // ALL HOOKS MUST BE CALLED BEFORE ANY CONDITIONAL RETURNS
  // State for data fetching when no props provided
  const [vehicle, setVehicle] = useState<VehicleReadWithListings | null>(vehicleProp || null);
  const [possessions, setPossessions] = useState<VehiclePossessionWithContactRead[]>(possessionsProp);
  const [maintenanceRecords, setMaintenanceRecords] = useState<VehicleMaintenanceRead[]>(maintenanceRecordsProp);
  const [bookings, setBookings] = useState<deprecated_BookingRead[]>(bookingsProp);
  const [currentUser, setCurrentUser] = useState(currentUserProp);
  const [currentUserPartyId, setCurrentUserPartyId] = useState<number | null>(null);
  const [loading, setLoading] = useState(!vehicleProp); // Only load if no vehicle prop provided
  const [error, setError] = useState<string | null>(null);

  // Other state
  const [timeRemaining, setTimeRemaining] = useState({ hours: 5, minutes: 45 });
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'vehicle' | 'images' | 'documents'>('vehicle');
  const [currentPossession, setCurrentPossession] =
    useState<VehiclePossessionWithContactRead | null>(null);
  const [maintenanceTab, setMaintenanceTab] = useState<"maintenance" | "bookings">(
    "maintenance"
  );
  const [maintenancePage, setMaintenancePage] = useState(1);
  const [bookingsPage, setBookingsPage] = useState(1);
  const itemsPerPage = 10;

  // Handle user attributes when provided as props
  useEffect(() => {
    if (currentUserProp?.["custom:db_id"]) {
      const partyId = parseInt(currentUserProp["custom:db_id"]);
      if (!isNaN(partyId)) {
        setCurrentUserPartyId(partyId);
      }
    }
  }, [currentUserProp]);

  // Fetch data when no props provided (navigation system usage)
  useEffect(() => {
    async function fetchVehicleData() {
      if (vehicleProp) return; // Skip if vehicle prop is provided
      
      try {
        setLoading(true);
        setError(null);

        console.log("VehicleStatusScreen - Fetching data for vehicle ID:", vehicleId);

        // Fetch all data in parallel
        const [vehicleData, userAttributes, maintenanceData, bookingsData] = await Promise.all([
          getVehicleByIdWithListings(+vehicleId).catch((error: any) => {
            console.error("Failed to get vehicle:", error);
            return null;
          }),
          fetchUserAttributes().catch((error: any) => {
            console.error("Failed to get user attributes:", error);
            return null;
          }),
          getVehicleMaintenanceByVehicleIdDrizzle(+vehicleId).catch((error: any) => {
            console.error("Failed to get maintenance records:", error);
            return [];
          }),
          deprecated_getVehicleBookingsWithDetails(+vehicleId).catch((error: any) => {
            console.error("Failed to get bookings:", error);
            return [];
          })
        ]);

        if (!vehicleData) {
          setError("Vehicle not found");
          return;
        }

        console.log("VehicleStatusScreen - Data loaded successfully");
        setVehicle(vehicleData);
        setMaintenanceRecords(maintenanceData || []);
        setBookings(bookingsData || []);
        setCurrentUser(userAttributes);
        
        // Extract current user's party ID for ownership checks
        if (userAttributes?.["custom:db_id"]) {
          const partyId = parseInt(userAttributes["custom:db_id"]);
          if (!isNaN(partyId)) {
            setCurrentUserPartyId(partyId);
          }
        }
        
        setPossessions([]); // Temporarily disabled as in original page
      } catch (error) {
        console.error("VehicleStatusScreen - Error fetching data:", error);
        setError("Failed to load vehicle data");
      } finally {
        setLoading(false);
      }
    }

    fetchVehicleData();
  }, [vehicleId, vehicleProp]);

  useEffect(() => {
    async function loadImages() {
      if (!vehicle?.media?.length || !Array.isArray(vehicle.media)) return;

      try {
        const urls: string[] = await Promise.all(
          vehicle.media.map(async (item: VehicleMediaRead) => {
            const result = await getUrl({ path: item.media_path });
            return result.url.toString();
          })
        );

        setImageUrls(urls);
      } catch (error) {
        console.error("Error loading vehicle images:", error);
        setImageUrls([]);
      }
    }

    if (vehicle) {
      loadImages();
    }
  }, [vehicle]);

  useEffect(() => {
    if (!currentPossession || !currentPossession.handover_expected_datetime)
      return;

    const updateTime = () => {
      const remaining = calculateTimeRemaining(
        currentPossession.handover_expected_datetime
      );
      setTimeRemaining(remaining);
    };

    updateTime();
    const intervalId = setInterval(updateTime, 60 * 1000);

    return () => clearInterval(intervalId);
  }, [currentPossession?.handover_expected_datetime]);

  useEffect(() => {
    async function fetchPossession() {
      if (!vehicle?.id || !Array.isArray(possessions)) return;
      
      try {
        const latest = possessions
          .filter((p) => p?.status === "pending")
          .sort(
            (a, b) =>
              new Date(b?.handover_expected_datetime || 0).getTime() -
              new Date(a?.handover_expected_datetime || 0).getTime()
          )[0];
        setCurrentPossession(latest || null);
      } catch (error) {
        console.error("Error processing possessions:", error);
        setCurrentPossession(null);
      }
    }

    if (vehicle?.id) {
      fetchPossession();
    }
  }, [vehicle?.id, possessions]);

  // ALL HOOKS ARE NOW CALLED - SAFE TO DO CONDITIONAL RETURNS

  // Header is always visible - shows vehicle info from state/props  
  const vehicleTitle = vehicle?.model?.make?.name && vehicle?.model?.model 
    ? `${vehicle.model.make.name} ${vehicle.model.model}` 
    : (vehicleProp?.model?.make?.name && vehicleProp?.model?.model
      ? `${vehicleProp.model.make.name} ${vehicleProp.model.model}`
      : "Vehicle");

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={goBack}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">
        {vehicleTitle} Status
      </h1>
    </div>
  );

  // Tabs are always visible but can be disabled
  const tabs = (
    <div className="bg-white border-b border-gray-200">
      <div className="flex px-4">
        <button
          className={`flex-1 py-4 text-center text-sm font-medium border-b-2 transition-colors ${
            activeTab === "vehicle"
              ? "text-[#009639] border-[#009639]"
              : "text-[#797879] border-transparent"
          } ${loading || error ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={() => !loading && !error && setActiveTab("vehicle")}
          disabled={loading || !!error}
        >
          Vehicle
        </button>
        <button
          className={`flex-1 py-4 text-center text-sm font-medium border-b-2 transition-colors ${
            activeTab === "images"
              ? "text-[#009639] border-[#009639]"
              : "text-[#797879] border-transparent"
          } ${loading || error ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={() => !loading && !error && setActiveTab("images")}
          disabled={loading || !!error}
        >
          Images
        </button>
        <button
          className={`flex-1 py-4 text-center text-sm font-medium border-b-2 transition-colors ${
            activeTab === "documents"
              ? "text-[#009639] border-[#009639]"
              : "text-[#797879] border-transparent"
          } ${loading || error ? "opacity-50 cursor-not-allowed" : ""}`}
          onClick={() => !loading && !error && setActiveTab("documents")}
          disabled={loading || !!error}
        >
          Documents
        </button>
      </div>
    </div>
  );

  // Show error state within the tab content if there's an error
  if (error) {
  return (
      <PageWithScroll
        header={header}
        tabs={tabs}
        className="bg-white"
        paddingBottom="pb-32"
      >
        <div className="p-4 text-center">
          <p className="text-[#797879] mb-4">{error}</p>
        <button
          onClick={goBack}
            className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
        >
            Go Back
        </button>
        </div>
      </PageWithScroll>
    );
  }

  // If we don't have vehicle data yet and we're loading, show skeletons
  if (loading || !vehicle) {
    return (
      <PageWithScroll
        header={header}
        tabs={tabs}
        className="bg-white"
        paddingBottom="pb-32"
      >
        {activeTab === "vehicle" && (
          <div>
            <VehicleImageSkeleton />
            <div className="p-4">
              <VehicleInfoSkeleton />
              <CurrentUserSkeleton />
              <VehicleInfoSkeleton />
              <VehicleInfoSkeleton />
            </div>
          </div>
        )}
        {activeTab === "images" && (
          <VehicleImageSkeleton />
        )}
        {activeTab === "documents" && (
          <div className="p-4">
            <VehicleInfoSkeleton />
          </div>
        )}
      </PageWithScroll>
    );
  }

  // Pagination logic for Maintenance
  const maintenanceItems = Array.isArray(maintenanceRecords) && maintenanceRecords.length > 0 
    ? maintenanceRecords 
    : (Array.isArray(vehicle?.maintenance_items) ? vehicle.maintenance_items : []);
  const totalMaintenancePages = Math.ceil(
    (maintenanceItems?.length || 0) / itemsPerPage
  );
  const paginatedMaintenanceItems = (maintenanceItems || []).slice(
    (maintenancePage - 1) * itemsPerPage,
    maintenancePage * itemsPerPage
  );

  // Pagination logic for Bookings
  const vehicleBookings = Array.isArray(bookings) && bookings.length > 0 
    ? bookings 
    : (Array.isArray(vehicle?.bookings) ? vehicle.bookings : []);

  // Debug logging to see what booking data we have
  console.log(`📊 VehicleStatusScreen booking data for vehicle ${vehicle?.id}:`, {
    bookingsFromProps: bookings?.length || 0,
    bookingsFromVehicle: vehicle?.bookings?.length || 0,
    totalBookings: vehicleBookings?.length || 0,
    bookings: vehicleBookings
  });
  
  // Filter upcoming bookings (future bookings that are confirmed/pending)
  const upcomingBookings = (vehicleBookings || []).filter(booking => 
    ["Confirmed", "Pending", "CONFIRMED", "PENDING"].includes(booking.status)
  ).filter(booking => 
    new Date(booking.start_datetime) > new Date()
  ).sort((a, b) => 
    new Date(a.start_datetime).getTime() - new Date(b.start_datetime).getTime()
  );

  // Check if car is currently booked (has active booking right now)
  const currentBooking = (vehicleBookings || []).find(booking => {
    const now = new Date();
    const startDate = new Date(booking.start_datetime);
    const endDate = new Date(booking.end_datetime);
    
    // Check for multiple booking statuses that indicate the vehicle is booked
    const isActiveStatus = ["Confirmed", "Pending", "Completed", "CONFIRMED", "PENDING", "COMPLETED"].includes(booking.status);
    
    // Debug logging to help diagnose booking status issues
    console.log(`🚗 Checking booking ${booking.reference || booking.id}:`, {
      status: booking.status,
      start: startDate.toISOString(),
      end: endDate.toISOString(),
      now: now.toISOString(),
      isActiveStatus,
      isCurrentlyActive: isActiveStatus && now >= startDate && now <= endDate
    });
    
    return isActiveStatus && now >= startDate && now <= endDate;
  });

  const isCurrentlyBooked = !!currentBooking;

  // Debug final booking status
  console.log(`🎯 Vehicle ${vehicle?.id} booking status:`, {
    isCurrentlyBooked,
    currentBooking: currentBooking ? {
      id: currentBooking.id,
      reference: currentBooking.reference,
      status: currentBooking.status,
      start: currentBooking.start_datetime,
      end: currentBooking.end_datetime
    } : null,
    upcomingBookingsCount: upcomingBookings.length
  });

  // Find current possessor (the person who currently has the vehicle)
  // Sort by handover datetime to get the most recent completed possession
  const currentPossessor = possessions
    ?.filter(possession => 
      possession.status === "completed" && 
      possession.handover_actual_datetime
    )
    ?.sort((a, b) => 
      new Date(b.handover_actual_datetime!).getTime() - 
      new Date(a.handover_actual_datetime!).getTime()
    )?.[0]?.to_party;
  
  const totalBookingsPages = Math.ceil((vehicleBookings?.length || 0) / itemsPerPage);
  const paginatedBookings = (vehicleBookings || []).slice(
    (bookingsPage - 1) * itemsPerPage,
    bookingsPage * itemsPerPage
  );

  return (
    <PageWithScroll
      header={header}
      tabs={tabs}
      className="bg-white"
      paddingBottom="pb-32"
    >
      {/* Tab Content */}
      {activeTab === "vehicle" && (
        <div>
          {/* Vehicle Image Preview */}
          <div className="p-4">
            <VehicleImageCarousel 
              images={imageUrls} 
              vehicle={vehicle}
              onImagesUpdate={async (newImages, newMediaRecords) => {
                // Update the local image URLs state
                setImageUrls(newImages);
                
                // Refresh vehicle data to get updated media records
                try {
                  const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                  if (refreshedVehicle) {
                    setVehicle(refreshedVehicle);
                  }
                } catch (error) {
                  console.error("Error refreshing vehicle data:", error);
                }
              }}
            />
          </div>

          {/* Vehicle Information */}
          <div className="p-4">
            <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Car size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Vehicle Information</h3>
            </div>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Make & Model:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.model?.make?.name} {vehicle.model?.model}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Registration:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.vehicle_registration || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Year:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.manufacturing_year || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">Color:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.color || 'Not specified'}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-[#797879] text-sm">VIN:</span>
              <span className="text-[#333333] text-sm font-medium">
                {vehicle.vin_number || 'Not specified'}
              </span>
            </div>
          </div>
          </div>
        </div>

      {/* Current User */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <User size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                {isCurrentlyBooked ? "Current User" : currentPossessor?.individual ? "Current Possessor" : "Vehicle Status"}
              </h3>
            </div>
            <span className={`text-xs px-2 py-1 rounded-full shadow-sm ${
              isCurrentlyBooked 
                ? "bg-[#FFD700] text-[#333333]" 
                : "bg-[#e6ffe6] text-[#007A2F]"
            }`}>
              {isCurrentlyBooked ? "Currently Booked" : "Available"}
            </span>
          </div>
          {isCurrentlyBooked && currentBooking && (
            <div className="flex items-center justify-between">
              <div>
                <p className="text-[#333333] font-medium">
                  Booking: {currentBooking.reference}
                </p>
                <div className="flex items-center mt-1">
                  <Calendar size={14} className="text-[#797879] mr-1" />
                  <p className="text-xs text-[#797879]">
                    Until:{" "}
                    {new Date(currentBooking.end_datetime).toLocaleString()}
                  </p>
                </div>
                {currentBooking.notes && (
                  <p className="text-xs text-[#797879] mt-1">
                    {currentBooking.notes}
                  </p>
                )}
              </div>
            </div>
          )}
          
          {!isCurrentlyBooked && (
            <div className="text-center py-4">
              {currentPossessor?.individual ? (
                <>
                  <p className="text-[#333333] font-medium mb-2">
                    Currently with {currentPossessor.individual.first_name} {currentPossessor.individual.last_name}
                  </p>
                  <p className="text-xs text-[#797879] mb-3">No active booking</p>
                </>
              ) : (
                <>
                  <p className="text-[#333333] font-medium mb-2">Vehicle is currently available</p>
                  <p className="text-xs text-[#797879] mb-3">No active booking</p>
                </>
              )}
              {upcomingBookings.length > 0 && (
                <div className="bg-[#fff7ed] p-3 rounded-lg border border-orange-100 mb-3">
                  <div className="flex items-center justify-center">
                    <Calendar size={14} className="text-orange-600 mr-1" />
                    <p className="text-xs text-orange-600">
                      Next booking: {new Date(upcomingBookings[0].start_datetime).toLocaleString()}
                    </p>
                  </div>
                </div>
              )}
              <div className="bg-[#f0f9ff] p-3 rounded-lg border border-blue-100">
                <div className="flex items-center justify-center">
                  <MapPin size={14} className="text-blue-600 mr-1" />
                  <p className="text-xs text-blue-600">GPS tracking not currently available</p>
                </div>
              </div>
            </div>
          )}
          
          {isCurrentlyBooked && currentBooking && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-[#797879] mb-1">
                <span>Time Remaining</span>
                <span>
                  {(() => {
                    const endTime = new Date(currentBooking.end_datetime);
                    const now = new Date();
                    const diffMs = endTime.getTime() - now.getTime();
                    const hours = Math.floor(diffMs / (1000 * 60 * 60));
                    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                    return `${Math.max(0, hours)}h ${Math.max(0, minutes)}m`;
                  })()}
                </span>
              </div>
              <div className="h-2 bg-[#f2f2f2] rounded-full overflow-hidden">
                <div
                  className="h-full bg-[#009639] rounded-full"
                  style={{
                    width: `${(() => {
                      const startTime = new Date(currentBooking.start_datetime);
                      const endTime = new Date(currentBooking.end_datetime);
                      const now = new Date();
                      const totalDuration = endTime.getTime() - startTime.getTime();
                      const elapsed = now.getTime() - startTime.getTime();
                      const progress = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
                      return 100 - progress;
                    })()}%`,
                  }}
                ></div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Upcoming Bookings List */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center">
              <Calendar size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">Upcoming Bookings</h3>
            </div>
          </div>
          <div className="space-y-3">
            {upcomingBookings && upcomingBookings.length > 0 ? (
              upcomingBookings.slice(0, 3).map((booking) => (
                <div
                  key={booking.id}
                  className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                >
                  <div className="flex items-start">
                    <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                      <Calendar size={18} className="text-[#009639]" />
                    </div>
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-[#333333]">{booking?.reference}</h3>
                          <p className="text-xs text-[#797879]">
                            {booking?.notes || 'No notes provided'}
                          </p>
                        </div>
                        <span
                          className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                            booking.status
                          )}`}
                        >
                          {getStatusLabel(booking.status)}
                        </span>
                      </div>
                      <div className="flex items-center mt-2">
                        <Clock size={14} className="text-[#797879] mr-1" />
                        <span className="text-xs text-[#797879]">
                          {formatDateForInput(booking?.start_datetime)} -{" "}
                          {formatDateForInput(booking?.end_datetime)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-[#797879] text-sm text-center py-4">
                No upcoming bookings.
              </p>
            )}
          </div>
          {upcomingBookings && upcomingBookings.length > 3 && (
            <div className="mt-3 text-center">
              <button
                className="text-[#009639] text-sm font-medium"
                onClick={() => setMaintenanceTab("bookings")}
              >
                View All Upcoming Bookings ({upcomingBookings.length})
        </button>
            </div>
          )}
            </div>
      </div>

                {/* Marketplace Listing */}
          <div className="p-4">
            <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#009639] mr-2">
                    <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/>
                  </svg>
                  <h3 className="text-[#333333] font-medium">Marketplace Listing</h3>
            </div>
                {/* Only show manage button if user owns any listing */}
                {currentUserPartyId && vehicle?.listings?.some(listing => listing.party_id === currentUserPartyId) && (
                  <button
                    className="text-[#009639] text-sm font-medium"
                    onClick={() => {
                      const userListing = vehicle?.listings?.find(listing => listing.party_id === currentUserPartyId);
                      if (userListing?.id) {
                        navigateToListingManagement(userListing.id.toString());
                      }
                    }}
                  >
                    Manage Listing
                  </button>
                )}
              </div>
              
                             {/* Display actual vehicle listings */}
               {vehicle?.listings && vehicle.listings.length > 0 ? (
                 <div className="space-y-3">
                   {vehicle.listings.slice(0, 2).map((listing, index) => {
                     const isOwner = currentUserPartyId && listing.party_id === currentUserPartyId;
                     return (
                      <div 
                        key={listing.id || index} 
                        className="p-3 bg-green-50 rounded-lg border border-green-100 cursor-pointer hover:bg-green-100 transition-colors"
                        onClick={() => {
                          if (isOwner && listing.id) {
                            navigateToListingManagement(listing.id.toString());
                          } else {
                            // Navigate to view-only listing details for non-owners
                            navigate(`/vehicle-fraction-details/${listing.id || vehicle.id}`);
                          }
                        }}
                      >
                        <div className="flex justify-between items-start mb-2">
            <div>
                            <p className="text-[#333333] font-medium text-sm">
                              {listing.listing_type === 'SHORT_TERM_LEASE_OUT' ? 'Short-term Lease' : 
                               listing.listing_type === 'LONG_TERM_LEASE_OUT' ? 'Long-term Lease' : 
                               listing.listing_type === 'CO_OWNERSHIP_SALE' ? 'Co-ownership Sale' : 'Active Listing'}
                            </p>
                            <p className="text-xs text-[#797879]">
                              {listing.listing_type === 'CO_OWNERSHIP_SALE' ? 
                                `${(listing.fraction * 100).toFixed(0)}% ownership available` : 
                                `${listing.condition === 'new' ? 'New' : 'Used'} vehicle`}
                            </p>
                            <p className="text-xs text-[#009639] font-medium">
                              R{listing.asking_price.toLocaleString()}
                            </p>
            </div>
                          <div className="flex flex-col items-end gap-1">
                            <span className="text-xs px-2 py-1 bg-green-500 text-white rounded-full">
                              Active
                            </span>
                            {!isOwner && (
                              <button 
                                className="text-xs px-2 py-1 bg-[#009639] text-white rounded-full hover:bg-[#007A2F] transition-colors"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Handle apply action for non-owners
                                  navigate(`/apply-listing/${listing.id || vehicle.id}`);
                                }}
                              >
                                Apply
                              </button>
                            )}
          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-green-600 mr-1">
                              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" fill="currentColor"/>
                            </svg>
                            <span className="text-xs text-green-600">
                              {listing.audience === 'BUSINESS' ? 'Business' : 
                               listing.audience === 'E_HAILING' ? 'E-Hailing' : 'Consumer'} Listing
                            </span>
                          </div>
                          <ChevronRight size={16} className="text-[#797879]" />
                        </div>
                      </div>
                     );
                   })}
                  
                   {vehicle.listings.length > 2 && (
                     <div className="text-center">
                       <button
                         className="text-[#009639] text-sm font-medium"
                         onClick={() => {
                           const hasOwnListings = currentUserPartyId && vehicle?.listings?.some(listing => listing.party_id === currentUserPartyId);
                           if (hasOwnListings) {
                             navigate(`/my-listing/${vehicle.id}`);
                           } else {
                             navigate(`/vehicle-marketplace`);
                           }
                         }}
                       >
                         {currentUserPartyId && vehicle?.listings?.some(listing => listing.party_id === currentUserPartyId)
                           ? `View All Listings (${vehicle.listings.length})` 
                           : 'View More Listings'}
                       </button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-4">
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#797879] mx-auto mb-3">
                    <path d="M7 18c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zM1 2v2h2l3.6 7.59-1.35 2.45c-.16.28-.25.61-.25.96 0 1.1.9 2 2 2h12v-2H7.42c-.14 0-.25-.11-.25-.25l.03-.12L8.1 13h7.45c.75 0 1.41-.41 1.75-1.03L21.7 4H5.21l-.94-2H1zm16 16c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/>
                  </svg>
                  {currentUserPartyId && vehicle?.party_id === currentUserPartyId ? (
                    // Vehicle owner view - show option to create listing
                    <>
                      <p className="text-[#333333] font-medium mb-2">Not Listed on Marketplace</p>
                      <p className="text-[#797879] text-sm mb-4">Create a listing to share your vehicle with others</p>
                      <button
                        className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors shadow-sm"
                        onClick={() => navigate('/list-vehicle')}
                      >
                        Create Listing
                      </button>
                    </>
                  ) : (
                    // Non-owner view - show that vehicle is not available
                    <>
                      <p className="text-[#333333] font-medium mb-2">Not Available for Sharing</p>
                      <p className="text-[#797879] text-sm mb-4">This vehicle is not currently listed on the marketplace</p>
                      <button
                        className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors shadow-sm"
                        onClick={() => navigate('/vehicle-marketplace')}
                      >
                        Browse Available Vehicles
                      </button>
                    </>
                  )}
                </div>
              )}
          </div>
        </div>

          {/* Emergency Contacts */}
          <div className="p-4">
            <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
              <div className="flex items-center mb-3">
                <AlertTriangle size={20} className="text-red-500 mr-2" />
                <h3 className="text-[#333333] font-medium">Emergency Contacts</h3>
              </div>
          <div className="space-y-3">
            {/* Police */}
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-[#009639]">
            <div className="flex items-center">
                <Shield size={16} className="text-red-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Police</p>
                  <p className="text-xs text-[#797879]">Emergency Services</p>
                </div>
              </div>
              <button
                className="flex items-center justify-center gap-2 bg-[#009639] text-white py-1 rounded-full text-sm font-medium shadow-sm w-20"
                onClick={() => window.open('tel:10111')}
              >
                <Phone size={16} className="text-white" /> 10111
              </button>
          </div>

            {/* Ambulance */}
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-[#009639]">
              <div className="flex items-center">
                <AlertTriangle size={16} className="text-blue-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Ambulance</p>
                  <p className="text-xs text-[#797879]">Medical Emergency</p>
                </div>
              </div>
              <button
                className="flex items-center justify-center gap-2 bg-[#009639] text-white py-1 rounded-full text-sm font-medium shadow-sm w-20"
                onClick={() => window.open('tel:10177')}
              >
                <Phone size={16} className="text-white" /> 10177
              </button>
            </div>

            {/* Roadside Assistance */}
            <div className="flex items-center justify-between p-3 bg-white rounded-lg border border-[#009639]">
              <div className="flex items-center">
                <Car size={16} className="text-yellow-600 mr-2" />
                <div>
                  <p className="text-[#333333] font-medium">Roadside Assistance</p>
                  <p className="text-xs text-[#797879]">Vehicle Breakdown</p>
                </div>
              </div>
              <button
                className="flex items-center justify-center gap-2 bg-[#009639] text-white py-1 rounded-full text-sm font-medium shadow-sm w-20"
                onClick={() => window.open('tel:0861102872')}
              >
                <Phone size={16} className="text-white" /> AA
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4">
        <div className="flex border-b border-gray-200 mb-4">
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              maintenanceTab === "maintenance"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
              setMaintenanceTab("maintenance");
              setMaintenancePage(1);
            }}
          >
            Maintenance
          </button>
          <button
            className={`flex-1 py-2 text-center text-sm font-medium ${
              maintenanceTab === "bookings"
                ? "text-[#009639] border-b-2 border-[#009639]"
                : "text-[#797879]"
            }`}
            onClick={() => {
                setMaintenanceTab("bookings");
              setBookingsPage(1);
            }}
          >
            All Bookings
          </button>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
          {maintenanceTab === "maintenance" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Car size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">Maintenance</h3>
                </div>
              </div>
              <div className="space-y-3">
                {paginatedMaintenanceItems.length > 0 ? (
                  paginatedMaintenanceItems.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Wrench size={18} className="text-[#009639]" />
              </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item.name}</h5>
                              <p className="text-xs text-[#797879]">
                                {item.description}
                              </p>
            </div>
                            <div className="flex items-center gap-2 ml-auto">
                              <span
                                className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                  item.status
                                )}`}
                              >
                                {getStatusLabel(item.status)}
                              </span>
                              <button
                                className="text-[#009639] text-sm flex items-center"
                                onClick={() =>
                                  navigateToMaintenanceDetails(item.id.toString())
                                }
                              >
                                <ChevronRight
                                  size={20}
                                  className="text-[#797879]"
                                />
                              </button>
          </div>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput((item as any).dueDate || (item as any).due_date)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No maintenance items available.
                  </p>
                )}
              </div>
              {/* Maintenance Pagination */}
              {totalMaintenancePages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={maintenancePage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {maintenancePage} of {totalMaintenancePages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setMaintenancePage((prev) =>
                        Math.min(prev + 1, totalMaintenancePages)
                      )
                    }
                    disabled={maintenancePage === totalMaintenancePages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}

          {maintenanceTab === "bookings" && (
            <>
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Calendar size={20} className="text-[#009639] mr-2" />
                  <h3 className="text-[#333333] font-medium">All Bookings</h3>
          </div>
              </div>
              <div className="space-y-3">
                {paginatedBookings.length > 0 ? (
                  paginatedBookings.map((item) => (
                    <div
                      key={item.id}
                      className="ride-card p-4 drop-shadow-sm rounded-xl border border-gray-100"
                    >
                      <div className="flex items-start">
                        <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                          <Calendar size={18} className="text-[#009639]" />
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3>{vehicle?.model?.model}</h3>
                              <h5>{item?.reference}</h5>
                              <p className="text-xs text-[#797879]">
                                {item?.notes}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-2 py-0.5 rounded-full shadow-sm ${getStatusColor(
                                item.status
                              )}`}
                            >
                              {getStatusLabel(item.status)}
                            </span>
                          </div>
                          <div className="flex items-center mt-2">
                            <Clock size={14} className="text-[#797879] mr-1" />
                            <span className="text-xs text-[#797879]">
                              {formatDateForInput(item?.start_datetime)} -{" "}
                              {formatDateForInput(item?.end_datetime)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-[#797879] text-sm">
                    No bookings available.
                  </p>
                )}
              </div>
              {/* Bookings Pagination */}
              {totalBookingsPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={bookingsPage === 1}
                  >
                    <ChevronLeft size={20} />
                  </button>
                  <span className="text-sm text-[#333333]">
                    Page {bookingsPage} of {totalBookingsPages}
                  </span>
                  <button
                    className="p-2 text-[#009639] disabled:text-[#797879]"
                    onClick={() =>
                      setBookingsPage((prev) =>
                        Math.min(prev + 1, totalBookingsPages)
                      )
                    }
                    disabled={bookingsPage === totalBookingsPages}
                  >
                    <ChevronRightPagination size={20} />
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
        </div>
      )}

      {/* Images Tab */}
      {activeTab === "images" && (
        <div className="pb-32">
          {imageUrls && imageUrls.length > 0 ? (
            <div>
              {/* Main Image Display */}
              <div className="p-4">
                <VehicleImageCarousel 
                  images={imageUrls} 
                  vehicle={vehicle}
                  onImagesUpdate={async (newImages, newMediaRecords) => {
                    setImageUrls(newImages);
                    try {
                      const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                      if (refreshedVehicle) {
                        setVehicle(refreshedVehicle);
                      }
                    } catch (error) {
                      console.error("Error refreshing vehicle data:", error);
                    }
                  }}
                />
              </div>
              
              {/* Thumbnails Section */}
              <div className="px-4 pb-4">
                <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-[#333333] font-medium">All Images ({imageUrls.length})</h3>
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      multiple
                      onChange={async (e) => {
                        const files = e.target.files;
                        if (!files || files.length === 0 || !vehicle) return;

                        try {
                          const uploadPromises = Array.from(files).map(async (file) => {
                            const uploadResult = await DocumentUpload(file, "vehicleMedia");
                            
                            if (uploadResult && uploadResult.path) {
                              const formData = new FormData();
                              formData.append("vehicleID", vehicle.id.toString());
                              formData.append("documentUrl", uploadResult.path);
                              
                              const result = await addVehicleMedia(null, formData);
                              if ('success' in result && result.success) {
                                return uploadResult.path;
                              }
                            }
                            throw new Error("Upload failed");
                          });
                          
                          const uploadedPaths = await Promise.all(uploadPromises);
                          
                          const newImageUrls = await Promise.all(
                            uploadedPaths.map(async (path) => {
                              const urlResult = await getUrl({ path });
                              return urlResult.url.toString();
                            })
                          );
                          
                          setImageUrls([...imageUrls, ...newImageUrls]);
                          
                          const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                          if (refreshedVehicle) {
                            setVehicle(refreshedVehicle);
                          }
                        } catch (error) {
                          console.error("Error uploading images:", error);
                        }
                        e.target.value = "";
                      }}
                      id="add-images-input"
                    />
                    <label 
                      htmlFor="add-images-input"
                      className="bg-[#009639] text-white p-2 rounded-full cursor-pointer hover:bg-[#007A2F] transition-colors shadow-sm"
                    >
                      <Plus size={20} />
                    </label>
              </div>
                  
                  <div className="grid grid-cols-4 gap-2">
                    {imageUrls.map((imageUrl, index) => (
                      <div key={index} className="relative aspect-square bg-[#f2f2f2] rounded-lg overflow-hidden">
                        <img
                          src={imageUrl}
                          alt={`Vehicle image ${index + 1}`}
                          className="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
                          onClick={() => {
                            // You can add logic here to focus on this image in the main carousel
                            // For now, this just shows the thumbnail is clickable
                          }}
                        />
                        <button
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                          onClick={async () => {
                            if (!vehicle?.media?.[index]) return;
                            
                            const confirmed = window.confirm("Are you sure you want to delete this image?");
                            if (!confirmed) return;

                            try {
                              const mediaRecord = vehicle.media[index];
                              await DocumentDelete(mediaRecord.media_path);
                              await deleteVehicleMedia(mediaRecord.id);
                              
                              const updatedImages = imageUrls.filter((_, i) => i !== index);
                              setImageUrls(updatedImages);
                              
                              const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                              if (refreshedVehicle) {
                                setVehicle(refreshedVehicle);
                              }
                            } catch (error) {
                              console.error("Error deleting image:", error);
                            }
                          }}
                        >
                          <X size={12} />
                        </button>
            </div>
                    ))}
          </div>
        </div>
      </div>
    </div>
          ) : (
            <div className="p-4">
              <div className="h-48 bg-[#f2f2f2] rounded-xl flex flex-col items-center justify-center">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#797879] mb-4">
                  <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" fill="currentColor"/>
                </svg>
                <p className="text-[#333333] font-medium mb-2">No Images Available</p>
                <p className="text-[#797879] text-sm mb-4">Upload images to showcase this vehicle</p>
                
                {vehicle && (
                  <>
                    <input
                      type="file"
                      className="hidden"
                      accept="image/*"
                      multiple
                      onChange={async (e) => {
                        const files = e.target.files;
                        if (!files || files.length === 0) return;

                        try {
                          const uploadPromises = Array.from(files).map(async (file) => {
                            const uploadResult = await DocumentUpload(file, "vehicleMedia");
                            
                            if (uploadResult && uploadResult.path) {
                              const formData = new FormData();
                              formData.append("vehicleID", vehicle.id.toString());
                              formData.append("documentUrl", uploadResult.path);
                              
                              const result = await addVehicleMedia(null, formData);
                              if ('success' in result && result.success) {
                                return uploadResult.path;
                              }
                            }
                            throw new Error("Upload failed");
                          });
                          
                          const uploadedPaths = await Promise.all(uploadPromises);
                          
                          const newImageUrls = await Promise.all(
                            uploadedPaths.map(async (path) => {
                              const urlResult = await getUrl({ path });
                              return urlResult.url.toString();
                            })
                          );
                          
                          setImageUrls(newImageUrls);
                          
                          const refreshedVehicle = await getVehicleByIdWithListings(vehicle.id);
                          if (refreshedVehicle) {
                            setVehicle(refreshedVehicle);
                          }
                        } catch (error) {
                          console.error("Error uploading images:", error);
                        }
                        e.target.value = "";
                      }}
                      id="empty-state-upload"
                    />
                    <label 
                      htmlFor="empty-state-upload"
                      className="bg-[#009639] mb-4 text-white px-6 py-3 rounded-full font-medium cursor-pointer hover:bg-[#007A2F] transition-colors shadow-sm flex items-center"
                    >
                      <Upload size={20} className="" />
                      
                    </label>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Documents Tab */}
      {activeTab === "documents" && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#009639] mr-2">
                  <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
                </svg>
                <h3 className="text-[#333333] font-medium">Vehicle Documents</h3>
              </div>
            </div>
            
            <div className="text-center py-8">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="text-[#797879] mx-auto mb-4">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fill="currentColor"/>
              </svg>
              <p className="text-[#333333] font-medium mb-2">Document Management</p>
              <p className="text-[#797879] text-sm mb-4">Vehicle documents will be available here</p>
              <p className="text-xs text-[#797879]">Feature coming soon</p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="p-4 bg-white border-t border-[#f2f2f2] space-y-3 mt-4">
        {/* Book Vehicle Button - only show if vehicle is not currently booked */}
        {!isCurrentlyBooked && (
          <button
            className="w-full py-3 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm mb-2"
            onClick={() => navigateToBookingCalendar(vehicle.id.toString())}
          >
            Book This Vehicle
          </button>
        )}
        
        {/* Handover Button */}
        <button
          className="w-full py-4 bg-[#009639] text-white rounded-full text-lg font-semibold shadow-sm"
          onClick={() => navigateToVehicleHandover(vehicle.id.toString())}
        >
          Initiate Vehicle Handover
        </button>
      </div>
    </PageWithScroll>
  );
} 