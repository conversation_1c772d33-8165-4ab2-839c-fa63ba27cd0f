"use client";

import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Plus, Users, MapPin } from "lucide-react";

interface CommunityScreenProps {
  params?: Record<string, any>;
}

export default function CommunityScreen({ params }: CommunityScreenProps) {
  const { goBack, canGoBack, navigateToCreateGroup, navigateToGroupDetails } = useNavigation();

  const mockGroups = [
    { id: "1", name: "Downtown Commuters", members: 12, location: "Downtown Area", activity: "Very Active" },
    { id: "2", name: "Weekend Warriors", members: 8, location: "Suburban", activity: "Active" },
    { id: "3", name: "Business Travelers", members: 15, location: "Airport District", activity: "Moderate" }
  ];

  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        {canGoBack() ? (
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
        ) : (
          <div className="w-10 h-10"></div>
        )}
        <h1 className="text-lg font-semibold text-gray-900">Community</h1>
        <button
          onClick={navigateToCreateGroup}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-[#009639]"
        >
          <Plus size={20} className="text-white" />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto scrollbar-hide p-4">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-[#009639] to-[#007A2F] rounded-lg p-6 mb-6 text-white">
          <h2 className="text-xl font-bold mb-2">Join the Community</h2>
          <p className="text-green-100 mb-4">Connect with local car sharing groups and save money while helping the environment.</p>
          <button
            onClick={navigateToCreateGroup}
            className="bg-white text-[#009639] px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Create New Group
          </button>
        </div>

        {/* Groups List */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Groups</h3>
          <div className="space-y-4">
            {mockGroups.map((group) => (
              <button
                key={group.id}
                onClick={() => navigateToGroupDetails(group.id)}
                className="w-full bg-white border border-gray-200 rounded-lg p-4 text-left hover:border-[#009639] transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">{group.name}</h4>
                    <div className="flex items-center text-sm text-gray-600 mb-2">
                      <MapPin size={14} className="mr-1" />
                      {group.location}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-sm text-gray-600">
                        <Users size={14} className="mr-1" />
                        {group.members} members
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        group.activity === 'Very Active' ? 'bg-green-100 text-green-800' :
                        group.activity === 'Active' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {group.activity}
                      </span>
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-900 mb-3">Community Impact</h3>
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-[#009639]">156</div>
              <div className="text-xs text-gray-600">Active Members</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-[#009639]">2.3k</div>
              <div className="text-xs text-gray-600">Rides Shared</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-[#009639]">$12k</div>
              <div className="text-xs text-gray-600">Money Saved</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 