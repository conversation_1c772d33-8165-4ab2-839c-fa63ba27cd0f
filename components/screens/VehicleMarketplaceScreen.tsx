"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Search, Filter, Star, TrendingUp, Percent, Calendar } from "lucide-react";

interface VehicleMarketplaceScreenProps {
  params?: Record<string, any>;
}

type FractionListing = {
  id: string;
  title: string;
  price: number;
  monthlyReturn: number;
  location: string;
  rating: number;
  totalShares: number;
  availableShares: number;
  roi: string;
  type: "fraction";
};

type LeaseListing = {
  id: string;
  title: string;
  price: number;
  period: string;
  location: string;
  rating: number;
  duration: string;
  type: "lease";
};

type PurchaseListing = {
  id: string;
  title: string;
  price: number;
  period: string;
  location: string;
  rating: number;
  mileage: string;
  type: "purchase";
};

type Listing = FractionListing | LeaseListing | PurchaseListing;

export default function VehicleMarketplaceScreen({ params }: VehicleMarketplaceScreenProps) {
  const { goBack, navigateToVehicleDetails, navigateToFractionPurchase } = useNavigation();
  const [activeTab, setActiveTab] = useState<"fractions" | "leases" | "purchases">("fractions");

  const mockFractions: FractionListing[] = [
    {
      id: "1",
      title: "Tesla Model 3 - 25% Share",
      price: 15000,
      monthlyReturn: 450,
      location: "Downtown",
      rating: 4.8,
      totalShares: 4,
      availableShares: 1,
      roi: "8.2%",
      type: "fraction"
    },
    {
      id: "2", 
      title: "BMW X3 - 50% Share",
      price: 22000,
      monthlyReturn: 680,
      location: "Uptown",
      rating: 4.6,
      totalShares: 2,
      availableShares: 1,
      roi: "9.1%",
      type: "fraction"
    }
  ];

  const mockLeases: LeaseListing[] = [
    {
      id: "3",
      title: "Honda Civic - 12 Month Lease",
      price: 350,
      period: "monthly",
      location: "Midtown",
      rating: 4.7,
      duration: "12 months",
      type: "lease"
    },
    {
      id: "4",
      title: "Toyota Prius - 6 Month Lease", 
      price: 280,
      period: "monthly",
      location: "Suburban",
      rating: 4.9,
      duration: "6 months",
      type: "lease"
    }
  ];

  const mockPurchases: PurchaseListing[] = [
    {
      id: "5",
      title: "Ford Focus - Full Purchase",
      price: 18500,
      period: "one-time",
      location: "Downtown",
      rating: 4.5,
      mileage: "45,000 km",
      type: "purchase"
    }
  ];

  const getCurrentListings = (): Listing[] => {
    switch (activeTab) {
      case "fractions": return mockFractions;
      case "leases": return mockLeases;
      case "purchases": return mockPurchases;
      default: return mockFractions;
    }
  };

  const handleItemClick = (item: Listing) => {
    if (item.type === "fraction") {
      // TODO: Add ownership check here when user context is available
      // For now, default to fraction purchase
      navigateToFractionPurchase(item.id);
    } else {
      navigateToVehicleDetails(item.id);
    }
  };

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <button
          onClick={goBack}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">Vehicle Marketplace</h1>
        <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
          <Filter size={20} className="text-gray-600" />
        </button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Search Bar */}
        <div className="p-4 border-b border-gray-100">
          <div className="relative">
            <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search vehicles, fractions, leases..."
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            />
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="px-4 py-2 border-b border-gray-100">
          <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            {[
              { key: "fractions", label: "Fractions", icon: Percent },
              { key: "leases", label: "Leases", icon: Calendar },
              { key: "purchases", label: "Buy", icon: TrendingUp }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setActiveTab(key as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md font-medium transition-all ${
                  activeTab === key
                    ? "bg-white text-[#009639] shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                <Icon size={16} />
                <span className="text-sm">{label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Featured Banner */}
        <div className="p-4">
          <div className="bg-gradient-to-r from-[#009639] to-[#007A2F] rounded-lg p-6 text-white mb-6">
            <h2 className="text-xl font-bold mb-2">
              {activeTab === "fractions" && "Own a Fraction, Earn Returns"}
              {activeTab === "leases" && "Flexible Vehicle Leasing"}
              {activeTab === "purchases" && "Buy Your Perfect Vehicle"}
            </h2>
            <p className="text-green-100">
              {activeTab === "fractions" && "Invest in vehicle shares and earn monthly returns from rentals."}
              {activeTab === "leases" && "Short-term and long-term vehicle leasing options available."}
              {activeTab === "purchases" && "Find quality pre-owned vehicles at great prices."}
            </p>
          </div>

          {/* Listings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Available {activeTab === "fractions" ? "Fractions" : activeTab === "leases" ? "Leases" : "Vehicles"}
              </h3>
              <span className="text-sm text-gray-600">{getCurrentListings().length} available</span>
            </div>

            {getCurrentListings().map((item) => (
              <button
                key={item.id}
                onClick={() => handleItemClick(item)}
                className="w-full bg-white border border-gray-200 rounded-lg p-4 text-left hover:border-[#009639] hover:shadow-sm transition-all"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.location}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-[#009639]">
                      ${item.price.toLocaleString()}
                      {item.type === "lease" && "/mo"}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Star size={12} className="text-yellow-400 fill-current mr-1" />
                      {item.rating}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  {item.type === "fraction" && (
                    <>
                      <span className="text-sm text-gray-600">
                        {item.availableShares}/{item.totalShares} shares available
                      </span>
                      <div className="flex items-center space-x-3">
                        <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {item.roi} ROI
                        </span>
                        <span className="text-sm text-gray-600">
                          ${item.monthlyReturn}/mo return
                        </span>
                      </div>
                    </>
                  )}
                  
                  {item.type === "lease" && (
                    <>
                      <span className="text-sm text-gray-600">{item.duration}</span>
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Available Now
                      </span>
                    </>
                  )}
                  
                  {item.type === "purchase" && (
                    <>
                      <span className="text-sm text-gray-600">{item.mileage}</span>
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Certified
                      </span>
                    </>
                  )}
                </div>
              </button>
            ))}
          </div>

          {/* Info Cards */}
          <div className="mt-8 grid grid-cols-2 gap-4">
            {activeTab === "fractions" && (
              <>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">📈</div>
                  <h4 className="font-semibold text-blue-900">High Returns</h4>
                  <p className="text-sm text-blue-700">Up to 12% ROI</p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">🔒</div>
                  <h4 className="font-semibold text-green-900">Secure Investment</h4>
                  <p className="text-sm text-green-700">Insured vehicles</p>
                </div>
              </>
            )}
            
            {activeTab === "leases" && (
              <>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">⚡</div>
                  <h4 className="font-semibold text-orange-900">Flexible Terms</h4>
                  <p className="text-sm text-orange-700">3-24 months</p>
                </div>
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">🛡️</div>
                  <h4 className="font-semibold text-purple-900">Full Coverage</h4>
                  <p className="text-sm text-purple-700">Insurance included</p>
                </div>
              </>
            )}
            
            {activeTab === "purchases" && (
              <>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">✅</div>
                  <h4 className="font-semibold text-red-900">Certified Quality</h4>
                  <p className="text-sm text-red-700">Inspected vehicles</p>
                </div>
                <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-4 text-center">
                  <div className="text-2xl mb-2">💰</div>
                  <h4 className="font-semibold text-indigo-900">Best Prices</h4>
                  <p className="text-sm text-indigo-700">Market value</p>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 