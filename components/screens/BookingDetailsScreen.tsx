"use client";

import React, { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { useBookingDetails } from "@/hooks/useBookingDetails";
import {
  ArrowLeft,
  Calendar,
  MessageSquare,
  ChevronRight,
  Edit,
  X,
  Phone,
  Mail,
  MapPin,
  RefreshCw,
  Car,
} from "lucide-react";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { Button } from "@/components/ui/button";
import { formatDateForInput } from "@/lib/utils";
import { deprecated_BookingStatus } from "@/types/bookings";
import type { ContactPointRead } from "@/types/contact-points";

const contactActions = {
  phone: {
    label: "Call",
    icon: <Phone size={18} className="mr-2" />,
    getUrl: (value: string) => `tel:${value}`,
  },
  email: {
    label: "Email",
    icon: <Mail size={18} className="mr-2" />,
    getUrl: (value: string) => `mailto:${value}`,
  },
  address: {
    label: "View Address",
    icon: <MapPin size={18} className="mr-2" />,
    getUrl: (value: string) =>
      `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(value)}`,
  },
};

interface BookingDetailsScreenProps {
  params?: { 
    bookingId?: string;
    bookingData?: {
      id: number;
      reference?: string;
      vehicle?: string;
      vehicleName?: string;
      member?: string;
      memberName?: string;
      startDate?: string;
      endDate?: string;
      start_datetime?: string;
      end_datetime?: string;
      status?: string;
      notes?: string;
      total_price?: number;
      // Member details if available
      memberFirstName?: string;
      memberLastName?: string;
      memberEmail?: string;
      memberPhone?: string;
    };
  };
}

export default function BookingDetailsScreen({ params }: BookingDetailsScreenProps) {
  const { goBack, navigateToMemberDetails } = useNavigation();
  
  // Debug the params
  console.log('BookingDetailsScreen params:', params);
  console.log('bookingId from params:', params?.bookingId);
  console.log('bookingData from params:', params?.bookingData);
  
  // Parse the bookingId more carefully
  const bookingIdString = params?.bookingId;
  const passedBookingData = params?.bookingData;
  let bookingId: number;
  
  if (!bookingIdString) {
    console.error('No bookingId provided in params');
    bookingId = 0; // Invalid ID that will cause proper error handling
  } else {
    bookingId = parseInt(bookingIdString);
    if (isNaN(bookingId)) {
      console.error('Invalid bookingId (not a number):', bookingIdString);
      bookingId = 0; // Invalid ID that will cause proper error handling
    }
  }
  
  console.log('Final parsed bookingId:', bookingId);
  console.log('Using passed booking data:', !!passedBookingData);
  
  const [showCancelModal, setShowCancelModal] = useState(false);
  
  // Only use SWR if we don't have booking data passed from parent
  const shouldFetch = !passedBookingData && bookingId > 0;
  const { 
    booking: fetchedBooking, 
    individual: fetchedIndividual, 
    contacts: fetchedContacts, 
    isLoading, 
    error, 
    refreshBookingDetails 
  } = useBookingDetails(shouldFetch ? bookingId : 0);

  // Use passed data if available, otherwise use fetched data
  const booking = passedBookingData ? {
    id: passedBookingData.id,
    reference: passedBookingData.reference || `BK-${passedBookingData.id}`,
    vehicle_id: passedBookingData.id, // Fallback if no vehicle ID
    start_datetime: passedBookingData.start_datetime || passedBookingData.startDate || '',
    end_datetime: passedBookingData.end_datetime || passedBookingData.endDate || '',
    status: passedBookingData.status || 'CONFIRMED',
    notes: passedBookingData.notes || '',
    total_price: passedBookingData.total_price || null,
    party_id: 1, // Will be used if we need to fetch more details
    created_at: new Date().toISOString(), // Fallback
    updated_at: new Date().toISOString(), // Fallback
  } : fetchedBooking;

  const individual = passedBookingData ? {
    id: 1, // Fallback ID
    first_name: passedBookingData.memberFirstName || passedBookingData.member?.split(' ')[0] || 'Unknown',
    last_name: passedBookingData.memberLastName || passedBookingData.member?.split(' ').slice(1).join(' ') || 'User',
    party_id: 1,
    middle_name: null,
    salutation: null,
    suffix: null,
    gender: null,
    birth_date: null,
    marital_status: null,
    nationality: null,
    preferred_language: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  } : fetchedIndividual;

  const contacts = passedBookingData ? [] : fetchedContacts; // Use empty array for passed data since we don't have contact details

  // Show loading only if we're actually fetching data
  const isActuallyLoading = shouldFetch && isLoading;

  // Create a simple header for error cases
  const simpleHeader = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Booking Details</h1>
      </div>
    </div>
  );

  // Early return for invalid bookingId (only if no data was passed)
  if (bookingId === 0 && !passedBookingData) {
    return (
      <PageWithScroll header={simpleHeader}>
        <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center px-4">
          <div className="text-center">
            <p className="text-red-600 mb-4">Invalid booking ID provided</p>
            <p className="text-[#797879] mb-4 text-sm">
              Provided ID: "{bookingIdString || 'undefined'}"
            </p>
            <Button
              onClick={goBack}
              className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            >
              Go Back
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "confirmed":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Confirmed
          </span>
        );
      case "active":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#e6ffe6] text-[#007A2F] font-medium">
            Active
          </span>
        );
      case "completed":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#f2f2f2] text-[#333333] font-medium">
            Completed
          </span>
        );
      case "cancelled":
        return (
          <span className="text-xs px-2 py-0.5 rounded-full bg-[#ffe6e6] text-[#7A0000] font-medium">
            Cancelled
          </span>
        );
      default:
        return null;
    }
  };

  const handleCancelBooking = () => {
    console.log("Cancelling booking:", booking?.reference);
    // In a real app, you would make an API call here
    setShowCancelModal(false);
    goBack();
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Booking Details</h1>
      </div>
      {booking?.status === deprecated_BookingStatus.CONFIRMED && (
        <button
          className="text-white text-sm font-medium"
          onClick={() => {
            // Navigate to edit booking - for now just show an alert
            alert("Edit booking functionality not implemented yet");
          }}
        >
          <Edit size={18} />
        </button>
      )}
    </div>
  );

  // Loading state (only for fetched data)
  if (isActuallyLoading) {
    return (
      <PageWithScroll header={simpleHeader}>
        <div className="min-h-screen bg-[#f5f5f5] p-4">
          <div className="bg-white rounded-xl p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded mb-4 w-3/4"></div>
            <div className="flex space-x-4">
              <div className="flex-1 h-16 bg-gray-200 rounded"></div>
              <div className="flex-1 h-16 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  // Error state (only for fetched data)
  if (shouldFetch && error) {
    return (
      <PageWithScroll header={simpleHeader}>
        <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center px-4">
          <div className="text-center">
            <p className="text-red-600 mb-4">{error.message || "Failed to load booking details"}</p>
            <Button
              onClick={() => refreshBookingDetails()}
              className="bg-[#009639] text-white"
            >
              <RefreshCw size={16} className="mr-2" />
              Retry
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  // Booking not found (only for fetched data where we expected to find something)
  if (shouldFetch && !booking) {
    return (
      <PageWithScroll header={simpleHeader}>
        <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center px-4">
          <div className="text-center">
            <p className="text-[#797879] mb-4">Booking not found</p>
            <Button
              onClick={goBack}
              className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            >
              Go Back
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  // Final fallback - if we somehow don't have booking data
  if (!booking) {
    return (
      <PageWithScroll header={simpleHeader}>
        <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center px-4">
          <div className="text-center">
            <p className="text-[#797879] mb-4">No booking data available</p>
            <Button
              onClick={goBack}
              className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
            >
              Go Back
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header}>
      <div className="min-h-screen bg-[#f5f5f5]">
        {/* Booking Status */}
        <div className="bg-white px-6 py-4 flex items-center justify-between border-b border-[#f2f2f2]">
          <div>
            <div className="flex items-center">
              <h2 className="text-[#333333] font-medium mr-2">
                Booking {booking.reference}
              </h2>
              {getStatusBadge(booking.status)}
            </div>
            <p className="text-xs text-[#797879] mt-1">
              Created on {formatDateForInput(booking.created_at)}
            </p>
          </div>
          {booking.status === deprecated_BookingStatus.CONFIRMED && (
            <button
              className="text-red-500 text-sm font-medium"
              onClick={() => setShowCancelModal(true)}
            >
              Cancel
            </button>
          )}
        </div>

        {/* Vehicle Info */}
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <div className="flex items-start">
              <div className="w-20 h-16 bg-[#f2f2f2] rounded-lg overflow-hidden mr-3 flex-shrink-0 flex items-center justify-center">
                <Car size={24} className="text-gray-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-[#333333] font-medium">
                  {passedBookingData?.vehicle || `Vehicle ID: ${booking.vehicle_id}`}
                </h3>
                <p className="text-gray-500 text-sm mt-1">
                  {passedBookingData?.vehicle ? 'Vehicle for this reservation' : 'Vehicle booked for this reservation'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Booking Details */}
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
            <h3 className="text-[#333333] font-medium mb-3">Booking Details</h3>
            <div className="space-y-4">
              <div className="flex items-start">
                <Calendar size={18} className="text-[#009639] mr-3 mt-0.5" />
                <div>
                  <p className="text-[#797879] text-xs">Dates</p>
                  <p className="text-[#333333]">
                    {formatDate(booking.start_datetime)} -{" "}
                    {formatDate(booking.end_datetime)}
                  </p>
                </div>
              </div>
              {booking.total_price && (
                <div className="flex items-start">
                  <div className="w-[18px] h-[18px] mr-3 mt-0.5 flex items-center justify-center">
                    <span className="text-[#009639] font-bold text-sm">$</span>
                  </div>
                  <div>
                    <p className="text-[#797879] text-xs">Total Cost</p>
                    <p className="text-[#333333] font-medium">${booking.total_price}</p>
                  </div>
                </div>
              )}
              {booking.notes && (
                <div className="border-t border-gray-100 pt-3">
                  <p className="text-[#797879] text-xs mb-1">Notes</p>
                  <p className="text-[#333333] text-sm">{booking.notes}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* User Info */}
        {individual && (
          <div className="p-4">
            <div className="bg-white rounded-xl shadow-md p-4 border border-gray-100">
              <h3 className="text-[#333333] font-medium mb-3">Booked By</h3>
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gray-200 mr-3 flex items-center justify-center">
                  <span className="text-gray-500 font-medium text-sm">
                    {individual.first_name?.[0]}{individual.last_name?.[0]}
                  </span>
                </div>
                <div className="flex-1">
                  <h4 className="text-[#333333] font-medium">
                    {individual.first_name} {individual.last_name}
                  </h4>
                  <button
                    className="text-[#009639] text-sm flex items-center mt-1"
                    onClick={() => navigateToMemberDetails(individual.id?.toString() || "1")}
                  >
                    View Profile{" "}
                    <ChevronRight size={16} className="text-[#009639]" />
                  </button>
                </div>
              </div>

              <div className="flex mt-4 space-x-3">
                <button
                  className="flex-1 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white py-3 rounded-full flex items-center justify-center shadow-md"
                  onClick={() => {
                    // Navigate to chat - for now just show an alert
                    alert("Chat functionality not implemented yet");
                  }}
                >
                  <MessageSquare size={18} className="mr-2" /> Message
                </button>
              </div>
            </div>
            {contacts.length > 0 && (
              <div className="flex gap-3 mt-3">
                {contacts.map((contact) => {
                  const type = contact.contact_point_type
                    .name as keyof typeof contactActions;
                  const action = contactActions[type];

                  if (!action) return null;

                  return (
                    <button
                      key={contact.id}
                      className="flex-1 border border-[#009639] text-[#009639] py-3 rounded-full flex items-center justify-center shadow-sm"
                      onClick={() => window.open(action.getUrl(contact.value))}
                    >
                      {action.icon}
                      {action.label}
                    </button>
                  );
                })}
              </div>
            )}
          </div>
        )}

        {/* Cancel Modal */}
        {showCancelModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl p-6 w-full max-w-sm shadow-lg border border-gray-100">
              <div className="flex justify-end">
                <button onClick={() => setShowCancelModal(false)}>
                  <X size={20} className="text-[#333333]" />
                </button>
              </div>
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-[#ffe6e6] rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
                  <X size={32} className="text-[#7A0000]" />
                </div>
                <h3 className="text-lg font-bold text-[#333333] mb-2">
                  Cancel Booking
                </h3>
                <p className="text-[#797879]">
                  Are you sure you want to cancel this booking? This action cannot
                  be undone.
                </p>
              </div>
              <div className="flex space-x-3">
                <button
                  className="flex-1 py-3 border border-[#d6d9dd] rounded-full text-[#333333] shadow-sm"
                  onClick={() => setShowCancelModal(false)}
                >
                  Keep Booking
                </button>
                <button
                  className="flex-1 py-3 bg-gradient-to-r from-[#FF3B30] to-[#7A0000] text-white rounded-full shadow-md"
                  onClick={handleCancelBooking}
                >
                  Cancel Booking
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </PageWithScroll>
  );
} 