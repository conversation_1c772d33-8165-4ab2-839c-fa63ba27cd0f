"use client";

import { useState, useTransition, useEffect } from "react";
import { ArrowLeft, Plus, Clock, Check, X, AlertCircle, Mail, UserPlus, Search } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { GroupRoleEnum, InvitationStatusEnum, GroupMembershipInvitationCreate } from "@/types/groups";
import { createGroupInvitation, cancelGroupInvitation, getGroupMemberManagementData } from "@/actions/member-management";

interface ContactPoint {
  partyId: number;
  contactValue: string;
  isPrimary: boolean;
  contactPointTypeId: number;
}

interface Member {
  id: number;
  partyId: number;
  firstName: string;
  lastName: string;
  name: string;
  role: GroupRoleEnum;
  joinedAt: string;
  isActive: boolean;
  contacts: ContactPoint[];
}

interface Invitation {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  role: GroupRoleEnum;
  status: InvitationStatusEnum;
  sentAt: string;
  expiresAt: string;
  invitedBy: string;
}

interface MemberManagementData {
  success: boolean;
  error?: string;
  isAdmin: boolean;
  members: Member[];
  invitations: Invitation[];
}

interface AddMembersScreenProps {
  params?: { groupId?: string };
}

export default function AddMembersScreen({ params }: AddMembersScreenProps) {
  const { goBack, navigateToGroupDetails } = useNavigation();
  const [isPending, startTransition] = useTransition();
  
  const groupId = params?.groupId ? +params.groupId : 0;
  
  const [searchQuery, setSearchQuery] = useState("");
  const [showInviteForm, setShowInviteForm] = useState(false);
  const [managementData, setManagementData] = useState<MemberManagementData | null>(null);
  const [loading, setLoading] = useState(true);
  const [newInvitation, setNewInvitation] = useState<GroupMembershipInvitationCreate>({
    firstName: "",
    lastName: "",
    email: "",
    role: GroupRoleEnum.MEMBER
  });

  // Fetch management data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (!groupId) return;
      
      try {
        setLoading(true);
        const data = await getGroupMemberManagementData(groupId);
        setManagementData(data);
      } catch (error) {
        console.error("Error fetching management data:", error);
        setManagementData({
          success: false,
          error: "Failed to load member management data",
          isAdmin: false,
          members: [],
          invitations: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [groupId]);

  // Refresh data function
  const refreshData = async () => {
    if (!groupId) return;
    
    try {
      const data = await getGroupMemberManagementData(groupId);
      setManagementData(data);
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Add Members</h1>
      </div>
      <button
        className="w-10 h-10 bg-[#007A2F] hover:bg-[#006428] rounded-full flex items-center justify-center"
        onClick={() => setShowInviteForm(!showInviteForm)}
        disabled={isPending || !managementData?.isAdmin}
      >
        <Plus size={24} className="text-white" />
      </button>
    </div>
  );

  // Show loading state
  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading member management data...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  // Show access denied if not admin
  if (!managementData?.success || !managementData?.isAdmin) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <AlertCircle size={48} className="text-red-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold text-[#333333] mb-2">Access Denied</h2>
            <p className="text-[#797879] mb-4">
              {managementData?.error || "You need admin permissions to add members."}
            </p>
            <Button 
              onClick={goBack}
              className="bg-[#009639] hover:bg-[#007A2F] text-white"
            >
              Go Back
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  const { invitations } = managementData;

  const filteredInvitations = invitations.filter(
    (invitation) =>
      invitation.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSendInvitation = () => {
    if (!newInvitation.firstName.trim() || !newInvitation.lastName.trim() || !newInvitation.email.trim()) {
      alert("Please fill in all required fields");
      return;
    }

    startTransition(async () => {
      try {
        const result = await createGroupInvitation(groupId, newInvitation);
        if (result.success) {
          setShowInviteForm(false);
          setNewInvitation({
            firstName: "",
            lastName: "",
            email: "",
            role: GroupRoleEnum.MEMBER
          });
          // Refresh the data to show updated invitations
          await refreshData();
        } else {
          alert(result.error || "Failed to create invitation");
        }
      } catch (error) {
        console.error("Error creating invitation:", error);
        alert("Failed to create invitation");
      }
    });
  };

  const handleCancelInvitation = (invitationId: number) => {
    if (!confirm("Are you sure you want to cancel this invitation?")) {
      return;
    }

    startTransition(async () => {
      try {
        const result = await cancelGroupInvitation(invitationId);
        if (result.success) {
          // Refresh the data to show updated invitations
          await refreshData();
        } else {
          alert(result.error || "Failed to cancel invitation");
        }
      } catch (error) {
        console.error("Error cancelling invitation:", error);
        alert("Failed to cancel invitation");
      }
    });
  };

  const getStatusIcon = (status: InvitationStatusEnum) => {
    switch (status) {
      case InvitationStatusEnum.SENT:
        return <Clock size={16} className="text-yellow-600" />;
      case InvitationStatusEnum.PENDING:
        return <Clock size={16} className="text-blue-600" />;
      case InvitationStatusEnum.ACCEPTED:
        return <Check size={16} className="text-green-600" />;
      case InvitationStatusEnum.DECLINED:
        return <X size={16} className="text-red-500" />;
      default:
        return <AlertCircle size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: InvitationStatusEnum) => {
    switch (status) {
      case InvitationStatusEnum.SENT:
        return "bg-yellow-100 text-yellow-800";
      case InvitationStatusEnum.PENDING:
        return "bg-blue-100 text-blue-800";
      case InvitationStatusEnum.ACCEPTED:
        return "bg-green-100 text-green-800";
      case InvitationStatusEnum.DECLINED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleBadge = (role: GroupRoleEnum) => {
    switch (role) {
      case GroupRoleEnum.ADMIN: return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Invite Form */}
      {showInviteForm && (
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center">
                <UserPlus size={16} className="text-[#009639]" />
              </div>
              <h2 className="text-lg font-semibold text-[#333333]">Send New Invitation</h2>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#333333] mb-2">First Name *</label>
                  <input
                    type="text"
                    value={newInvitation.firstName}
                    onChange={(e) => setNewInvitation(prev => ({ ...prev, firstName: e.target.value }))}
                    placeholder="First name"
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-[#333333] mb-2">Last Name *</label>
                  <input
                    type="text"
                    value={newInvitation.lastName}
                    onChange={(e) => setNewInvitation(prev => ({ ...prev, lastName: e.target.value }))}
                    placeholder="Last name"
                    className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Email *</label>
                <input
                  type="email"
                  value={newInvitation.email}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-[#333333] mb-2">Role</label>
                <select
                  value={newInvitation.role}
                  onChange={(e) => setNewInvitation(prev => ({ ...prev, role: e.target.value as GroupRoleEnum }))}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                >
                  <option value={GroupRoleEnum.MEMBER}>Member</option>
                  <option value={GroupRoleEnum.ADMIN}>Admin</option>
                </select>
              </div>
              
              <div className="flex gap-3 pt-2">
                <Button
                  variant="outline"
                  onClick={() => setShowInviteForm(false)}
                  className="flex-1"
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSendInvitation}
                  disabled={isPending || !newInvitation.firstName.trim() || !newInvitation.lastName.trim() || !newInvitation.email.trim()}
                  className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
                >
                  {isPending ? "Sending..." : "Send Invitation"}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search */}
      <div className="p-4">
        <div className="relative">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#797879]" />
          <input
            type="text"
            placeholder="Search invitations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent bg-white"
          />
        </div>
      </div>

      {/* Summary Stats */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
          <h3 className="text-[#333333] font-semibold mb-3">Invitation Summary</h3>
          <div className="grid grid-cols-3 gap-3 text-center">
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">{invitations.length}</p>
              <p className="text-xs text-[#797879]">Total Invitations</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">
                {invitations.filter(i => [InvitationStatusEnum.SENT, InvitationStatusEnum.PENDING].includes(i.status)).length}
              </p>
              <p className="text-xs text-[#797879]">Pending</p>
            </div>
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-xl font-bold text-[#009639]">
                {invitations.filter(i => i.status === InvitationStatusEnum.ACCEPTED).length}
              </p>
              <p className="text-xs text-[#797879]">Accepted</p>
            </div>
          </div>
        </div>
      </div>

      {/* Invitations List */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
          <div className="p-4 border-b border-gray-100">
            <h3 className="font-semibold text-[#333333]">
              All Invitations ({filteredInvitations.length})
            </h3>
          </div>
          
          {filteredInvitations.length > 0 ? (
            filteredInvitations.map((invitation, index) => (
              <div
                key={invitation.id}
                className={`p-4 ${
                  index < filteredInvitations.length - 1 ? "border-b border-[#f2f2f2]" : ""
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                      <span className="text-[#009639] font-medium text-lg">
                        {invitation.firstName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-[#333333] font-medium">
                        {invitation.firstName} {invitation.lastName}
                      </h4>
                      <p className="text-sm text-[#797879]">{invitation.email}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge className={getStatusColor(invitation.status)}>
                          <span className="flex items-center gap-1">
                            {getStatusIcon(invitation.status)}
                            {invitation.status.toLowerCase()}
                          </span>
                        </Badge>
                        <Badge className={getRoleBadge(invitation.role)}>
                          {invitation.role.toLowerCase()}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  {[InvitationStatusEnum.SENT, InvitationStatusEnum.PENDING].includes(invitation.status) && (
                    <button
                      onClick={() => handleCancelInvitation(invitation.id)}
                      disabled={isPending}
                      className="text-red-600 hover:bg-red-100 px-3 py-1 rounded-lg text-sm disabled:opacity-50"
                    >
                      Cancel
                    </button>
                  )}
                </div>
                
                <div className="mt-3 text-xs text-[#797879]">
                  Sent {new Date(invitation.sentAt).toLocaleDateString()} at {new Date(invitation.sentAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  {invitation.invitedBy && ` by ${invitation.invitedBy}`}
                </div>
              </div>
            ))
          ) : (
            <div className="p-6 text-center">
              {searchQuery ? (
                <>
                  <Search size={48} className="text-[#797879] mx-auto mb-4" />
                  <p className="text-[#797879]">No invitations found matching "{searchQuery}"</p>
                </>
              ) : (
                <>
                  <Mail size={48} className="text-[#797879] mx-auto mb-4" />
                  <p className="text-[#797879]">No invitations found</p>
                  <p className="text-sm text-[#797879] mt-2">
                    Tap the + button to invite new members
                  </p>
                </>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
          <h3 className="text-[#333333] font-semibold mb-3">Quick Actions</h3>
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => navigateToGroupDetails(groupId.toString())}
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Group Dashboard
            </Button>
          </div>
        </div>
      </div>
    </PageWithScroll>
  );
} 