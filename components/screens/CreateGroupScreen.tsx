"use client";

import { useState, useEffect } from "react";
import { 
  ArrowLeft, 
  Plus, 
  X, 
  Users, 
  Building, 
  Car, 
  MapPin,
  FileText,
  Hash,
  Calendar,
  Palette,
  Mail
} from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { useCurrentUser } from "@/hooks/use-current-user";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageWithScroll } from "@/components/ui/scroll-container";
import ImageUploader, { UploadedImage } from "@/app/components/ImageUploader";
import {
  createGroup,
} from "@/drizzle-actions/groups";
import { getCities, getCountriesWithIds } from "@/drizzle-actions/community";
import { getAllVehicleMakes, getVehicleModelsByMakeId, getVehicleVariantsByModelId } from "@/drizzle-actions/vehicle-domain";
import { CompanyPurposeEnum } from "@/types/company";
import { GroupRoleEnum } from "@/types/groups";
import { Switch } from "../ui/switch";


interface City {
  id: number;
  name: string;
  province: string;
  country: string;
}

interface Country {
  id: number;
  name: string;
  code: string;
}

interface MemberInvitation {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: GroupRoleEnum;
}

interface VehicleMake {
  id: number;
  name: string;
}

interface VehicleModel {
  id: number;
  model: string;
  year_model?: number;
  make_name?: string;
}

interface VehicleVariant {
  id: number;
  name: string;
  year?: number;
}

interface CreateGroupScreenProps {
  params?: Record<string, any>;
}

export default function CreateGroupScreen({ params }: CreateGroupScreenProps) {
  const { goBack, navigateToGroupDetails } = useNavigation();
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();
  
  // Vehicle images
  const [vehicleImages, setVehicleImages] = useState<UploadedImage[]>([]);
  const [error, setError] = useState("");
  
  // Group Information
  const [groupName, setGroupName] = useState("");
  const [description, setDescription] = useState("");
  const [purpose, setPurpose] = useState<CompanyPurposeEnum>(CompanyPurposeEnum.OTHER);
  const [isManaged, setIsManaged] = useState(false);
  const [selectedCityId, setSelectedCityId] = useState<number | undefined>();
  const [selectedCountryId, setSelectedCountryId] = useState<number>(1); // Default to South Africa
  
  // Vehicle Information (Required)
  const [vinNumber, setVinNumber] = useState("");
  const [vehicleRegistration, setVehicleRegistration] = useState("");
  const [manufacturingYear, setManufacturingYear] = useState<number>(new Date().getFullYear());
  const [color, setColor] = useState("");
  const [makeId, setMakeId] = useState<number>(0);
  const [make, setMake] = useState<string>("");
  const [modelId, setModelId] = useState<number>(0);
  const [model, setModel] = useState<string>("");
  const [variantId, setVariantId] = useState<number>(0);
  const [vehicleYear, setVehicleYear] = useState<string>("");
  
  // Member Invitations
  const [memberInvitations, setMemberInvitations] = useState<MemberInvitation[]>([]);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberFirstName, setNewMemberFirstName] = useState("");
  const [newMemberLastName, setNewMemberLastName] = useState("");
  const [newMemberRole, setNewMemberRole] = useState<GroupRoleEnum>(GroupRoleEnum.MEMBER);
  
  // Data and State
  const [cities, setCities] = useState<City[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [makes, setMakes] = useState<VehicleMake[]>([]);
  const [models, setModels] = useState<VehicleModel[]>([]);
  const [variants, setVariants] = useState<VehicleVariant[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMakes, setLoadingMakes] = useState(false);
  const [loadingModels, setLoadingModels] = useState(false);
  const [loadingVariants, setLoadingVariants] = useState(false);
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch cities, countries, and makes in parallel
        const [cityList, countryList] = await Promise.all([
          getCities(),
          getCountriesWithIds(),
        ]);
        
        setCities(cityList);
        setCountries(countryList);
        
        // Load makes separately
        setLoadingMakes(true);
        const makesData = await getAllVehicleMakes();
        setMakes(makesData);
        setLoadingMakes(false);
        
      } catch (error) {
        console.error("Failed to fetch data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Load models when make is selected
  useEffect(() => {
    if (makeId > 0) {
      const loadModels = async () => {
        setLoadingModels(true);
        setModels([]);
        setVariants([]);
        setModelId(0);
        setModel("");
        setVariantId(0);
        setVehicleYear("");

        try {
          const modelsData = await getVehicleModelsByMakeId(makeId);
          setModels(modelsData);
        } catch (error) {
          console.error("Error loading models:", error);
        } finally {
          setLoadingModels(false);
        }
      };

      loadModels();
    }
  }, [makeId]);

  // Load variants when model is selected
  useEffect(() => {
    if (modelId > 0) {
      const loadVariants = async () => {
        setLoadingVariants(true);
        setVariants([]);
        setVariantId(0);
        setVehicleYear("");

        try {
          const variantsData = await getVehicleVariantsByModelId(modelId);
          setVariants(variantsData);
        } catch (error) {
          console.error("Error loading variants:", error);
        } finally {
          setLoadingVariants(false);
        }
      };

      loadVariants();
    }
  }, [modelId]);

  const addMemberInvitation = () => {
    if (
      !newMemberEmail.trim() ||
      !newMemberFirstName.trim() ||
      !newMemberLastName.trim()
    ) {
      alert("Please fill in all member details");
      return;
    }

    if (memberInvitations.some((m) => m.email === newMemberEmail)) {
      alert("This email is already in the invitation list");
      return;
    }

    const newInvitation: MemberInvitation = {
      id: Date.now().toString(),
      email: newMemberEmail.trim(),
      firstName: newMemberFirstName.trim(),
      lastName: newMemberLastName.trim(),
      role: newMemberRole,
    };

    setMemberInvitations([...memberInvitations, newInvitation]);
    setNewMemberEmail("");
    setNewMemberFirstName("");
    setNewMemberLastName("");
    setNewMemberRole(GroupRoleEnum.MEMBER);
  };

  const removeMemberInvitation = (id: string) => {
    setMemberInvitations(memberInvitations.filter((m) => m.id !== id));
  };

  const handleCreateGroup = async () => {
    if (!currentUserPartyId) {
      alert("User not authenticated");
      return;
    }

    if (vehicleImages.length < 3) {
      alert("Please upload at least 3 vehicle photos");
      return;
    }

    if (!groupName.trim()) {
      alert("Please enter a group name");
      return;
    }

    if (!description.trim()) {
      alert("Please enter a description");
      return;
    }

    if (!selectedCityId) {
      alert("Please select a city");
      return;
    }

    if (!vinNumber.trim()) {
      alert("Please enter a VIN number");
      return;
    }

    if (!variantId || variantId === 0) {
      alert("Please select a vehicle variant");
      return;
    }

    try {
      setCreating(true);

      const groupData = {
        groupCreate: {
          name: groupName.trim(),
          description: description.trim(),
          cityId: selectedCityId,
          countryId: selectedCountryId,
          InitialPurpose: purpose,
          isManaged: isManaged,
          createdBy: currentUserPartyId,
        },
        memberInvitations: memberInvitations.length > 0 ? memberInvitations.map(invite => ({
          firstName: invite.firstName,
          lastName: invite.lastName,
          email: invite.email,
          role: invite.role,
        })) : undefined,
        vehicleCreate: {
          model_id: modelId,
          vin_number: vinNumber.trim(),
          vehicle_registration: vehicleRegistration.trim() || undefined,
          country_id: selectedCountryId,
          manufacturing_year: parseInt(vehicleYear) || manufacturingYear,
          color: color.trim() || undefined,
          is_active: true,
        },
      };

      const result = await createGroup(groupData);

      if (result && result.group) {
        alert("Group created successfully!");
        navigateToGroupDetails(result.group.id.toString());
      } else {
        alert("Failed to create group");
      }
    } catch (error) {
      console.error("Error creating group:", error);
      alert("Failed to create group. Please try again.");
    } finally {
      setCreating(false);
    }
  };

  const purposeOptions = [
    { value: CompanyPurposeEnum.RIDE_SHARE, label: "Ride Share", description: "Share vehicles for transportation services" },
    { value: CompanyPurposeEnum.FLEET, label: "Fleet Management", description: "Manage a fleet of vehicles" },
    { value: CompanyPurposeEnum.GROUP_MONETIZATION, label: "Group Monetization", description: "Generate income through group activities" },
    { value: CompanyPurposeEnum.OTHER, label: "Other", description: "Other purposes not listed above" },
  ];

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
      <button className="mr-4" onClick={goBack}>
        <ArrowLeft size={24} className="text-white" />
      </button>
      <h1 className="text-xl font-bold text-white">Create Group</h1>
    </div>
  );

  // Handle image upload errors with user-friendly messages
  const handleImageError = (error: string) => {
    console.error('Image upload error:', error);
    
    // Convert technical errors to user-friendly messages
    if (error.includes('not authorized') || error.includes('s3:PutObject')) {
      setError("Unable to upload images. Please try again or contact support if the problem persists.");
    } else if (error.includes('network') || error.includes('timeout')) {
      setError("Network issue detected. Please check your connection and try again.");
    } else if (error.includes('file size') || error.includes('too large')) {
      setError("One or more images are too large. Please use images under 10MB.");
    } else if (error.includes('file type') || error.includes('format')) {
      setError("Invalid image format. Please use JPG, PNG, or WEBP images.");
    } else {
      setError("Failed to upload images. Please try again.");
    }
  };

  if (loading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-6">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading form data...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Error Message */}
      {error && (
        <div className="mx-6 mt-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      <div className="p-6 pb-24">
        {/* Basic Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Group Information
          </h2>

          {/* Group Name */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Users size={16} className="mr-2 text-[#009639]" />
              Group Name
            </label>
            <input
              type="text"
              value={groupName}
              onChange={(e) => setGroupName(e.target.value)}
              placeholder="Enter group name (e.g., City Commuters)"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              maxLength={100}
            />
          </div>

          {/* Description */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <FileText size={16} className="mr-2 text-[#009639]" />
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Describe your group's purpose"
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] resize-none"
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-[#797879] mt-1">
              {description.length}/500 characters
            </p>
          </div>

          {/* Purpose */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              Purpose
            </label>
            <select
              value={purpose}
              onChange={(e) => setPurpose(e.target.value as CompanyPurposeEnum)}
              className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
            >
              {purposeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Location */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <MapPin size={16} className="mr-2 text-[#009639]" />
              Location
            </label>
            {loading ? (
              <div className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg bg-gray-100 animate-pulse">
                Loading cities...
              </div>
            ) : (
              <select
                value={selectedCityId || ""}
                onChange={(e) =>
                  setSelectedCityId(
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                required
              >
                <option value="">Select a city</option>
                {cities.map((city) => (
                  <option key={city.id} value={city.id}>
                    {city.name}, {city.province}, {city.country}
                  </option>
                ))}
              </select>
            )}
          </div>

          {/* Managed Group */}
          <div className="mb-4">
            <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
              <Building size={16} className="mr-2 text-[#009639]" />
              <span className="mr-2">Register as a company</span>
              <Switch checked={isManaged} onCheckedChange={setIsManaged} />
            </label>
            <p className="text-xs text-[#797879] ml-6">
              Poolly will register and manage CIPC company for this group, all members will be added as shareholders.
            </p>
          </div>
        </div>

        {/* Vehicle Information */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <h2 className="text-lg font-semibold text-[#333333] mb-6">
            Vehicle Information
          </h2>

          {/* Vehicle Images */}
          <div className="mb-6">
            <ImageUploader
              images={vehicleImages}
              onImagesChange={setVehicleImages}
              uploadPath="vehicleMedia"
              minImages={3}
              maxImages={10}
              title="Vehicle Photos"
              onError={handleImageError}
              borderEnabled={false}
            />
          </div>

          <div className="space-y-4">
            {/* Make and Model */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                  <Car size={16} className="mr-2 text-[#009639]" />
                  Make
                </label>
                <select
                  value={makeId}
                  onChange={(e) => {
                    const selectedMakeId = parseInt(e.target.value);
                    const selectedMake = makes.find(make => make.id === selectedMakeId);
                    setMakeId(selectedMakeId);
                    setMake(selectedMake?.name || "");
                    setModelId(0);
                    setModel("");
                    setVariantId(0);
                    setVehicleYear("");
                  }}
                  className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                  required
                  disabled={loadingMakes}
                >
                  <option value={0}>
                    {loadingMakes ? "Loading makes..." : "Select Make"}
                  </option>
                  {makes.map(make => (
                    <option key={make.id} value={make.id}>
                      {make.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                  <Car size={16} className="mr-2 text-[#009639]" />
                  Model
                </label>
                <select
                  value={modelId}
                  onChange={(e) => {
                    const selectedModelId = parseInt(e.target.value);
                    const selectedModel = models.find(model => model.id === selectedModelId);
                    setModelId(selectedModelId);
                    setModel(selectedModel?.model || "");
                    setVariantId(0);
                    setVehicleYear("");
                  }}
                  className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                  required
                  disabled={loadingModels || makeId === 0}
                >
                  <option value={0}>
                    {loadingModels ? "Loading models..." : 
                     makeId === 0 ? "Select Make First" : "Select Model"}
                  </option>
                  {models.map(model => (
                    <option key={model.id} value={model.id}>
                      {model.model}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Variant and Year */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                  <Car size={16} className="mr-2 text-[#009639]" />
                  Variant
                </label>
                <select
                  value={variantId}
                  onChange={(e) => {
                    const selectedVariantId = parseInt(e.target.value);
                    const selectedVariant = variants.find(variant => variant.id === selectedVariantId);
                    setVariantId(selectedVariantId);
                    setVehicleYear(selectedVariant?.year?.toString() || "");
                  }}
                  className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                  required
                  disabled={loadingVariants || modelId === 0}
                >
                  <option value={0}>
                    {loadingVariants ? "Loading variants..." : 
                     modelId === 0 ? "Select Model First" : "Select Variant"}
                  </option>
                  {variants.map(variant => (
                    <option key={variant.id} value={variant.id}>
                      {variant.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                  <Calendar size={16} className="mr-2 text-[#009639]" />
                  Year
                </label>
                <input
                  type="text"
                  value={vehicleYear}
                  className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] bg-gray-100"
                  placeholder="Auto-filled from variant"
                  readOnly
                  required
                />
              </div>
            </div>

            {/* VIN Number */}
            <div>
              <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                <Hash size={16} className="mr-2 text-[#009639]" />
                VIN Number
              </label>
              <input
                type="text"
                value={vinNumber}
                onChange={(e) => setVinNumber(e.target.value)}
                placeholder="Enter VIN number"
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                maxLength={17}
                required
              />
            </div>

            {/* Vehicle Registration */}
            <div>
              <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                <FileText size={16} className="mr-2 text-[#009639]" />
                Registration Number (Optional)
              </label>
              <input
                type="text"
                value={vehicleRegistration}
                onChange={(e) => setVehicleRegistration(e.target.value)}
                placeholder="Enter registration number"
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>

            {/* Color */}
            <div>
              <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                <Palette size={16} className="mr-2 text-[#009639]" />
                Color (Optional)
              </label>
              <input
                type="text"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                placeholder="Enter vehicle color"
                className="w-full px-4 py-3 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>
          </div>
        </div>

        {/* Member Invitations */}
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-[#333333]">
              Invite Members
            </h2>
          </div>

          {/* Add Member Form */}
          <div className= "flex flex-col gap-3 rounded-lg mb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <input
                type="text"
                value={newMemberFirstName}
                onChange={(e) => setNewMemberFirstName(e.target.value)}
                placeholder="First name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <input
                type="text"
                value={newMemberLastName}
                onChange={(e) => setNewMemberLastName(e.target.value)}
                placeholder="Last name"
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
            </div>
        
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <input
                type="email"
                value={newMemberEmail}
                onChange={(e) => setNewMemberEmail(e.target.value)}
                placeholder="Email address"
                className="md:col-span-2 px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              />
              <select
                value={newMemberRole}
                onChange={(e) => setNewMemberRole(e.target.value as GroupRoleEnum)}
                className="px-3 py-2 border border-[#e5e5e5] rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
              >
                <option value={GroupRoleEnum.MEMBER}>Member</option>
                <option value={GroupRoleEnum.ADMIN}>Admin</option>
              </select>
            </div>
         
            <button
              onClick={addMemberInvitation}
              disabled={
                !newMemberEmail.trim() ||
                !newMemberFirstName.trim() ||
                !newMemberLastName.trim()
              }
              className="w-full bg-[#009639] text-white py-2 rounded-full font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              
              Add Member
            </button>
          </div>

          {/* Member List */}
          {memberInvitations.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-[#333333] mb-2">
                Members to Invite ({memberInvitations.length})
              </h4>
              {memberInvitations.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-3 bg-[#f8f9fa] rounded-lg"
                >
                  <div className="flex-1">
                    <p className="font-medium text-[#333333]">
                      {member.firstName} {member.lastName}
                    </p>
                    <p className="text-sm text-[#797879]">{member.email}</p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-[#009639] mr-3">
                      {member.role}
                    </span>
                    <button
                      onClick={() => removeMemberInvitation(member.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <X size={16} />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Create Button */}
        <div className="bottom-0 left-0 right-0 p-4 bg-white z-50">
          <button
            onClick={handleCreateGroup}
            disabled={
              creating ||
              vehicleImages.length < 3 ||
              !groupName.trim() ||
              !description.trim() ||
              !selectedCityId ||
              !vinNumber.trim() ||
              !variantId ||
              variantId === 0 ||
              !currentUserPartyId
            }
            className="w-full bg-[#009639] text-white p-2 rounded-full font-semibold shadow-md disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Creating Group...
              </>
            ) : (
              "Create Group"
            )}
          </button>
        </div>
      </div>
    </PageWithScroll>
  );
} 