"use client";

import { useState, useRef, useEffect } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { 
  ArrowLeft, 
  Send, 
  Paperclip, 
  Image, 
  Phone,
  Video,
  MoreVertical,
  Users,
  MapPin,
  Car,
  Calendar
} from "lucide-react";

interface GroupChatScreenProps {
  params?: { groupId?: string };
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'location' | 'booking' | 'system';
  metadata?: any;
}

export default function GroupChatScreen({ params }: GroupChatScreenProps) {
  const { goBack, navigateToGroupDetails, navigateToMemberDetails, navigateToVehicleDetails } = useNavigation();
  const groupId = params?.groupId || "1";
  
  const [message, setMessage] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock group data
  const group = {
    id: groupId,
    name: "Downtown Commuters",
    members: [
      { id: "1", name: "<PERSON> (You)", isCurrentUser: true, online: true },
      { id: "2", name: "<PERSON>", isCurrentUser: false, online: true },
      { id: "3", name: "Mike <PERSON>", isCurrentUser: false, online: false },
      { id: "4", name: "Emma Liu", isCurrentUser: false, online: true }
    ]
  };

  // Mock messages
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "1",
      senderId: "2",
      senderName: "Sarah Mitchell",
      content: "Hey everyone! Just wanted to confirm that the Tesla will be available for pickup at 8 AM tomorrow.",
      timestamp: new Date(Date.now() - 3600000),
      type: "text"
    },
    {
      id: "2",
      senderId: "3",
      senderName: "Mike Rodriguez", 
      content: "Perfect! I'll be there. Thanks Sarah 👍",
      timestamp: new Date(Date.now() - 3000000),
      type: "text"
    },
    {
      id: "3",
      senderId: "system",
      senderName: "System",
      content: "Emma Liu booked Tesla Model 3 for Jan 22, 2024 - 2 hours",
      timestamp: new Date(Date.now() - 2400000),
      type: "booking",
      metadata: {
        vehicleId: "v1",
        bookingId: "b123",
        vehicle: "Tesla Model 3",
        date: "Jan 22, 2024",
        duration: "2 hours"
      }
    },
    {
      id: "4",
      senderId: "4",
      senderName: "Emma Liu",
      content: "Just booked the Tesla for my client meeting. Will return it by 5 PM!",
      timestamp: new Date(Date.now() - 2100000),
      type: "text"
    },
    {
      id: "5",
      senderId: "1",
      senderName: "John Smith",
      content: "Great! Don't forget to charge it after use. The charging station is in spot B12.",
      timestamp: new Date(Date.now() - 1800000),
      type: "text"
    },
    {
      id: "6",
      senderId: "2",
      senderName: "Sarah Mitchell",
      content: "📍 Downtown Parking Garage - Level P2",
      timestamp: new Date(Date.now() - 900000),
      type: "location",
      metadata: {
        address: "123 King Street W, Toronto, ON",
        name: "Downtown Parking Garage - Level P2"
      }
    }
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (message.trim()) {
      const newMessage: Message = {
        id: Date.now().toString(),
        senderId: "1",
        senderName: "John Smith",
        content: message.trim(),
        timestamp: new Date(),
        type: "text"
      };
      
      setMessages([...messages, newMessage]);
      setMessage("");
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  const renderMessage = (msg: Message, index: number) => {
    const isCurrentUser = msg.senderId === "1";
    const isSystem = msg.type === "system" || msg.senderId === "system";
    const showAvatar = !isCurrentUser && !isSystem && (index === 0 || messages[index - 1].senderId !== msg.senderId);
    const showName = !isCurrentUser && !isSystem && showAvatar;

    if (isSystem) {
      return (
        <div key={msg.id} className="flex justify-center my-4">
          <div className="bg-gray-100 px-4 py-2 rounded-full">
            <p className="text-xs text-gray-600 text-center">{msg.content}</p>
          </div>
        </div>
      );
    }

    if (msg.type === "booking") {
      return (
        <div key={msg.id} className="flex justify-center my-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-sm">
            <div className="flex items-center space-x-2 mb-2">
              <Car size={16} className="text-blue-600" />
              <span className="text-sm font-medium text-blue-900">New Booking</span>
            </div>
            <p className="text-sm text-gray-900 mb-2">{msg.metadata.vehicle}</p>
            <div className="flex items-center space-x-4 text-xs text-gray-600">
              <div className="flex items-center space-x-1">
                <Calendar size={12} />
                <span>{msg.metadata.date}</span>
              </div>
              <span>{msg.metadata.duration}</span>
            </div>
            <button 
              onClick={() => navigateToVehicleDetails(msg.metadata.vehicleId)}
              className="text-xs text-blue-600 font-medium mt-2"
            >
              View Details
            </button>
          </div>
        </div>
      );
    }

    if (msg.type === "location") {
      return (
        <div key={msg.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`}>
          <div className={`max-w-xs ${isCurrentUser ? 'bg-[#009639] text-white' : 'bg-white border border-gray-200'} rounded-lg p-3`}>
            {showName && (
              <p className="text-xs text-gray-500 mb-1">{msg.senderName}</p>
            )}
            <div className="flex items-center space-x-2">
              <MapPin size={16} className={isCurrentUser ? "text-white" : "text-gray-600"} />
              <div>
                <p className={`text-sm font-medium ${isCurrentUser ? "text-white" : "text-gray-900"}`}>
                  {msg.metadata.name}
                </p>
                <p className={`text-xs ${isCurrentUser ? "text-green-100" : "text-gray-500"}`}>
                  {msg.metadata.address}
                </p>
              </div>
            </div>
            <p className={`text-xs mt-2 ${isCurrentUser ? "text-green-100" : "text-gray-500"}`}>
              {formatTime(msg.timestamp)}
            </p>
          </div>
        </div>
      );
    }

    return (
      <div key={msg.id} className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'} mb-4`}>
        {!isCurrentUser && showAvatar && (
          <div className="w-8 h-8 bg-gray-300 rounded-full mr-2 flex-shrink-0"></div>
        )}
        <div className={`max-w-xs ${isCurrentUser ? 'bg-[#009639] text-white' : 'bg-white border border-gray-200'} rounded-lg p-3`}>
          {showName && (
            <p className="text-xs text-gray-500 mb-1">{msg.senderName}</p>
          )}
          <p className={`text-sm ${isCurrentUser ? "text-white" : "text-gray-900"}`}>
            {msg.content}
          </p>
          <p className={`text-xs mt-1 ${isCurrentUser ? "text-green-100" : "text-gray-500"}`}>
            {formatTime(msg.timestamp)}
          </p>
        </div>
        {isCurrentUser && (
          <div className="w-8 h-8 ml-2 flex-shrink-0"></div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-3">
            <button
              onClick={goBack}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
            >
              <ArrowLeft size={20} className="text-gray-600" />
            </button>
            <div>
              <h1 className="text-lg font-semibold text-gray-900">{group.name}</h1>
              <p className="text-sm text-gray-600">
                {group.members.filter(m => m.online).length} online
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
              <Phone size={20} className="text-gray-600" />
            </button>
            <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
              <Video size={20} className="text-gray-600" />
            </button>
            <button 
              onClick={() => navigateToGroupDetails(groupId)}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
            >
              <MoreVertical size={20} className="text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.map((msg, index) => renderMessage(msg, index))}
        {isTyping && (
          <div className="flex justify-start mb-4">
            <div className="w-8 h-8 bg-gray-300 rounded-full mr-2"></div>
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-100 p-4">
        <div className="flex items-center space-x-3">
          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
            <Paperclip size={20} className="text-gray-600" />
          </button>
          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
            <Image size={20} className="text-gray-600" />
          </button>
          <div className="flex-1 relative">
            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="Type a message..."
              className="w-full px-4 py-3 border border-gray-200 rounded-full focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            />
          </div>
          <button 
            onClick={handleSendMessage}
            disabled={!message.trim()}
            className={`flex items-center justify-center w-10 h-10 rounded-full transition-all ${
              message.trim() 
                ? 'bg-[#009639] text-white hover:bg-[#007A2F]'
                : 'bg-gray-100 text-gray-400'
            }`}
          >
            <Send size={20} />
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white border-t border-gray-100 px-4 py-2">
        <div className="flex items-center justify-center space-x-4">
          <button className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-full text-sm">
            <MapPin size={14} />
            <span>Location</span>
          </button>
          <button className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-full text-sm">
            <Car size={14} />
            <span>Vehicle</span>
          </button>
          <button className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-full text-sm">
            <Calendar size={14} />
            <span>Schedule</span>
          </button>
        </div>
      </div>
    </div>
  );
} 