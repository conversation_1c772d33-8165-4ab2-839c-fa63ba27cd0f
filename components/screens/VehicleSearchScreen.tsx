"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Search, Filter, MapPin, Star } from "lucide-react";

interface VehicleSearchScreenProps {
  params?: Record<string, any>;
}

export default function VehicleSearchScreen({ params }: VehicleSearchScreenProps) {
  const { goBack, navigateToVehicleDetails } = useNavigation();
  const [searchQuery, setSearchQuery] = useState("");

  const mockVehicles = [
    {
      id: "1",
      name: "Tesla Model 3",
      location: "Downtown",
      distance: "0.5 miles",
      price: 89,
      rating: 4.8,
      available: true,
      image: "🚗"
    },
    {
      id: "2", 
      name: "Honda Civic",
      location: "Uptown",
      distance: "1.2 miles",
      price: 45,
      rating: 4.6,
      available: true,
      image: "🚙"
    },
    {
      id: "3",
      name: "Toyota Prius",
      location: "Midtown", 
      distance: "0.8 miles",
      price: 52,
      rating: 4.9,
      available: false,
      image: "🚐"
    }
  ];

  const filteredVehicles = mockVehicles.filter(vehicle =>
    vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.location.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <button
          onClick={goBack}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">Search Vehicles</h1>
        <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
          <Filter size={20} className="text-gray-600" />
        </button>
      </div>

      {/* Search Bar */}
      <div className="p-4 border-b border-gray-100">
        <div className="relative">
          <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <input
            type="text"
            placeholder="Search by vehicle or location..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Search Results */}
        <div className="mb-4">
          <p className="text-sm text-gray-600">
            {filteredVehicles.length} vehicles found
          </p>
        </div>

        {/* Vehicle List */}
        <div className="space-y-4">
          {filteredVehicles.map((vehicle) => (
            <button
              key={vehicle.id}
              onClick={() => navigateToVehicleDetails(vehicle.id)}
              className={`w-full bg-white border rounded-lg p-4 text-left transition-all ${
                vehicle.available 
                  ? 'border-gray-200 hover:border-[#009639] hover:shadow-sm' 
                  : 'border-gray-100 bg-gray-50'
              }`}
              disabled={!vehicle.available}
            >
              <div className="flex items-start space-x-4">
                {/* Vehicle Icon */}
                <div className="text-3xl">{vehicle.image}</div>
                
                {/* Vehicle Info */}
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className={`font-semibold ${vehicle.available ? 'text-gray-900' : 'text-gray-500'}`}>
                        {vehicle.name}
                      </h3>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin size={14} className="mr-1" />
                        {vehicle.location} • {vehicle.distance}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-bold ${vehicle.available ? 'text-[#009639]' : 'text-gray-400'}`}>
                        ${vehicle.price}/day
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Star size={12} className="text-yellow-400 fill-current mr-1" />
                        {vehicle.rating}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      vehicle.available 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {vehicle.available ? 'Available' : 'Not Available'}
                    </span>
                  </div>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* No Results */}
        {filteredVehicles.length === 0 && searchQuery && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <Search size={48} className="mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No vehicles found</h3>
            <p className="text-gray-600">Try adjusting your search criteria</p>
          </div>
        )}

        {/* Popular Searches */}
        {!searchQuery && (
          <div className="mt-8">
            <h3 className="font-semibold text-gray-900 mb-4">Popular Searches</h3>
            <div className="flex flex-wrap gap-2">
              {['Electric vehicles', 'Luxury cars', 'Budget friendly', 'Near me'].map((tag) => (
                <button
                  key={tag}
                  onClick={() => setSearchQuery(tag)}
                  className="px-3 py-2 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors"
                >
                  {tag}
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 