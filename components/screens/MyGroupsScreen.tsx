"use client";

import React, { useState } from "react";
import { Plus, Users, Car, DollarSign, TrendingUp, Search, Settings, MessageSquare, ChevronRight, MapPin, RefreshCw } from "lucide-react";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useGroupsDashboard } from "@/hooks/useGroupsDashboard";
import { CommunityGroup, GroupStats } from "@/types/community";
import { GroupRoleEnum } from "@/types/groups";
import Image from "next/image";

interface MyGroupsScreenProps {
    params?: Record<string, any>;
}

export default function MyGroupsScreen({ params }: MyGroupsScreenProps) {
      const {
    navigateToCreateGroup,
    navigateToGroupDetails,
    navigateToGroupSettings,
    navigateToGroupChat,
    navigateToCommunity
  } = useNavigation();

    const [searchQuery, setSearchQuery] = useState("");
    const [filterRole, setFilterRole] = useState<string>("All");
    
    // Use SWR for data fetching
    const { groups, stats, isLoading: loading, error, refetch, isAuthenticated } = useGroupsDashboard();

    // Calculate user-specific stats from groups data
    const userStats = {
        totalGroups: groups.length,
        ownedGroups: groups.filter((group: CommunityGroup) => group.memberRole === GroupRoleEnum.ADMIN).length,
        totalVehicles: groups.reduce((sum: number, group: CommunityGroup) => sum + group.vehicles, 0),
        monthlyRevenue: groups.length * 450 // Placeholder calculation
    };

    // Filter groups based on search and role filter
    const filteredGroups = groups.filter((group: CommunityGroup) => {
        const matchesSearch = group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            group.description.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesRole = filterRole === "All" ||
            (filterRole === "Owned" && group.memberRole === GroupRoleEnum.ADMIN) ||
            (filterRole === "Member" && group.memberRole === GroupRoleEnum.MEMBER);

        return matchesSearch && matchesRole;
    });

    const getRoleBadgeColor = (role?: GroupRoleEnum) => {
        switch (role) {
            case GroupRoleEnum.ADMIN:
                return "bg-white text-[#009639]";
            case GroupRoleEnum.MEMBER:
                return "bg-white text-[#009639]";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getStatusColor = (group: CommunityGroup) => {
        // You can add logic here based on group activity, bookings, etc.
        return group.vehicles > 0 ? "bg-green-500" : "bg-yellow-500";
    };

    const header = (
        <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
            <h1 className="text-xl font-bold text-white">My Groups</h1>
            <button
                onClick={navigateToCreateGroup}
                className="w-10 h-10 bg-[#007A2F] hover:bg-[#006428] rounded-full flex items-center justify-center"
            >
                <Plus size={24} className="text-white" />
            </button>
        </div>
    );

    // Show loading only when authenticated and actually loading
    if (isAuthenticated && loading && !groups.length) {
        return (
            <div className="h-screen flex items-center justify-center bg-gray-50">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
            </div>
        );
    }

    // Show error only when authenticated and there's an actual error
    if (isAuthenticated && error) {
        return (
            <PageWithScroll>
                <div className="px-6 py-4 bg-[#009639]">
                    <h1 className="text-xl font-bold text-white">My Groups</h1>
                </div>

                <div className="flex-1 flex items-center justify-center px-4">
                    <div className="text-center">
                        <p className="text-red-600 mb-4">{error.message || "Failed to load groups"}</p>
                        <Button
                            onClick={() => refetch()}
                            className="bg-[#009639] text-white"
                        >
                            <RefreshCw size={16} className="mr-2" />
                            Retry
                        </Button>
                    </div>
                </div>
            </PageWithScroll>
        );
    }

    return (
        <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
            {/* Overview Stats
            <div className="p-4">
                <div className="bg-white rounded-xl shadow-md p-4 mb-4 border border-gray-100">
                    <h2 className="text-lg font-semibold text-[#333333] mb-3">Overview</h2>
                    <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-[#009639]">{userStats.totalGroups}</div>
                            <div className="text-sm text-[#797879]">Total Groups</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-[#009639]">{userStats.ownedGroups}</div>
                            <div className="text-sm text-[#797879]">Owned Groups</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-[#009639]">{userStats.totalVehicles}</div>
                            <div className="text-sm text-[#797879]">Vehicles</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-[#009639]">R{userStats.monthlyRevenue.toLocaleString()}</div>
                            <div className="text-sm text-[#797879]">Monthly Revenue</div>
                        </div>
                    </div>
                </div>
            </div>
 */}
            {/* Search and Filter */}
            <div className="p-4">
                <div className="flex space-x-2 mb-4">
                    <div className="flex-1 bg-white rounded-full px-4 py-2 flex items-center shadow-md border border-gray-100">
                        <Search size={18} className="text-[#797879] mr-2" />
                        <input
                            type="text"
                            placeholder="Search groups..."
                            className="flex-1 bg-transparent focus:outline-none text-[#333333] text-sm"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                </div>

                {/* Filter Buttons */}
                <div className="flex space-x-2 mb-6">
                    {["All", "Owned", "Member"].map((filter) => (
                        <button
                            key={filter}
                            onClick={() => setFilterRole(filter)}
                            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${filterRole === filter
                                ? "bg-[#009639] text-white"
                                : "bg-white text-[#797879] border border-gray-200"
                                }`}
                        >
                            {filter}
                        </button>
                    ))}
                </div>
            </div>

            {/* Groups List */}
            <div className="p-4">
                <h3 className="font-semibold text-[#333333] mb-4">
                    Your Groups ({filteredGroups.length})
                </h3>
                
                {filteredGroups.length > 0 ? (
                    <div className="space-y-3">
                        {filteredGroups.map((group) => (
                            <div
                                key={group.id}
                                className="bg-white rounded-xl shadow-md p-4 border border-gray-100 cursor-pointer hover:shadow-lg transition-shadow"
                                onClick={() => navigateToGroupDetails(group.id.toString())}
                            >
                                {/* Title Row with Badges */}
                                <div className="flex items-center justify-between mb-2">
                                    <h4 className="text-[#333333] font-semibold flex-1">{group.name}</h4>
                                    <div className="flex items-center gap-2 flex-shrink-0 ml-2">
                                        {group.memberRole && (
                                            <Badge className={getRoleBadgeColor(group.memberRole)}>
                                                {group.memberRole}
                                            </Badge>
                                        )}
                                        <Badge className={group.vehicles > 0 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                                            {group.vehicles > 0 ? "Active" : "Inactive"}
                                        </Badge>
                                    </div>
                                </div>
                                
                                {/* Description */}
                                <p className="text-sm text-[#797879] mb-3">{group.description}</p>
                                
                                {/* Stats Row */}
                                <div className="flex justify-between items-center text-xs text-[#797879] gap-4 mb-3">
                                    <div className="flex flex-col items-center gap-1">
                                        <Users size={12} />
                                        <span>{group.members} members</span>
                                    </div>
                                    <div className="flex flex-col items-center gap-1">
                                        <Car size={12} />
                                        <span>{group.vehicles} vehicles</span>
                                    </div>
                                    <div className="flex flex-col items-center gap-1">
                                        <MapPin size={12} />
                                        <span>{group.location}</span>
                                    </div>
                                </div>
                                
                                {/* Action Buttons */}
                                <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                                    <Button
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            navigateToGroupDetails(group.id.toString());
                                        }}
                                        variant="outline"
                                        size="sm"
                                        className="flex-1 mr-2 bg-[#009639] text-white hover:bg-[#009639] hover:text-white rounded-full"
                                    >
                                        View Group Detail
                                        
                                    </Button>
                                    
                                  {/*  <div className="flex items-center gap-1">
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                navigateToGroupChat(group.id.toString());
                                            }}
                                            className="p-2 text-[#009639] hover:bg-[#e6ffe6] rounded-lg transition-colors"
                                        >
                                            <MessageSquare size={16} />
                                        </button>
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                navigateToGroupSettings(group.id.toString());
                                            }}
                                            className="p-2 text-[#797879] hover:bg-gray-100 rounded-lg transition-colors"
                                        >
                                            <Settings size={16} />
                                        </button>
                                    </div> */}
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100 text-center">
                        {searchQuery ? (
                            <>
                                <Search size={48} className="text-[#797879] mx-auto mb-4" />
                                <p className="text-[#797879]">No groups found matching "{searchQuery}"</p>
                            </>
                        ) : (
                            <>
                                <Users size={48} className="text-[#797879] mx-auto mb-4" />
                                <p className="text-[#797879] mb-4">You're not part of any groups yet</p>
                                <Button
                                    onClick={() => navigateToCreateGroup()}
                                    className="bg-[#009639] text-white px-6 py-2 rounded-full font-medium"
                                >
                                    <Plus size={16} className="mr-2" />
                                    Create Your First Group
                                </Button>
                            </>
                        )}
                    </div>
                )}
            </div>

            {/* Quick Actions 
            <div className="p-4">
                <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
                    <h3 className="text-[#333333] font-semibold mb-3">Quick Actions</h3>
                    <div className="space-y-2">
                        <Button
                            onClick={navigateToCreateGroup}
                            className="w-full justify-start bg-[#009639] hover:bg-[#007A2F] text-white"
                        >
                            <Plus size={16} className="mr-2" />
                            Create New Group
                        </Button>
                        <Button
                            variant="outline"
                            onClick={navigateToCommunity}
                            className="w-full justify-start"
                        >
                            <Search size={16} className="mr-2" />
                            Discover Groups
                        </Button>
                    </div>
                </div>
            </div>
            */}
        </PageWithScroll>
    );
} 