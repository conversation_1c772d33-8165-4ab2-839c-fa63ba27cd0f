"use client";

import { useState, useEffect } from "react";
import { Arrow<PERSON>eft, CheckCircle, Clock, AlertCircle, Camera, Upload, MapPin, Car, X, Gauge, User } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PageWithScroll } from "@/components/ui/scroll-container";
import { useNavigation } from "@/hooks/useNavigation";
import { useCurrentUser } from "@/hooks/use-current-user";
import { getTaskById, completeBookingAcknowledgmentTask, completeTask } from "@/actions/tasks";
import GroupInvitationTaskContent from "@/components/GroupInvitationTaskContent";
import Image from "next/image";
import { DocumentUpload } from "@/lib/utils";
import VehicleHandoverTaskScreen from "./VehicleHandoverTaskScreen";

// Import proper DAO types from existing type files
import { 
  HandoverStatus, 
  HandoverType, 
  InspectionType, 
  InspectionStatus,
  ConditionLevel,
  GeneralCondition,
  LightsCondition,
  CleanlinessLevel,
  DashboardCondition,
  OdorLevel,
  ConditionEnhanced,
  PhotoType,
  VehicleHandoverRead,
  VehicleInspectionRead,
  CompleteInspectionRequest
} from "@/types/bookings";

// Import task-specific types (reusing from TasksScreen)
type TaskType = 'onboarding' | 'compliance' | 'invitations' | 'approvals' | 'maintenance' | 'financial' | 'handover' | 'GROUP_INVITATION' | 'BOOKING_ACKNOWLEDGMENT' | 'VEHICLE_HANDOVER_GIVER' | 'VEHICLE_HANDOVER_RECEIVER';
type TaskPriority = 'blocking' | 'urgent' | 'normal' | 'optional' | 'BLOCKING' | 'URGENT' | 'NORMAL' | 'OPTIONAL';
type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'dismissed' | 'expired' | 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'DISMISSED' | 'EXPIRED';

interface UserTask {
  id: string;
  userId: string;
  type: TaskType;
  priority: TaskPriority;
  status: TaskStatus;
  title: string;
  description: string;
  actionText: string;
  completionText?: string;
  dueDate?: Date;
  estimatedMinutes?: number;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  dismissedAt?: Date;
  expiresAt?: Date;
}

// Task content components interface
interface TaskContentProps {
  task: UserTask;
  onComplete: (result?: any) => void;
  onCancel: () => void;
}

interface TaskDetailScreenProps {
  params?: { taskId?: string };
}

export default function TaskDetailScreen({ params }: TaskDetailScreenProps) {
  const taskId = params?.taskId || '';
  
  if (!taskId) {
    console.error('TaskDetailScreen: No taskId provided');
    return null;
  }
  const [task, setTask] = useState<UserTask | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { goBack } = useNavigation();
  const currentUser = useCurrentUser();

  // Mock data - in real app, this would fetch from API
  const mockTasks: UserTask[] = [
    {
      id: '1',
      userId: 'user_123',
      type: 'compliance',
      priority: 'blocking',
      status: 'pending',
      title: 'Accept Updated Terms & Conditions',
      description: 'Review and accept updated terms to continue using Poolly.',
      actionText: 'Review & Accept Terms',
      estimatedMinutes: 5,
      metadata: { termsVersion: 'v2.1', termsUrl: '/terms-v2.1.pdf' },
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: '2',
      userId: 'user_123',
      type: 'onboarding',
      priority: 'blocking',
      status: 'pending',
      title: 'Complete Profile Setup',
      description: 'Add required information to activate your account.',
      actionText: 'Complete Profile',
      estimatedMinutes: 10,
      metadata: { 
        completionPercentage: 60,
        missingFields: ['phone', 'address', 'emergency_contact'],
        currentStep: 2,
        totalSteps: 4
      },
      createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 60 * 60 * 1000)
    },
    {
      id: '3',
      userId: 'user_123',
      type: 'invitations',
      priority: 'urgent',
      status: 'pending',
      title: 'Group Invitation from John Doe',
      description: 'You have been invited to join "Weekend Warriors" vehicle group.',
      actionText: 'View Invitation',
      dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
      metadata: { 
        groupId: 'group_123', 
        inviterName: 'John Doe',
        groupName: 'Weekend Warriors',
        memberCount: 4,
        vehicleCount: 2,
        inviteMessage: 'Hey! Would love to have you join our group for weekend adventures!'
      },
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
    },
    {
      id: '4',
      userId: 'user_123',
      type: 'maintenance',
      priority: 'normal',
      status: 'pending',
      title: 'Schedule Vehicle Inspection',
      description: 'Toyota Camry requires annual inspection.',
      actionText: 'Schedule Inspection',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      metadata: { vehicleId: 'vehicle_456', vehicleName: 'Toyota Camry 2019' },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '5',
      userId: 'user_123',
      type: 'financial',
      priority: 'optional',
      status: 'pending',
      title: 'Set Up Direct Debit',
      description: 'Automate your monthly contributions for convenience.',
      actionText: 'Set Up Payment',
      estimatedMinutes: 3,
      metadata: { paymentType: 'direct_debit' },
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    },
    // Handover tasks aligned with database schema
    {
      id: '6',
      userId: 'user_123',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Vehicle Handover Reminder',
      description: 'You have a vehicle handover scheduled for tomorrow at 10:00 AM. Please prepare for the vehicle inspection.',
      actionText: 'View Details',
      estimatedMinutes: 30,
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
      metadata: { 
        handoverId: '123',
        vehicleId: '456',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
        fromPartyId: '789',
        toPartyId: 'user_123',
        vehicleDetails: {
          make: 'Tesla',
          model: 'Model 3',
          year: 2023,
          registration: 'ABC123',
          color: 'White'
        },
        location: '123 Main St, Toronto, ON',
        isInspection: false, // Reminder mode
        handoverStatus: 'SCHEDULED',
        bookingReference: 'BK-2024-001'
      },
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      id: '7',
      userId: 'user_456',
      type: 'handover',
      priority: 'blocking',
      status: 'pending',
      title: 'Complete Vehicle Inspection',
      description: 'Your handover time has arrived. Please complete the vehicle inspection before taking possession.',
      actionText: 'Start Inspection',
      estimatedMinutes: 15,
      dueDate: new Date(), // Now
      metadata: { 
        handoverId: '124',
        vehicleId: '457',
        handoverType: 'BOOKING_START',
        scheduledTime: new Date(),
        fromPartyId: '790',
        toPartyId: 'user_456',
        vehicleDetails: {
          make: 'BMW',
          model: 'X3',
          year: 2022,
          registration: 'DEF456',
          color: 'Black'
        },
        location: '456 Oak Ave, Toronto, ON',
        isInspection: true, // Inspection mode
        handoverStatus: 'IN_PROGRESS',
        bookingReference: 'BK-2024-002',
        inspectionType: 'PRE_HANDOVER',
        relatedInspectionId: null // For comparison later
      },
      createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: '8',
      userId: 'user_789',
      type: 'handover',
      priority: 'urgent',
      status: 'pending',
      title: 'Acknowledge Vehicle Return',
      description: 'A vehicle return has been requested. Please confirm the handover and complete the return inspection.',
      actionText: 'Acknowledge Return',
      estimatedMinutes: 20,
      dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // In 2 hours
      metadata: { 
        handoverId: '125',
        vehicleId: '458',
        handoverType: 'BOOKING_END',
        scheduledTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
        fromPartyId: 'user_456',
        toPartyId: 'user_789',
        vehicleDetails: {
          make: 'Audi',
          model: 'A4',
          year: 2021,
          registration: 'GHI789',
          color: 'Silver'
        },
        location: '789 Pine St, Toronto, ON',
        isInspection: false, // Acknowledgment mode first
        handoverStatus: 'SCHEDULED',
        bookingReference: 'BK-2024-003',
        role: 'receiver' // Receiving the vehicle back
      },
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000)  
    }
  ];

  useEffect(() => {
    const loadTask = async () => {
      if (!taskId) return;
      
      setIsLoading(true);
      setError(null);
      
      try {
        const result = await getTaskById(parseInt(taskId));
        
        if (result.success && result.task) {
          // Convert database task to UI format
          const uiTask: UserTask = {
            id: result.task.id.toString(),
            userId: result.task.partyId?.toString() || result.task.email || '',
            type: result.task.type as TaskType,
            priority: result.task.priority as TaskPriority,
            status: result.task.status as TaskStatus,
            title: result.task.title,
            description: result.task.description || '',
            actionText: result.task.type === 'GROUP_INVITATION' ? 'Review Invitation' : 'View Details',
            estimatedMinutes: result.task.estimatedMinutes || undefined,
            dueDate: result.task.expiresAt ? new Date(result.task.expiresAt) : undefined,
            metadata: result.task.metadata || {},
            createdAt: new Date(result.task.createdAt),
            updatedAt: new Date(result.task.createdAt),
            // Add invitation details if available
            ...(('invitationDetails' in result.task) && { invitationDetails: result.task.invitationDetails })
          };
          
          setTask(uiTask);
        } else {
          setError('error' in result ? result.error || 'Task not found' : 'Task not found');
        }
      } catch (err) {
        console.error('Error loading task:', err);
        setError('Failed to load task');
      } finally {
        setIsLoading(false);
      }
    };

    loadTask();
  }, [taskId]);

  const handleTaskComplete = (result?: any) => {
    if (!task) return;
    
    // Check if this is a backend task completion result
    if (result?.taskResult?.success) {
      console.log('✅ Task completed successfully via backend:', task.title, result);
      
      // Update task status in UI
      setTask(prev => prev ? {
        ...prev,
        status: 'completed' as TaskStatus,
        completedAt: new Date(),
        completionText: `${prev.actionText} completed successfully`
      } : null);

      // Show success message and navigate back
      setTimeout(() => {
        console.log('✅ Booking acknowledgment completed successfully');
        goBack();
      }, 1500);
    } else if (result?.taskResult?.success === false) {
      // Backend call failed, don't update UI state
      console.error('❌ Backend task completion failed:', result.taskResult.error);
      // Error handling is done in the specific task component
    } else {
      // Handle non-backend task completions (legacy/mock tasks)
      console.log('Task completed (local only):', task.title, result);
      
      setTask(prev => prev ? {
        ...prev,
        status: 'completed' as TaskStatus,
        completedAt: new Date(),
        completionText: `${prev.actionText} completed`
      } : null);

      setTimeout(() => goBack(), 1500);
    }
  };

  const handleTaskCancel = () => {
    goBack();
  };

  // Render task-specific content based on type
  const renderTaskContent = () => {
    if (!task) return null;

    switch (task.type) {
      case 'GROUP_INVITATION':
        return <GroupInvitationTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'BOOKING_ACKNOWLEDGMENT':
        return <BookingAcknowledgmentTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'VEHICLE_HANDOVER_GIVER':
      case 'VEHICLE_HANDOVER_RECEIVER':
        return <VehicleHandoverTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'compliance':
        return <ComplianceTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'onboarding':
        return <OnboardingTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'invitations':
        return <InvitationTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      case 'maintenance':
        return <MaintenanceTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
      default:
        return <DefaultTaskContent task={task} onComplete={handleTaskComplete} onCancel={handleTaskCancel} />;
    }
  };

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case 'blocking':
      case 'BLOCKING': 
        return 'bg-red-100 text-red-800';
      case 'urgent':
      case 'URGENT':
        return 'bg-orange-100 text-orange-800';
      case 'normal':
      case 'NORMAL':
        return 'bg-blue-100 text-blue-800';
      case 'optional':
      case 'OPTIONAL':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const header = (
    <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
      <div className="flex items-center">
        <button className="mr-4" onClick={goBack}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">
          {isLoading ? 'Loading Task...' : task?.title || 'Task Details'}
        </h1>
      </div>
      {task && (
        <Badge className={getPriorityColor(task.priority)}>
          {(task.priority === 'blocking' || task.priority === 'BLOCKING') ? 'Required' : 
           (task.priority === 'urgent' || task.priority === 'URGENT') ? 'Urgent' :
           (task.priority === 'normal' || task.priority === 'NORMAL') ? 'Normal' : 'Optional'}
        </Badge>
      )}
    </div>
  );

  if (isLoading) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
            <p className="text-[#797879]">Loading task details...</p>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  if (error || !task) {
    return (
      <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
        <div className="p-4">
          <div className="bg-white rounded-xl shadow-md p-6 text-center border border-gray-100">
            <AlertCircle size={48} className="text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-[#333333] mb-2">Task Not Found</h3>
            <p className="text-[#797879] mb-4">The task you're looking for doesn't exist or has been removed.</p>
            <Button onClick={goBack} className="bg-[#009639] hover:bg-[#007A2F] text-white">
              Go Back
            </Button>
          </div>
        </div>
      </PageWithScroll>
    );
  }

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      {/* Task Overview */}
      <div className="p-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4 mb-4">
          <div className="flex items-start gap-3 mb-3">
            <div className="w-12 h-12 bg-[#e6ffe6] rounded-full flex items-center justify-center flex-shrink-0">
              {task.status === 'completed' ? (
                <CheckCircle size={24} className="text-[#009639]" />
              ) : (
                <Clock size={24} className="text-[#009639]" />
              )}
            </div>
            <div className="flex-1">
              <h2 className="text-lg font-semibold text-[#333333] mb-1">{task.title}</h2>
              <p className="text-[#797879] text-sm">{task.description}</p>
            </div>
          </div>
          
          {/* Task metadata */}
          <div className="flex items-center gap-4 text-xs text-[#797879] pt-3 border-t border-gray-100">
            {task.estimatedMinutes && (
              <div className="flex items-center gap-1">
                <Clock size={12} />
                {task.estimatedMinutes < 60 ? `${task.estimatedMinutes} min` : `${Math.round(task.estimatedMinutes / 60)}h`}
              </div>
            )}
            {task.dueDate && (
              <div className="flex items-center gap-1">
                <AlertCircle size={12} />
                Due {task.dueDate.toLocaleDateString()}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Task-specific content */}
      {renderTaskContent()}
    </PageWithScroll>
  );
}

// Default task content (fallback)
function DefaultTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [isCompleting, setIsCompleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const currentUser = useCurrentUser();

      const handleComplete = async () => {
      if (!currentUser?.partyId) {
        setError("User information not available");
        return;
      }

      setIsCompleting(true);
      setError(null);

      try {
        const result = await completeTask(
          parseInt(task.id), 
          parseInt(currentUser.partyId.toString())
        );

      if (result.success) {
        onComplete({ 
          completed: true, 
          timestamp: new Date().toISOString(),
          taskResult: result 
        });
      } else {
        setError(result.error || "Failed to complete task");
      }
    } catch (err) {
      console.error("Error completing task:", err);
      setError("An unexpected error occurred");
    } finally {
      setIsCompleting(false);
    }
  };

  return (
    <div className="p-4">
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 text-center">
        <h3 className="text-lg font-semibold text-[#333333] mb-4">Complete this task</h3>
        <p className="text-[#797879] mb-6">Mark this task as completed when you're done.</p>
        
        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}
        
        <div className="flex gap-3">
          <Button variant="outline" onClick={onCancel} className="flex-1" disabled={isCompleting}>
            Cancel
          </Button>
          <Button 
            onClick={handleComplete} 
            disabled={isCompleting}
            className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCompleting ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Completing...
              </div>
            ) : (
              "Mark Complete"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}

// Booking Acknowledgment Task Content - single acknowledgment button
function BookingAcknowledgmentTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [isAcknowledging, setIsAcknowledging] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const currentUser = useCurrentUser();

  // Add detailed logging to understand the issue
  console.log("🔍 BookingAcknowledgmentTaskContent - Debug Info:");
  console.log("📋 Task:", task);
  console.log("👤 User attributes:", currentUser);
  console.log("🆔 Party ID:", currentUser?.partyId);
  console.log("📧 Email:", currentUser?.email);
  console.log("📱 All attributes keys:", currentUser ? Object.keys(currentUser) : "No attributes");

  const handleAcknowledge = async () => {
    console.log("🔘 handleAcknowledge called");
    console.log("👤 Current attributes:", currentUser);
    console.log("🆔 Party ID check:", currentUser?.partyId);
    
    if (!currentUser?.partyId) {
      console.error("❌ Missing partyId in currentUser:", {
        attributes: currentUser,
        hasAttributes: !!currentUser,
        partyId: currentUser?.partyId,
        attributesKeys: currentUser ? Object.keys(currentUser) : null
      });
      setError("User information not available");
      return;
    }

    console.log("✅ Party ID found, proceeding with acknowledgment:", currentUser.partyId);
    setIsAcknowledging(true);
    setError(null);

    try {
      console.log("📞 Calling completeBookingAcknowledgmentTask with:", {
        taskId: parseInt(task.id),
        partyId: parseInt(currentUser.partyId.toString())
      });

      const result = await completeBookingAcknowledgmentTask(
        parseInt(task.id), 
        parseInt(currentUser.partyId.toString())
      );

      console.log("📋 Backend result:", result);

      if (result.success) {
        console.log("✅ Task completed successfully");
        // Pass the result to the parent component
        onComplete({ 
          acknowledged: true, 
          timestamp: new Date().toISOString(),
          taskResult: result 
        });
      } else {
        console.error("❌ Backend returned error:", result.error);
        setError(result.error || "Failed to acknowledge booking");
      }
    } catch (err) {
      console.error("💥 Exception during acknowledgment:", err);
      setError("An unexpected error occurred");
    } finally {
      setIsAcknowledging(false);
    }
  };

  return (
    <div className="p-4 space-y-4">
      {/* Booking Details Card */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <div className="flex items-center gap-3 mb-4">
          <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle size={24} className="text-green-600" />
          </div>
          <div>
            <h3 className="font-semibold text-[#333333]">Booking Confirmed</h3>
            <p className="text-[#797879] text-sm">Reference: {task.metadata.bookingReference}</p>
          </div>
        </div>
        
        <div className="space-y-3">
          <div>
            <span className="text-[#797879] text-sm">Vehicle:</span>
            <p className="font-medium text-[#333333]">{task.metadata.vehicleName}</p>
          </div>
          <div>
            <span className="text-[#797879] text-sm">Booking Period:</span>
            <p className="font-medium text-[#333333]">
              {new Date(task.metadata.requestedStart).toLocaleDateString()} - {new Date(task.metadata.requestedEnd).toLocaleDateString()}
            </p>
          </div>
          {task.metadata.fromPartyName && (
            <div>
              <span className="text-[#797879] text-sm">Requested by:</span>
              <p className="font-medium text-[#333333]">{task.metadata.fromPartyName}</p>
            </div>
          )}
        </div>
      </div>

      {/* Acknowledgment Notice */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <h4 className="font-semibold text-blue-800 mb-2">Acknowledgment Required</h4>
        <p className="text-blue-700 text-sm">
          Please acknowledge this booking request. This confirms that you are aware of the upcoming vehicle handover and are prepared to make the vehicle available at the scheduled time.
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-4">
          <h4 className="font-semibold text-red-800 mb-1">Error</h4>
          <p className="text-red-700 text-sm">{error}</p>
        </div>
      )}

      {/* Single Acknowledge Button */}
      <div className="flex justify-center pt-2">
        <Button 
          onClick={handleAcknowledge}
          disabled={isAcknowledging}
          className="bg-[#009639] hover:bg-[#007A2F] text-white px-8 py-3 text-lg font-semibold rounded-full disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isAcknowledging ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Processing...
            </div>
          ) : (
            "Acknowledge Booking"
          )}
        </Button>
      </div>
    </div>
  );
}

// Vehicle Handover Task Content - wraps the VehicleHandoverTaskScreen
function VehicleHandoverTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [vehicle, setVehicle] = useState<any>(null);

  // Extract vehicle information from task metadata
  useEffect(() => {
    const fetchVehicleData = async () => {
      try {
        setIsLoading(true);
        
        // For demo purposes, create a mock vehicle object
        // In a real implementation, you would fetch this from the API using task.metadata.vehicleId
        const mockVehicle = {
          id: task.metadata?.vehicleId || 1,
          model: {
            model: task.metadata?.vehicleName || "Vehicle",
            make: "Demo",
            year: 2023,
          },
          // Add other required vehicle properties...
        };
        
        setVehicle(mockVehicle);
        setError(null);
      } catch (err: any) {
        console.error("Error fetching vehicle data:", err);
        setError(err.message || "Failed to load vehicle information");
      } finally {
        setIsLoading(false);
      }
    };

    fetchVehicleData();
  }, [task]);

  if (isLoading) {
    return (
      <div className="p-4 space-y-4">
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-6 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-4"></div>
          <p className="text-[#797879]">Loading vehicle information...</p>
        </div>
      </div>
    );
  }

  if (error || !vehicle) {
    return (
      <div className="p-4 space-y-4">
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center">
          <p className="text-red-800 font-medium mb-2">Unable to load vehicle information</p>
          <p className="text-red-600 text-sm">{error || "Vehicle data not available"}</p>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
          >
            Close Task
          </button>
        </div>
      </div>
    );
  }

  return (
    <VehicleHandoverTaskScreen
      task={task}
      vehicle={vehicle}
      onComplete={onComplete}
      onCancel={onCancel}
    />
  );
}

// ComplianceTaskContent - for terms acceptance
function ComplianceTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [hasRead, setHasRead] = useState(false);
  const [accepted, setAccepted] = useState(false);

  return (
    <div className="p-4 space-y-4">
      {/* Terms Document */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Terms & Conditions</h3>
        <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
          <div className="text-sm text-[#333333] space-y-3">
            <p><strong>Terms & Conditions v{task.metadata.termsVersion}</strong></p>
            <p>By using Poolly, you agree to the following terms...</p>
            <p>• Data collection and privacy policies</p>
            <p>• Vehicle sharing responsibilities</p>
            <p>• Payment and billing agreements</p>
            <p>• Community guidelines and behavior</p>
            <p>• Insurance and liability coverage</p>
            <p>• Dispute resolution procedures</p>
            <p>• Account termination conditions</p>
            <p>[... rest of terms content ...]</p>
          </div>
        </div>
        
        <div className="mt-4 space-y-3">
          <label className="flex items-center gap-2">
            <input 
              type="checkbox" 
              checked={hasRead}
              onChange={(e) => setHasRead(e.target.checked)}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-[#333333]">I have read and understood these terms</span>
          </label>
          
          <label className="flex items-center gap-2">
            <input 
              type="checkbox" 
              checked={accepted}
              onChange={(e) => setAccepted(e.target.checked)}
              disabled={!hasRead}
              className="rounded border-gray-300"
            />
            <span className="text-sm text-[#333333]">I accept these terms and conditions</span>
          </label>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
        <Button 
          onClick={() => onComplete({ accepted: true, version: task.metadata.termsVersion })}
          disabled={!hasRead || !accepted}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
        >
          Accept Terms
        </Button>
      </div>
    </div>
  );
}

// OnboardingTaskContent - for profile setup
function OnboardingTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [currentStep, setCurrentStep] = useState(task.metadata.currentStep || 1);
  const [formData, setFormData] = useState({
    phone: '',
    address: '',
    emergencyContact: ''
  });
  const totalSteps = task.metadata.totalSteps || 4;
  const completionPercentage = (currentStep / totalSteps) * 100;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isStepComplete = () => {
    return formData.phone && formData.address && formData.emergencyContact;
  };

  return (
    <div className="p-4 space-y-4">
      {/* Progress */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <div className="flex justify-between items-center mb-2">
          <h3 className="font-semibold text-[#333333]">Profile Setup Progress</h3>
          <span className="text-sm text-[#797879]">{currentStep} of {totalSteps}</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-[#009639] h-2 rounded-full transition-all duration-300"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
        <p className="text-xs text-[#797879] mt-2">{Math.round(completionPercentage)}% complete</p>
      </div>

      {/* Current Step Content */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Step {currentStep}: Personal Information</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Phone Number</label>
            <input 
              type="tel" 
              placeholder="+27 xxx xxx xxxx"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Address</label>
            <input 
              type="text" 
              placeholder="Street address"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Emergency Contact</label>
            <input 
              type="text" 
              placeholder="Contact name and number"
              value={formData.emergencyContact}
              onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Save & Exit
        </Button>
        <Button 
          onClick={() => onComplete({ 
            step: currentStep, 
            completed: currentStep === totalSteps,
            formData 
          })}
          disabled={!isStepComplete()}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
        >
          {currentStep === totalSteps ? 'Complete Setup' : 'Continue'}
        </Button>
      </div>
    </div>
  );
}

// InvitationTaskContent - for group invitations
function InvitationTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  return (
    <div className="p-4 space-y-4">
      {/* Group Details */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Group Details</h3>
        <div className="space-y-3">
          <div className="flex justify-between">
            <span className="text-[#797879]">Group Name:</span>
            <span className="font-medium text-[#333333]">{task.metadata.groupName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-[#797879]">Members:</span>
            <span className="font-medium text-[#333333]">{task.metadata.memberCount} people</span>
          </div>
          <div className="flex justify-between">
            <span className="text-[#797879]">Vehicles:</span>
            <span className="font-medium text-[#333333]">{task.metadata.vehicleCount} vehicles</span>
          </div>
          <div className="flex justify-between">
            <span className="text-[#797879]">Invited by:</span>
            <span className="font-medium text-[#333333]">{task.metadata.inviterName}</span>
          </div>
        </div>
      </div>

      {/* Invitation Message */}
      {task.metadata.inviteMessage && (
        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
          <h3 className="font-semibold text-[#333333] mb-2">Message</h3>
          <p className="text-[#797879] italic">"{task.metadata.inviteMessage}"</p>
        </div>
      )}

      {/* Actions */}
      <div className="flex gap-3">
        <Button 
          variant="outline" 
          onClick={() => onComplete({ action: 'declined' })}
          className="flex-1 border-red-300 text-red-700 hover:bg-red-50"
        >
          Decline
        </Button>
        <Button 
          onClick={() => onComplete({ action: 'accepted', groupId: task.metadata.groupId })}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
        >
          Accept Invitation
        </Button>
      </div>
    </div>
  );
}

// MaintenanceTaskContent - for vehicle maintenance
function MaintenanceTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');

  return (
    <div className="p-4 space-y-4">
      {/* Vehicle Info */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Vehicle Information</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-[#797879]">Vehicle:</span>
            <span className="font-medium text-[#333333]">{task.metadata.vehicleName}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-[#797879]">Service Type:</span>
            <span className="font-medium text-[#333333]">Annual Inspection</span>
          </div>
        </div>
      </div>

      {/* Schedule Selection */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Schedule Appointment</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Preferred Date</label>
            <input 
              type="date" 
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Preferred Time</label>
            <select 
              value={selectedTime}
              onChange={(e) => setSelectedTime(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            >
              <option value="">Select a time</option>
              <option value="09:00">9:00 AM</option>
              <option value="10:00">10:00 AM</option>
              <option value="11:00">11:00 AM</option>
              <option value="14:00">2:00 PM</option>
              <option value="15:00">3:00 PM</option>
              <option value="16:00">4:00 PM</option>
            </select>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Cancel
        </Button>
        <Button 
          onClick={() => onComplete({ 
            vehicleId: task.metadata.vehicleId,
            scheduledDate: selectedDate,
            scheduledTime: selectedTime
          })}
          disabled={!selectedDate || !selectedTime}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
        >
          Schedule Inspection
        </Button>
      </div>
    </div>
  );
}

// FinancialTaskContent - for payment setup
function FinancialTaskContent({ task, onComplete, onCancel }: TaskContentProps) {
  const [bankDetails, setBankDetails] = useState({
    accountHolder: '',
    bankName: '',
    accountNumber: '',
    branchCode: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setBankDetails(prev => ({ ...prev, [field]: value }));
  };

  const isFormComplete = () => {
    return Object.values(bankDetails).every(value => value.trim() !== '');
  };

  return (
    <div className="p-4 space-y-4">
      {/* Payment Info */}
      <div className="bg-white rounded-xl shadow-md border border-gray-100 p-4">
        <h3 className="font-semibold text-[#333333] mb-3">Direct Debit Setup</h3>
        <p className="text-[#797879] text-sm mb-4">
          Set up direct debit to automatically pay your monthly contributions. This helps ensure you never miss a payment.
        </p>
        
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Account Holder Name</label>
            <input 
              type="text" 
              placeholder="Full name as on bank account"
              value={bankDetails.accountHolder}
              onChange={(e) => handleInputChange('accountHolder', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Bank Name</label>
            <select 
              value={bankDetails.bankName}
              onChange={(e) => handleInputChange('bankName', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            >
              <option value="">Select your bank</option>
              <option value="Standard Bank">Standard Bank</option>
              <option value="FNB">First National Bank (FNB)</option>
              <option value="ABSA">ABSA</option>
              <option value="Nedbank">Nedbank</option>
              <option value="Capitec">Capitec Bank</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Account Number</label>
            <input 
              type="text" 
              placeholder="Account number"
              value={bankDetails.accountNumber}
              onChange={(e) => handleInputChange('accountNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-[#333333] mb-1">Branch Code</label>
            <input 
              type="text" 
              placeholder="6-digit branch code"
              value={bankDetails.branchCode}
              onChange={(e) => handleInputChange('branchCode', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#009639]"
            />
          </div>
        </div>
      </div>

      {/* Security Notice */}
      <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
        <p className="text-xs text-blue-800">
          🔒 Your banking information is encrypted and secure. We use bank-level security to protect your data.
        </p>
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        <Button variant="outline" onClick={onCancel} className="flex-1">
          Skip for Now
        </Button>
        <Button 
          onClick={() => onComplete({ bankDetails, paymentType: task.metadata.paymentType })}
          disabled={!isFormComplete()}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex-1"
        >
          Set Up Direct Debit
        </Button>
      </div>
    </div>
  );
}

// Helper function for condition options
function getConditionOptions(type: 'condition' | 'general' | 'lights' | 'cleanliness' | 'dashboard' | 'odor') {
  switch (type) {
    case 'condition':
      return [
        { value: ConditionLevel.NONE, label: 'None', color: 'bg-green-100 text-green-800' },
        { value: ConditionLevel.MINOR, label: 'Minor', color: 'bg-yellow-100 text-yellow-800' },
        { value: ConditionLevel.MAJOR, label: 'Major', color: 'bg-red-100 text-red-800' }
      ];
    case 'general':
      return [
        { value: GeneralCondition.GOOD, label: 'Good', color: 'bg-green-100 text-green-800' },
        { value: GeneralCondition.FAIR, label: 'Fair', color: 'bg-yellow-100 text-yellow-800' },
        { value: GeneralCondition.POOR, label: 'Poor', color: 'bg-red-100 text-red-800' }
      ];
    case 'lights':
      return [
        { value: LightsCondition.WORKING, label: 'Working', color: 'bg-green-100 text-green-800' },
        { value: LightsCondition.PARTIAL, label: 'Partial', color: 'bg-yellow-100 text-yellow-800' },
        { value: LightsCondition.BROKEN, label: 'Broken', color: 'bg-red-100 text-red-800' }
      ];
    case 'cleanliness':
      return [
        { value: CleanlinessLevel.CLEAN, label: 'Clean', color: 'bg-green-100 text-green-800' },
        { value: CleanlinessLevel.ACCEPTABLE, label: 'Acceptable', color: 'bg-yellow-100 text-yellow-800' },
        { value: CleanlinessLevel.DIRTY, label: 'Dirty', color: 'bg-red-100 text-red-800' }
      ];
    case 'dashboard':
      return [
        { value: DashboardCondition.WORKING, label: 'Working', color: 'bg-green-100 text-green-800' },
        { value: DashboardCondition.PARTIAL, label: 'Partial', color: 'bg-yellow-100 text-yellow-800' },
        { value: DashboardCondition.ISSUES, label: 'Issues', color: 'bg-red-100 text-red-800' }
      ];
    case 'odor':
      return [
        { value: OdorLevel.NONE, label: 'None', color: 'bg-green-100 text-green-800' },
        { value: OdorLevel.MILD, label: 'Mild', color: 'bg-yellow-100 text-yellow-800' },
        { value: OdorLevel.STRONG, label: 'Strong', color: 'bg-red-100 text-red-800' }
      ];
    default:
      return [];
  }
} 