"use client";

import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Car, CheckCircle, Clock, MapPin } from "lucide-react";

interface VehicleHandoverScreenProps {
  params?: { vehicleId?: string };
}

export default function VehicleHandoverScreen({ params }: VehicleHandoverScreenProps) {
  const { goBack } = useNavigation();
  const vehicleId = params?.vehicleId || "1";

  const mockHandover = {
    vehicleId,
    vehicleName: "Tesla Model 3",
    handoverDate: "2024-01-20",
    handoverTime: "10:00 AM",
    location: "Downtown Pickup Center",
    status: "Scheduled",
    checklist: [
      { item: "Vehicle inspection", completed: true },
      { item: "Documents verification", completed: true },
      { item: "Keys handover", completed: false },
      { item: "Insurance verification", completed: false }
    ]
  };

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <button
          onClick={goBack}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">Vehicle Handover</h1>
        <div className="w-10 h-10"></div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Vehicle Info */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
              <Car className="text-gray-400" size={24} />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{mockHandover.vehicleName}</h2>
              <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {mockHandover.status}
              </span>
            </div>
          </div>
        </div>

        {/* Handover Details */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-3">Handover Details</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Clock className="text-blue-600" size={16} />
              <div>
                <p className="text-sm font-medium text-blue-900">{mockHandover.handoverDate}</p>
                <p className="text-xs text-blue-700">{mockHandover.handoverTime}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="text-blue-600" size={16} />
              <p className="text-sm text-blue-900">{mockHandover.location}</p>
            </div>
          </div>
        </div>

        {/* Handover Checklist */}
        <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-gray-900 mb-4">Handover Checklist</h3>
          <div className="space-y-3">
            {mockHandover.checklist.map((item, index) => (
              <div key={index} className="flex items-center space-x-3">
                <div className={`w-5 h-5 rounded-full flex items-center justify-center ${
                  item.completed ? 'bg-green-100' : 'bg-gray-100'
                }`}>
                  {item.completed && <CheckCircle className="text-green-600" size={14} />}
                </div>
                <span className={`text-sm ${
                  item.completed ? 'text-green-900 line-through' : 'text-gray-900'
                }`}>
                  {item.item}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="font-semibold text-yellow-900 mb-2">Important Instructions</h3>
          <div className="text-sm text-yellow-800 space-y-1">
            <p>• Arrive 15 minutes before scheduled time</p>
            <p>• Bring valid ID and driving license</p>
            <p>• Complete vehicle inspection thoroughly</p>
            <p>• Report any damages immediately</p>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-gray-100">
        <button className="w-full bg-[#009639] text-white py-4 rounded-lg font-semibold hover:bg-[#007A2F] transition-colors">
          
        </button>
      </div>
    </div>
  );
} 