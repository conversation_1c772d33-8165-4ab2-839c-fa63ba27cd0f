"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Car, Upload, MapPin, DollarSign } from "lucide-react";

interface ListVehicleScreenProps {
  params?: Record<string, any>;
}

export default function ListVehicleScreen({ params }: ListVehicleScreenProps) {
  const { goBack } = useNavigation();
  const [formData, setFormData] = useState({
    make: "",
    model: "",
    year: "",
    location: "",
    pricePerDay: "",
    description: "",
    features: [] as string[]
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    console.log("Submitting vehicle listing:", formData);
    // Handle form submission
  };

  const vehicleFeatures = [
    "Air Conditioning", "GPS Navigation", "Bluetooth", "USB Charging",
    "Backup Camera", "Heated Seats", "Sunroof", "Premium Audio"
  ];

  const toggleFeature = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  return (
    <div className="h-full bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <button
          onClick={goBack}
          className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
        >
          <ArrowLeft size={20} className="text-gray-600" />
        </button>
        <h1 className="text-lg font-semibold text-gray-900">List Your Vehicle</h1>
        <div className="w-10 h-10"></div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-4">
        {/* Vehicle Photos */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-900 mb-3">Vehicle Photos</h3>
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-[#009639] transition-colors cursor-pointer">
            <Upload className="mx-auto text-gray-400 mb-2" size={32} />
            <p className="text-gray-600">Tap to upload photos</p>
            <p className="text-sm text-gray-500">Add at least 3 photos</p>
          </div>
        </div>

        {/* Vehicle Details */}
        <div className="space-y-4 mb-6">
          <h3 className="font-semibold text-gray-900">Vehicle Details</h3>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Make</label>
              <input
                type="text"
                placeholder="e.g., Tesla"
                value={formData.make}
                onChange={(e) => handleInputChange("make", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Model</label>
              <input
                type="text"
                placeholder="e.g., Model 3"
                value={formData.model}
                onChange={(e) => handleInputChange("model", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Year</label>
            <input
              type="number"
              placeholder="e.g., 2023"
              value={formData.year}
              onChange={(e) => handleInputChange("year", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
            <div className="relative">
              <MapPin size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Where is your vehicle located?"
                value={formData.location}
                onChange={(e) => handleInputChange("location", e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Price per Day</label>
            <div className="relative">
              <DollarSign size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="number"
                placeholder="0"
                value={formData.pricePerDay}
                onChange={(e) => handleInputChange("pricePerDay", e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              placeholder="Describe your vehicle..."
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent resize-none"
            />
          </div>
        </div>

        {/* Features */}
        <div className="mb-8">
          <h3 className="font-semibold text-gray-900 mb-3">Features</h3>
          <div className="grid grid-cols-2 gap-2">
            {vehicleFeatures.map((feature) => (
              <button
                key={feature}
                onClick={() => toggleFeature(feature)}
                className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                  formData.features.includes(feature)
                    ? 'bg-[#009639] text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {feature}
              </button>
            ))}
          </div>
        </div>

        {/* Pricing Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-900 mb-2">Pricing Tips</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• Check similar vehicles in your area</p>
            <p>• Consider seasonal demand</p>
            <p>• Factor in fuel and maintenance costs</p>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-gray-100">
        <button
          onClick={handleSubmit}
          className="w-full bg-[#009639] text-white py-4 rounded-lg font-semibold hover:bg-[#007A2F] transition-colors"
        >
          List Vehicle
        </button>
      </div>
    </div>
  );
} 