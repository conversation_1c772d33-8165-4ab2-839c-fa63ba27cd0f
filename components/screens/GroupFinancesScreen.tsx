"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { 
  ArrowLeft, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Download,
  Filter,
  CreditCard,
  Users,
  Car,
  AlertTriangle
} from "lucide-react";

interface GroupFinancesScreenProps {
  params?: { groupId?: string };
}

export default function GroupFinancesScreen({ params }: GroupFinancesScreenProps) {
  const { goBack } = useNavigation();
  const groupId = params?.groupId || "1";
  
  const [activeTab, setActiveTab] = useState<'overview' | 'transactions' | 'payouts'>('overview');
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month');

  // Mock financial data
  const financialData = {
    totalRevenue: 2450,
    totalExpenses: 890,
    netProfit: 1560,
    pendingPayouts: 340,
    monthlyGrowth: 12.5,
    transactions: [
      {
        id: "t1",
        type: "income",
        amount: 180,
        description: "Tesla Model 3 booking - <PERSON>",
        date: "2024-01-15T14:30:00Z",
        status: "completed"
      },
      {
        id: "t2", 
        type: "expense",
        amount: 85,
        description: "Vehicle maintenance - BMW X3",
        date: "2024-01-14T10:00:00Z",
        status: "completed"
      },
      {
        id: "t3",
        type: "income",
        amount: 120,
        description: "Honda Civic booking - Mike R.",
        date: "2024-01-13T16:45:00Z",
        status: "completed"
      },
      {
        id: "t4",
        type: "expense",
        amount: 45,
        description: "Insurance payment",
        date: "2024-01-12T09:15:00Z",
        status: "pending"
      },
      {
        id: "t5",
        type: "income",
        amount: 200,
        description: "Tesla Model 3 booking - Emma L.",
        date: "2024-01-11T11:20:00Z",
        status: "completed"
      }
    ],
    payouts: [
      {
        id: "p1",
        member: "Sarah Mitchell",
        amount: 450,
        percentage: 28.8,
        status: "completed",
        date: "2024-01-01T00:00:00Z"
      },
      {
        id: "p2",
        member: "Mike Rodriguez", 
        amount: 320,
        percentage: 20.5,
        status: "pending",
        date: "2024-01-01T00:00:00Z"
      },
      {
        id: "p3",
        member: "Emma Liu",
        amount: 280,
        percentage: 17.9,
        status: "completed",
        date: "2024-01-01T00:00:00Z"
      },
      {
        id: "p4",
        member: "David Chen",
        amount: 180,
        percentage: 11.5,
        status: "pending",
        date: "2024-01-01T00:00:00Z"
      }
    ]
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const getTransactionIcon = (type: string) => {
    return type === 'income' ? (
      <TrendingUp size={16} className="text-green-600" />
    ) : (
      <TrendingDown size={16} className="text-red-600" />
    );
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Group Finances</h1>
          <button className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100">
            <Download size={20} className="text-gray-600" />
          </button>
        </div>
      </div>

      {/* Period Selector */}
      <div className="bg-white border-b border-gray-100">
        <div className="p-4">
          <div className="flex bg-gray-100 rounded-lg p-1">
            {[
              { key: 'week', label: 'This Week' },
              { key: 'month', label: 'This Month' },
              { key: 'quarter', label: 'This Quarter' }
            ].map(({ key, label }) => (
              <button
                key={key}
                onClick={() => setSelectedPeriod(key as any)}
                className={`flex-1 py-2 px-3 rounded-md font-medium transition-all ${
                  selectedPeriod === key
                    ? "bg-white text-[#009639] shadow-sm"
                    : "text-gray-600"
                }`}
              >
                {label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex px-4">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'transactions', label: 'Transactions' },
            { key: 'payouts', label: 'Payouts' }
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`flex-1 py-3 px-4 font-medium transition-all ${
                activeTab === key
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-gray-600"
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'overview' && (
          <div className="p-4 space-y-4">
            {/* Summary Cards */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                    <DollarSign size={20} className="text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Total Revenue</p>
                    <p className="text-xl font-bold text-gray-900">{formatCurrency(financialData.totalRevenue)}</p>
                  </div>
                </div>
              </div>
              
              <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <TrendingUp size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Net Profit</p>
                    <p className="text-xl font-bold text-gray-900">{formatCurrency(financialData.netProfit)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Growth Indicator */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Monthly Growth</h3>
                  <div className="flex items-center space-x-2">
                    <TrendingUp size={16} className="text-green-600" />
                    <span className="text-lg font-bold text-green-600">+{financialData.monthlyGrowth}%</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  Revenue has increased by {financialData.monthlyGrowth}% compared to last month
                </p>
              </div>
            </div>

            {/* Expense Breakdown */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100">
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Expense Breakdown</h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Vehicle Maintenance</span>
                    <span className="font-medium text-gray-900">$420</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Insurance</span>
                    <span className="font-medium text-gray-900">$280</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-600">Platform Fees</span>
                    <span className="font-medium text-gray-900">$190</span>
                  </div>
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-gray-900">Total Expenses</span>
                      <span className="text-lg font-bold text-gray-900">{formatCurrency(financialData.totalExpenses)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Pending Payouts Alert */}
            {financialData.pendingPayouts > 0 && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-xl">
                <div className="p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle size={20} className="text-yellow-600 mt-1" />
                    <div>
                      <h4 className="font-medium text-yellow-900">Pending Payouts</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        {formatCurrency(financialData.pendingPayouts)} in payouts are pending distribution
                      </p>
                      <button className="text-sm text-yellow-800 font-medium mt-2">
                        Review Payouts →
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'transactions' && (
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
              <button className="flex items-center space-x-2 px-3 py-2 bg-gray-100 rounded-lg text-sm">
                <Filter size={16} />
                <span>Filter</span>
              </button>
            </div>

            {financialData.transactions.map((transaction) => (
              <div key={transaction.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getTransactionIcon(transaction.type)}
                      <div>
                        <p className="font-medium text-gray-900">{transaction.description}</p>
                        <p className="text-sm text-gray-600">{formatDate(transaction.date)}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-lg font-bold ${
                        transaction.type === 'income' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {transaction.type === 'income' ? '+' : '-'}{formatCurrency(transaction.amount)}
                      </p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                        {transaction.status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'payouts' && (
          <div className="p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Member Payouts</h3>
              <button className="px-4 py-2 bg-[#009639] text-white rounded-lg text-sm font-medium">
                Process Payouts
              </button>
            </div>

            {financialData.payouts.map((payout) => (
              <div key={payout.id} className="bg-white rounded-xl shadow-sm border border-gray-100">
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                      <div>
                        <p className="font-medium text-gray-900">{payout.member}</p>
                        <p className="text-sm text-gray-600">{payout.percentage}% share</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-lg font-bold text-gray-900">{formatCurrency(payout.amount)}</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payout.status)}`}>
                        {payout.status}
                      </span>
                    </div>
                  </div>
                  
                  {payout.status === 'pending' && (
                    <div className="mt-3 flex space-x-2">
                      <button className="flex-1 py-2 bg-[#009639] text-white rounded-lg text-sm font-medium">
                        Approve
                      </button>
                      <button className="flex-1 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium">
                        Review
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Payout Summary */}
            <div className="bg-blue-50 border border-blue-200 rounded-xl">
              <div className="p-4">
                <h4 className="font-medium text-blue-900 mb-2">Payout Information</h4>
                <p className="text-sm text-blue-700">
                  Payouts are calculated based on vehicle contribution and usage. 
                  All pending payouts will be processed on the 1st of each month.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 