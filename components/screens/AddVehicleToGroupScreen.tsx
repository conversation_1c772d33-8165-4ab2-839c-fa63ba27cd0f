"use client";

import { ArrowLeft } from "lucide-react";
import { useUserAttributes } from "@/hooks/useUserAttributes";
import { useNavigation } from "@/hooks/useNavigation";
import { PageWithScroll } from "@/components/ui/scroll-container";
import VehicleToGroupForm from "@/components/vehicle-to-group-form";

interface AddVehicleToGroupScreenProps {
  params?: { id?: string };
}

export default function AddVehicleToGroupScreen({ params }: AddVehicleToGroupScreenProps) {
  const groupId = parseInt(params?.id || "1");
  const { navigateToGroupDetails, goBack } = useNavigation();
  const { attributes } = useUserAttributes();

  // Get current user party ID from auth context
  const currentUserPartyId = parseInt(attributes?.["custom:db_id"] || "1");

  // Header component
  const header = (
    <div className="bg-white border-b border-[#f2f2f2] px-4 py-3">
      <div className="flex items-center">
        <button
          onClick={goBack}
          className="mr-3 p-2 -ml-2 text-[#333333]"
        >
          <ArrowLeft size={20} />
        </button>
        <h1 className="text-lg font-semibold text-[#333333]">
          Add Vehicle to Group
        </h1>
      </div>
    </div>
  );

  return (
    <PageWithScroll header={header} className="bg-[#f5f5f5]" paddingBottom="pb-32">
      <VehicleToGroupForm
        currentUserPartyId={currentUserPartyId}
        preselectedCompanyId={groupId}
        onSuccess={(data) => {
          // Navigate back to group details on success
          navigateToGroupDetails(groupId.toString());
        }}
        onCancel={() => {
          // Navigate back to group details on cancel
          goBack();
        }}
        hideHeader={true}
      />
    </PageWithScroll>
  );
} 