"use client";

import { useState } from "react";
import { useNavigation } from "@/hooks/useNavigation";
import { ArrowLeft, Calendar, Clock, MapPin, Car, Star, Users, Fuel } from "lucide-react";

interface BookingCalendarNewScreenProps {
  params?: { vehicleId?: string };
}

export default function BookingCalendarNewScreen({ params }: BookingCalendarNewScreenProps) {
  const { goBack, navigateToBookingConfirmation } = useNavigation();
  const vehicleId = params?.vehicleId || "1";
  
  const [selectedDuration, setSelectedDuration] = useState<'hourly' | 'daily' | 'weekly'>('hourly');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedTime, setSelectedTime] = useState<string>('09:00');
  const [duration, setDuration] = useState<number>(4);

  // Mock vehicle data
  const vehicle = {
    id: vehicleId,
    make: "Tesla",
    model: "Model 3",
    year: 2023,
    location: "Downtown Toronto",
    rating: 4.8,
    reviews: 124,
    pricePerHour: 25,
    pricePerDay: 180,
    pricePerWeek: 1200,
    features: ["Electric", "Autopilot", "Premium Interior"],
    image: "/images/cars/tesla-model-3.jpg",
    fuelType: "Electric",
    seats: 5
  };

  const timeSlots = [
    "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00",
    "13:00", "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00"
  ];

  const calculatePrice = () => {
    switch (selectedDuration) {
      case 'hourly': return vehicle.pricePerHour * duration;
      case 'daily': return vehicle.pricePerDay * duration;
      case 'weekly': return vehicle.pricePerWeek * duration;
      default: return 0;
    }
  };

  const handleContinue = () => {
    const bookingId = `${vehicleId}_${selectedDate.toISOString().split('T')[0]}_${selectedTime}_${selectedDuration}_${duration}`;
    navigateToBookingConfirmation(bookingId);
  };

  return (
    <div className="h-full bg-gray-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-gray-100">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Book Vehicle</h1>
          <div className="w-10 h-10" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {/* Vehicle Card */}
        <div className="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <div className="flex items-start space-x-4">
              <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-xl flex items-center justify-center">
                <Car size={32} className="text-blue-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">
                  {vehicle.year} {vehicle.make} {vehicle.model}
                </h2>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin size={14} className="mr-1" />
                    {vehicle.location}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Star size={14} className="mr-1 text-yellow-400 fill-current" />
                    {vehicle.rating} ({vehicle.reviews})
                  </div>
                </div>
                <div className="flex items-center space-x-6 mt-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Users size={14} className="mr-1" />
                    {vehicle.seats} seats
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Fuel size={14} className="mr-1" />
                    {vehicle.fuelType}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Features */}
            <div className="flex flex-wrap gap-2 mt-4">
              {vehicle.features.map((feature, index) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-green-50 text-green-700 text-xs font-medium rounded-full"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>

        {/* Booking Options */}
        <div className="bg-white mx-4 mt-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Rental Duration</h3>
            
            {/* Duration Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1 mb-6">
              {[
                { key: 'hourly', label: 'Hourly', price: vehicle.pricePerHour },
                { key: 'daily', label: 'Daily', price: vehicle.pricePerDay },
                { key: 'weekly', label: 'Weekly', price: vehicle.pricePerWeek }
              ].map(({ key, label, price }) => (
                <button
                  key={key}
                  onClick={() => setSelectedDuration(key as any)}
                  className={`flex-1 py-3 px-4 rounded-md font-medium transition-all ${
                    selectedDuration === key
                      ? "bg-white text-[#009639] shadow-sm"
                      : "text-gray-600"
                  }`}
                >
                  <div className="text-sm">{label}</div>
                  <div className="text-xs text-gray-500">${price}</div>
                </button>
              ))}
            </div>

            {/* Date Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Date
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={selectedDate.toISOString().split('T')[0]}
                  onChange={(e) => setSelectedDate(new Date(e.target.value))}
                  min={new Date().toISOString().split('T')[0]}
                  className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#009639] focus:border-transparent"
                />
                <Calendar size={20} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* Time Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Start Time
              </label>
              <div className="grid grid-cols-3 gap-2">
                {timeSlots.map(time => (
                  <button
                    key={time}
                    onClick={() => setSelectedTime(time)}
                    className={`p-3 rounded-lg text-sm font-medium transition-all ${
                      selectedTime === time
                        ? 'bg-[#009639] text-white'
                        : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {time}
                  </button>
                ))}
              </div>
            </div>

            {/* Duration Amount */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Number of {selectedDuration === 'hourly' ? 'Hours' : selectedDuration === 'daily' ? 'Days' : 'Weeks'}
              </label>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setDuration(Math.max(1, duration - 1))}
                  className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center font-medium text-gray-600 hover:bg-gray-200"
                >
                  -
                </button>
                <span className="flex-1 text-center text-lg font-semibold text-gray-900">
                  {duration}
                </span>
                <button
                  onClick={() => setDuration(duration + 1)}
                  className="w-10 h-10 rounded-lg bg-gray-100 flex items-center justify-center font-medium text-gray-600 hover:bg-gray-200"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Price Summary */}
        <div className="bg-white mx-4 mt-4 mb-4 rounded-xl shadow-sm border border-gray-100">
          <div className="p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Summary</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">
                  {selectedDuration.charAt(0).toUpperCase() + selectedDuration.slice(1)} Rate
                </span>
                <span className="font-medium text-gray-900">
                  ${selectedDuration === 'hourly' ? vehicle.pricePerHour : selectedDuration === 'daily' ? vehicle.pricePerDay : vehicle.pricePerWeek}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Duration</span>
                <span className="font-medium text-gray-900">
                  {duration} {selectedDuration === 'hourly' ? 'hour(s)' : selectedDuration === 'daily' ? 'day(s)' : 'week(s)'}
                </span>
              </div>
              <div className="border-t border-gray-200 pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Total</span>
                  <span className="text-2xl font-bold text-[#009639]">
                    ${calculatePrice().toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Action */}
      <div className="bg-white border-t border-gray-100 p-4">
        <button
          onClick={handleContinue}
          className="w-full py-4 bg-[#009639] text-white rounded-lg font-medium hover:bg-[#007A2F] transition-all"
        >
          Continue to Confirmation
        </button>
      </div>
    </div>
  );
} 