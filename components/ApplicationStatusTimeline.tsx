"use client";

import React from "react";
import {
  Clock,
  FileText,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
} from "lucide-react";

interface TimelineItem {
  id: number;
  status: string;
  reason?: string | null;
  timestamp: Date;
  reviewerName?: string | null;
}

interface ApplicationStatusTimelineProps {
  timeline: TimelineItem[];
  currentStatus?: string;
}

const getStatusConfig = (status: string) => {
  switch (status) {
    case "pending":
      return {
        icon: <Clock size={16} className="text-white" />,
        bgColor: "bg-yellow-500",
        borderColor: "border-yellow-200",
        cardBg: "bg-yellow-50",
        textColor: "text-yellow-900",
        descColor: "text-yellow-700",
        title: "Application Submitted",
        description: "Your application has been submitted and is awaiting review",
      };
    case "under_review":
      return {
        icon: <Eye size={16} className="text-white" />,
        bgColor: "bg-blue-500",
        borderColor: "border-blue-200",
        cardBg: "bg-blue-50",
        textColor: "text-blue-900",
        descColor: "text-blue-700",
        title: "Under Review",
        description: "Your application is currently being reviewed by our team",
      };
    case "approved":
      return {
        icon: <CheckCircle size={16} className="text-white" />,
        bgColor: "bg-[#009639]",
        borderColor: "border-green-200",
        cardBg: "bg-green-50",
        textColor: "text-green-900",
        descColor: "text-green-700",
        title: "Application Approved",
        description: "Congratulations! Your application has been approved",
      };
    case "rejected":
      return {
        icon: <XCircle size={16} className="text-white" />,
        bgColor: "bg-red-500",
        borderColor: "border-red-200",
        cardBg: "bg-red-50",
        textColor: "text-red-900",
        descColor: "text-red-700",
        title: "Application Rejected",
        description: "Your application was not approved at this time",
      };
    case "withdrawn":
      return {
        icon: <AlertCircle size={16} className="text-white" />,
        bgColor: "bg-gray-500",
        borderColor: "border-gray-200",
        cardBg: "bg-gray-50",
        textColor: "text-gray-900",
        descColor: "text-gray-700",
        title: "Application Withdrawn",
        description: "This application has been withdrawn",
      };
    default:
      return {
        icon: <FileText size={16} className="text-white" />,
        bgColor: "bg-gray-400",
        borderColor: "border-gray-200",
        cardBg: "bg-gray-50",
        textColor: "text-gray-900",
        descColor: "text-gray-700",
        title: "Status Update",
        description: "Application status has been updated",
      };
  }
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date));
};

export default function ApplicationStatusTimeline({
  timeline,
  currentStatus,
}: ApplicationStatusTimelineProps) {
  if (!timeline || timeline.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText size={48} className="mx-auto text-gray-300 mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">
          No Status Updates
        </h3>
        <p className="text-sm text-gray-500">
          Status updates will appear here as your application progresses
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center mb-6">
        <FileText size={20} className="text-[#009639] mr-2" />
        <h3 className="text-lg font-semibold text-[#333333]">Status Timeline</h3>
      </div>

      <div className="space-y-4">
        {timeline.map((item, index) => {
          const config = getStatusConfig(item.status);
          const isLatest = index === 0;
          const isCurrent = item.status === currentStatus;

          return (
            <div key={item.id} className="flex items-start">
              {/* Timeline Icon */}
              <div className="flex flex-col items-center mr-4">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm ${
                    isLatest || isCurrent ? config.bgColor : "bg-gray-300"
                  }`}
                >
                  {config.icon}
                </div>
                {index < timeline.length - 1 && (
                  <div
                    className={`w-0.5 h-8 mt-2 ${
                      isLatest || isCurrent ? config.bgColor : "bg-gray-200"
                    }`}
                  />
                )}
              </div>

              {/* Timeline Content */}
              <div className="flex-1 pb-4">
                <div
                  className={`rounded-xl p-4 border ${
                    isLatest || isCurrent
                      ? `${config.cardBg} ${config.borderColor}`
                      : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <h5
                    className={`font-medium mb-1 ${
                      isLatest || isCurrent ? config.textColor : "text-gray-700"
                    }`}
                  >
                    {config.title}
                  </h5>
                  <p
                    className={`text-sm mb-2 ${
                      isLatest || isCurrent ? config.descColor : "text-gray-600"
                    }`}
                  >
                    {config.description}
                  </p>

                  {/* Reason if provided */}
                  {item.reason && (
                    <div className="mb-2 p-2 bg-white rounded-lg border border-gray-100">
                      <p className="text-sm text-gray-700">
                        <strong>Note:</strong> {item.reason}
                      </p>
                    </div>
                  )}

                  {/* Timestamp and Reviewer */}
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center">
                      <Clock size={12} className="mr-1" />
                      {formatDate(item.timestamp)}
                    </div>
                    {item.reviewerName && (
                      <div className="flex items-center">
                        <User size={12} className="mr-1" />
                        {item.reviewerName}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
