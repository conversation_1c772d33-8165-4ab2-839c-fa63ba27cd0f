"use client";

import React, { useState, useEffect } from "react";
import {
  Car,
  DollarSign,
  Check,
  ChevronRight,
  MessageSquare,
  ChevronLeft,
  Settings,
  X,
} from "lucide-react";
import Image from "next/image";
import { useCurrentUser } from "@/hooks/use-current-user";
import {
  getListingByIdDrizzle,
  type ListingRead,
} from "@/drizzle-actions/listings";
import { getVehicleByIdWithListings } from "@/drizzle-actions/vehicle-dashboard";
import { getUrl } from "aws-amplify/storage";
import type { VehicleReadWithListings } from "@/types/vehicles";
import ApplicationProcessDrawer from "@/app/(main)/home/<USER>/ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "@/app/(main)/home/<USER>/ApplicationSubmittedDrawer";

interface ListingDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  listingId: number;
  onApply?: () => void;
}

export default function ListingDetailsDrawer({
  isOpen,
  onClose,
  listingId,
  onApply,
}: ListingDetailsDrawerProps) {
  const { partyId: currentUserPartyId } = useCurrentUser();

  const [listing, setListing] = useState<ListingRead | null>(null);
  const [vehicle, setVehicle] = useState<VehicleReadWithListings | null>(null);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("details");
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Application flow state
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  // Load listing data when drawer opens
  useEffect(() => {
    if (!isOpen || !listingId) return;

    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch listing details
        const listingData = await getListingByIdDrizzle(listingId);
        if (!listingData) {
          setError("Listing not found");
          return;
        }
        setListing(listingData);

        // Fetch vehicle details
        const vehicleData = await getVehicleByIdWithListings(
          listingData.vehicle_id
        );
        if (!vehicleData) {
          setError("Vehicle not found");
          return;
        }
        setVehicle(vehicleData);

        // Load images
        if (vehicleData.media && vehicleData.media.length > 0) {
          const urls = await Promise.all(
            vehicleData.media.map(async (media) => {
              try {
                const result = await getUrl({
                  path: media.media_path,
                });
                return result.url.toString();
              } catch (error) {
                console.error("Error loading image:", error);
                return "/images/car-1.jpg";
              }
            })
          );
          setImageUrls(urls);
        }
      } catch (err) {
        console.error("Error loading listing data:", err);
        setError("Failed to load listing details");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [isOpen, listingId]);

  // Reset state when drawer closes
  useEffect(() => {
    if (!isOpen) {
      setListing(null);
      setVehicle(null);
      setImageUrls([]);
      setActiveTab("details");
      setCurrentImageIndex(0);
      setError(null);
    }
  }, [isOpen]);

  // Convert to vehicle format for application flow
  const convertToVehicleFormat = () => {
    if (!listing || !vehicle) return null;

    const rate = listing.asking_price || 2700;

    return {
      id: listing.id,
      make: vehicle.model?.make?.name || "Unknown",
      model: vehicle.model?.model || "Vehicle",
      year: vehicle.manufacturing_year || 2020,
      weeklyRate: rate,
      image: imageUrls[0] || "/images/car-1.jpg",
      requirements: {
        minDeposit: rate,
        documents: [
          "Valid Driver's License",
          "Proof of Income",
          "Bank Statement",
        ],
      },
      leaseTerms: {
        ownershipTimeline:
          listing.listing_type === "CO_OWNERSHIP_SALE"
            ? "Immediate"
            : "Monthly lease",
        paymentOptions: ["Bank Transfer", "Card Payment"],
      },
    };
  };

  if (!isOpen) return null;

  const isOwner = currentUserPartyId && listing?.partyId === currentUserPartyId;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-40" onClick={onClose} />

      {/* Drawer */}
      <div className="fixed inset-x-0 bottom-0 top-16 bg-white z-50 overflow-hidden flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[#f2f2f2] flex-shrink-0">
          <button
            onClick={onClose}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-[#f2f2f2]"
            aria-label="Close details"
          >
            <X size={20} className="text-[#333333]" />
          </button>
          <h1 className="text-lg font-semibold text-[#333333]">
            Vehicle Details
          </h1>
          <div className="w-10 h-10"></div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#009639] mx-auto mb-4"></div>
                <p className="text-[#797879]">Loading listing details...</p>
              </div>
            </div>
          ) : error || !listing || !vehicle ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Car size={48} className="text-[#797879] mx-auto mb-4" />
                <h2 className="text-xl font-semibold text-[#333333] mb-2">
                  Listing Not Found
                </h2>
                <p className="text-[#797879] mb-4">
                  {error ||
                    "The listing you're looking for doesn't exist or has been removed."}
                </p>
              </div>
            </div>
          ) : (
            <>
              {/* Image Carousel */}
              <div className="relative h-64 bg-[#f2f2f2]">
                {imageUrls.length > 0 ? (
                  <>
                    <Image
                      src={imageUrls[currentImageIndex]}
                      alt={`${vehicle.model?.make?.name} ${vehicle.model?.model}`}
                      fill
                      className="object-cover"
                      onError={(e) => {
                        e.currentTarget.src = "/images/car-1.jpg";
                      }}
                    />
                    {imageUrls.length > 1 && (
                      <>
                        <button
                          onClick={() =>
                            setCurrentImageIndex((prev) =>
                              prev === 0 ? imageUrls.length - 1 : prev - 1
                            )
                          }
                          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md"
                          aria-label="Previous image"
                        >
                          <ChevronLeft size={20} className="text-[#333333]" />
                        </button>
                        <button
                          onClick={() =>
                            setCurrentImageIndex((prev) =>
                              prev === imageUrls.length - 1 ? 0 : prev + 1
                            )
                          }
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 p-2 rounded-full shadow-md"
                          aria-label="Next image"
                        >
                          <ChevronRight size={20} className="text-[#333333]" />
                        </button>
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                          {imageUrls.map((_, index) => (
                            <button
                              key={index}
                              onClick={() => setCurrentImageIndex(index)}
                              className={`w-3 h-3 rounded-full ${
                                index === currentImageIndex
                                  ? "bg-white"
                                  : "bg-white/50"
                              }`}
                              aria-label={`View image ${index + 1}`}
                            />
                          ))}
                        </div>
                      </>
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Car size={48} className="text-[#797879]" />
                  </div>
                )}
              </div>

              {/* Title and Price */}
              <div className="bg-white p-4 border-b border-[#f2f2f2]">
                <h2 className="text-xl font-bold text-[#333333]">
                  {vehicle.manufacturing_year} {vehicle.model?.make?.name}{" "}
                  {vehicle.model?.model}
                </h2>
                <div className="flex items-center justify-between mt-2">
                  <div className="flex items-center">
                    <DollarSign size={18} className="text-[#009639] mr-1" />
                    <span className="text-xl font-bold text-[#333333]">
                      R{listing.asking_price.toLocaleString()}
                      {listing.listing_type === "SHORT_TERM_LEASE_OUT" && (
                        <span className="text-sm font-medium">/month</span>
                      )}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-sm bg-[#e6ffe6] text-[#007A2F] px-2 py-1 rounded-full">
                      {listing.listing_type === "SHORT_TERM_LEASE_OUT"
                        ? "For Rent"
                        : listing.listing_type === "CO_OWNERSHIP_SALE"
                          ? "Co-Ownership"
                          : "For Lease"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Tabs */}
              <div className="bg-white border-b border-[#f2f2f2]">
                <div className="flex">
                  <button
                    className={`flex-1 py-3 text-sm font-medium ${
                      activeTab === "details"
                        ? "text-[#009639] border-b-2 border-[#009639]"
                        : "text-[#797879]"
                    }`}
                    onClick={() => setActiveTab("details")}
                  >
                    Details
                  </button>
                  <button
                    className={`flex-1 py-3 text-sm font-medium ${
                      activeTab === "features"
                        ? "text-[#009639] border-b-2 border-[#009639]"
                        : "text-[#797879]"
                    }`}
                    onClick={() => setActiveTab("features")}
                  >
                    Features
                  </button>
                </div>
              </div>

              {/* Tab Content */}
              <div className="pb-24">
                {/* Details Tab */}
                {activeTab === "details" && (
                  <div className="p-4 space-y-4">
                    {/* Vehicle Specifications */}
                    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                      <h3 className="text-[#333333] font-medium mb-3">
                        Vehicle Specifications
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-xs text-[#797879]">Make</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {vehicle.model?.make?.name || "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Model</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {vehicle.model?.model || "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Year</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {vehicle.manufacturing_year || "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Color</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {vehicle.color || "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Registration</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {vehicle.vehicle_registration || "N/A"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Condition</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {listing.condition || "Excellent"}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Listing Details */}
                    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                      <h3 className="text-[#333333] font-medium mb-3">
                        Listing Details
                      </h3>
                      <div className="space-y-3">
                        <div>
                          <p className="text-xs text-[#797879]">Listing Type</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {listing.listing_type.replace(/_/g, " ")}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Audience</p>
                          <p className="text-sm text-[#333333] font-medium">
                            {listing.audience === "BUSINESS"
                              ? "Business"
                              : listing.audience === "E_HAILING"
                                ? "E-Hailing"
                                : "Consumer"}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">Asking Price</p>
                          <p className="text-sm text-[#333333] font-medium">
                            R{listing.asking_price.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-[#797879]">
                            Ownership Fraction
                          </p>
                          <p className="text-sm text-[#333333] font-medium">
                            {(listing.fraction * 100).toFixed(1)}%
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Requirements */}
                    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                      <h3 className="text-[#333333] font-medium mb-3">
                        Requirements
                      </h3>
                      <div className="space-y-2">
                        {[
                          "Valid Driver's License",
                          "Proof of Income",
                          "Bank Statement",
                        ].map((req, index) => (
                          <div key={index} className="flex items-center">
                            <Check size={16} className="text-[#009639] mr-2" />
                            <span className="text-sm text-[#333333]">
                              {req}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {/* Features Tab */}
                {activeTab === "features" && (
                  <div className="p-4">
                    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
                      <h3 className="text-[#333333] font-medium mb-3">
                        Vehicle Features
                      </h3>
                      <div className="grid grid-cols-1 gap-3">
                        {[
                          "Air Conditioning",
                          "Power Steering",
                          "Electric Windows",
                          "Central Locking",
                          "ABS Brakes",
                          "Airbags",
                          "Radio/CD Player",
                          "Bluetooth Connectivity",
                        ].map((feature, index) => (
                          <div key={index} className="flex items-center">
                            <Check size={16} className="text-[#009639] mr-2" />
                            <span className="text-sm text-[#333333]">
                              {feature}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Bottom Actions */}
              <div className="absolute bottom-0 left-0 right-0 p-4 bg-white border-t border-[#f2f2f2] flex space-x-3">
                {isOwner ? (
                  <button
                    className="flex-1 bg-[#009639] text-white py-4 rounded-full font-medium flex items-center justify-center"
                    onClick={() => {
                      onClose();
                      // Navigate to edit listing - this would need router access
                    }}
                  >
                    <Settings size={20} className="mr-2" />
                    Manage Listing
                  </button>
                ) : (
                  <>
                    <button
                      className="flex-1 border border-[#009639] text-[#009639] py-4 rounded-full font-medium flex items-center justify-center"
                      onClick={() => {
                        console.log("Contact owner");
                      }}
                    >
                      <MessageSquare size={20} className="mr-2" />
                      Contact Owner
                    </button>
                    <button
                      className="flex-1 bg-[#009639] text-white py-4 rounded-full font-medium flex items-center justify-center"
                      onClick={() => {
                        const vehicleData = convertToVehicleFormat();
                        setSelectedVehicle(vehicleData);
                        setShowApplicationDrawer(true);
                      }}
                    >
                      <Car size={20} className="mr-2" />
                      Apply Now
                    </button>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Application Process Drawer */}
      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        listingId={listing?.id}
        selectedVehicle={selectedVehicle}
        listingType={
          listing?.listing_type === "SHORT_TERM_LEASE_OUT"
            ? "rental"
            : listing?.listing_type === "CO_OWNERSHIP_SALE"
              ? "fractional"
              : "ehailing-platform"
        }
      />

      {/* Application Submitted Confirmation Drawer */}
      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
        onViewStatus={() => {
          setShowSubmittedDrawer(false);
          onClose();
          // Navigate to profile applications - this would need router access
        }}
      />
    </>
  );
}
