'use client';

import { usePWAInstall } from '@/hooks/use-pwa-install';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Smartphone, Download, X, CheckCircle } from 'lucide-react';
import { useState } from 'react';

export function PWAInstallBanner() {
  const { isInstallable, isInstalled, promptInstall, canInstall } = usePWAInstall();
  const [isDismissed, setIsDismissed] = useState(false);

  if (isDismissed || isInstalled || !isInstallable) {
    return null;
  }

  const handleInstall = async () => {
    const success = await promptInstall();
    if (!success) {
      setIsDismissed(true);
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
  };

  return (
    <Alert className="border-blue-200 bg-blue-50 text-blue-900">
      <Smartphone className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex items-center gap-2">
          <span className="font-medium">Install Poolly App</span>
          <Badge variant="secondary" className="text-xs">
            PWA
          </Badge>
          <span className="text-sm">
            Get the full app experience with offline access and notifications
          </span>
        </div>
        <div className="flex items-center gap-2 ml-4">
          <Button
            size="sm"
            onClick={handleInstall}
            disabled={!canInstall}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Download className="h-3 w-3 mr-1" />
            Install
          </Button>
          <Button
            size="sm"
            variant="ghost"
            onClick={handleDismiss}
            className="text-blue-600 hover:text-blue-700"
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}

export function PWAInstallCard() {
  const { isInstallable, isInstalled, promptInstall, canInstall } = usePWAInstall();

  if (isInstalled) {
    return (
      <Alert className="border-green-200 bg-green-50 text-green-900">
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-2">
            <span className="font-medium">Poolly App Installed</span>
            <Badge variant="secondary" className="text-xs">
              PWA
            </Badge>
          </div>
          <p className="text-sm mt-1">
            You can now use Poolly as a native app with offline access and push notifications.
          </p>
        </AlertDescription>
      </Alert>
    );
  }

  if (!isInstallable) {
    return (
      <Alert>
        <Smartphone className="h-4 w-4" />
        <AlertDescription>
          <div className="flex items-center gap-2 mb-2">
            <span className="font-medium">Install Poolly App</span>
            <Badge variant="outline" className="text-xs">
              Not Available
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            The install option will appear automatically when you've used Poolly for a while. 
            On iOS, use Safari's "Add to Home Screen" from the share menu.
          </p>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Alert className="border-blue-200 bg-blue-50 text-blue-900">
      <Smartphone className="h-4 w-4" />
      <AlertDescription>
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium">Install Poolly App</span>
            <Badge variant="secondary" className="text-xs">
              Available
            </Badge>
          </div>
          <Button
            size="sm"
            onClick={promptInstall}
            disabled={!canInstall}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Download className="h-3 w-3 mr-1" />
            Install Now
          </Button>
        </div>
        <p className="text-sm">
          Install Poolly for faster access, offline use, and native push notifications. 
          Works just like a regular app on your device.
        </p>
      </AlertDescription>
    </Alert>
  );
} 