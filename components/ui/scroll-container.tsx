"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface ScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  paddingBottom?: "pb-16" | "pb-20" | "pb-24" | "pb-32" | "pb-nav" | "pb-nav-safe";
}

export function ScrollContainer({ 
  children, 
  className = "",
  paddingBottom = "pb-nav-safe"
}: ScrollContainerProps) {
  // Map padding options to appropriate classes
  const paddingClass = paddingBottom === "pb-nav" 
    ? "pb-[68px]" // Just the navigation height
    : paddingBottom === "pb-nav-safe"
    ? "pb-[calc(68px+env(safe-area-inset-bottom,0px))]" // Navigation + safe area
    : paddingBottom; // Use original value for backward compatibility

  return (
    <div className={cn("flex-1 overflow-y-auto scrollbar-hide", className)}>
      <div className={paddingClass}>
        {children}
      </div>
    </div>
  );
}

interface PageWithScrollProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  tabs?: React.ReactNode;
  className?: string;
  paddingBottom?: "pb-16" | "pb-20" | "pb-24" | "pb-32" | "pb-nav" | "pb-nav-safe";
}

export function PageWithScroll({ 
  children, 
  header, 
  tabs, 
  className = "",
  paddingBottom = "pb-nav-safe"
}: PageWithScrollProps) {
  return (
    <div className={cn("h-screen flex flex-col", className)}>
      {/* Fixed Header */}
      {header && (
        <div className="flex-shrink-0">
          {header}
        </div>
      )}
      
      {/* Fixed Tabs */}
      {tabs && (
        <div className="flex-shrink-0">
          {tabs}
        </div>
      )}
      
      {/* Scrollable Content */}
      <ScrollContainer paddingBottom={paddingBottom}>
        {children}
      </ScrollContainer>
    </div>
  );
} 