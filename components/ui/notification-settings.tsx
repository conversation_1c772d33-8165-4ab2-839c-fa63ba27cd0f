'use client';

import { usePushNotifications } from '@/hooks/use-push-notifications';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Bell, BellOff, Smartphone, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { PWAInstallCard } from './pwa-install-banner';

export function NotificationSettings() {
  const {
    isSupported,
    isSubscribed,
    isLoading,
    error,
    subscribe,
    unsubscribe,
    testNotification,
    canSubscribe,
    permission
  } = usePushNotifications();

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Push notifications are not supported in this browser
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            Please use a modern browser that supports push notifications such as Chrome, Firefox, or Safari.
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleToggle = async (checked: boolean) => {
    if (checked) {
      await subscribe();
    } else {
      await unsubscribe();
    }
  };

  const handleTest = async () => {
    await testNotification();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="h-5 w-5" />
          Push Notifications
          {isSubscribed && (
            <Badge variant="secondary" className="ml-auto">
              <CheckCircle className="h-3 w-3 mr-1" />
              Active
            </Badge>
          )}
          {permission === 'denied' && (
            <Badge variant="destructive" className="ml-auto">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Blocked
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Get notified about bookings, group updates, maintenance, and important tasks
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {permission === 'denied' && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Notifications are blocked. Please enable them in your browser settings and refresh the page.
            </AlertDescription>
          </Alert>
        )}
        
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="font-medium">
              Push Notifications
            </p>
            <p className="text-sm text-muted-foreground">
              {isSubscribed
                ? 'Receive notifications for bookings, groups, and updates on this device'
                : 'Enable to receive important notifications on this device'
              }
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
            <Switch
              checked={isSubscribed}
              onCheckedChange={handleToggle}
              disabled={isLoading || !canSubscribe || permission === 'denied'}
            />
          </div>
        </div>

        {isSubscribed && (
          <>
            <div className="space-y-3 pt-4 border-t">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">Notification Types</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleTest}
                  disabled={isLoading}
                >
                  Test Notification
                </Button>
              </div>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full" />
                  <span>Booking updates</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span>Group activities</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full" />
                  <span>Maintenance alerts</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full" />
                  <span>Financial updates</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  <span>Compliance tasks</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-gray-500 rounded-full" />
                  <span>General updates</span>
                </div>
              </div>
            </div>

            <div className="bg-muted/50 p-3 rounded-lg">
              <p className="text-xs text-muted-foreground">
                💡 <strong>Tip:</strong> Install Poolly as an app for the best experience with notifications and offline access.
              </p>
            </div>
          </>
        )}

        {!canSubscribe && !isLoading && permission !== 'denied' && (
          <div className="text-sm text-muted-foreground">
            Please sign in to enable notifications
          </div>
        )}
        
        {/* PWA Install Section */}
        <div className="pt-4 border-t">
          <PWAInstallCard />
        </div>
      </CardContent>
    </Card>
  );
} 