"use client";

import React, { useState } from "react";
import { X } from "lucide-react";
import ListVehicleForm from "./list-vehicle-form";

interface ListVehicleDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (data: any) => void;
}

export default function ListVehicleDrawer({
  isOpen,
  onClose,
  onSuccess
}: ListVehicleDrawerProps) {
  const handleSuccess = (data: any) => {
    if (onSuccess) {
      onSuccess(data);
    }
    // Keep drawer open to show success state, user can close it
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-300"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div 
        className={`fixed top-0 right-0 h-full w-full max-w-2xl bg-white shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        {/* Header */}
        <div className="bg-[#009639] px-6 py-4 flex items-center justify-between border-b border-[#007A2F]">
          <h1 className="text-xl font-bold text-white">List Vehicle</h1>
          <button 
            onClick={onClose}
            className="text-white hover:bg-[#007A2F] p-2 rounded-lg transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Content */}
        <div className="h-full overflow-y-auto pb-20">
          <div className="p-6">
            <ListVehicleForm
              onClose={onClose}
              onSuccess={handleSuccess}
            />
          </div>
        </div>
      </div>
    </>
  );
} 