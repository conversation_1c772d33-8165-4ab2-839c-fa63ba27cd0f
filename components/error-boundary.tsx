"use client";

import { Component, ReactNode, ErrorInfo } from 'react';
import * as Sentry from '@sentry/nextjs';
import { AlertCircle, RotateCcw, Home } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isRetrying: boolean;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  showNavigation?: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isRetrying: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Log to Sentry
    Sentry.withScope((scope) => {
      scope.setTag('errorBoundary', true);
      scope.setExtra('errorInfo', errorInfo);
      scope.setLevel('error');
      Sentry.captureException(error);
    });

    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = async () => {
    if (this.retryCount >= this.maxRetries) {
      this.handleGoHome();
      return;
    }

    this.setState({ isRetrying: true });
    this.retryCount++;

    try {
      // Clear caches that might be causing issues
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames
            .filter(name => name.includes('next') || name.includes('static'))
            .map(cacheName => caches.delete(cacheName))
        );
      }

      // Clear any module cache issues by forcing a reload after a delay
      setTimeout(() => {
        this.setState({
          hasError: false,
          error: null,
          errorInfo: null,
          isRetrying: false,
        });
      }, 1000);
    } catch (err) {
      console.error('Error during retry:', err);
      this.setState({ isRetrying: false });
      // Fallback to page reload if cache clearing fails
      window.location.reload();
    }
  };

  handleGoHome = () => {
    window.location.href = '/home';
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error } = this.state;
      const isChunkError = error?.message?.includes('Loading chunk') || 
                          error?.message?.includes('Failed to fetch') ||
                          error?.name === 'ChunkLoadError';

      const isNetworkError = error?.message?.includes('Failed to fetch') ||
                            error?.message?.includes('NetworkError') ||
                            error?.name === 'NetworkError';

      const getErrorMessage = () => {
        if (isChunkError) {
          return "We're experiencing some trouble loading the page. Please refresh to continue.";
        }
        if (isNetworkError) {
          return "We're having trouble connecting to our servers. Please check your internet connection and try again.";
        }
        return "We're experiencing some technical difficulties and our team is actively working to resolve this. Please try again in a moment.";
      };

      const getErrorTitle = () => {
        if (isChunkError) return "It's not you, it's us";
        if (isNetworkError) return "Connection Issue";
        return "Service Temporarily Unavailable";
      };

      return (
        <div className="min-h-screen bg-[#f5f5f5] flex flex-col">
          {/* Header - only show if navigation is enabled */}
          {this.props.showNavigation !== false && (
            <div className="bg-[#009639] px-4 py-4 flex items-center border-b border-gray-100">
              <div className="w-10 h-10"></div>
              <h1 className="text-lg font-semibold text-white flex-1 text-center bg-[#009639]">Service Update</h1>
              <div className="w-10 h-10"></div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="bg-white rounded-2xl p-8 shadow-sm text-center max-w-md w-full">
              {/* Poolly Logo */}
              
              
              {/* Error Icon */}
              <div className="w-20 h-20 bg-[#f0f9ff] rounded-full flex items-center justify-center mx-auto mb-6">
                <RotateCcw size={40} className="text-[#009639]" />
              </div>
              
              {/* Error Title */}
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {getErrorTitle()}
              </h2>
              
              {/* Error Message */}
              <p className="text-gray-600 mb-8 leading-relaxed">
                {getErrorMessage()}
              </p>

              {/* Retry limit warning */}
              {this.retryCount >= this.maxRetries && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                  <p className="text-blue-800 text-sm">
                    Taking you back to the home screen...
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={this.handleRetry}
                  disabled={this.state.isRetrying || this.retryCount >= this.maxRetries}
                  className="w-full flex items-center justify-center gap-2 bg-[#009639] text-white py-3 px-6 rounded-lg font-medium hover:bg-[#007A2F] transition-colors disabled:opacity-60 disabled:cursor-not-allowed"
                >
                  {this.state.isRetrying ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Updating...
                    </>
                  ) : this.retryCount >= this.maxRetries ? (
                    <>
                      <Home size={20} />
                      Return to Home
                    </>
                  ) : (
                    <>
                      <RotateCcw size={20} />
                      {isChunkError ? 'Refresh Page' : `Try Again (${this.maxRetries - this.retryCount} left)`}
                    </>
                  )}
                </button>
                
                {this.retryCount < this.maxRetries && (
                  <button
                    onClick={this.handleGoHome}
                    disabled={this.state.isRetrying}
                    className="w-full flex items-center justify-center gap-2 bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-60"
                  >
                    <Home size={20} />
                    Return to Home
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Bottom Navigation - only show if navigation is enabled */}
          {this.props.showNavigation !== false && (
            <div className="h-16"></div> // Placeholder for bottom navigation space
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary; 