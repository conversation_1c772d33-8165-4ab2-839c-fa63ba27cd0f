"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import {
  ChevronLeft,
  ChevronRight,
  DollarSign,
  MapPin,
  Check,
} from "lucide-react";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerFooter,
  DrawerClose,
} from "@/components/ui/drawer";
import { Button } from "@/components/ui/button";

interface BusinessOpportunityDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  listing: any; // Business listing data
  onApply: () => void;
}

export default function BusinessOpportunityDetailsDrawer({
  isOpen,
  onClose,
  listing,
  onApply,
}: BusinessOpportunityDetailsDrawerProps) {
  const [activeTab, setActiveTab] = useState("details");
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  // Reset image index when drawer opens or listing changes
  useEffect(() => {
    if (isOpen) {
      setCurrentImageIndex(0);
      setImageLoading(true);
      setImageError(false);
      setRetryCount(0);
    }
  }, [isOpen, listing?.id]);

  // Reset loading state when image index changes
  useEffect(() => {
    setImageLoading(true);
    setImageError(false);
  }, [currentImageIndex]);

  if (!isOpen || !listing) return null;

  // Parse listing details to get comprehensive information
  const getOpportunityDetails = (listing: any) => {
    try {
      const details = JSON.parse(listing.listingDetails);

      if (listing.listingType === "rental") {
        // For rental listings, use vehicle media array
        const imageArray =
          listing.media && Array.isArray(listing.media)
            ? listing.media.map((m: any) => m.media_path).filter(Boolean)
            : [];

        return {
          title: `${listing.makeName || "Unknown"} ${listing.modelName || "Vehicle"}`,
          subtitle: `${listing.vehicleYear || "Unknown"} • ${listing.vehicleColor || "Unknown"}`,
          price: `R${details.rate?.toLocaleString() || "TBA"}/${details.type || "month"}`,
          description: `Available for ${details.type || "rental"}`,
          images: imageArray.length > 0 ? imageArray : ["/images/car-1.jpg"],
          company: {
            name: "Vehicle Owner",
            rating: 4.5,
            verified: true,
            responseTime: "Within 2 hours",
            memberSince: "2020",
          },
          details: {
            duration: details.duration || "Flexible",
            startDate: listing.effectiveFrom || new Date().toISOString(),
            requirements: [
              "Valid driver's license",
              "Clean driving record",
              "Proof of income",
              "Bank statements",
            ],
            benefits: [
              "Flexible rental period",
              "Well-maintained vehicle",
              "Responsive owner",
              "Competitive rates",
            ],
            location: "South Africa",
            type: "Vehicle Rental",
            weeklyRate: details.rate || 2000,
            deposit: details.rate || 2000,
          },
          features: [
            "Flexible terms",
            "Competitive pricing",
            "Well-maintained",
            "Responsive support",
            "Insurance included",
            "Roadside assistance",
          ],
        };
      } else if (listing.listingType === "fractional") {
        const percentage = details.fraction
          ? (details.fraction * 100).toFixed(1)
          : "25";

        // For fractional listings, use vehicle media array
        const imageArray =
          listing.media && Array.isArray(listing.media)
            ? listing.media.map((m: any) => m.media_path).filter(Boolean)
            : [];

        return {
          title: `${listing.makeName || "Unknown"} ${listing.modelName || "Vehicle"}`,
          subtitle: `${listing.vehicleYear || "Unknown"} • ${percentage}% Ownership`,
          price: `R${details.amount?.toLocaleString() || "TBA"}`,
          description: `Fractional ownership opportunity`,
          images: imageArray.length > 0 ? imageArray : ["/images/car-1.jpg"],
          company: {
            name: "Vehicle Owner",
            rating: 4.7,
            verified: true,
            responseTime: "Within 4 hours",
            memberSince: "2019",
          },
          details: {
            duration: "Long-term ownership",
            startDate: listing.effectiveFrom || new Date().toISOString(),
            requirements: [
              "Valid driver's license",
              "Proof of income",
              "Credit assessment",
              "Legal agreement",
            ],
            benefits: [
              "Shared ownership costs",
              "Flexible usage",
              "Asset appreciation",
              "Lower entry barrier",
            ],
            location: "South Africa",
            type: "Fractional Ownership",
            weeklyRate: 0,
            deposit: Math.round((details.amount || 50000) * 0.1),
          },
          features: [
            "Shared ownership",
            "Asset appreciation",
            "Flexible usage",
            "Lower costs",
            "Legal protection",
            "Exit options",
          ],
        };
      } else {
        // E-hailing (catalog) - now uses unified media array format
        const ehailingImages =
          listing.media && Array.isArray(listing.media)
            ? listing.media.map((m: any) => m.media_path).filter(Boolean)
            : [];

        return {
          title: `${listing.catalogMake || "Unknown"} ${listing.catalogModel || "Vehicle"}`,
          subtitle: listing.catalogDescription || "E-hailing Opportunity",
          price: `R${details.rate?.toLocaleString() || "2700"}/week`,
          description: `E-hailing platform opportunity`,
          images:
            ehailingImages.length > 0 ? ehailingImages : ["/images/car-1.jpg"],
          company: {
            name: "E-hailing Platform",
            rating: 4.8,
            verified: true,
            responseTime: "Within 1 hour",
            memberSince: "2018",
          },
          details: {
            duration: "Flexible",
            startDate: listing.effectiveFrom || new Date().toISOString(),
            requirements: [
              "Valid driver's license",
              "Professional driving permit (PrDP)",
              "Police clearance certificate",
              "Vehicle in good condition",
            ],
            benefits: [
              "Guaranteed weekly income",
              "Flexible working hours",
              "Fuel support",
              "Platform support",
            ],
            location: "South Africa",
            type: "E-hailing Service",
            weeklyRate: details.rate || 2700,
            deposit: details.rate || 2700,
          },
          features: [
            "Guaranteed income",
            "Flexible hours",
            "Platform support",
            "Fuel assistance",
            "Insurance coverage",
            "Performance bonuses",
          ],
        };
      }
    } catch (error) {
      console.error("Error parsing listing details:", error);
      return {
        title: "Vehicle Opportunity",
        subtitle: "Details available",
        price: "Contact for pricing",
        description: "Business opportunity available",
        images: ["/images/car-1.jpg"],
        company: {
          name: "Business Partner",
          rating: 4.5,
          verified: true,
          responseTime: "Within 2 hours",
          memberSince: "2020",
        },
        details: {
          duration: "Flexible",
          startDate: new Date().toISOString(),
          requirements: ["Valid requirements"],
          benefits: ["Great opportunity"],
          location: "South Africa",
          type: "Business Opportunity",
          weeklyRate: 2000,
          deposit: 2000,
        },
        features: ["Great opportunity"],
      };
    }
  };

  const opportunity = getOpportunityDetails(listing);

  // More robust image URL validation
  const validateImageUrl = (url: string): boolean => {
    if (!url || typeof url !== "string") return false;

    // Remove any whitespace
    url = url.trim();

    // Check for valid paths and URLs
    const isValidPath = url.startsWith("/") && url.length > 1;
    const isValidHttp = url.startsWith("http://") || url.startsWith("https://");
    const hasValidExtension =
      /\.(jpg|jpeg|png|gif|webp|svg|avif|heic|heif)$/i.test(url);
    const isS3Url = url.includes("amazonaws.com") || url.includes("s3.");

    // S3 URLs should be valid regardless of file extension (they might have query parameters)
    if (isS3Url && (isValidHttp || isValidPath)) {
      return true;
    }

    // For other URLs, check both path/HTTP and extension requirements
    return (
      (isValidPath || isValidHttp) &&
      (hasValidExtension || url.includes("/images/"))
    );
  };

  // Filter and validate images - ensure we handle arrays properly
  const validImages = Array.isArray(opportunity.images)
    ? opportunity.images
        .filter(validateImageUrl)
        .map((img: string) => img.trim())
    : [];

  // Always ensure we have a safe fallback
  const safeImages =
    validImages.length > 0 ? validImages : ["/images/car-1.jpg"];

  // Debug logging for image issues
  console.log("Image debugging:", {
    listingType: listing.listingType,
    originalImages: opportunity.images,
    validImages,
    safeImages,
    currentIndex: currentImageIndex,
  });

  // Log validation details for debugging
  if (opportunity.images?.length > 0) {
    opportunity.images.forEach((img: string, index: number) => {
      const isValid = validateImageUrl(img);
      console.log(`Image ${index + 1} validation:`, {
        url: img,
        valid: isValid,
        isS3: img.includes("amazonaws.com") || img.includes("s3."),
        hasExtension: /\.(jpg|jpeg|png|gif|webp|svg|avif|heic|heif)$/i.test(
          img
        ),
        isHttp: img.startsWith("http://") || img.startsWith("https://"),
      });
    });
  }

  // Add extra safety check for current image
  const getCurrentImage = () => {
    // Ensure currentImageIndex is within bounds
    const safeIndex = Math.max(
      0,
      Math.min(currentImageIndex, safeImages.length - 1)
    );
    const currentImg = safeImages[safeIndex] || "/images/car-1.jpg";

    return validateImageUrl(currentImg) ? currentImg : "/images/car-1.jpg";
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
    setRetryCount(0);
  };

  const handleImageError = () => {
    console.warn("Image failed to load:", getCurrentImage());
    setImageLoading(false);
    setImageError(true);
  };

  const retryImage = () => {
    if (retryCount < 2) {
      // Max 2 retries
      setRetryCount((prev) => prev + 1);
      setImageLoading(true);
      setImageError(false);
    }
  };

  const handleNextImage = () => {
    setCurrentImageIndex((prev) => {
      const nextIndex = prev === safeImages.length - 1 ? 0 : prev + 1;
      return Math.max(0, Math.min(nextIndex, safeImages.length - 1));
    });
  };

  const handlePrevImage = () => {
    setCurrentImageIndex((prev) => {
      const prevIndex = prev === 0 ? safeImages.length - 1 : prev - 1;
      return Math.max(0, Math.min(prevIndex, safeImages.length - 1));
    });
  };

  return (
    <Drawer open={isOpen} onOpenChange={onClose}>
      <DrawerContent className="max-h-[95vh] flex flex-col">
        <DrawerHeader className="p-0 flex-shrink-0">
          {/* Image Gallery */}
          <div className="relative bg-[#f2f2f2] h-64">
            {/* Loading State */}
            {imageLoading && (
              <div className="absolute inset-0 bg-[#f2f2f2] flex items-center justify-center">
                <div className="flex flex-col items-center space-y-2">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639]"></div>
                  <span className="text-sm text-[#797879]">
                    Loading image...
                  </span>
                </div>
              </div>
            )}

            {/* Error State */}
            {imageError && !imageLoading && (
              <div className="absolute inset-0 bg-[#f2f2f2] flex items-center justify-center">
                <div className="flex flex-col items-center space-y-3 p-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-2xl">📷</span>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-[#797879] mb-2">
                      Image could not be loaded
                    </p>
                    {retryCount < 2 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={retryImage}
                        className="text-xs"
                      >
                        Retry ({retryCount + 1}/3)
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Actual Image */}
            <Image
              key={`${getCurrentImage()}-${retryCount}`} // Force re-render on retry
              src={getCurrentImage()}
              alt={opportunity.title}
              fill
              className={`object-cover transition-opacity duration-300 ${
                imageLoading ? "opacity-0" : "opacity-100"
              }`}
              onLoad={handleImageLoad}
              onError={handleImageError}
              priority={currentImageIndex === 0}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
            />

            {/* Image Navigation */}
            {safeImages.length > 1 && !imageError && (
              <>
                <div className="absolute bottom-4 left-0 right-0 flex justify-center">
                  <div className="flex space-x-1 px-2 py-1 bg-black bg-opacity-50 rounded-full">
                    {safeImages.map((_: string, index: number) => (
                      <div
                        key={index}
                        className={`w-2 h-2 rounded-full cursor-pointer transition-colors ${
                          index === currentImageIndex
                            ? "bg-white"
                            : "bg-white bg-opacity-50"
                        }`}
                        onClick={() => setCurrentImageIndex(index)}
                      ></div>
                    ))}
                  </div>
                </div>
                <button
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center hover:bg-opacity-90 transition-all"
                  onClick={handlePrevImage}
                  title="Previous image"
                  disabled={imageLoading}
                >
                  <ChevronLeft size={20} className="text-[#333333]" />
                </button>
                <button
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white bg-opacity-70 rounded-full flex items-center justify-center hover:bg-opacity-90 transition-all"
                  onClick={handleNextImage}
                  title="Next image"
                  disabled={imageLoading}
                >
                  <ChevronRight size={20} className="text-[#333333]" />
                </button>
              </>
            )}

            {/* Image Counter */}
            {safeImages.length > 1 && !imageError && (
              <div className="absolute top-4 left-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded-full text-xs">
                {currentImageIndex + 1} / {safeImages.length}
              </div>
            )}
          </div>

          {/* Opportunity Title and Earnings */}
          <div className="bg-white p-4 border-b border-[#f2f2f2]">
            <DrawerTitle className="text-xl font-bold text-[#333333] mb-2">
              {opportunity.title}
            </DrawerTitle>
            <DrawerDescription className="flex items-center justify-between">
              <div className="flex items-center">
                <DollarSign size={18} className="text-[#009639] mr-1" />
                <span className="text-xl font-bold text-[#333333]">
                  {opportunity.price}
                </span>
              </div>
              <div className="flex items-center">
                <MapPin size={14} className="text-[#797879] mr-1" />
                <span className="text-sm text-[#797879]">
                  {opportunity.details.location}
                </span>
              </div>
            </DrawerDescription>
          </div>
        </DrawerHeader>

        {/* Tabs - Only first two tabs */}
        <div className="bg-white border-b border-[#f2f2f2] flex-shrink-0">
          <div className="flex">
            <button
              className={`flex-1 py-3 text-sm font-medium ${
                activeTab === "details"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("details")}
            >
              Details
            </button>
            <button
              className={`flex-1 py-3 text-sm font-medium ${
                activeTab === "features"
                  ? "text-[#009639] border-b-2 border-[#009639]"
                  : "text-[#797879]"
              }`}
              onClick={() => setActiveTab("features")}
            >
              Benefits
            </button>
          </div>
        </div>

        {/* Content - No scrolling, fit to available space */}
        <div className="flex-1 min-h-0 p-4">
          {/* Details Tab */}
          {activeTab === "details" && (
            <div className="h-full flex flex-col space-y-4">
              <div className="ride-card p-4">
                <h3 className="text-[#333333] font-medium mb-3">
                  Opportunity Details
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-xs text-[#797879]">Company</p>
                    <p className="text-[#333333] font-medium">
                      {opportunity.company.name}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-[#797879]">Duration</p>
                    <p className="text-[#333333] font-medium">
                      {opportunity.details.duration}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-[#797879]">Type</p>
                    <p className="text-[#333333] font-medium">
                      {opportunity.details.type}
                    </p>
                  </div>
                  <div>
                    <p className="text-xs text-[#797879]">Rate</p>
                    <p className="text-[#333333] font-medium">
                      {opportunity.price}
                    </p>
                  </div>
                </div>
              </div>

              <div className="ride-card p-4 flex-1 min-h-0">
                <h3 className="text-[#333333] font-medium mb-3">
                  Requirements
                </h3>
                <div className="space-y-2 text-sm">
                  {opportunity.details.requirements
                    .slice(0, 4)
                    .map((requirement, index) => (
                      <div key={index} className="flex items-center">
                        <Check
                          size={14}
                          className="text-[#009639] mr-2 flex-shrink-0"
                        />
                        <span className="text-[#333333]">{requirement}</span>
                      </div>
                    ))}
                  {opportunity.details.requirements.length > 4 && (
                    <p className="text-xs text-[#797879] mt-2">
                      +{opportunity.details.requirements.length - 4} more
                      requirements
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Benefits Tab */}
          {activeTab === "features" && (
            <div className="h-full flex flex-col space-y-4">
              <div className="ride-card p-4">
                <h3 className="text-[#333333] font-medium mb-3">
                  Key Benefits
                </h3>
                <div className="grid grid-cols-1 gap-2 text-sm">
                  {opportunity.features.slice(0, 4).map((feature, index) => (
                    <div key={index} className="flex items-center">
                      <Check
                        size={14}
                        className="text-[#009639] mr-2 flex-shrink-0"
                      />
                      <span className="text-[#333333]">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="ride-card p-4 flex-1 min-h-0">
                <h3 className="text-[#333333] font-medium mb-3">
                  Additional Benefits
                </h3>
                <div className="space-y-2 text-sm">
                  {opportunity.details.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-center">
                      <Check
                        size={14}
                        className="text-[#009639] mr-2 flex-shrink-0"
                      />
                      <span className="text-[#333333]">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        <DrawerFooter className="flex-shrink-0">
          <Button
            onClick={onApply}
            className="w-full py-4 bg-gradient-to-r from-[#009639] to-[#007A2F] text-white rounded-full text-lg font-semibold shadow-md hover:from-[#007A2F] hover:to-[#005A1F]"
          >
            Apply Now
          </Button>
          <DrawerClose asChild>
            <Button variant="outline" className="w-full">
              Close
            </Button>
          </DrawerClose>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  );
}
