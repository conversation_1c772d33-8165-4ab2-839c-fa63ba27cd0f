'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { RefreshCw, Cloud, TrendingUp, MessageSquare } from 'lucide-react';
import { generatePresetWordCloud, type WordCloudAnalysis, type WordCloudWord } from '@/actions/word-cloud-processor';

interface WordCloudDisplayProps {
  className?: string;
}

// Simple word cloud using CSS transforms and scaling
function SimpleWordCloud({ words, title }: { words: WordCloudWord[]; title: string }) {
  const getWordStyle = (word: WordCloudWord, index: number) => {
    const baseSize = 14;
    const maxSize = 32;
    const size = baseSize + (word.weight * (maxSize - baseSize));
    
    // Color based on category
    const categoryColors = {
      business: '#3b82f6', // blue
      location: '#10b981', // green  
      action: '#f59e0b', // amber
      time: '#8b5cf6', // purple
      quantity: '#ef4444', // red
      vehicle: '#06b6d4', // cyan
      general: '#6b7280' // gray
    };
    
    const color = categoryColors[word.category as keyof typeof categoryColors] || categoryColors.general;
    
    return {
      fontSize: `${size}px`,
      color: color,
      fontWeight: word.weight > 0.7 ? 'bold' : word.weight > 0.4 ? '600' : 'normal',
      opacity: 0.7 + (word.weight * 0.3),
      margin: '2px 6px',
      display: 'inline-block',
      lineHeight: 1.2,
      textShadow: word.weight > 0.6 ? '1px 1px 2px rgba(0,0,0,0.1)' : 'none'
    };
  };

  if (words.length === 0) {
    return (
      <div className="flex items-center justify-center py-8 text-gray-500">
        <Cloud className="h-8 w-8 mr-2" />
        <span>No words found for analysis</span>
      </div>
    );
  }

  return (
    <div className="word-cloud-container p-4">
      <h4 className="text-sm font-medium mb-3 text-gray-700">{title}</h4>
      <div 
        className="word-cloud-content text-center leading-relaxed"
        style={{ minHeight: '200px' }}
      >
        {words.map((word, index) => (
          <span
            key={`${word.word}-${index}`}
            style={getWordStyle(word, index)}
            title={`${word.word}: ${word.frequency} occurrences (${(word.weight * 100).toFixed(1)}%)`}
            className="cursor-default hover:opacity-100 transition-opacity duration-200"
          >
            {word.word}
          </span>
        ))}
      </div>
    </div>
  );
}

// Phrases display component
function PhrasesDisplay({ analysis }: { analysis: WordCloudAnalysis }) {
  if (analysis.phrases.length === 0) {
    return (
      <div className="flex items-center justify-center py-8 text-gray-500">
        <MessageSquare className="h-8 w-8 mr-2" />
        <span>No common phrases found</span>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium text-gray-700">Common Phrases</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {analysis.phrases.map((phrase, index) => (
          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div>
              <span className="font-medium text-gray-900">"{phrase.phrase}"</span>
              <div className="flex items-center mt-1">
                <Badge variant="outline" className="text-xs mr-2">
                  {phrase.wordCount} words
                </Badge>
                <span className="text-xs text-gray-500">
                  {phrase.frequency} times
                </span>
              </div>
            </div>
            <div 
              className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden"
              title={`Weight: ${(phrase.weight * 100).toFixed(1)}%`}
            >
              <div 
                className="h-full bg-[#009639] transition-all duration-300"
                style={{ width: `${phrase.weight * 100}%` }}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Main word cloud display component
export default function WordCloudDisplay({ className }: WordCloudDisplayProps) {
  const [analyses, setAnalyses] = useState<Record<string, WordCloudAnalysis>>({});
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [activePreset, setActivePreset] = useState('all_leads');

  const presets = [
    { key: 'all_leads', label: 'All Leads', description: 'Complete overview' },
    { key: 'business_focus', label: 'Business Solutions', description: 'Business-focused leads' },
    { key: 'investment_interest', label: 'Investment Interest', description: 'Co-ownership inquiries' },
    { key: 'service_requests', label: 'Service Requests', description: 'Management & E-Hailing' }
  ];

  const generateAnalysis = async (presetKey: string) => {
    setLoading(prev => ({ ...prev, [presetKey]: true }));
    try {
      const analysis = await generatePresetWordCloud(presetKey);
      setAnalyses(prev => ({ ...prev, [presetKey]: analysis }));
    } catch (error) {
      console.error(`Error generating word cloud for ${presetKey}:`, error);
    } finally {
      setLoading(prev => ({ ...prev, [presetKey]: false }));
    }
  };

  useEffect(() => {
    // Generate analysis for the active preset
    if (!analyses[activePreset] && !loading[activePreset]) {
      generateAnalysis(activePreset);
    }
  }, [activePreset]);

  const currentAnalysis = analyses[activePreset];
  const isLoading = loading[activePreset];

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Cloud className="h-5 w-5 mr-2" />
              Word Cloud Analysis
            </CardTitle>
            <CardDescription>
              Text insights from your lead data
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => generateAnalysis(activePreset)}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activePreset} onValueChange={setActivePreset}>
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            {presets.map(preset => (
              <TabsTrigger key={preset.key} value={preset.key} className="text-xs">
                {preset.label}
              </TabsTrigger>
            ))}
          </TabsList>

          {presets.map(preset => (
            <TabsContent key={preset.key} value={preset.key} className="mt-6">
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#009639] mx-auto mb-4"></div>
                    <p className="text-gray-600">Analyzing text data...</p>
                  </div>
                </div>
              ) : currentAnalysis ? (
                <>
                  {/* Analysis Summary */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <p className="text-sm font-medium text-blue-700">Total Words</p>
                      <p className="text-2xl font-bold text-blue-900">{currentAnalysis.words.length}</p>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <p className="text-sm font-medium text-green-700">Submissions</p>
                      <p className="text-2xl font-bold text-green-900">{currentAnalysis.totalSubmissions}</p>
                    </div>
                    <div className="text-center p-3 bg-purple-50 rounded-lg">
                      <p className="text-sm font-medium text-purple-700">Phrases</p>
                      <p className="text-2xl font-bold text-purple-900">{currentAnalysis.phrases.length}</p>
                    </div>
                    <div className="text-center p-3 bg-amber-50 rounded-lg">
                      <p className="text-sm font-medium text-amber-700">Categories</p>
                      <p className="text-2xl font-bold text-amber-900">
                        {new Set(currentAnalysis.words.map(w => w.category)).size}
                      </p>
                    </div>
                  </div>

                  {/* Word Cloud Tabs */}
                  <Tabs defaultValue="words" className="w-full">
                    <TabsList>
                      <TabsTrigger value="words">Word Cloud</TabsTrigger>
                      <TabsTrigger value="phrases">Phrases</TabsTrigger>
                      <TabsTrigger value="categories">Categories</TabsTrigger>
                    </TabsList>

                    <TabsContent value="words">
                      <SimpleWordCloud 
                        words={currentAnalysis.words} 
                        title={`${preset.description} - Most Common Terms`}
                      />
                    </TabsContent>

                    <TabsContent value="phrases">
                      <PhrasesDisplay analysis={currentAnalysis} />
                    </TabsContent>

                    <TabsContent value="categories">
                      <div className="space-y-4">
                        {Object.entries(
                          currentAnalysis.words.reduce((acc, word) => {
                            const category = word.category || 'general';
                            if (!acc[category]) acc[category] = [];
                            acc[category].push(word);
                            return acc;
                          }, {} as Record<string, WordCloudWord[]>)
                        ).map(([category, words]) => (
                          <div key={category} className="border rounded-lg p-4">
                            <h5 className="font-medium mb-2 capitalize">{category} Terms</h5>
                            <div className="flex flex-wrap gap-2">
                              {words.slice(0, 10).map((word, index) => (
                                <Badge 
                                  key={index} 
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {word.word} ({word.frequency})
                                </Badge>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </>
              ) : (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <Cloud className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-4">Click "Refresh" to generate word cloud analysis</p>
                    <Button onClick={() => generateAnalysis(activePreset)}>
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Generate Analysis
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>
          ))}
        </Tabs>
      </CardContent>
    </Card>
  );
} 