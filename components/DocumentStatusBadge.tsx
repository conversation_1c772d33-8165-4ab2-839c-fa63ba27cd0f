/**
 * DocumentStatusBadge Component
 *
 * Displays document status with appropriate color coding, icons, and timestamps.
 * Handles all document status types: pending, verified, rejected, superseded.
 */

import React from "react";
import { CheckCircle, XCircle, Clock, Archive, Upload } from "lucide-react";
import type { DocumentWithStatus } from "@/lib/document-grouping";

interface DocumentStatusBadgeProps {
  document: DocumentWithStatus;
  showTimestamp?: boolean;
  size?: "sm" | "md" | "lg";
}

interface StatusConfig {
  color: string;
  icon: React.ComponentType<{ size?: string | number; className?: string }>;
  text: string;
}

/**
 * Gets the configuration for a document status including color, icon, and text
 */
function getStatusConfig(status: string): StatusConfig {
  switch (status) {
    case "verified":
      return {
        color: "text-green-700 bg-green-100 border-green-200",
        icon: CheckCircle,
        text: "Verified",
      };
    case "rejected":
      return {
        color: "text-red-700 bg-red-100 border-red-200",
        icon: XCircle,
        text: "Rejected",
      };
    case "superseded":
      return {
        color: "text-gray-700 bg-gray-100 border-gray-200",
        icon: Archive,
        text: "Superseded",
      };
    case "uploaded":
      return {
        color: "text-blue-700 bg-blue-100 border-blue-200",
        icon: Upload,
        text: "Uploaded",
      };
    case "pending":
    default:
      return {
        color: "text-yellow-700 bg-yellow-100 border-yellow-200",
        icon: Clock,
        text: "Pending Review",
      };
  }
}

/**
 * Formats a date/time string for display in the status badge
 */
function formatStatusTimestamp(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });
}

/**
 * Gets size-specific classes for the badge
 */
function getSizeClasses(size: "sm" | "md" | "lg"): {
  container: string;
  text: string;
  icon: number;
} {
  switch (size) {
    case "sm":
      return {
        container: "px-2 py-1 text-xs",
        text: "text-xs",
        icon: 12,
      };
    case "lg":
      return {
        container: "px-4 py-2 text-base",
        text: "text-base",
        icon: 20,
      };
    case "md":
    default:
      return {
        container: "px-3 py-1.5 text-sm",
        text: "text-sm",
        icon: 16,
      };
  }
}

export default function DocumentStatusBadge({
  document,
  showTimestamp = true,
  size = "md",
}: DocumentStatusBadgeProps) {
  // Determine the effective status
  const effectiveStatus = document.status || "pending";

  // Get status configuration
  const config = getStatusConfig(effectiveStatus);
  const sizeClasses = getSizeClasses(size);
  const Icon = config.icon;

  // Determine which timestamp to show
  const timestampToShow = document.statusAt || document.uploadedAt;
  const timestampLabel = document.statusAt ? "Updated" : "Uploaded";

  return (
    <div
      className={`
        inline-flex items-center gap-2 rounded-full font-medium border
        ${config.color} ${sizeClasses.container}
      `}
      role="status"
      aria-label={`Document status: ${config.text}`}
    >
      <Icon size={sizeClasses.icon} className="flex-shrink-0" />
      <span className={`font-medium ${sizeClasses.text}`}>{config.text}</span>

      {showTimestamp && timestampToShow && (
        <>
          <span className="text-current opacity-50">•</span>
          <span className={`opacity-75 ${sizeClasses.text}`}>
            {size === "sm"
              ? formatStatusTimestamp(timestampToShow)
              : `${timestampLabel} ${formatStatusTimestamp(timestampToShow)}`}
          </span>
        </>
      )}
    </div>
  );
}

// Export additional utility functions for use in other components
export { getStatusConfig, formatStatusTimestamp, getSizeClasses };
