import React, { useState } from "react";
import { useAmplifyImage } from "@/hooks/use-amplify-image";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Car, Eye, XCircle, Upload, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { DocumentUpload } from "@/lib/utils";
import { toast } from "sonner";

export interface VehicleMediaItem {
  id: number;
  vehicleId: number;
  mediaPath: string;
  mediaType: "image" | "video";
  uploadedAt: string;
  uploadedBy: number;
}

interface VehicleImageGalleryProps {
  vehicleId: number;
  vehicleMedia: VehicleMediaItem[];
  onMediaUpdate?: (updatedMedia: VehicleMediaItem[]) => void;
  canUpload?: boolean;
  canDelete?: boolean;
  showUploadButton?: boolean;
  compact?: boolean;
}

// Image Modal Component
function ImageModal({
  isOpen,
  onClose,
  imageUrl,
  alt,
}: {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  alt: string;
}) {
  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-2">
        <div className="relative">
          <img
            src={imageUrl}
            alt={alt}
            className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 bg-black bg-opacity-50 hover:bg-opacity-70 text-white"
            onClick={onClose}
          >
            <XCircle size={20} />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Individual Vehicle Image Component
function VehicleImage({
  mediaPath,
  alt,
  index,
  totalImages,
  onDelete,
  canDelete = false,
}: {
  mediaPath: string;
  alt: string;
  index: number;
  totalImages: number;
  onDelete?: () => void;
  canDelete?: boolean;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { imageUrl, isLoading, error } = useAmplifyImage(
    mediaPath,
    "/placeholder.svg?height=192&width=256",
    {
      validateObjectExistence: true,
      expiresIn: 900, // 15 minutes
    }
  );

  if (isLoading) {
    return (
      <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#009639] mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  if (
    error ||
    !imageUrl ||
    imageUrl === "/placeholder.svg?height=192&width=256"
  ) {
    return (
      <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
        <div className="text-center">
          <Car size={24} className="mx-auto text-gray-400 mb-2" />
          <p className="text-sm text-gray-500">Image unavailable</p>
          <p className="text-xs text-gray-400 mt-1">Path: {mediaPath}</p>
          {error && <p className="text-xs text-red-400 mt-1">Error: {error}</p>}
        </div>
      </div>
    );
  }

  return (
    <>
      <div
        className="relative group cursor-pointer"
        onClick={() => setIsModalOpen(true)}
      >
        <img
          src={imageUrl}
          alt={alt}
          className="w-full h-48 object-cover rounded-lg border border-gray-200 hover:border-gray-300 transition-colors"
        />
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity rounded-lg flex items-center justify-center">
          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="sm"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
            >
              <Eye size={16} className="mr-2" />
              View Full Size
            </Button>
            {canDelete && onDelete && (
              <Button
                variant="destructive"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
              >
                <Trash2 size={16} className="mr-2" />
                Delete
              </Button>
            )}
          </div>
        </div>
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          {index + 1} of {totalImages}
        </div>
      </div>

      <ImageModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        imageUrl={imageUrl}
        alt={alt}
      />
    </>
  );
}

export default function VehicleImageGallery({
  vehicleId,
  vehicleMedia,
  onMediaUpdate,
  canUpload = false,
  canDelete = false,
  showUploadButton = true,
  compact = false,
}: VehicleImageGalleryProps) {
  const [isUploading, setIsUploading] = useState(false);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size must be less than 5MB");
      return;
    }

    try {
      setIsUploading(true);
      toast.loading("Uploading image...", { id: "image-upload" });

      // Upload to S3 with vehicleMedia prefix
      const uploadResult = await DocumentUpload(file, "vehicleMedia");

      if (uploadResult && uploadResult.path) {
        // TODO: Call API to save to database
        // For now, just show success
        toast.success("Image uploaded successfully!", { id: "image-upload" });

        // Create new media item for optimistic update
        const newMediaItem: VehicleMediaItem = {
          id: Date.now(), // Temporary ID
          vehicleId,
          mediaPath: uploadResult.path,
          mediaType: "image",
          uploadedAt: new Date().toISOString(),
          uploadedBy: 1, // TODO: Get current user ID
        };

        if (onMediaUpdate) {
          onMediaUpdate([...vehicleMedia, newMediaItem]);
        }
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      toast.error("Failed to upload image. Please try again.", {
        id: "image-upload",
      });
    } finally {
      setIsUploading(false);
      // Reset file input
      e.target.value = "";
    }
  };

  const handleDeleteImage = async (mediaItem: VehicleMediaItem) => {
    if (!confirm("Are you sure you want to delete this image?")) return;

    try {
      toast.loading("Deleting image...", { id: "image-delete" });

      // TODO: Call API to delete from database and S3
      // For now, just remove from local state
      if (onMediaUpdate) {
        onMediaUpdate(vehicleMedia.filter((item) => item.id !== mediaItem.id));
      }

      toast.success("Image deleted successfully!", { id: "image-delete" });
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image. Please try again.", {
        id: "image-delete",
      });
    }
  };

  if (!vehicleMedia || vehicleMedia.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Vehicle Images</h3>
          {canUpload && showUploadButton && (
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                disabled={isUploading}
                title="Upload vehicle image"
                aria-label="Upload vehicle image"
              />
              <Button
                variant="outline"
                size="sm"
                disabled={isUploading}
                className="flex items-center gap-2"
              >
                <Upload size={16} />
                {isUploading ? "Uploading..." : "Upload Image"}
              </Button>
            </div>
          )}
        </div>
        <div className="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
          <div className="text-center">
            <Car size={32} className="mx-auto text-gray-400 mb-2" />
            <p className="text-sm text-gray-500">No images available</p>
            {canUpload && (
              <p className="text-xs text-gray-400 mt-1">
                Click "Upload Image" to add photos
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">
          Vehicle Images ({vehicleMedia.length})
        </h3>
        {canUpload && showUploadButton && (
          <div className="relative">
            <input
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              disabled={isUploading}
              title="Add vehicle image"
              aria-label="Add vehicle image"
            />
            <Button
              variant="outline"
              size="sm"
              disabled={isUploading}
              className="flex items-center gap-2"
            >
              <Upload size={16} />
              {isUploading ? "Uploading..." : "Add Image"}
            </Button>
          </div>
        )}
      </div>

      <div
        className={`grid gap-4 ${compact ? "grid-cols-2 sm:grid-cols-3" : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"}`}
      >
        {vehicleMedia.map((mediaItem, index) => (
          <VehicleImage
            key={mediaItem.id}
            mediaPath={mediaItem.mediaPath}
            alt={`Vehicle image ${index + 1}`}
            index={index}
            totalImages={vehicleMedia.length}
            onDelete={
              canDelete ? () => handleDeleteImage(mediaItem) : undefined
            }
            canDelete={canDelete}
          />
        ))}
      </div>
    </div>
  );
}
