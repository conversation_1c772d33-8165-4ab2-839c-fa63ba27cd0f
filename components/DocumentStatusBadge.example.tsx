/**
 * Example usage of DocumentStatusBadge component with different statuses
 * This demonstrates how the admin applications documents tab now properly displays all status types
 */

import React from "react";
import DocumentStatusBadge from "./DocumentStatusBadge";
import type { DocumentWithStatus } from "@/lib/document-grouping";

const createMockDocument = (
  overrides: Partial<DocumentWithStatus> = {}
): DocumentWithStatus => ({
  id: 1,
  documentType: "ID Document",
  documentUrl: "https://example.com/doc.pdf",
  uploadedAt: "2024-01-15T10:30:00Z",
  isLatest: true,
  version: 1,
  ...overrides,
});

export default function DocumentStatusExample() {
  const documentStatuses: Array<{
    status: DocumentWithStatus["status"];
    label: string;
    description: string;
  }> = [
    {
      status: "pending",
      label: "Pending Review",
      description: "Document uploaded but not yet reviewed by admin",
    },
    {
      status: "uploaded",
      label: "Uploaded",
      description: "Document has been uploaded to the system",
    },
    {
      status: "verified",
      label: "Verified",
      description: "Document has been reviewed and approved by admin",
    },
    {
      status: "rejected",
      label: "Rejected",
      description: "Document has been reviewed and rejected by admin",
    },
    {
      status: "superseded",
      label: "Superseded",
      description: "Document has been replaced by a newer version",
    },
  ];

  return (
    <div className="p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          Document Status Badge Examples
        </h2>
        <p className="text-gray-600">
          These are the document statuses now properly displayed in the admin
          applications documents tab:
        </p>
      </div>

      <div className="grid gap-4">
        {documentStatuses.map(({ status, label, description }) => (
          <div
            key={status || "undefined"}
            className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-white"
          >
            <div>
              <h3 className="font-medium text-gray-900">{label}</h3>
              <p className="text-sm text-gray-600">{description}</p>
            </div>
            <DocumentStatusBadge
              document={createMockDocument({
                status,
                statusAt:
                  status === "verified" || status === "rejected"
                    ? "2024-01-15T14:30:00Z"
                    : undefined,
              })}
            />
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="font-medium text-green-900 mb-2">✅ Fixed Issues:</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• All 5 document statuses now display correctly</li>
          <li>• "Uploaded" status is no longer shown as "Pending"</li>
          <li>
            • "Superseded" status is properly handled for older document
            versions
          </li>
          <li>• Document grouping by type with version management</li>
          <li>• Proper visual distinction between latest and older versions</li>
          <li>• Verification actions only available for latest documents</li>
        </ul>
      </div>
    </div>
  );
}
