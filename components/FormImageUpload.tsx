import React, { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Camera, Upload, Trash2, Eye, XCircle, Plus } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { DocumentUpload } from "@/lib/utils";
import { useAmplifyImage } from "@/hooks/use-amplify-image";
import { toast } from "sonner";

export interface FormImageItem {
  id: number;
  imageUrl: string;
  isPrimary: boolean;
  file?: File; // For newly uploaded files
}

interface FormImageUploadProps {
  images: FormImageItem[];
  onImagesChange: (images: FormImageItem[]) => void;
  maxImages?: number;
  readOnly?: boolean;
  compact?: boolean;
}

function ImageModal({
  isOpen,
  onClose,
  imageUrl,
  alt,
}: {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  alt: string;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <div className="relative">
          <img
            src={imageUrl}
            alt={alt}
            className="w-full h-auto max-h-[85vh] object-contain"
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white"
          >
            <XCircle size={20} />
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ImagePreview({
  image,
  index,
  totalImages,
  onDelete,
  onSetPrimary,
  canDelete = true,
  canSetPrimary = true,
}: {
  image: FormImageItem;
  index: number;
  totalImages: number;
  onDelete?: () => void;
  onSetPrimary?: () => void;
  canDelete?: boolean;
  canSetPrimary?: boolean;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [blobUrl, setBlobUrl] = useState<string>("");

  // Use Amplify hook for S3 images
  const {
    imageUrl: amplifyImageUrl,
    isLoading,
    error,
  } = useAmplifyImage(
    image.file ? undefined : image.imageUrl, // Only use hook for S3 paths, not blob URLs
    "/placeholder.svg?height=128&width=128",
    {
      validateObjectExistence: true,
      expiresIn: 900, // 15 minutes
    }
  );

  // Handle blob URLs for newly uploaded files
  React.useEffect(() => {
    if (image.file) {
      const url = URL.createObjectURL(image.file);
      setBlobUrl(url);
      return () => URL.revokeObjectURL(url);
    } else {
      setBlobUrl("");
    }
  }, [image.file]);

  // Choose the appropriate image URL
  const displayImageUrl = image.file ? blobUrl : amplifyImageUrl;

  if (isLoading) {
    return (
      <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#009639] mx-auto mb-2"></div>
          <p className="text-xs text-gray-500">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="relative group">
        <img
          src={displayImageUrl}
          alt={`Vehicle image ${index + 1}`}
          className="w-full h-32 object-cover rounded-lg border border-gray-200 cursor-pointer"
          onClick={() => setIsModalOpen(true)}
        />

        {/* Primary badge */}
        {image.isPrimary && (
          <Badge className="absolute top-2 left-2 bg-[#009639] text-white text-xs">
            Primary
          </Badge>
        )}

        {/* Image counter */}
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
          {index + 1} of {totalImages}
        </div>

        {/* Hover actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity rounded-lg flex items-center justify-center">
          <div className="flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="secondary"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setIsModalOpen(true);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye size={14} />
            </Button>

            {canSetPrimary && !image.isPrimary && onSetPrimary && (
              <Button
                variant="secondary"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onSetPrimary();
                }}
                className="h-8 w-8 p-0"
                title="Set as primary image"
              >
                <Camera size={14} />
              </Button>
            )}

            {canDelete && onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete();
                }}
                className="h-8 w-8 p-0"
              >
                <Trash2 size={14} />
              </Button>
            )}
          </div>
        </div>
      </div>

      <ImageModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        imageUrl={displayImageUrl}
        alt={`Vehicle image ${index + 1}`}
      />
    </>
  );
}

export default function FormImageUpload({
  images,
  onImagesChange,
  maxImages = 10,
  readOnly = false,
  compact = false,
}: FormImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (files: FileList | null) => {
    if (!files || files.length === 0) return;

    const newFiles = Array.from(files);

    // Check if adding these files would exceed max images
    if (images.length + newFiles.length > maxImages) {
      toast.error(`Maximum ${maxImages} images allowed`);
      return;
    }

    setIsUploading(true);

    try {
      const uploadPromises = newFiles.map(async (file) => {
        // Validate file type
        if (!file.type.startsWith("image/")) {
          toast.error(`${file.name} is not an image file`);
          return null;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          toast.error(`${file.name} is too large (max 5MB)`);
          return null;
        }

        // Upload the file
        const uploadResult = await DocumentUpload(file, "vehicleMedia");

        if (uploadResult && uploadResult.path) {
          return {
            id: Date.now() + Math.random(), // Temporary ID
            imageUrl: uploadResult.path,
            isPrimary: images.length === 0, // First image is primary
            file: file,
          };
        }
        return null;
      });

      const uploadedImages = (await Promise.all(uploadPromises)).filter(
        Boolean
      ) as FormImageItem[];

      if (uploadedImages.length > 0) {
        onImagesChange([...images, ...uploadedImages]);
        toast.success(
          `${uploadedImages.length} image(s) uploaded successfully`
        );
      }
    } catch (error) {
      console.error("Error uploading images:", error);
      toast.error("Failed to upload images");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);

    if (readOnly) return;

    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleDeleteImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);

    // If we deleted the primary image, make the first remaining image primary
    if (newImages.length > 0 && !newImages.some((img) => img.isPrimary)) {
      newImages[0].isPrimary = true;
    }

    onImagesChange(newImages);
  };

  const handleSetPrimary = (index: number) => {
    const newImages = images.map((img, i) => ({
      ...img,
      isPrimary: i === index,
    }));
    onImagesChange(newImages);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      {!readOnly && (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragOver
              ? "border-[#009639] bg-[#009639]/5"
              : "border-gray-300 hover:border-gray-400"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            <Upload className="h-8 w-8 text-gray-400" />
            <div className="text-sm text-gray-600">
              <button
                type="button"
                onClick={handleUploadClick}
                className="text-[#009639] hover:text-[#007A2F] font-medium"
              >
                Click to upload
              </button>{" "}
              or drag and drop
            </div>
            <p className="text-xs text-gray-500">
              PNG, JPG, WEBP up to 5MB (max {maxImages} images)
            </p>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
            aria-label="Upload images"
          />
        </div>
      )}

      {/* Loading State */}
      {isUploading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#009639] mr-2"></div>
          <span className="text-sm text-gray-600">Uploading images...</span>
        </div>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div
          className={`grid gap-4 ${compact ? "grid-cols-3" : "grid-cols-2 md:grid-cols-3"}`}
        >
          {images.map((image, index) => (
            <ImagePreview
              key={image.id}
              image={image}
              index={index}
              totalImages={images.length}
              onDelete={() => handleDeleteImage(index)}
              onSetPrimary={() => handleSetPrimary(index)}
              canDelete={!readOnly}
              canSetPrimary={!readOnly}
            />
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && readOnly && (
        <div className="text-center py-8 text-gray-500">
          <Camera className="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>No images uploaded</p>
        </div>
      )}
    </div>
  );
}
