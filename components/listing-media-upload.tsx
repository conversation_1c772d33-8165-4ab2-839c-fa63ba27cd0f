"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Upload, X, AlertCircle } from "lucide-react";
import Image from "next/image";
import { DocumentUpload, DocumentDelete } from "@/lib/utils";
import { addVehicleMedia } from "@/actions/vehicle-media";

interface UploadedImage {
  file: File;
  previewUrl: string;
  s3Path: string;
}

interface ListingMediaUploadProps {
  vehicleId: number;
  listingId: number;
  onSuccess?: () => void;
  onClose?: () => void;
}

export default function ListingMediaUpload({
  vehicleId,
  listingId,
  onSuccess,
  onClose
}: ListingMediaUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [vehicleImages, setVehicleImages] = useState<UploadedImage[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setIsUploading(true);
      setError(null);
      
      try {
        const uploadPromises = Array.from(files).map(async (file) => {
          // Upload to S3 using DocumentUpload
          const uploadResult = await DocumentUpload(file, "listingsMedia");
          
          if (uploadResult && uploadResult.path) {
            return {
              file,
              previewUrl: URL.createObjectURL(file),
              s3Path: uploadResult.path,
            };
          }
          throw new Error("Upload failed");
        });
        
        const uploadedImages = await Promise.all(uploadPromises);
        setVehicleImages(prev => [...prev, ...uploadedImages]);
      } catch (err: any) {
        console.error("Error uploading images:", err);
        setError(err?.message || "Failed to upload images. Please try again.");
      } finally {
        setIsUploading(false);
      }
    }
  };

  const handleRemoveImage = async (index: number) => {
    const imageToRemove = vehicleImages[index];
    if (!imageToRemove) return;
    
    setIsUploading(true);
    try {
      // Delete from S3
      await DocumentDelete(imageToRemove.s3Path);
      
      // Revoke preview URL
      URL.revokeObjectURL(imageToRemove.previewUrl);
      
      // Remove from state
      setVehicleImages(prev => prev.filter((_, i) => i !== index));
    } catch (error) {
      console.error("Error deleting image:", error);
      setError("Failed to delete image. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async () => {
    if (vehicleImages.length === 0) {
      setError("Please upload at least one image");
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      // Upload each image to both vehicle_media and listing_media tables
      for (const image of vehicleImages) {
        const formData = new FormData();
        formData.append("vehicleID", vehicleId.toString());
        formData.append("documentUrl", image.s3Path);

        await addVehicleMedia(null, formData);
      }

      setSuccess(true);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error saving media:", error);
      setError("Failed to save media. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  if (success) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <div className="mx-auto mb-4 h-16 w-16 text-[#009639]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-[#009639] mb-2">Media Uploaded Successfully!</h2>
            <p className="text-[#333333] mb-6">
              Your vehicle images have been uploaded and are now visible in the listing.
            </p>
            <Button 
              onClick={onClose || (() => {})} 
              className="bg-[#009639] hover:bg-[#007A2F] w-full"
            >
              Continue
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-h-screen overflow-y-auto">
      <form className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-[#333333]">Upload Vehicle Images</h2>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Media Upload Section */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
              <Upload className="h-5 w-5 text-[#009639]" />
              Vehicle Images
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-white rounded-xl p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-[#333333] font-medium">Vehicle Photos</h3>
                <input
                  type="file"
                  ref={fileInputRef}
                  className="hidden"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  disabled={isUploading}
                />
                <button
                  type="button"
                  className="bg-[#009639] text-white p-2 rounded-full shadow-sm disabled:opacity-50"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  {isUploading ? <Loader2 size={20} className="animate-spin" /> : <Upload size={20} />}
                </button>
              </div>

              {vehicleImages.length > 0 ? (
                <div className="grid grid-cols-3 gap-2">
                  {vehicleImages.map((image, index) => (
                    <div
                      key={index}
                      className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
                    >
                      <Image
                        src={image.previewUrl}
                        alt={`Vehicle photo ${index + 1}`}
                        fill
                        className="object-cover"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-white rounded-full p-1 disabled:opacity-50"
                        onClick={() => handleRemoveImage(index)}
                        disabled={isUploading}
                      >
                        <X size={14} className="text-red-500" />
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div
                  className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload size={32} className="text-[#009639] mb-2" />
                  <p className="text-[#797879] text-center">
                    Upload photos of your vehicle (min. 1 photo)
                  </p>
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                <p className="text-xs text-blue-800">
                  <strong>Photo Tips:</strong> Include exterior shots from all angles, interior photos, and any special features.
                  Good lighting helps showcase your vehicle better.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-500 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {onClose && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              className="order-2 sm:order-1 border-gray-200 text-[#333333] hover:bg-gray-50"
            >
              Skip
            </Button>
          )}
          
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={vehicleImages.length === 0 || isUploading}
            className="order-1 sm:order-2 bg-[#009639] hover:bg-[#007A2F] flex-1 sm:flex-none sm:min-w-[200px]"
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Uploading Images...
              </>
            ) : (
              "Upload Images"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
} 