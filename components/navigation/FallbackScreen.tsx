"use client";

import { useNavigation } from '@/hooks/useNavigation';
import { ArrowLeft, AlertCircle } from 'lucide-react';

interface FallbackScreenProps {
  params?: Record<string, any>;
}

export default function FallbackScreen({ params }: FallbackScreenProps) {
  const { goBack, canGoBack, navigateToHome } = useNavigation();

  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        {canGoBack() ? (
          <button
            onClick={goBack}
            className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100"
          >
            <ArrowLeft size={20} className="text-gray-600" />
          </button>
        ) : (
          <div className="w-10 h-10"></div>
        )}
        <h1 className="text-lg font-semibold text-gray-900">Page Not Found</h1>
        <div className="w-10 h-10"></div>
      </div>

      {/* Content */}
      <div className="flex-1 flex flex-col items-center justify-center p-8">
        <div className="text-center max-w-sm">
          <div className="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
            <AlertCircle size={32} className="text-gray-400" />
          </div>
          
          <h2 className="text-xl font-semibold text-gray-900 mb-3">
            Page Not Found
          </h2>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            The page you're looking for doesn't exist or is temporarily unavailable.
          </p>

          {/* Debug info in development */}
          {process.env.NODE_ENV === 'development' && params && Object.keys(params).length > 0 && (
            <div className="mb-6 p-3 bg-gray-50 rounded-lg text-left">
              <p className="text-sm font-medium text-gray-700 mb-2">Debug Info:</p>
              <pre className="text-xs text-gray-600">
                {JSON.stringify(params, null, 2)}
              </pre>
            </div>
          )}

          <div className="space-y-3">
            <button
              onClick={navigateToHome}
              className="w-full px-6 py-3 bg-[#009639] text-white rounded-lg font-medium hover:bg-[#007A2F] transition-colors"
            >
              Go to Home
            </button>
            
            {canGoBack() && (
              <button
                onClick={goBack}
                className="w-full px-6 py-3 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors"
              >
                Go Back
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 