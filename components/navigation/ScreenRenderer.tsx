"use client";

import { useState, useEffect, Suspense, lazy, useMemo } from 'react';
import { useNavigationStore } from '@/lib/navigation/navigationStore';
import { SCREEN_COMPONENTS, type ScreenName } from '@/components/screens';

// Screen cache to prevent re-importing
const screenCache = new Map();

const getScreenComponent = (screenName: string) => {
  if (!screenCache.has(screenName)) {
    // Check if screen exists in registry
    if (screenName in SCREEN_COMPONENTS) {
      const Component = lazy(SCREEN_COMPONENTS[screenName as ScreenName]);
      screenCache.set(screenName, Component);
    } else {
      // Fallback for unknown screens
      const FallbackComponent = lazy(() => import('./FallbackScreen'));
      screenCache.set(screenName, FallbackComponent);
    }
  }
  return screenCache.get(screenName);
};

interface ScreenProps {
  params?: Record<string, any>;
}

export default function ScreenRenderer() {
  const { currentScreen, params, navigationDirection } = useNavigationStore();
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousScreen, setPreviousScreen] = useState<string | null>(null);

  const CurrentComponent = useMemo(() => getScreenComponent(currentScreen), [currentScreen]);

  useEffect(() => {
    if (previousScreen && previousScreen !== currentScreen) {
      setIsTransitioning(true);
      const timer = setTimeout(() => {
        setIsTransitioning(false);
        setPreviousScreen(null);
      }, 300); // Animation duration
      return () => clearTimeout(timer);
    }
    setPreviousScreen(currentScreen);
  }, [currentScreen, previousScreen]);

  const getAnimationClass = () => {
    if (!isTransitioning) return 'translate-x-0 opacity-100';
    
    switch (navigationDirection) {
      case 'forward':
        return 'translate-x-full opacity-0';
      case 'back':
        return '-translate-x-full opacity-0';
      default:
        return 'translate-x-0 opacity-0';
    }
  };

  const getPreviousAnimationClass = () => {
    switch (navigationDirection) {
      case 'forward':
        return '-translate-x-full opacity-0';
      case 'back':
        return 'translate-x-full opacity-0';
      default:
        return 'translate-x-0 opacity-0';
    }
  };

  return (
    <div className="relative h-full overflow-hidden bg-white">
      {/* Current Screen */}
      <div
        className={`absolute inset-0 transition-all duration-300 ease-out ${getAnimationClass()}`}
        style={{ willChange: 'transform, opacity' }}
      >
        <Suspense fallback={<ScreenLoadingSkeleton />}>
          <CurrentComponent params={params} />
        </Suspense>
      </div>
      
      {/* Previous Screen (for transition) */}
      {isTransitioning && previousScreen && previousScreen !== currentScreen && (
        <div
          className={`absolute inset-0 transition-all duration-300 ease-out ${getPreviousAnimationClass()}`}
          style={{ willChange: 'transform, opacity' }}
        >
          <div className="h-full bg-white">
            {/* Empty div to show during transition */}
          </div>
        </div>
      )}
    </div>
  );
}

function ScreenLoadingSkeleton() {
  return (
    <div className="h-full bg-white">
      <div className="animate-pulse p-4">
        {/* Header skeleton */}
        <div className="flex items-center justify-between mb-6">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="h-6 bg-gray-200 rounded w-6"></div>
        </div>
        
        {/* Content skeleton */}
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        </div>
        
        {/* Card skeletons */}
        <div className="mt-8 space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center space-x-4">
                <div className="h-12 w-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
} 