// "use client";

// import { SWRConfig } from "swr";
// import { ReactNode } from "react";

// interface SWRProviderProps {
//   children: ReactNode;
// }

// export default function SWRProvider({ children }: SWRProviderProps) {
//   return (
//     <SWRConfig
//       value={{
//         // Global SWR configuration
//         revalidateOnFocus: false,
//         revalidateOnReconnect: true,
//         refreshInterval: 0, // Disable global refresh interval (set per hook)
//         errorRetryCount: 3,
//         errorRetryInterval: 5000,
//         dedupingInterval: 2000,
//         focusThrottleInterval: 5000,
//         onError: (error, key) => {
//           console.error(`SWR Error for key "${key}":`, error);
//         },
//         onSuccess: (data, key, config) => {
//           console.log(`SWR Success for key "${key}"`);
//         },
//       }}
//     >
//       {children}
//     </SWRConfig>
//   );
// }

"use client";

import { SWRConfig } from "swr";
import { ReactNode, createContext, useContext, useState } from "react";

interface SWRContextType {
  isPaused: boolean;
  pauseSWR: () => void;
  resumeSWR: () => void;
}

const SWRContext = createContext<SWRContextType | undefined>(undefined);

export const useSWRControl = () => {
  const context = useContext(SWRContext);
  if (!context) {
    throw new Error("useSWRControl must be used within SWRProvider");
  }
  return context;
};

interface SWRProviderProps {
  children: ReactNode;
}

export default function SWRProvider({ children }: SWRProviderProps) {
  const [isPaused, setIsPaused] = useState(false);

  const pauseSWR = () => setIsPaused(true);
  const resumeSWR = () => setIsPaused(false);

  return (
    <SWRContext.Provider value={{ isPaused, pauseSWR, resumeSWR }}>
      <SWRConfig
        value={{
          // Halt all revalidation when paused
          revalidateOnFocus: !isPaused,
          revalidateOnReconnect: !isPaused,
          refreshInterval: isPaused ? 0 : 0, // Keep at 0 for manual control
          errorRetryCount: isPaused ? 0 : 3,
          errorRetryInterval: 5000,
          dedupingInterval: 2000,
          focusThrottleInterval: 5000,
          onError: (error, key) => {
            if (!isPaused) {
              console.error(`SWR Error for key "${key}":`, error);
            }
          },
          onSuccess: (data, key, config) => {
            if (!isPaused) {
              console.log(`SWR Success for key "${key}"`);
            }
          },
        }}
      >
        {children}
      </SWRConfig>
    </SWRContext.Provider>
  );
}
