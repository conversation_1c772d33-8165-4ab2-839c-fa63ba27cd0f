"use client";

import { useEffect } from 'react';
import * as Sen<PERSON> from '@sentry/nextjs';

interface ClientErrorHandlerProps {
  children: React.ReactNode;
}

export default function ClientErrorHandler({ children }: ClientErrorHandlerProps) {
  useEffect(() => {
    // Handle unhandled promise rejections (like chunk loading errors)
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection:', event.reason);
      
      // Check if this is a chunk loading error
      const isChunkError = event.reason?.message?.includes('Loading chunk') ||
                          event.reason?.message?.includes('Failed to fetch') ||
                          event.reason?.name === 'ChunkLoadError';

      if (isChunkError) {
        // Prevent the browser from showing the error
        event.preventDefault();
        
        // Log to Sentry
        Sentry.captureException(event.reason, {
          tags: {
            errorType: 'chunkLoadError',
            handled: true
          }
        });

        // Show user-friendly message and reload the page
        const shouldReload = confirm(
          'We\'re experiencing some trouble loading the page. Would you like to refresh to continue?'
        );
        
        if (shouldReload) {
          window.location.reload();
        }
        return;
      }

      // For other network errors, also prevent browser-level errors
      const isNetworkError = event.reason?.message?.includes('Failed to fetch') ||
                            event.reason?.message?.includes('NetworkError') ||
                            event.reason?.name === 'NetworkError';

      if (isNetworkError) {
        event.preventDefault();
        
        Sentry.captureException(event.reason, {
          tags: {
            errorType: 'networkError',
            handled: true
          }
        });

        console.warn('Network error handled gracefully:', event.reason);
        return;
      }

      // Log other unhandled rejections to Sentry but don't prevent default
      Sentry.captureException(event.reason, {
        tags: {
          errorType: 'unhandledRejection',
          handled: false
        }
      });
    };

    // Handle general JavaScript errors
    const handleError = (event: ErrorEvent) => {
      console.error('Unhandled error:', event.error);
      
      // Check if this is a chunk loading error
      const isChunkError = event.error?.message?.includes('Loading chunk') ||
                          event.error?.message?.includes('Failed to fetch') ||
                          event.error?.name === 'ChunkLoadError';

      if (isChunkError) {
        // Prevent the browser from showing the error
        event.preventDefault();
        
        Sentry.captureException(event.error, {
          tags: {
            errorType: 'chunkLoadError',
            handled: true
          }
        });

        // Automatically reload for chunk errors
        window.location.reload();
        return;
      }

      // Log other errors to Sentry
      Sentry.captureException(event.error, {
        tags: {
          errorType: 'globalError',
          handled: false
        }
      });
    };

    // Add event listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    // Cleanup function
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);

  return <>{children}</>;
} 