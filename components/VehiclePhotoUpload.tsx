"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { DocumentUpload } from "@/lib/utils";
import { cn } from "@/lib/utils";
import { Camera, Upload, X, Plus, Loader2, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

// Types
export interface PhotoSlot {
  file: File | null;
  previewUrl: string | null;
  s3Url: string | null;
}

export interface MultiplePhotos {
  file: File;
  previewUrl: string;
  s3Url: string;
}

export interface PhotoSection {
  id: string;
  title: string;
  description?: string;
  required: boolean;
  photos: { [key: string]: PhotoSlot } | MultiplePhotos[];
  multiple?: boolean;
  maxFiles?: number;
  layout?: "grid" | "single" | "side-by-side";
  photoTypes?: { key: string; label: string; height?: string }[];
}

export interface VehiclePhotoUploadProps {
  sections: PhotoSection[];
  onPhotosChange: (sectionId: string, photos: any) => void;
  onError?: (error: string) => void;
  uploadFolder?: string;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  showValidation?: boolean;
  validationErrors?: string[];
}

const VehiclePhotoUpload: React.FC<VehiclePhotoUploadProps> = ({
  sections,
  onPhotosChange,
  onError,
  uploadFolder = "vehicleMedia",
  isLoading = false,
  disabled = false,
  className,
  showValidation = false,
  validationErrors = [],
}) => {
  const [uploadingStates, setUploadingStates] = useState<{
    [key: string]: boolean;
  }>({});
  const [localErrors, setLocalErrors] = useState<{ [key: string]: string }>({});
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  // Handle individual photo upload
  const handlePhotoUpload = async (
    sectionId: string,
    photoKey: string,
    files: FileList | null,
    isMultiple: boolean = false
  ) => {
    if (!files || files.length === 0) return;

    const uploadStateKey = `${sectionId}-${photoKey}`;
    setUploadingStates((prev) => ({ ...prev, [uploadStateKey]: true }));
    setLocalErrors((prev) => ({ ...prev, [uploadStateKey]: "" }));

    try {
      const section = sections.find((s) => s.id === sectionId);
      if (!section) return;

      if (isMultiple) {
        // Handle multiple photos
        const newPhotos = await Promise.all(
          Array.from(files).map(async (file) => {
            const previewUrl = URL.createObjectURL(file);
            const uploadResult = await DocumentUpload(file, uploadFolder);
            if (!uploadResult || !uploadResult.path) {
              URL.revokeObjectURL(previewUrl);
              throw new Error("Failed to upload photo to S3");
            }
            return { file, previewUrl, s3Url: uploadResult.path };
          })
        );

        const currentPhotos = section.photos as MultiplePhotos[];
        const updatedPhotos = [...currentPhotos, ...newPhotos];

        // Check max files limit
        if (section.maxFiles && updatedPhotos.length > section.maxFiles) {
          const excess = updatedPhotos.slice(section.maxFiles);
          excess.forEach((photo) => URL.revokeObjectURL(photo.previewUrl));
          onPhotosChange(sectionId, updatedPhotos.slice(0, section.maxFiles));
        } else {
          onPhotosChange(sectionId, updatedPhotos);
        }
      } else {
        // Handle single photo
        const file = files[0];
        const previewUrl = URL.createObjectURL(file);
        const uploadResult = await DocumentUpload(file, uploadFolder);

        if (!uploadResult || !uploadResult.path) {
          URL.revokeObjectURL(previewUrl);
          throw new Error(`Failed to upload ${photoKey} photo to S3`);
        }

        const currentPhotos = section.photos as { [key: string]: PhotoSlot };
        const updatedPhotos = {
          ...currentPhotos,
          [photoKey]: { file, previewUrl, s3Url: uploadResult.path },
        };

        onPhotosChange(sectionId, updatedPhotos);
      }
    } catch (error: any) {
      console.error(`Error uploading ${photoKey} photo:`, error);
      const errorMessage =
        error.message || "An error occurred while uploading the photo.";
      setLocalErrors((prev) => ({ ...prev, [uploadStateKey]: errorMessage }));
      onError?.(errorMessage);
    } finally {
      setUploadingStates((prev) => ({ ...prev, [uploadStateKey]: false }));
    }
  };

  // Handle photo removal
  const handleRemovePhoto = (
    sectionId: string,
    photoKey: string,
    index?: number
  ) => {
    const section = sections.find((s) => s.id === sectionId);
    if (!section) return;

    if (section.multiple) {
      // Remove from multiple photos array
      const currentPhotos = section.photos as MultiplePhotos[];
      if (index !== undefined) {
        const photoToRemove = currentPhotos[index];
        if (photoToRemove) {
          URL.revokeObjectURL(photoToRemove.previewUrl);
          const updatedPhotos = currentPhotos.filter((_, i) => i !== index);
          onPhotosChange(sectionId, updatedPhotos);
        }
      }
    } else {
      // Remove single photo
      const currentPhotos = section.photos as { [key: string]: PhotoSlot };
      const photoToRemove = currentPhotos[photoKey];
      if (photoToRemove?.previewUrl) {
        URL.revokeObjectURL(photoToRemove.previewUrl);
      }

      const updatedPhotos = {
        ...currentPhotos,
        [photoKey]: { file: null, previewUrl: null, s3Url: null },
      };
      onPhotosChange(sectionId, updatedPhotos);
    }
  };

  // Cleanup URLs on unmount
  useEffect(() => {
    return () => {
      sections.forEach((section) => {
        if (section.multiple) {
          const photos = section.photos as MultiplePhotos[];
          photos.forEach((photo) => {
            if (photo.previewUrl) {
              URL.revokeObjectURL(photo.previewUrl);
            }
          });
        } else {
          const photos = section.photos as { [key: string]: PhotoSlot };
          Object.values(photos).forEach((photo) => {
            if (photo.previewUrl) {
              URL.revokeObjectURL(photo.previewUrl);
            }
          });
        }
      });
    };
  }, [sections]);

  // Render upload area
  const renderUploadArea = (
    sectionId: string,
    photoKey: string,
    label: string,
    height: string = "h-40",
    isMultiple: boolean = false
  ) => {
    const uploadStateKey = `${sectionId}-${photoKey}`;
    const isUploading = uploadingStates[uploadStateKey];
    const error = localErrors[uploadStateKey];

    return (
      <div className="space-y-2">
        <p className="text-sm text-[#797879] mb-2">{label}</p>
        <input
          ref={(el) => (fileInputRefs.current[uploadStateKey] = el)}
          type="file"
          className="hidden"
          accept="image/*"
          multiple={isMultiple}
          onChange={(e) =>
            handlePhotoUpload(sectionId, photoKey, e.target.files, isMultiple)
          }
          disabled={disabled || isUploading}
        />

        <div
          className={cn(
            "border-2 border-dashed border-[#d6d9dd] rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-[#009639] transition-colors",
            height,
            "p-4",
            disabled || isUploading ? "opacity-50 cursor-not-allowed" : ""
          )}
          onClick={() =>
            !disabled &&
            !isUploading &&
            fileInputRefs.current[uploadStateKey]?.click()
          }
        >
          {isUploading ? (
            <Loader2 size={24} className="text-[#009639] animate-spin mb-2" />
          ) : (
            <Upload size={24} className="text-[#797879] mb-2" />
          )}
          <p className="text-[#797879] text-center text-sm">
            {isUploading ? "Uploading..." : `Add ${label.toLowerCase()}`}
          </p>
        </div>

        {error && (
          <div className="flex items-center gap-2 text-red-500 text-sm">
            <AlertCircle size={16} />
            <span>{error}</span>
          </div>
        )}
      </div>
    );
  };

  // Render photo preview
  const renderPhotoPreview = (
    sectionId: string,
    photoKey: string,
    photo: PhotoSlot,
    label: string,
    height: string = "h-40",
    onRemove: () => void
  ) => {
    return (
      <div className="space-y-2">
        <p className="text-sm text-[#797879] mb-2">{label}</p>
        <div
          className={cn(
            "relative bg-[#f2f2f2] rounded-lg overflow-hidden",
            height
          )}
        >
          <Image
            src={photo.previewUrl!}
            alt={label}
            fill
            className="object-cover"
          />
          <button
            type="button"
            className="absolute top-2 right-2 bg-white rounded-full p-1 shadow-sm hover:bg-gray-100"
            onClick={onRemove}
            disabled={disabled}
          >
            <X size={16} className="text-red-500" />
          </button>
        </div>
      </div>
    );
  };

  // Render single photo section
  const renderSinglePhotoSection = (section: PhotoSection) => {
    const photos = section.photos as { [key: string]: PhotoSlot };
    const photoTypes = section.photoTypes || [];

    if (section.layout === "side-by-side") {
      return (
        <div className="grid grid-cols-2 gap-3">
          {photoTypes.map(({ key, label, height = "h-32" }) => (
            <div key={key}>
              {photos[key]?.previewUrl
                ? renderPhotoPreview(
                    section.id,
                    key,
                    photos[key],
                    label,
                    height,
                    () => handleRemovePhoto(section.id, key)
                  )
                : renderUploadArea(section.id, key, label, height)}
            </div>
          ))}
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {photoTypes.map(({ key, label, height = "h-40" }) => (
          <div key={key}>
            {photos[key]?.previewUrl
              ? renderPhotoPreview(
                  section.id,
                  key,
                  photos[key],
                  label,
                  height,
                  () => handleRemovePhoto(section.id, key)
                )
              : renderUploadArea(section.id, key, label, height)}
          </div>
        ))}
      </div>
    );
  };

  // Render multiple photos section
  const renderMultiplePhotosSection = (section: PhotoSection) => {
    const photos = section.photos as MultiplePhotos[];
    const uploadStateKey = `${section.id}-multiple`;
    const isUploading = uploadingStates[uploadStateKey];

    return (
      <div>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Camera size={20} className="text-[#009639] mr-2" />
            <h3 className="text-[#333333] font-medium">{section.title}</h3>
          </div>
          <input
            ref={(el) => (fileInputRefs.current[uploadStateKey] = el)}
            type="file"
            className="hidden"
            accept="image/*"
            multiple
            onChange={(e) =>
              handlePhotoUpload(section.id, "multiple", e.target.files, true)
            }
            disabled={disabled || isUploading}
          />
          <Button
            type="button"
            size="sm"
            className="bg-[#009639] text-white hover:bg-[#007A2F]"
            onClick={() =>
              !disabled &&
              !isUploading &&
              fileInputRefs.current[uploadStateKey]?.click()
            }
            disabled={disabled || isUploading}
          >
            {isUploading ? (
              <Loader2 size={16} className="animate-spin" />
            ) : (
              <Camera size={16} />
            )}
          </Button>
        </div>

        {photos.length > 0 ? (
          <div className="grid grid-cols-3 gap-2">
            {photos.map((photo, index) => (
              <div
                key={index}
                className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
              >
                <Image
                  src={photo.previewUrl}
                  alt={`${section.title} ${index + 1}`}
                  fill
                  className="object-cover"
                />
                <button
                  type="button"
                  className="absolute top-1 right-1 bg-white rounded-full p-1 hover:bg-gray-100"
                  onClick={() =>
                    handleRemovePhoto(section.id, "multiple", index)
                  }
                  disabled={disabled}
                >
                  <X size={14} className="text-red-500" />
                </button>
              </div>
            ))}
            {(!section.maxFiles || photos.length < section.maxFiles) && (
              <div
                className="border-2 border-dashed border-[#d6d9dd] rounded-lg h-24 flex flex-col items-center justify-center cursor-pointer hover:border-[#009639] transition-colors"
                onClick={() =>
                  !disabled &&
                  !isUploading &&
                  fileInputRefs.current[uploadStateKey]?.click()
                }
              >
                <Plus size={24} className="text-[#797879]" />
              </div>
            )}
          </div>
        ) : (
          <div
            className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer hover:border-[#009639] transition-colors"
            onClick={() =>
              !disabled &&
              !isUploading &&
              fileInputRefs.current[uploadStateKey]?.click()
            }
          >
            <Upload size={32} className="text-[#797879] mb-2" />
            <p className="text-[#797879] text-center">
              {section.description || "Add photos"}
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("space-y-4", className)}>
      {sections.map((section) => (
        <div
          key={section.id}
          className="bg-white rounded-xl shadow-md p-4 border border-gray-100"
        >
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <Camera size={20} className="text-[#009639] mr-2" />
              <h3 className="text-[#333333] font-medium">
                {section.title} {section.required && "(Required)"}
              </h3>
            </div>
          </div>

          {section.description && (
            <p className="text-sm text-[#797879] mb-4">{section.description}</p>
          )}

          {section.multiple
            ? renderMultiplePhotosSection(section)
            : renderSinglePhotoSection(section)}
        </div>
      ))}

      {showValidation && validationErrors.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle size={20} className="text-red-500" />
            <h4 className="text-red-700 font-medium">Validation Errors</h4>
          </div>
          <ul className="text-sm text-red-600 space-y-1">
            {validationErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {isLoading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 size={32} className="animate-spin text-[#009639]" />
          <span className="ml-2 text-[#797879]">Processing...</span>
        </div>
      )}
    </div>
  );
};

export default VehiclePhotoUpload;
