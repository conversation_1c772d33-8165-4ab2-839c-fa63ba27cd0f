import { PhotoSection } from "./VehiclePhotoUpload";

// Preset configurations for different scenarios

// Vehicle listing for admin approval
export const vehicleListingPhotoConfig: PhotoSection[] = [
  {
    id: "exterior",
    title: "Exterior Photos",
    description: "Take photos of all sides of the vehicle",
    required: true,
    multiple: false,
    layout: "single",
    photos: {
      front: { file: null, previewUrl: null, s3Url: null },
      back: { file: null, previewUrl: null, s3Url: null },
      left: { file: null, previewUrl: null, s3Url: null },
      right: { file: null, previewUrl: null, s3Url: null },
    },
    photoTypes: [
      { key: "front", label: "Front View", height: "h-40" },
      { key: "back", label: "Back View", height: "h-40" },
      { key: "left", label: "Left Side", height: "h-32" },
      { key: "right", label: "Right Side", height: "h-32" },
    ],
  },
  {
    id: "interior",
    title: "Interior Photos",
    description: "Take photos of the interior condition",
    required: true,
    multiple: false,
    layout: "side-by-side",
    photos: {
      dashboard: { file: null, previewUrl: null, s3Url: null },
      seats: { file: null, previewUrl: null, s3Url: null },
    },
    photoTypes: [
      { key: "dashboard", label: "Dashboard", height: "h-32" },
      { key: "seats", label: "Seats", height: "h-32" },
    ],
  },
  {
    id: "additional",
    title: "Additional Photos",
    description: "Add any additional photos (optional)",
    required: false,
    multiple: true,
    maxFiles: 10,
    photos: [],
  },
];

// Vehicle handover photos
export const vehicleHandoverPhotoConfig: PhotoSection[] = [
  {
    id: "condition",
    title: "Vehicle Condition",
    description: "Document current condition of the vehicle",
    required: true,
    multiple: false,
    layout: "single",
    photos: {
      front: { file: null, previewUrl: null, s3Url: null },
      back: { file: null, previewUrl: null, s3Url: null },
      left: { file: null, previewUrl: null, s3Url: null },
      right: { file: null, previewUrl: null, s3Url: null },
      dashboard: { file: null, previewUrl: null, s3Url: null },
      seats: { file: null, previewUrl: null, s3Url: null },
    },
    photoTypes: [
      { key: "front", label: "Front View", height: "h-40" },
      { key: "back", label: "Back View", height: "h-40" },
      { key: "left", label: "Left Side", height: "h-32" },
      { key: "right", label: "Right Side", height: "h-32" },
      { key: "dashboard", label: "Dashboard", height: "h-32" },
      { key: "seats", label: "Seats", height: "h-32" },
    ],
  },
  {
    id: "damages",
    title: "Document Damages",
    description: "Take photos of any damages, scratches, or issues",
    required: false,
    multiple: true,
    maxFiles: 20,
    photos: [],
  },
];

// Admin inventory photos
export const adminInventoryPhotoConfig: PhotoSection[] = [
  {
    id: "vehicle-photos",
    title: "Vehicle Photos",
    description: "Add photos for the vehicle inventory",
    required: true,
    multiple: true,
    maxFiles: 15,
    photos: [],
  },
  {
    id: "documents",
    title: "Document Photos",
    description: "Photos of vehicle documents (registration, insurance, etc.)",
    required: false,
    multiple: true,
    maxFiles: 10,
    photos: [],
  },
];

// Simple multiple photos config
export const simpleMultiplePhotosConfig: PhotoSection[] = [
  {
    id: "photos",
    title: "Photos",
    description: "Upload photos",
    required: true,
    multiple: true,
    maxFiles: 10,
    photos: [],
  },
];
