"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, CheckCircle, XCircle, Calendar, Crown } from "lucide-react";
import { acceptGroupInvitation, declineGroupInvitation } from "@/actions/tasks";
import { useUserAttributes } from "@/hooks/useUserAttributes";

interface GroupInvitationTaskContentProps {
  task: any; // Task with invitation details
  onComplete: (result?: any) => void;
  onCancel: () => void;
}

export default function GroupInvitationTaskContent({ 
  task, 
  onComplete, 
  onCancel 
}: GroupInvitationTaskContentProps) {
  const { attributes } = useUserAttributes();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const partyId = attributes?.["custom:db_id"] ? parseInt(attributes["custom:db_id"]) : undefined;

  const handleAccept = async () => {
    if (!partyId) {
      setError("Unable to identify user account");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await acceptGroupInvitation(parseInt(task.id), partyId);
      
      if (result.success) {
        onComplete({ action: 'accepted', taskId: task.id });
      } else {
        setError('error' in result ? result.error : "Failed to accept invitation");
      }
    } catch (err) {
      console.error("Error accepting invitation:", err);
      setError("Failed to accept invitation");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDecline = async () => {
    if (!partyId) {
      setError("Unable to identify user account");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await declineGroupInvitation(parseInt(task.id), partyId);
      
      if (result.success) {
        onComplete({ action: 'declined', taskId: task.id });
      } else {
        setError('error' in result ? result.error : "Failed to decline invitation");
      }
    } catch (err) {
      console.error("Error declining invitation:", err);
      setError("Failed to decline invitation");
    } finally {
      setIsLoading(false);
    }
  };

  const invitationDetails = task.invitationDetails || {};
  const metadata = task.metadata || {};

  return (
    <div className="p-4 space-y-4">
      {error && (
        <Alert className="border-red-200 bg-red-50">
          <XCircle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Group Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-[#009639]" />
            Group Invitation Details
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold text-lg text-[#333333]">
              {metadata.groupName || invitationDetails.groupName || "Group Invitation"}
            </h3>
            <p className="text-[#797879] text-sm">
              You've been invited to join this co-ownership group
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-[#333333]">Invited by</p>
              <p className="text-[#797879]">
                {metadata.inviterName || invitationDetails.inviterName || "Unknown"}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-[#333333]">Role</p>
              <Badge variant="outline" className="mt-1">
                <Crown className="h-3 w-3 mr-1" />
                {metadata.role || invitationDetails.role || "Member"}
              </Badge>
            </div>
          </div>

          {task.dueDate && (
            <div className="flex items-center gap-2 text-sm text-[#797879]">
              <Calendar className="h-4 w-4" />
              <span>
                Expires: {new Date(task.dueDate).toLocaleDateString()}
              </span>
            </div>
          )}

          {task.description && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-[#333333]">{task.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Group Benefits */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">What you'll get</CardTitle>
        </CardHeader>
        <CardContent>
          <ul className="space-y-2 text-sm text-[#797879]">
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              Access to shared vehicles in the group
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              Shared ownership costs and responsibilities
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              Booking and scheduling coordination
            </li>
            <li className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              Group communication and updates
            </li>
          </ul>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex gap-3 pt-4">
        <Button
          onClick={handleAccept}
          disabled={isLoading}
          className="flex-1 bg-[#009639] hover:bg-[#007A2F] text-white"
        >
          {isLoading ? "Processing..." : "Accept Invitation"}
        </Button>
        <Button
          onClick={handleDecline}
          disabled={isLoading}
          variant="outline"
          className="flex-1"
        >
          {isLoading ? "Processing..." : "Decline"}
        </Button>
      </div>

      <Button
        onClick={onCancel}
        variant="ghost"
        className="w-full text-[#797879]"
        disabled={isLoading}
      >
        Back to Tasks
      </Button>
    </div>
  );
} 