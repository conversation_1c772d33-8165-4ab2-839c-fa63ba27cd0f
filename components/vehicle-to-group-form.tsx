"use client";

import React, { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, CheckCircle, AlertCircle, Car, Users, Shield, ArrowLeft, Upload, X, Camera } from "lucide-react";
import { useRouter } from "next/navigation";
import { useNavigation } from "@/hooks/useNavigation";
import PageWithBottomNav from "@/components/PageWithBottomNav";
import Image from "next/image";
import { 
  addVehicleToGroup,
  getVehicleMakes,
  getVehicleModelsByMake,
  getCompanies
} from "@/drizzle-actions/vehicle-groups";
import { addVehicleMedia } from "@/actions/vehicle-media";
import { DocumentUpload } from "@/lib/utils";
import { OWNERSHIP_DISCLAIMER } from "@/types/vehicle-groups";
import type { 
  VehicleToGroupRequest, 
} from "@/types/vehicle-groups";
import type { CompanyRead } from "@/types/company";
import type { VehicleMakeRead } from "@/types/vehicle-makes";
import type { VehicleModelReadWithMake } from "@/types/vehicle-model";

interface VehicleToGroupFormProps {
  currentUserPartyId: number;
  preselectedCompanyId?: number;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  hideHeader?: boolean;
}

interface UploadedImage {
  file: File;
  previewUrl: string;
  s3Path: string;
}

function VehicleToGroupFormContent({
  currentUserPartyId,
  preselectedCompanyId,
  onSuccess,
  onCancel,
  hideHeader = false
}: VehicleToGroupFormProps) {
  const router = useRouter();
  const { goBack, navigateToGroupDetails } = useNavigation();
  
  const [formData, setFormData] = useState({
    vin_number: "",
    make_id: "",
    model_id: "",
    vehicle_registration: "",
    country_of_registration: "",
    manufacturing_year: "",
    purchase_date: "",
    color: "",
    company_id: preselectedCompanyId ? preselectedCompanyId.toString() : "",
    ownership_disclaimer_accepted: false,
    ownership_confirmation: false,
    owner_statement: ""
  });

  const [companies, setCompanies] = useState<CompanyRead[]>([]);
  const [makes, setMakes] = useState<VehicleMakeRead[]>([]);
  const [models, setModels] = useState<VehicleModelReadWithMake[]>([]);
  
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Image upload state
  const [vehicleImages, setVehicleImages] = useState<UploadedImage[]>([]);
  const [isUploadingImages, setIsUploadingImages] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load companies and makes on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [companiesData, makesData] = await Promise.all([
          getCompanies(),
          getVehicleMakes()
        ]);
        // Map companies data to match CompanyRead type
        setCompanies(companiesData.map(company => ({
          ...company,
          registration_country: company.registration_country?.toString() || null
        })) as CompanyRead[]);
        
        // Map makes data to match VehicleMakeRead type
        setMakes(makesData.map(make => ({
          ...make,
          is_active: true, // Default to active since these are returned from active query
          created_at: make.created_at?.toString() || new Date().toISOString(),
          updated_at: make.updated_at?.toString() || new Date().toISOString()
        })) as VehicleMakeRead[]);
      } catch (error) {
        console.error("Error loading initial data:", error);
        setError("Failed to load form data");
      }
    };

    loadInitialData();
  }, []);

  // Load models when make changes
  useEffect(() => {
    const loadModels = async () => {
      if (formData.make_id) {
        try {
          const modelsData = await getVehicleModelsByMake(parseInt(formData.make_id));
          // Map models data to match VehicleModelReadWithMake type
          setModels(modelsData.map(model => ({
            ...model,
            first_year: model.first_year,
            last_year: model.last_year,
            body_type: null,
            slug: null,
            created_at: model.created_at?.toString() || new Date().toISOString(),
            updated_at: model.updated_at?.toString() || new Date().toISOString(),
            make: model.make ? {
              id: model.make.id,
              name: model.make.model, // Note: the API returns 'model' as the name
              description: null,
              is_active: true,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            } : null
          })) as VehicleModelReadWithMake[]);
        } catch (error) {
          console.error("Error loading models:", error);
          setModels([]);
        }
      } else {
        setModels([]);
      }
    };

    loadModels();
  }, [formData.make_id]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear model selection when make changes
    if (name === "make_id") {
      setFormData(prev => ({
        ...prev,
        model_id: ""
      }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setIsUploadingImages(true);
    setError(null);

    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Upload to S3 using DocumentUpload
        const uploadResult = await DocumentUpload(file, "vehicleMedia");
        
        if (uploadResult && uploadResult.path) {
          return {
            file,
            previewUrl: URL.createObjectURL(file),
            s3Path: uploadResult.path,
          };
        }
        throw new Error("Upload failed");
      });
      
      const uploadedImages = await Promise.all(uploadPromises);
      setVehicleImages(prev => [...prev, ...uploadedImages]);
    } catch (err: any) {
      console.error("Error uploading images:", err);
      setError(err?.message || "Failed to upload images. Please try again.");
    } finally {
      setIsUploadingImages(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleRemoveImage = async (index: number) => {
    const imageToRemove = vehicleImages[index];
    if (!imageToRemove) return;
    
    // Revoke preview URL to prevent memory leaks
    URL.revokeObjectURL(imageToRemove.previewUrl);
    
    // Remove from state
    setVehicleImages(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.company_id) {
      setError("Please select a group");
      return;
    }

    if (!formData.ownership_disclaimer_accepted || !formData.ownership_confirmation) {
      setError("You must accept the ownership disclaimer and confirm ownership");
      return;
    }

    if (!formData.owner_statement.trim()) {
      setError("Please provide an ownership statement");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const request: VehicleToGroupRequest = {
        company_id: parseInt(formData.company_id),
        vin_number: formData.vin_number,
        make_id: parseInt(formData.make_id),
        model_id: parseInt(formData.model_id),
        vehicle_registration: formData.vehicle_registration || undefined,
        country_of_registration: formData.country_of_registration || undefined,
        manufacturing_year: formData.manufacturing_year ? parseInt(formData.manufacturing_year) : undefined,
        purchase_date: formData.purchase_date || undefined,
        color: formData.color || undefined,
        ownership_disclaimer_accepted: formData.ownership_disclaimer_accepted,
        ownership_confirmation: formData.ownership_confirmation,
        owner_statement: formData.owner_statement
      };

      const result = await addVehicleToGroup(request);

      if (result.success && result.data?.vehicle_id) {
        // Upload images to VehicleMedia if any were uploaded
        if (vehicleImages.length > 0) {
          try {
            for (const image of vehicleImages) {
              const formData = new FormData();
              formData.append("vehicleID", result.data.vehicle_id.toString());
              formData.append("documentUrl", image.s3Path);
              
              await addVehicleMedia(null, formData);
            }
          } catch (imageError) {
            console.error("Error uploading vehicle images:", imageError);
            // Don't fail the whole process for image upload errors
          }
        }

        setSuccess(true);
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error("Submit error:", error);
      setError("Failed to add vehicle to group");
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      goBack();
    }
  };

  if (success) {
    return (
      <div className={hideHeader ? "bg-[#f5f5f5]" : "min-h-screen bg-[#f5f5f5]"}>
        {/* Header */}
        {!hideHeader && (
          <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
            <button className="mr-4" onClick={handleCancel}>
              <ArrowLeft size={24} className="text-white" />
            </button>
            <h1 className="text-xl font-bold text-white">Add Vehicle to Group</h1>
          </div>
        )}

        {/* Success Content */}
        <div className="p-6 flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md mx-auto">
            <CardContent className="p-6 text-center">
              <CheckCircle className="mx-auto mb-4 h-16 w-16 text-[#009639]" />
              <h2 className="text-2xl font-bold text-[#009639] mb-2">Success!</h2>
              <p className="text-[#333333] mb-6">
                Your vehicle has been successfully added to the group.
              </p>
              <Button 
                onClick={() => {
                  if (preselectedCompanyId) {
                    navigateToGroupDetails(preselectedCompanyId.toString());
                  } else {
                    handleCancel();
                  }
                }} 
                className="bg-[#009639] hover:bg-[#007A2F] w-full"
              >
                Continue
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className={hideHeader ? "bg-[#f5f5f5]" : "min-h-screen bg-[#f5f5f5]"}>
      {/* Header */}
      {!hideHeader && (
        <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
          <button className="mr-4" onClick={handleCancel}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Add Vehicle to Group</h1>
        </div>
      )}

      {/* Form Content */}
      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Vehicle Information */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* VIN Number */}
              <div>
                <Label htmlFor="vin_number" className="text-sm text-[#333333] font-medium">
                  VIN Number *
                </Label>
                <Input
                  id="vin_number"
                  name="vin_number"
                  value={formData.vin_number}
                  onChange={handleInputChange}
                  placeholder="Enter VIN number"
                  className="mt-1 uppercase border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  maxLength={17}
                  required
                />
              </div>

              {/* Make and Model */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="make_id" className="text-sm text-[#333333] font-medium">
                    Make *
                  </Label>
                  <Select
                    value={formData.make_id}
                    onValueChange={(value) => handleSelectChange("make_id", value)}
                    required
                  >
                    <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                      <SelectValue placeholder="Select make" />
                    </SelectTrigger>
                    <SelectContent>
                      {makes.map((make) => (
                        <SelectItem key={make.id} value={make.id.toString()}>
                          {make.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="model_id" className="text-sm text-[#333333] font-medium">
                    Model *
                  </Label>
                  <Select
                    value={formData.model_id}
                    onValueChange={(value) => handleSelectChange("model_id", value)}
                    disabled={!formData.make_id}
                    required
                  >
                    <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      {models.map((model) => (
                        <SelectItem key={model.id} value={model.id.toString()}>
                          {model.model} ({model.first_year || 'Unknown'})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Registration and Country */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="vehicle_registration" className="text-sm text-[#333333] font-medium">
                    Registration Number
                  </Label>
                  <Input
                    id="vehicle_registration"
                    name="vehicle_registration"
                    value={formData.vehicle_registration}
                    onChange={handleInputChange}
                    placeholder="e.g., ABC123GP"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>

                <div>
                  <Label htmlFor="country_of_registration" className="text-sm text-[#333333] font-medium">
                    Country of Registration
                  </Label>
                  <Input
                    id="country_of_registration"
                    name="country_of_registration"
                    value={formData.country_of_registration}
                    onChange={handleInputChange}
                    placeholder="e.g., South Africa"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>
              </div>

              {/* Year and Color */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="manufacturing_year" className="text-sm text-[#333333] font-medium">
                    Manufacturing Year
                  </Label>
                  <Input
                    id="manufacturing_year"
                    name="manufacturing_year"
                    type="number"
                    value={formData.manufacturing_year}
                    onChange={handleInputChange}
                    placeholder="e.g., 2020"
                    min="1900"
                    max={new Date().getFullYear() + 1}
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>

                <div>
                  <Label htmlFor="color" className="text-sm text-[#333333] font-medium">
                    Color
                  </Label>
                  <Input
                    id="color"
                    name="color"
                    value={formData.color}
                    onChange={handleInputChange}
                    placeholder="e.g., White, Black"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  />
                </div>
              </div>

              {/* Purchase Date */}
              <div>
                <Label htmlFor="purchase_date" className="text-sm text-[#333333] font-medium">
                  Purchase Date
                </Label>
                <Input
                  id="purchase_date"
                  name="purchase_date"
                  type="date"
                  value={formData.purchase_date}
                  onChange={handleInputChange}
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                />
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Images */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                <Camera className="h-5 w-5 text-[#009639]" />
                Vehicle Images
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-white rounded-xl p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-[#333333] font-medium">Vehicle Photos</h3>
                  <input
                    type="file"
                    ref={fileInputRef}
                    className="hidden"
                    accept="image/*"
                    multiple
                    onChange={handleImageUpload}
                    disabled={isUploadingImages}
                  />
                  <button
                    type="button"
                    className="bg-[#009639] text-white p-2 rounded-full shadow-sm disabled:opacity-50"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploadingImages}
                  >
                    {isUploadingImages ? <Loader2 size={20} className="animate-spin" /> : <Upload size={20} />}
                  </button>
                </div>

                {vehicleImages.length > 0 ? (
                  <div className="grid grid-cols-3 gap-2">
                    {vehicleImages.map((image, index) => (
                      <div
                        key={index}
                        className="relative h-24 bg-[#f2f2f2] rounded-lg overflow-hidden"
                      >
                        <Image
                          src={image.previewUrl}
                          alt={`Vehicle photo ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                        <button
                          type="button"
                          className="absolute top-1 right-1 bg-white rounded-full p-1 disabled:opacity-50"
                          onClick={() => handleRemoveImage(index)}
                          disabled={isUploadingImages}
                        >
                          <X size={14} className="text-red-500" />
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div
                    className="border-2 border-dashed border-[#d6d9dd] rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload size={32} className="text-[#009639] mb-2" />
                    <p className="text-[#797879] text-center">
                      Upload photos of your vehicle (optional)
                    </p>
                  </div>
                )}

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                  <p className="text-xs text-blue-800">
                    <strong>Photo Tips:</strong> Include exterior shots from all angles, interior photos, and any special features.
                    Good lighting helps showcase your vehicle better.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Group Selection */}
          <Card className="shadow-sm">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                <Users className="h-5 w-5 text-[#009639]" />
                Select Group
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="company_id" className="text-sm text-[#333333] font-medium">
                  Group *
                </Label>
                <Select
                  value={formData.company_id}
                  onValueChange={(value) => handleSelectChange("company_id", value)}
                  disabled={!!preselectedCompanyId}
                  required
                >
                  <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                    <SelectValue placeholder="Select a group" />
                  </SelectTrigger>
                  <SelectContent>
                    {companies.map((company) => (
                      <SelectItem key={company.id} value={company.id.toString()}>
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Ownership Disclaimer */}
          {formData.company_id && (
            <Card className="shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
                  <Shield className="h-5 w-5 text-[#009639]" />
                  {OWNERSHIP_DISCLAIMER.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Disclaimer Content */}
                <Card className="bg-yellow-50 border-yellow-200">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {OWNERSHIP_DISCLAIMER.content.map((item, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <span className="text-yellow-600 font-bold mt-1">•</span>
                          <p className="text-sm text-[#333333]">{item}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Checkboxes */}
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="ownership_disclaimer"
                      checked={formData.ownership_disclaimer_accepted}
                      onCheckedChange={(checked) => 
                        handleCheckboxChange("ownership_disclaimer_accepted", checked as boolean)
                      }
                    />
                    <Label htmlFor="ownership_disclaimer" className="text-sm text-[#333333] leading-5">
                      I have read and accept the ownership disclaimer and co-ownership agreement terms *
                    </Label>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox
                      id="ownership_confirmation"
                      checked={formData.ownership_confirmation}
                      onCheckedChange={(checked) => 
                        handleCheckboxChange("ownership_confirmation", checked as boolean)
                      }
                    />
                    <Label htmlFor="ownership_confirmation" className="text-sm text-[#333333] leading-5">
                      I confirm that I am the legal owner of this vehicle *
                    </Label>
                  </div>
                </div>

                {/* Ownership Statement */}
                <div>
                  <Label htmlFor="owner_statement" className="text-sm text-[#333333] font-medium">
                    Ownership Statement *
                  </Label>
                  <Textarea
                    id="owner_statement"
                    name="owner_statement"
                    value={formData.owner_statement}
                    onChange={handleInputChange}
                    placeholder="Please write a brief statement confirming your ownership of this vehicle and your intent to enter into a co-ownership agreement..."
                    className="mt-1 min-h-20 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                    required
                  />
                  <p className="text-xs text-[#797879] mt-1">
                    This statement will be recorded for legal purposes.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert className="border-red-500 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-700">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleCancel}
              className="order-2 sm:order-1 border-gray-200 text-[#333333] hover:bg-gray-50"
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              disabled={
                !formData.vin_number ||
                !formData.make_id ||
                !formData.model_id ||
                !formData.company_id ||
                !formData.ownership_disclaimer_accepted ||
                !formData.ownership_confirmation ||
                !formData.owner_statement.trim() ||
                submitting
              }
              className="order-1 sm:order-2 bg-[#009639] hover:bg-[#007A2F] flex-1 sm:flex-none sm:min-w-[200px]"
            >
              {submitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding Vehicle...
                </>
              ) : (
                "Add Vehicle to Group"
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default function VehicleToGroupForm(props: VehicleToGroupFormProps) {
  return (
    <VehicleToGroupFormContent {...props} />
  );
} 