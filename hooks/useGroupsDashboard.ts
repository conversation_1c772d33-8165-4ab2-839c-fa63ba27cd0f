import useSWR from 'swr';
import { useMemo } from 'react';
import { fetchUserAttributes } from "aws-amplify/auth";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { getUserGroups, getCommunityStats } from "@/drizzle-actions/groups-community";
import type { CommunityGroup, GroupStats } from "@/types/community";

interface GroupsDashboardData {
  groups: CommunityGroup[];
  stats: GroupStats;
}

const fetchGroupsDashboardData = async (): Promise<GroupsDashboardData> => {
  const attributes = await fetchUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  
  if (!dbId || !externalId) {
    throw new Error("Missing user information");
  }

  // Fetch user's groups and community stats in parallel
  const [userGroups, communityStats] = await Promise.all([
    getUserGroups(),
    getCommunityStats()
  ]);

  return {
    groups: userGroups,
    stats: communityStats,
  };
};

export function useGroupsDashboard() {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  
  // Only fetch data when user is authenticated
  const shouldFetch = authStatus === 'authenticated' && user;
  
  const { data, error, isLoading, mutate } = useSWR<GroupsDashboardData>(
    shouldFetch ? 'groups-dashboard' : null,
    fetchGroupsDashboardData,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 30000, // Refresh every 30 seconds
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  // Memoize arrays to prevent unnecessary re-renders
  const groups = useMemo(() => data?.groups || [], [data?.groups]);
  const stats = useMemo(() => data?.stats || {
    totalGroups: 0,
    totalMembers: 0,
    totalVehicles: 0,
    activeBookings: 0
  }, [data?.stats]);

  return {
    data,
    groups,
    stats,
    isLoading: shouldFetch ? isLoading : false,
    error,
    refetch: mutate,
    isAuthenticated: shouldFetch,
  };
} 