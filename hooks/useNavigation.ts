import { useNavigationStore } from '@/lib/navigation/navigationStore';

export function useNavigation() {
  const { navigate, goBack, replace, reset, canGoBack } = useNavigationStore();

  // Main navigation methods
  const navigateToHome = () => navigate('home');
  const navigateToVehicleDashboard = () => navigate('vehicle-dashboard');
  const navigateToProfile = (section?: string) => navigate('profile', { section });
  const navigateToCommunity = () => navigate('community');
  const navigateToOpportunities = () => navigate('opportunities');
  const navigateToTasks = () => navigate('tasks');
  const navigateToTaskDetail = (taskId: string) => navigate('task-detail', { taskId });
  
  // Detail navigation methods
  const navigateToVehicleDetails = (vehicleId: string) => {
    navigate('vehicle-status', { vehicleId });
  };

  const navigateToBookingCalendar = (vehicleId: string) => {
    navigate('booking-calendar', { vehicleId });
  };

  const navigateToBookingCalendarNew = (vehicleId: string) => {
    navigate('booking-calendar-new', { vehicleId });
  };

  const navigateToBookingConfirmation = (bookingId: string) => {
    navigate('booking-confirmation', { bookingId });
  };

  const navigateToBookingDetails = (bookingId: string, bookingData?: any) => {
    navigate('booking-details', { bookingId, bookingData });
  };

  const navigateToGroupDetails = (groupId: string) => {
    navigate('group-details', { groupId });
  };

  const navigateToGroupFinances = (groupId: string) => {
    navigate('group-finances', { groupId });
  };

  const navigateToGroupSettings = (groupId: string) => {
    navigate('group-settings', { groupId });
  };

  const navigateToGroupChat = (groupId: string) => {
    navigate('group-chat', { groupId });
  };

  const navigateToMyGroups = () => navigate('my-groups');

  const navigateToMemberDetails = (memberId: string) => {
    navigate('member-details', { memberId });
  };

  const navigateToMemberManagement = (groupId: string) => {
    navigate('member-management', { groupId });
  };

  const navigateToMaintenanceSchedule = () => navigate('maintenance-schedule');
  
  const navigateToMaintenanceDetails = (maintenanceId: string) => {
    navigate('maintenance-details', { maintenanceId });
  };

  const navigateToScheduleMaintenance = (vehicleId: string) => {
    navigate('schedule-maintenance', { vehicleId });
  };

  const navigateToEditMaintenance = (maintenanceId: string) => {
    navigate('edit-maintenance', { maintenanceId });
  };

  const navigateToVehicleStatus = (vehicleId: string) => {
    navigate('vehicle-status', { vehicleId });
  };

  const navigateToVehicleHandover = (vehicleId: string) => {
    navigate('vehicle-handover', { vehicleId });
  };

  const navigateToTracking = (trackingId: string) => {
    navigate('tracking', { trackingId });
  };

  const navigateToPayment = (paymentId: string) => {
    navigate('payment', { paymentId });
  };

  const navigateToFractionPurchase = (vehicleId: string) => {
    navigate('fraction-purchase', { vehicleId });
  };

  const navigateToListVehicle = () => navigate('list-vehicle');
  
  const navigateToCreateGroup = () => navigate('create-group');
  
  const navigateToVehicleSearch = () => navigate('vehicle-search');
  
  const navigateToNotifications = () => navigate('notifications');
  
  const navigateToHelp = () => navigate('help');
  
  const navigateToPrivacy = () => navigate('privacy');

  const navigateToUploadDocument = (externalId: string) => {
    navigate('upload-document', { externalId });
  };

  const navigateToComplianceDashboard = () => navigate('compliance-dashboard');
  
  const navigateToDisputeResolution = () => navigate('dispute-resolution');
  
  const navigateToFeedback = () => navigate('feedback');
  
  const navigateToLegalEntityFormation = () => navigate('legal-entity-formation');

  const navigateToListingManagement = (listingId: string) => {
    navigate('listing-management', { listingId });
  };

  const navigateToListingDetails = (listingId: string) => {
    navigate('listing-details', { listingId });
  };

  return {
    // Core navigation
    navigate,
    goBack,
    replace,
    reset,
    canGoBack,
    
    // Main screens
    navigateToHome,
    navigateToVehicleDashboard,
    navigateToProfile,
    navigateToCommunity,
    navigateToOpportunities,
    navigateToTasks,
    navigateToTaskDetail,
    
    // Detail screens
    navigateToVehicleDetails,
    navigateToBookingCalendar,
    navigateToBookingCalendarNew,
    navigateToBookingConfirmation,
    navigateToBookingDetails,
    navigateToGroupDetails,
    navigateToGroupFinances,
    navigateToGroupSettings,
    navigateToGroupChat,
    navigateToMyGroups,
    navigateToMemberDetails,
    navigateToMemberManagement,
    navigateToMaintenanceSchedule,
    navigateToMaintenanceDetails,
    navigateToScheduleMaintenance,
    navigateToEditMaintenance,
    navigateToVehicleStatus,
    navigateToVehicleHandover,
    navigateToTracking,
    navigateToPayment,
    navigateToFractionPurchase,
    navigateToListVehicle,
    navigateToCreateGroup,
    navigateToVehicleSearch,
    navigateToNotifications,
    navigateToHelp,
    navigateToPrivacy,
    navigateToUploadDocument,
    navigateToComplianceDashboard,
    navigateToDisputeResolution,
    navigateToFeedback,
    navigateToLegalEntityFormation,
    navigateToListingManagement,
    navigateToListingDetails,
  };
} 