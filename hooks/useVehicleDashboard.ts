import useSWR from 'swr';
import { useMemo } from 'react';
import { fetchUserAttributes } from "aws-amplify/auth";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { getCompanyOwnershipByParty, getVehiclesByParties } from "@/drizzle-actions/vehicle-dashboard";
import type { VehicleReadWithListings } from "@/types/vehicles";
import type { CompanyOwnershipReadWithRelations } from "@/types/company-ownerships";

interface VehicleDashboardData {
  vehicles: VehicleReadWithListings[];
  ownerships: CompanyOwnershipReadWithRelations[];
  partyId: number;
}

const fetchVehicleDashboardData = async (): Promise<VehicleDashboardData> => {
  const attributes = await fetchUserAttributes();
  const { ["custom:db_id"]: dbId, sub: externalId } = attributes || {};
  
  if (!dbId || !externalId) {
    throw new Error("Missing user information");
  }

  const userPartyId = +dbId;

  const fetchedOwnerships = await getCompanyOwnershipByParty(userPartyId);
  
  const companyPartyIds = fetchedOwnerships
    .map((ownership) => ownership.company?.party_id)
    .filter((id): id is number => typeof id === "number");
  
  const partyIds: number[] = [...new Set([userPartyId, ...companyPartyIds])];
  const fetchedVehicles = await getVehiclesByParties(partyIds);

  return {
    vehicles: fetchedVehicles,
    ownerships: fetchedOwnerships,
    partyId: userPartyId,
  };
};

export function useVehicleDashboard() {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  
  // Only fetch data when user is authenticated
  const shouldFetch = authStatus === 'authenticated' && user;
  
  const { data, error, isLoading, mutate } = useSWR<VehicleDashboardData>(
    shouldFetch ? 'vehicle-dashboard' : null,
    fetchVehicleDashboardData,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 30000, // Refresh every 30 seconds
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  // Memoize arrays to prevent unnecessary re-renders
  const vehicles = useMemo(() => data?.vehicles || [], [data?.vehicles]);
  const ownerships = useMemo(() => data?.ownerships || [], [data?.ownerships]);

  return {
    data,
    vehicles,
    ownerships,
    partyId: data?.partyId || 0,
    isLoading: shouldFetch ? isLoading : false,
    error,
    refetch: mutate,
    isAuthenticated: shouldFetch,
  };
} 