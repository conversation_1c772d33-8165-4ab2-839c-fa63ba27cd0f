"use client";

import { useCallback } from 'react';
import * as Sen<PERSON> from '@sentry/nextjs';

interface UseErrorHandlerOptions {
  showToast?: boolean;
  fallbackToHome?: boolean;
  logToConsole?: boolean;
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    showToast = true,
    fallbackToHome = false,
    logToConsole = true
  } = options;

  const handleError = useCallback((error: Error | unknown, context?: string) => {
    // Normalize error
    const normalizedError = error instanceof Error ? error : new Error(String(error));
    
    if (logToConsole) {
      console.error(`Error ${context ? `in ${context}` : ''}:`, normalizedError);
    }

    // Log to Sentry
    Sentry.withScope((scope) => {
      if (context) {
        scope.setTag('context', context);
      }
      scope.setLevel('error');
      Sentry.captureException(normalizedError);
    });

    // Handle specific error types
    const isChunkError = normalizedError.message?.includes('Loading chunk') ||
                        normalizedError.message?.includes('Failed to fetch') ||
                        normalizedError.name === 'ChunkLoadError';

    const isNetworkError = normalizedError.message?.includes('Failed to fetch') ||
                          normalizedError.message?.includes('NetworkError') ||
                          normalizedError.name === 'NetworkError';

    if (isChunkError) {
      // For chunk errors, offer to reload
      const shouldReload = confirm(
        'We\'re experiencing some trouble loading the page. Would you like to refresh to continue?'
      );
      if (shouldReload) {
        window.location.reload();
      }
      return;
    }

    if (isNetworkError) {
      if (showToast) {
        // You could integrate with a toast library here
        console.warn('We\'re having trouble connecting to our servers. Please check your internet connection.');
      }
      return;
    }

    // For other errors, show generic message
    if (showToast) {
      console.warn('We\'re experiencing some technical difficulties. Our team is working to resolve this.');
    }

    // Fallback to home if requested
    if (fallbackToHome) {
      setTimeout(() => {
        window.location.href = '/home';
      }, 2000);
    }
  }, [showToast, fallbackToHome, logToConsole]);

  const handleAsyncError = useCallback(async (
    asyncFn: () => Promise<any>,
    context?: string
  ): Promise<any> => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error, context);
      return null;
    }
  }, [handleError]);

  const createErrorHandler = useCallback((context?: string) => {
    return (error: Error | unknown) => handleError(error, context);
  }, [handleError]);

  return {
    handleError,
    handleAsyncError,
    createErrorHandler,
  };
}

// Utility function for wrapping components with error handling
export function withErrorHandler<T extends object>(
  Component: React.ComponentType<T>,
  errorContext?: string
) {
  return function WrappedComponent(props: T) {
    const { createErrorHandler } = useErrorHandler();
    
    try {
      return <Component {...props} />;
    } catch (error) {
      createErrorHandler(errorContext)(error);
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <div className="w-16 h-10 bg-[#009639] rounded-lg flex items-center justify-center mx-auto mb-4">
              <span className="text-white font-bold">Poolly</span>
            </div>
            <p className="text-gray-600 mb-6">
              We're experiencing some technical difficulties. Our team is working to resolve this.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-[#009639] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#007A2F] transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }
  };
} 