import useS<PERSON> from 'swr';
import { useMemo } from 'react';
import { 
  getGroupDetails, 
  getGroupVehiclesWithStatus, 
  getGroupInvitations, 
  cancelInvitation 
} from "@/drizzle-actions/groups-community";
import type { GroupDetails, GroupVehicle } from "@/types/community";

interface GroupDetailsData {
  group: GroupDetails | null;
  vehicles: GroupVehicle[];
  invitations: GroupInvitation[];
}

interface GroupInvitation {
  id: number;
  email: string;
  name: string;
  firstName: string | null;
  lastName: string | null;
  fraction: number;
  status: string;
  sentAt: string | null;
}

const fetchGroupDetailsData = async (groupId: number): Promise<GroupDetailsData> => {
  // Fetch group details and invitations in parallel
  const [groupDetails, groupInvitations] = await Promise.all([
    getGroupDetails(groupId),
    getGroupInvitations(groupId)
  ]);

  return {
    group: groupDetails,
    vehicles: groupDetails?.vehicles || [],
    invitations: groupInvitations,
  };
};

export function useGroupDetails(groupId: number) {
  const { data, error, isLoading, mutate } = useSWR<GroupDetailsData>(
    groupId ? `group-details-${groupId}` : null,
    () => fetchGroupDetailsData(groupId),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 30000, // Refresh every 30 seconds
      errorRetryCount: 3,
      errorRetryInterval: 5000,
    }
  );

  // Function to refresh group data
  const refreshGroupData = async () => {
    try {
      const updatedGroup = await getGroupDetails(groupId);
      if (updatedGroup && data) {
        mutate({
          ...data,
          group: updatedGroup,
          vehicles: updatedGroup.vehicles
        }, false);
      }
    } catch (error) {
      console.error("Failed to refresh group details:", error);
    }
  };

  // Function to fetch vehicles with updated status
  const refreshVehiclesWithStatus = async () => {
    try {
      const vehiclesWithStatus = await getGroupVehiclesWithStatus(groupId);
      // Transform to match GroupVehicle interface and remove duplicates
      const transformedVehicles = vehiclesWithStatus.map(vehicle => ({
        ...vehicle,
        registration: vehicle.registration || "Unknown",
        status: vehicle.status as 'available' | 'in-use' | 'maintenance',
        year: vehicle.year || undefined,
        color: vehicle.color || undefined
      }));
      
      // Remove duplicates based on vehicle ID
      const uniqueVehicles = transformedVehicles.filter((vehicle, index, self) => 
        index === self.findIndex(v => v.id === vehicle.id)
      );

      if (data) {
        mutate({
          ...data,
          vehicles: uniqueVehicles
        }, false);
      }
      
      return uniqueVehicles;
    } catch (error) {
      console.error("Failed to fetch vehicles with status:", error);
      return data?.vehicles || [];
    }
  };

  // Function to cancel invitation
  const handleCancelInvitation = async (invitationId: number) => {
    try {
      const result = await cancelInvitation(invitationId);
      if (result.success && data) {
        const updatedInvitations = data.invitations.filter(inv => inv.id !== invitationId);
        mutate({
          ...data,
          invitations: updatedInvitations
        }, false);
        return { success: true };
      } else {
        return { success: false, message: result.message || "Failed to cancel invitation" };
      }
    } catch (error) {
      console.error("Error cancelling invitation:", error);
      return { success: false, message: "Failed to cancel invitation" };
    }
  };

  // Memoize data to prevent unnecessary re-renders
  const group = useMemo(() => data?.group || null, [data?.group]);
  const vehicles = useMemo(() => data?.vehicles || [], [data?.vehicles]);
  const invitations = useMemo(() => data?.invitations || [], [data?.invitations]);

  return {
    data,
    group,
    vehicles,
    invitations,
    isLoading: groupId ? isLoading : false,
    error,
    refetch: mutate,
    refreshGroupData,
    refreshVehiclesWithStatus,
    handleCancelInvitation,
  };
} 