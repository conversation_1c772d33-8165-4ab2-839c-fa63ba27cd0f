import useSWR from 'swr';
import { useMemo } from 'react';
import { deprecated_getBookingByIdDrizzle } from '@/drizzle-actions/bookings';
import { getIndividualByPartyId } from '@/actions/individuals';
import { getContactPointsByPartyId } from '@/actions/contact-points';
import type { deprecated_BookingRead } from '@/types/bookings';
import type { IndividualRead } from '@/types/individuals';
import type { ContactPointRead } from '@/types/contact-points';

interface BookingDetailsData {
  booking: deprecated_BookingRead | null;
  individual: IndividualRead | null;
  contacts: ContactPointRead[];
}

const fetchBookingDetails = async (bookingId: number): Promise<BookingDetailsData> => {
  try {
    console.log('Fetching booking with ID:', bookingId);

    // Fetch the booking directly from the database using Drizzle
    const booking = await deprecated_getBookingByIdDrizzle(bookingId);
    if (!booking) {
      throw new Error(`Booking with ID ${bookingId} not found`);
    }

    console.log('Booking found:', {
      id: booking.id,
      reference: booking.reference,
      vehicle_id: booking.vehicle_id,
      party_id: booking.party_id,
      status: booking.status,
      start_datetime: booking.start_datetime,
      end_datetime: booking.end_datetime,
      total_price: booking.total_price
    });

    // Only fetch individual and contacts for the "Booked By" section
    const [individualResult, contactsResult] = await Promise.allSettled([
      getIndividualByPartyId(booking.party_id),
      getContactPointsByPartyId(booking.party_id),
    ]);

    // Handle results gracefully - if they fail, we still show the booking
    const individual = individualResult.status === 'fulfilled' ? individualResult.value : null;
    const contacts = contactsResult.status === 'fulfilled' ? (contactsResult.value || []) : [];

    // Log any issues but don't throw errors
    if (individualResult.status === 'rejected') {
      console.warn('Could not fetch individual data:', individualResult.reason);
    }
    if (contactsResult.status === 'rejected') {
      console.warn('Could not fetch contacts:', contactsResult.reason);
    }

    return {
      booking,
      individual,
      contacts,
    };
  } catch (error) {
    console.error('Error fetching booking details:', error);
    throw error;
  }
};

export function useBookingDetails(bookingId: number) {
  console.log('useBookingDetails called with bookingId:', bookingId);
  
  const { data, error, isLoading, mutate } = useSWR<BookingDetailsData>(
    bookingId && bookingId > 0 ? ['booking-details', bookingId] : null,
    () => {
      console.log('SWR fetcher called for bookingId:', bookingId);
      return fetchBookingDetails(bookingId);
    },
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      errorRetryCount: 3,
      errorRetryInterval: 1000,
      keepPreviousData: true,
      dedupingInterval: 30000, // 30 seconds
    }
  );

  console.log('useBookingDetails state:', { 
    bookingId, 
    isLoading, 
    hasError: !!error, 
    errorMessage: error?.message,
    hasBooking: !!data?.booking,
    bookingReference: data?.booking?.reference
  });

  const memoizedData = useMemo(() => {
    if (!data) return {
      booking: null,
      individual: null,
      contacts: [],
    };

    return data;
  }, [data]);

  const refreshBookingDetails = () => {
    console.log('Refreshing booking details for bookingId:', bookingId);
    mutate();
  };

  return {
    ...memoizedData,
    isLoading,
    error,
    refreshBookingDetails,
  };
} 