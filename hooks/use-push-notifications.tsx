'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from './use-current-user';

function urlB64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export function usePushNotifications() {
  const { userId, isLoading: userLoading } = useCurrentUser();
  const [isSupported, setIsSupported] = useState(false);
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const supported = 'serviceWorker' in navigator && 'PushManager' in window;
      setIsSupported(supported);
      
      if (supported && userId && !userLoading) {
        checkExistingSubscription();
      }
    }
  }, [userId, userLoading]);

  const checkExistingSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const existingSubscription = await registration.pushManager.getSubscription();
      
      if (existingSubscription) {
        setSubscription(existingSubscription);
        setIsSubscribed(true);
      }
    } catch (err) {
      console.error('Error checking subscription:', err);
    }
  };

  const subscribe = async () => {
    if (!isSupported) {
      setError('Push notifications are not supported');
      return false;
    }

    if (!userId) {
      setError('User not authenticated');
      return false;
    }

    if (!process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY) {
      setError('Push notifications not configured');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      
      if (permission !== 'granted') {
        setError('Notification permission denied');
        return false;
      }

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready;

      // Subscribe to push notifications
      const newSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlB64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
        )
      });

      // Get device info
      const deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        screen: {
          width: screen.width,
          height: screen.height,
          colorDepth: screen.colorDepth
        }
      };

      // Send subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: newSubscription,
          userAgent: navigator.userAgent,
          deviceInfo
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save subscription');
      }

      setSubscription(newSubscription);
      setIsSubscribed(true);
      return true;

    } catch (err) {
      console.error('Subscription failed:', err);
      setError(err instanceof Error ? err.message : 'Subscription failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const unsubscribe = async () => {
    if (!subscription) return false;

    setIsLoading(true);
    setError(null);

    try {
      // Unsubscribe from push service
      await subscription.unsubscribe();

      // Remove from server
      const response = await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: subscription.endpoint
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.warn('Failed to remove subscription from server:', errorData.error);
        // Continue anyway since we unsubscribed from the push service
      }

      setSubscription(null);
      setIsSubscribed(false);
      return true;

    } catch (err) {
      console.error('Unsubscription failed:', err);
      setError(err instanceof Error ? err.message : 'Unsubscription failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const testNotification = async () => {
    if (!isSubscribed || !userId) {
      setError('Not subscribed to notifications');
      return false;
    }

    try {
      // For testing, we'll create a local notification
      if (Notification.permission === 'granted') {
        new Notification('Poolly Test', {
          body: 'Push notifications are working!',
          icon: '/icon-192x192.png',
          tag: 'test-notification'
        });
        return true;
      }
    } catch (err) {
      console.error('Test notification failed:', err);
      setError('Test notification failed');
      return false;
    }
  };

  return {
    isSupported,
    isSubscribed,
    isLoading,
    error,
    subscribe,
    unsubscribe,
    testNotification,
    canSubscribe: !userLoading && !!userId && isSupported,
    permission: typeof window !== 'undefined' ? Notification.permission : 'default'
  };
} 