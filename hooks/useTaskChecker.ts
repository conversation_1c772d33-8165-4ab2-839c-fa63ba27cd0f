"use client";

import { useState, useEffect } from "react";
import { useAuthenticator } from "@aws-amplify/ui-react";
import { checkPendingTasks } from "@/actions/tasks";
import { useUserAttributes } from "./useUserAttributes";
import { useNavigation } from "./useNavigation";

interface TaskCheckResult {
  isLoading: boolean;
  hasPendingTasks: boolean;
  hasGroupInvitations: boolean;
  pendingTasks: any[];
  error?: string;
}

export function useTaskChecker(): TaskCheckResult {
  const { authStatus } = useAuthenticator();
  const { attributes, isAuthenticated } = useUserAttributes();
  const { navigateToTasks } = useNavigation();
  
  const [isLoading, setIsLoading] = useState(false);
  const [hasPendingTasks, setHasPendingTasks] = useState(false);
  const [hasGroupInvitations, setHasGroupInvitations] = useState(false);
  const [pendingTasks, setPendingTasks] = useState<any[]>([]);
  const [error, setError] = useState<string>();
  const [hasChecked, setHasChecked] = useState(false);

  useEffect(() => {
    // Only check tasks when user is authenticated and we have user attributes
    if (!isAuthenticated || !attributes?.email || hasChecked) {
      return;
    }

    const checkUserTasks = async () => {
      setIsLoading(true);
      setError(undefined);

      try {
        const email = attributes.email;
        if (!email) {
          setError("No email address found");
          return;
        }
        
        const partyId = attributes["custom:db_id"] ? parseInt(attributes["custom:db_id"]) : undefined;

        const result = await checkPendingTasks(email, partyId);

        if (result.success) {
          setPendingTasks(result.tasks || []);
          setHasPendingTasks((result.tasks || []).length > 0);
          setHasGroupInvitations(result.hasGroupInvitations || false);

          // If there are group invitation tasks, automatically show the tasks screen
          if (result.hasGroupInvitations) {
            // Wait a moment to let the page load, then navigate
            setTimeout(() => {
              navigateToTasks();
            }, 1000);
          }
        } else {
          setError(result.error);
        }
      } catch (err) {
        console.error("Error checking tasks:", err);
        setError("Failed to check tasks");
      } finally {
        setIsLoading(false);
        setHasChecked(true);
      }
    };

    // Don't block the login process - check tasks asynchronously
    setTimeout(checkUserTasks, 500);
  }, [isAuthenticated, attributes, hasChecked, navigateToTasks]);

  // Reset check status when user logs out
  useEffect(() => {
    if (authStatus !== 'authenticated') {
      setHasChecked(false);
      setHasPendingTasks(false);
      setHasGroupInvitations(false);
      setPendingTasks([]);
      setError(undefined);
    }
  }, [authStatus]);

  return {
    isLoading,
    hasPendingTasks,
    hasGroupInvitations,
    pendingTasks,
    error
  };
} 