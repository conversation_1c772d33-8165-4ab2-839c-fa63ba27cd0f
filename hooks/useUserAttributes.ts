import useSWR from 'swr';
import { fetchUserAttributes, FetchUserAttributesOutput } from "aws-amplify/auth";
import { useAuthenticator } from '@aws-amplify/ui-react';
import { clearUserAttributesCache, getCachedUserAttributes } from "@/lib/userAttributes";

const fetchUserAttributesData = async (): Promise<FetchUserAttributesOutput> => {
  try {
    // Try to get cached attributes first
    const cachedAttributes = await fetchUserAttributes();
    if (cachedAttributes) {
      return cachedAttributes;
    }

    // Fetch fresh attributes if no cache
    const userAttributes = await fetchUserAttributes();
    return userAttributes;
  } catch (error) {
    console.error("Error fetching user attributes:", error);
    throw error;
  }
};

export function useUserAttributes() {
  const { user, authStatus } = useAuthenticator((context) => [context.user, context.authStatus]);
  
  // Only fetch data when user is authenticated
  const shouldFetch = authStatus === 'authenticated' && user;

  const { data, error, isLoading, mutate } = useSWR<FetchUserAttributesOutput>(
    shouldFetch ? 'user-attributes' : null,
    fetchUserAttributesData,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: true,
      refreshInterval: 300000, // Refresh every 5 minutes
      errorRetryCount: 3,
      errorRetryInterval: 5000,
      onError: (error) => {
        console.error("SWR Error fetching user attributes:", error);
        // Clear cache on error to force refresh next time
        clearUserAttributesCache();
      }
    }
  );

  // Helper function to get display name
  const getDisplayName = (attributes: FetchUserAttributesOutput | undefined) => {
    if (!attributes) return "User";
    
    const firstName = attributes["given_name"];
    const lastName = attributes["family_name"];
    const nickname = attributes["nickname"];
    const email = attributes["email"];

    if (firstName && lastName) {
      return `${firstName} ${lastName}`;
    } else if (nickname) {
      return nickname;
    } else if (firstName) {
      return firstName;
    } else if (email) {
      return email.split("@")[0];
    } else {
      return "User";
    }
  };

  return {
    attributes: data,
    displayName: getDisplayName(data),
    isLoading: shouldFetch ? isLoading : false,
    error,
    refetch: mutate,
    clearCache: () => {
      clearUserAttributesCache();
      mutate();
    },
    isAuthenticated: shouldFetch,
  };
} 