# Poolly PWA Push Notifications Implementation Plan

## Project Overview
Implement PWA capabilities with push notifications for <PERSON>ly's Next.js app router project using existing AWS Amplify auth, Neon PostgreSQL, and Drizzle ORM infrastructure.

## Current Project Context
- **Framework**: Next.js 15.2.4 with App Router
- **Auth**: AWS Amplify Cognito (already configured)
- **Database**: Neon PostgreSQL with Drizzle ORM
- **UI**: Radix UI components + Tailwind CSS
- **Monitoring**: Sentry integration
- **Config**: `next.config.mjs` (ESM format)

## Phase 1: PWA Setup

### 1.1 Install PWA Dependencies
```bash
npm install next-pwa web-push
npm install --save-dev @types/web-push
```

### 1.2 Configure PWA (next.config.mjs)
Update your existing `next.config.mjs`:

```javascript
import { withSentryConfig } from "@sentry/nextjs";
const withPWA = require('next-pwa')({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
  runtimeCaching: [
    {
      urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
      handler: 'CacheFirst',
      options: {
        cacheName: 'google-fonts',
        expiration: {
          maxEntries: 4,
          maxAgeSeconds: 365 * 24 * 60 * 60 // 365 days
        }
      }
    }
  ]
});

// Your existing user config loading logic...
let userConfig = undefined;
try {
  userConfig = await import("./v0-user-next.config.mjs");
} catch (e) {
  try {
    userConfig = await import("./v0-user-next.config");
  } catch (innerError) {
    // ignore error
  }
}

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your existing config...
  eslint: { ignoreDuringBuilds: true },
  typescript: { ignoreBuildErrors: true },
  images: { unoptimized: true },
  experimental: {
    webpackBuildWorker: true,
    parallelServerBuildTraces: true,
    parallelServerCompiles: true,
  },
  // Your existing webpack config...
};

// Your existing config merging logic...
if (userConfig) {
  const config = userConfig.default || userConfig;
  for (const key in config) {
    if (typeof nextConfig[key] === "object" && !Array.isArray(nextConfig[key])) {
      nextConfig[key] = { ...nextConfig[key], ...config[key] };
    } else {
      nextConfig[key] = config[key];
    }
  }
}

export default withSentryConfig(withPWA(nextConfig), {
  // Your existing Sentry config...
  org: "datagnu-79",
  project: "javascript-nextjs",
  silent: !process.env.CI,
  widenClientFileUpload: true,
  tunnelRoute: "/monitoring",
  disableLogger: true,
  automaticVercelMonitors: true,
});
```

### 1.3 Create PWA Manifest (public/manifest.json)
```json
{
  "name": "Poolly - Vehicle Co-ownership Platform",
  "short_name": "Poolly",
  "description": "Making vehicle ownership possible for everyone through co-ownership and sharing",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#0066cc",
  "orientation": "portrait-primary",
  "scope": "/",
  "categories": ["business", "finance", "transportation"],
  "icons": [
    {
      "src": "/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-96x96.png",
      "sizes": "96x96",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-128x128.png",
      "sizes": "128x128",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-144x144.png",
      "sizes": "144x144",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-152x152.png",
      "sizes": "152x152",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-384x384.png",
      "sizes": "384x384",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    }
  ],
  "shortcuts": [
    {
      "name": "My Groups",
      "short_name": "Groups",
      "description": "View your vehicle co-ownership groups",
      "url": "/home?tab=groups",
      "icons": [{ "src": "/icon-192x192.png", "sizes": "192x192" }]
    },
    {
      "name": "Book a Ride",
      "short_name": "Book",
      "description": "Book a vehicle from your groups",
      "url": "/ride-selection",
      "icons": [{ "src": "/icon-192x192.png", "sizes": "192x192" }]
    }
  ]
}
```

### 1.4 Update Root Layout (app/layout.tsx)
```typescript
import outputs from "@/amplify_outputs.json";
import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";
import { CookieStorage } from "aws-amplify/utils";
import type { Metadata, Viewport } from "next";
import { Poppins } from "next/font/google";
import type React from "react";
import ClientErrorHandler from "@/components/client-error-handler";
import "./globals.css";

Amplify.configure(outputs);
cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

export const metadata: Metadata = {
  title: "Poolly - Vehicle Co-ownership Platform",
  description: "Making vehicle ownership possible for everyone through co-ownership and sharing",
  generator: "v0.dev",
  applicationName: "Poolly",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "Poolly",
  },
  formatDetection: {
    telephone: false,
  },
  robots: {
    index: false,
    follow: false,
  },
  manifest: "/manifest.json",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  viewportFit: "cover",
  themeColor: "#0066cc",
};

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "700"],
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="mobile-web-app-capable" content="yes" />
      </head>
      <body className={`${poppins.className} bg-white`}>
        <ClientErrorHandler>
          {children}
        </ClientErrorHandler>
      </body>
    </html>
  );
}
```

## Phase 2: Database Schema Integration

### 2.1 Extend Existing Schema (db/schema.ts)
```typescript
import { pgTable, text, timestamp, integer, boolean, json } from "drizzle-orm/pg-core";

// Your existing formSubmissions table...
export const formSubmissions = pgTable("form_submissions", {
  // ... existing fields
});

// New push subscriptions table
export const pushSubscriptions = pgTable("push_subscriptions", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").notNull(), // AWS Cognito user ID
  partyId: integer("party_id"), // Reference to your party system
  endpoint: text("endpoint").notNull(),
  p256dhKey: text("p256dh_key").notNull(),
  authKey: text("auth_key").notNull(),
  expirationTime: timestamp("expiration_time"),
  userAgent: text("user_agent"),
  deviceInfo: json("device_info"), // Store device details
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Notification log for tracking
export const notificationLogs = pgTable("notification_logs", {
  id: text("id").primaryKey().$defaultFn(() => crypto.randomUUID()),
  userId: text("user_id").notNull(),
  subscriptionId: text("subscription_id").references(() => pushSubscriptions.id),
  title: text("title").notNull(),
  body: text("body").notNull(),
  payload: json("payload").notNull(),
  sentAt: timestamp("sent_at").defaultNow().notNull(),
  deliveryStatus: text("delivery_status", { 
    enum: ["pending", "sent", "failed", "expired"] 
  }).default("pending"),
  errorMessage: text("error_message"),
  clickedAt: timestamp("clicked_at"),
});

export type PushSubscription = typeof pushSubscriptions.$inferSelect;
export type InsertPushSubscription = typeof pushSubscriptions.$inferInsert;
export type NotificationLog = typeof notificationLogs.$inferSelect;
export type InsertNotificationLog = typeof notificationLogs.$inferInsert;
```

### 2.2 Generate Migration
```bash
npx drizzle-kit generate
npx drizzle-kit push
```

## Phase 3: VAPID Keys Setup

### 3.1 Generate VAPID Keys
```bash
npx web-push generate-vapid-keys
```

### 3.2 Environment Variables
Add to your existing `.env.local`:
```bash
# Push Notifications
VAPID_PUBLIC_KEY=your_generated_public_key
VAPID_PRIVATE_KEY=your_generated_private_key
VAPID_EMAIL=mailto:<EMAIL>
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_generated_public_key

# Notification Settings
NEXT_PUBLIC_NOTIFICATION_TYPES=booking,group,maintenance,financial,compliance
```

## Phase 4: Server-Side Implementation

### 4.1 Web Push Utility (lib/webpush.ts)
```typescript
import webpush from 'web-push';

if (!process.env.VAPID_EMAIL || !process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || !process.env.VAPID_PRIVATE_KEY) {
  throw new Error('VAPID environment variables are required');
}

webpush.setVapidDetails(
  process.env.VAPID_EMAIL,
  process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
  process.env.VAPID_PRIVATE_KEY
);

export { webpush };

export interface PoollyNotificationPayload {
  title: string;
  body: string;
  type: 'booking' | 'group' | 'maintenance' | 'financial' | 'compliance' | 'general';
  icon?: string;
  badge?: string;
  tag?: string;
  url?: string;
  groupId?: string;
  vehicleId?: string;
  bookingId?: string;
  actions?: Array<{
    action: string;
    title: string;
    icon?: string;
  }>;
  data?: Record<string, any>;
}

export interface PushSubscriptionData {
  endpoint: string;
  keys: {
    p256dh: string;
    auth: string;
  };
}
```

### 4.2 Push Notification Actions (actions/push-notifications.ts)
Following your existing action patterns:

```typescript
import { db } from '@/db';
import { pushSubscriptions, notificationLogs } from '@/db/schema';
import { eq, and, inArray } from 'drizzle-orm';
import { webpush, type PoollyNotificationPayload, type PushSubscriptionData } from '@/lib/webpush';

export async function saveSubscription(
  userId: string,
  partyId: number | undefined,
  subscription: PushSubscriptionData,
  userAgent?: string,
  deviceInfo?: any
) {
  try {
    const result = await db.insert(pushSubscriptions).values({
      userId,
      partyId,
      endpoint: subscription.endpoint,
      p256dhKey: subscription.keys.p256dh,
      authKey: subscription.keys.auth,
      userAgent,
      deviceInfo,
      updatedAt: new Date(),
    }).onConflictDoUpdate({
      target: pushSubscriptions.endpoint,
      set: {
        userId,
        partyId,
        p256dhKey: subscription.keys.p256dh,
        authKey: subscription.keys.auth,
        userAgent,
        deviceInfo,
        isActive: true,
        updatedAt: new Date(),
      }
    }).returning();
    
    return { success: true, subscription: result[0] };
  } catch (error) {
    console.error('Error saving subscription:', error);
    return { success: false, error: 'Failed to save subscription' };
  }
}

export async function getUserSubscriptions(userId: string) {
  return await db.select()
    .from(pushSubscriptions)
    .where(and(
      eq(pushSubscriptions.userId, userId),
      eq(pushSubscriptions.isActive, true)
    ));
}

export async function getGroupMemberSubscriptions(userIds: string[]) {
  return await db.select()
    .from(pushSubscriptions)
    .where(and(
      inArray(pushSubscriptions.userId, userIds),
      eq(pushSubscriptions.isActive, true)
    ));
}

export async function removeSubscription(userId: string, endpoint: string) {
  await db.update(pushSubscriptions)
    .set({ isActive: false, updatedAt: new Date() })
    .where(and(
      eq(pushSubscriptions.userId, userId),
      eq(pushSubscriptions.endpoint, endpoint)
    ));
}

export async function sendNotificationToUser(
  userId: string, 
  payload: PoollyNotificationPayload
) {
  const subscriptions = await getUserSubscriptions(userId);
  
  const results = await Promise.allSettled(
    subscriptions.map(async (sub) => {
      const pushSubscription: PushSubscriptionData = {
        endpoint: sub.endpoint,
        keys: {
          p256dh: sub.p256dhKey,
          auth: sub.authKey,
        }
      };
      
      try {
        // Log notification attempt
        const logEntry = await db.insert(notificationLogs).values({
          userId,
          subscriptionId: sub.id,
          title: payload.title,
          body: payload.body,
          payload: payload as any,
          deliveryStatus: 'pending',
        }).returning();
        
        await webpush.sendNotification(
          pushSubscription,
          JSON.stringify({
            ...payload,
            icon: payload.icon || '/icon-192x192.png',
            badge: payload.badge || '/icon-192x192.png',
          })
        );
        
        // Update log as sent
        await db.update(notificationLogs)
          .set({ deliveryStatus: 'sent' })
          .where(eq(notificationLogs.id, logEntry[0].id));
        
        return { success: true, endpoint: sub.endpoint };
      } catch (error: any) {
        console.error('Push notification failed:', error);
        
        // Update log with error
        await db.update(notificationLogs)
          .set({ 
            deliveryStatus: 'failed',
            errorMessage: error.message 
          })
          .where(eq(notificationLogs.id, logEntry[0].id));
        
        // Handle expired subscriptions
        if (error.statusCode === 410) {
          await db.update(pushSubscriptions)
            .set({ isActive: false })
            .where(eq(pushSubscriptions.endpoint, sub.endpoint));
        }
        
        return { success: false, endpoint: sub.endpoint, error };
      }
    })
  );
  
  return results;
}

// Poolly-specific notification helpers
export async function sendBookingNotification(
  userId: string,
  bookingId: string,
  type: 'confirmed' | 'reminder' | 'cancelled',
  vehicleName: string
) {
  const payload: PoollyNotificationPayload = {
    title: type === 'confirmed' ? 'Booking Confirmed' : 
           type === 'reminder' ? 'Upcoming Booking' : 'Booking Cancelled',
    body: `${vehicleName} - ${type === 'confirmed' ? 'Your booking is confirmed' : 
           type === 'reminder' ? 'Your booking starts soon' : 'Your booking has been cancelled'}`,
    type: 'booking',
    url: `/booking-details/${bookingId}`,
    bookingId,
    tag: `booking-${bookingId}`,
  };
  
  return await sendNotificationToUser(userId, payload);
}

export async function sendGroupInviteNotification(
  userId: string,
  groupName: string,
  inviterName: string,
  groupId: string
) {
  const payload: PoollyNotificationPayload = {
    title: 'Group Invitation',
    body: `${inviterName} invited you to join "${groupName}"`,
    type: 'group',
    url: `/group-details/${groupId}`,
    groupId,
    actions: [
      { action: 'accept', title: 'Accept' },
      { action: 'decline', title: 'Decline' }
    ],
  };
  
  return await sendNotificationToUser(userId, payload);
}
```

### 4.3 API Routes Integration

#### app/api/push/subscribe/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from 'aws-amplify/auth/server';
import { runWithAmplifyServerContext } from '@/lib/amplifyServerUtils';
import { saveSubscription } from '@/actions/push-notifications';
import { getCachedUserAttributes } from '@/lib/userAttributes';

export async function POST(request: NextRequest) {
  return runWithAmplifyServerContext({
    nextServerContext: { request },
    operation: async (contextSpec) => {
      try {
        const user = await getCurrentUser(contextSpec);
        if (!user) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        
        const { subscription, userAgent, deviceInfo } = await request.json();
        
        if (!subscription || !subscription.endpoint || !subscription.keys) {
          return NextResponse.json(
            { error: 'Invalid subscription object' },
            { status: 400 }
          );
        }
        
        // Get party ID from user attributes
        const attributes = await getCachedUserAttributes();
        const partyId = attributes["custom:db_id"] 
          ? parseInt(attributes["custom:db_id"]) 
          : undefined;
        
        const result = await saveSubscription(
          user.userId, 
          partyId,
          subscription, 
          userAgent,
          deviceInfo
        );
        
        if (result.success) {
          return NextResponse.json({ success: true });
        } else {
          return NextResponse.json(
            { error: result.error },
            { status: 500 }
          );
        }
      } catch (error) {
        console.error('Subscription error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    }
  });
}
```

#### app/api/push/unsubscribe/route.ts
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from 'aws-amplify/auth/server';
import { runWithAmplifyServerContext } from '@/lib/amplifyServerUtils';
import { removeSubscription } from '@/actions/push-notifications';

export async function POST(request: NextRequest) {
  return runWithAmplifyServerContext({
    nextServerContext: { request },
    operation: async (contextSpec) => {
      try {
        const user = await getCurrentUser(contextSpec);
        if (!user) {
          return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }
        
        const { endpoint } = await request.json();
        
        await removeSubscription(user.userId, endpoint);
        
        return NextResponse.json({ success: true });
      } catch (error) {
        console.error('Unsubscription error:', error);
        return NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
      }
    }
  });
}
```

## Phase 5: Client-Side Implementation

### 5.1 Push Notifications Hook (hooks/use-push-notifications.tsx)
```typescript
'use client';

import { useState, useEffect } from 'react';
import { useCurrentUser } from './use-current-user';

function urlB64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');
  
  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);
  
  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

export function usePushNotifications() {
  const { userId, isLoading: userLoading } = useCurrentUser();
  const [isSupported, setIsSupported] = useState(false);
  const [subscription, setSubscription] = useState<PushSubscription | null>(null);
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const supported = 'serviceWorker' in navigator && 'PushManager' in window;
      setIsSupported(supported);
      
      if (supported && userId && !userLoading) {
        checkExistingSubscription();
      }
    }
  }, [userId, userLoading]);

  const checkExistingSubscription = async () => {
    try {
      const registration = await navigator.serviceWorker.ready;
      const existingSubscription = await registration.pushManager.getSubscription();
      
      if (existingSubscription) {
        setSubscription(existingSubscription);
        setIsSubscribed(true);
      }
    } catch (err) {
      console.error('Error checking subscription:', err);
    }
  };

  const subscribe = async () => {
    if (!isSupported) {
      setError('Push notifications are not supported');
      return false;
    }

    if (!userId) {
      setError('User not authenticated');
      return false;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      
      if (permission !== 'granted') {
        setError('Notification permission denied');
        return false;
      }

      // Get service worker registration
      const registration = await navigator.serviceWorker.ready;

      // Subscribe to push notifications
      const newSubscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlB64ToUint8Array(
          process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY!
        )
      });

      // Get device info
      const deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
      };

      // Send subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          subscription: newSubscription,
          userAgent: navigator.userAgent,
          deviceInfo
        })
      });

      if (!response.ok) {
        throw new Error('Failed to save subscription');
      }

      setSubscription(newSubscription);
      setIsSubscribed(true);
      return true;

    } catch (err) {
      console.error('Subscription failed:', err);
      setError(err instanceof Error ? err.message : 'Subscription failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const unsubscribe = async () => {
    if (!subscription) return false;

    setIsLoading(true);
    setError(null);

    try {
      // Unsubscribe from push service
      await subscription.unsubscribe();

      // Remove from server
      await fetch('/api/push/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          endpoint: subscription.endpoint
        })
      });

      setSubscription(null);
      setIsSubscribed(false);
      return true;

    } catch (err) {
      console.error('Unsubscription failed:', err);
      setError(err instanceof Error ? err.message : 'Unsubscription failed');
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isSupported,
    isSubscribed,
    isLoading,
    error,
    subscribe,
    unsubscribe,
    canSubscribe: !userLoading && !!userId
  };
}
```

### 5.2 Notification Settings Component (components/ui/notification-settings.tsx)
```typescript
'use client';

import { usePushNotifications } from '@/hooks/use-push-notifications';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Bell, BellOff, Smartphone, CheckCircle } from 'lucide-react';

export function NotificationSettings() {
  const {
    isSupported,
    isSubscribed,
    isLoading,
    error,
    subscribe,
    unsubscribe,
    canSubscribe
  } = usePushNotifications();

  if (!isSupported) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Push Notifications
          </CardTitle>
          <CardDescription>
            Push notifications are not supported in this browser
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-muted-foreground">
            Please use a modern browser that supports push notifications.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Smartphone className="h-5 w-5" />
          Push Notifications
          {isSubscribed && (
            <Badge variant="secondary" className="ml-auto">
              <CheckCircle className="h-3 w-3 mr-1" />
              Active
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Get notified about bookings, group updates, and important tasks
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <p className="font-medium">
              Push Notifications
            </p>
            <p className="text-sm text-muted-foreground">
              {isSubscribed
                ? 'Receive notifications for bookings, groups, and updates'
                : 'Enable to receive important notifications on this device'
              }
            </p>
          </div>
          
          <Switch
            checked={isSubscribed}
            onCheckedChange={isSubscribed ? unsubscribe : subscribe}
            disabled={isLoading || !canSubscribe}
          />
        </div>

        {isSubscribed && (
          <div className="space-y-3 pt-4 border-t">
            <p className="text-sm font-medium">Notification Types</p>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
                Booking updates
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full" />
                Group activities
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full" />
                Maintenance alerts
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full" />
                Financial updates
              </div>
            </div>
          </div>
        )}

        {!canSubscribe && (
          <div className="text-sm text-muted-foreground">
            Please sign in to enable notifications
          </div>
        )}
      </CardContent>
    </Card>
  );
}
```

### 5.3 Service Worker (public/sw.js)
```javascript
// Poolly Service Worker for PWA and Push Notifications
const CACHE_NAME = 'poolly-v1';
const urlsToCache = [
  '/',
  '/home',
  '/manifest.json',
  '/icon-192x192.png',
  '/icon-512x512.png'
];

// Install Service Worker
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

// Fetch Strategy
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});

// Push Notification Handler
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  if (!event.data) {
    console.log('Push event has no data');
    return;
  }

  try {
    const data = event.data.json();
    console.log('Push notification data:', data);

    const options = {
      title: data.title || 'Poolly Notification',
      body: data.body || 'You have a new update',
      icon: data.icon || '/icon-192x192.png',
      badge: data.badge || '/icon-192x192.png',
      tag: data.tag || `poolly-${Date.now()}`,
      data: {
        url: data.url || '/',
        type: data.type || 'general',
        groupId: data.groupId,
        vehicleId: data.vehicleId,
        bookingId: data.bookingId,
        timestamp: new Date().toISOString(),
        ...data.data
      },
      actions: data.actions || [],
      requireInteraction: data.type === 'booking' || data.type === 'financial',
      silent: false,
      vibrate: data.type === 'booking' ? [200, 100, 200] : [100]
    };

    // Add type-specific styling
    if (data.type === 'booking') {
      options.badge = '/booking-badge.png';
    } else if (data.type === 'group') {
      options.badge = '/group-badge.png';
    }

    event.waitUntil(
      self.registration.showNotification(options.title, options)
    );
  } catch (error) {
    console.error('Error parsing push data:', error);
    
    // Fallback notification
    event.waitUntil(
      self.registration.showNotification('Poolly', {
        body: 'You have a new update',
        icon: '/icon-192x192.png',
        data: { url: '/' }
      })
    );
  }
});

// Notification Click Handler
self.addEventListener('notificationclick', (event) => {
  console.log('Notification click received:', event);
  
  event.notification.close();

  const data = event.notification.data || {};
  const urlToOpen = data.url || '/';

  // Handle action clicks
  if (event.action) {
    console.log('Action clicked:', event.action);
    
    // Handle specific actions based on notification type
    switch (event.action) {
      case 'accept':
        if (data.type === 'group') {
          urlToOpen = `/group-details/${data.groupId}?action=accept`;
        }
        break;
      case 'view':
        if (data.bookingId) {
          urlToOpen = `/booking-details/${data.bookingId}`;
        }
        break;
      default:
        break;
    }
  }

  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url.includes(urlToOpen.split('?')[0]) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Background Sync for offline actions
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle background sync for offline actions
      console.log('Background sync triggered')
    );
  }
});

// Message handler for communication with main thread
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
});
```

## Phase 6: Integration with Existing Task System

Based on your existing task system in `actions/notifications.md`, integrate push notifications:

### 6.1 Task Notification Triggers (actions/task-notifications.ts)
```typescript
import { sendNotificationToUser, PoollyNotificationPayload } from './push-notifications';

export async function sendTaskNotification(
  userId: string,
  taskType: 'onboarding' | 'compliance' | 'invitations' | 'approvals' | 'maintenance' | 'financial',
  priority: 'blocking' | 'urgent' | 'normal' | 'optional',
  title: string,
  body: string,
  actionUrl: string
) {
  const payload: PoollyNotificationPayload = {
    title,
    body,
    type: taskType === 'maintenance' ? 'maintenance' : 
          taskType === 'financial' ? 'financial' :
          taskType === 'invitations' ? 'group' : 'general',
    url: actionUrl,
    tag: `task-${taskType}-${userId}`,
    actions: priority === 'blocking' ? [
      { action: 'complete', title: 'Complete Now' }
    ] : undefined,
  };

  return await sendNotificationToUser(userId, payload);
}
```

## Phase 7: Testing & Deployment

### 7.1 PWA Testing Checklist
- [ ] Install PWA on mobile device
- [ ] Test offline functionality
- [ ] Verify standalone mode
- [ ] Test notification subscription
- [ ] Test notification delivery
- [ ] Test notification clicks
- [ ] Verify manifest.json
- [ ] Test on multiple browsers

### 7.2 Environment Variables for Production
```bash
# Amplify Console Environment Variables
VAPID_PRIVATE_KEY=your_private_key
VAPID_EMAIL=mailto:<EMAIL>
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your_public_key
NEXT_PUBLIC_NOTIFICATION_TYPES=booking,group,maintenance,financial,compliance
```

This implementation integrates seamlessly with your existing Poolly architecture, using AWS Amplify for auth, your Drizzle schema patterns, and your existing UI components. 