import { TimestampMixin } from "./base";
export enum deprecated_BookingStatus {
  PENDING = "Pending",
  CONFIRMED = "Confirmed",
  CANCELLED = "Cancelled",
  COMPLETED = "Completed",
}

export interface deprecated_BookingBase  {
  vehicle_id: number;
  reference: string;
  start_datetime: string; // ISO string date
  end_datetime: string; // ISO string date
  status: deprecated_BookingStatus;
  total_price?: number | null;
  notes?: string | null;
  party_id: number;
}

export interface deprecated_BookingCreate extends deprecated_BookingBase {}

export interface deprecated_BookingUpdate {
  vehicle_id: number;
  reference: string;
  start_datetime: string;
  end_datetime: string;
  status?: deprecated_BookingStatus | null;
  total_price?: number | null;
  notes?: string | null;
  party_id: number;
}

export interface deprecated_BookingRead extends deprecated_BookingBase, TimestampMixin {
  id: number;
}

// =====================================================
// NEW IMMUTABLE BOOKING SYSTEM TYPES
// =====================================================

// Enums matching the schema
export enum BookingStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
  DISPUTED = 'DISPUTED'
}

export enum HandoverStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  DISPUTED = 'DISPUTED'
}

export enum HandoverType {
  BOOKING_START = 'BOOKING_START',
  BOOKING_END = 'BOOKING_END',
  OWNERSHIP_TRANSFER = 'OWNERSHIP_TRANSFER',
  MAINTENANCE_DROP = 'MAINTENANCE_DROP',
  MAINTENANCE_PICKUP = 'MAINTENANCE_PICKUP'
}

export enum InspectionType {
  PRE_HANDOVER = 'PRE_HANDOVER',
  POST_HANDOVER = 'POST_HANDOVER',
  PERIODIC = 'PERIODIC',
  INCIDENT = 'INCIDENT',
  MAINTENANCE = 'MAINTENANCE'
}

export enum InspectionStatus {
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  DISPUTED = 'DISPUTED',
  CANCELLED = 'CANCELLED'
}

export enum PossessionType {
  OWNER = 'OWNER',
  RENTER = 'RENTER',
  BORROWER = 'BORROWER',
  MAINTENANCE = 'MAINTENANCE'
}

export enum PermissionType {
  OWNER = 'OWNER',
  AUTHORIZED_USER = 'AUTHORIZED_USER',
  GROUP_MEMBER = 'GROUP_MEMBER'
}

export enum IssueType {
  CONDITION_DISPUTE = 'CONDITION_DISPUTE',
  NO_SHOW = 'NO_SHOW',
  LATE_ARRIVAL = 'LATE_ARRIVAL',
  MISSING_ITEMS = 'MISSING_ITEMS',
  DAMAGE_CLAIM = 'DAMAGE_CLAIM',
  OTHER = 'OTHER'
}

export enum PhotoType {
  LEFT_VIEW = 'left_view',
  RIGHT_VIEW = 'right_view',
  REAR_VIEW = 'rear_view',
  FRONT_VIEW = 'front_view',
  DASHBOARD = 'dashboard',
  SEATS_VIEW = 'seats_view',
  INTERIOR = 'interior',
  ODOMETER = 'odometer',
  DAMAGE_DETAIL = 'damage_detail',
  TIRES = 'tires',
  INSPECTOR_SIGNATURE = 'inspector_signature'
}

export enum ConditionLevel {
  NONE = 'none',
  MINOR = 'minor',
  MAJOR = 'major'
}

// Additional enums for inspection fields (matching database schema)
export enum ConditionLevelBasic {
  NONE = 'none',
  MINOR = 'minor',
  MAJOR = 'major'
}

export enum GeneralCondition {
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

export enum LightsCondition {
  WORKING = 'working',
  PARTIAL = 'partial',
  BROKEN = 'broken'
}

export enum CleanlinessLevel {
  CLEAN = 'clean',
  ACCEPTABLE = 'acceptable',
  DIRTY = 'dirty'
}

export enum DashboardCondition {
  WORKING = 'working',
  PARTIAL = 'partial',
  ISSUES = 'issues'
}

export enum OdorLevel {
  NONE = 'none',
  MILD = 'mild',
  STRONG = 'strong'
}

export enum ConditionEnhanced {
  EXCELLENT = 'excellent',
  GOOD = 'good',
  FAIR = 'fair',
  POOR = 'poor'
}

// Base interfaces for the main tables
export interface BookingEventBase {
  bookingReference: string;
  vehicleId: number;
  borrowerPartyId: number;
  eventType: string;
  status: BookingStatus;
  requestedStart: string;
  requestedEnd: string;
  confirmedStart?: string | null;
  confirmedEnd?: string | null;
  actualStart?: string | null;
  actualEnd?: string | null;
  changedBy: number;
  approvedBy?: number | null;
  quotedPrice?: string | null;
  finalPrice?: string | null;
  currency?: string | null;
  purpose?: string | null;
  specialRequirements?: string | null;
  notes?: string | null;
  changeReason?: string | null;
  eventTimestamp: string;
}

export interface BookingEventCreate extends Omit<BookingEventBase, 'eventTimestamp'> {}

export interface BookingEventRead extends BookingEventBase {
  id: number;
}

export interface VehiclePossessionEventBase {
  vehicleId: number;
  possessorPartyId: number;
  possessionStart: string;
  possessionEnd?: string | null;
  possessionType: PossessionType;
  triggerType: string;
  triggerReference?: string | null;
  handoverId?: number | null;
  recordedBy: number;
  notes?: string | null;
  eventTimestamp: string;
}

export interface VehiclePossessionEventCreate extends Omit<VehiclePossessionEventBase, 'eventTimestamp'> {}

export interface VehiclePossessionEventRead extends VehiclePossessionEventBase {
  id: number;
}

export interface VehicleAccessEventBase {
  vehicleId: number;
  partyId: number;
  eventType: string;
  permissionType: PermissionType;
  effectiveFrom: string;
  effectiveTo?: string | null;
  grantedBy: number;
  reason?: string | null;
  eventTimestamp: string;
}

export interface VehicleAccessEventCreate extends Omit<VehicleAccessEventBase, 'eventTimestamp'> {}

export interface VehicleAccessEventRead extends VehicleAccessEventBase {
  id: number;
}

export interface VehicleHandoverBase {
  vehicleId: number;
  handoverType: HandoverType;
  bookingReference?: string | null;
  fromPartyId: number;
  toPartyId: number;
  scheduledTime: string;
  handoverLocation?: string | null;
  handoverCoordinates?: string | null;
  notes?: string | null;
  createdBy: number;
  createdAt: string;
}

export interface VehicleHandoverCreate extends Omit<VehicleHandoverBase, 'createdAt'> {}

export interface VehicleHandoverRead extends VehicleHandoverBase {
  id: number;
}

export interface HandoverStatusEventBase {
  handoverId: number;
  status: HandoverStatus;
  statusTimestamp?: string | null;
  changedBy: number;
  notes?: string | null;
  eventTimestamp: string;
}

export interface HandoverStatusEventCreate extends Omit<HandoverStatusEventBase, 'eventTimestamp'> {}

export interface HandoverStatusEventRead extends HandoverStatusEventBase {
  id: number;
}

export interface VehicleInspectionBase {
  vehicleId: number;
  handoverId?: number | null;
  inspectorPartyId: number;
  inspectionType: InspectionType;
  odometer: number;
  fuelLevel?: number | null;
  scratches?: ConditionLevel | null;
  dents?: ConditionLevel | null;
  tires?: GeneralCondition | null;
  lights?: LightsCondition | null;
  cleanliness?: CleanlinessLevel | null;
  seats?: GeneralCondition | null;
  dashboardControls?: DashboardCondition | null;
  odors?: OdorLevel | null;
  overallCondition: ConditionEnhanced;
  knownIssues?: string | null;
  newDamage?: string | null;
  itemsInVehicle?: string | null;
  inspectorSignature?: string | null;
  inspectionCompletedAt: string;
  relatedInspectionId?: number | null;
  createdAt: string;
}

export interface VehicleInspectionCreate extends Omit<VehicleInspectionBase, 'createdAt'> {}

export interface VehicleInspectionRead extends VehicleInspectionBase {
  id: number;
}

export interface InspectionPhotoBase {
  inspectionId: number;
  photoType: PhotoType;
  fileUrl: string;
  description?: string | null;
  capturedAt: string;
}

export interface InspectionPhotoCreate extends Omit<InspectionPhotoBase, 'capturedAt'> {}

export interface InspectionPhotoRead extends InspectionPhotoBase {
  id: number;
}

export interface HandoverIssueBase {
  handoverId: number;
  reportedBy: number;
  issueType: IssueType;
  description: string;
  reportedAt: string;
}

export interface HandoverIssueCreate extends Omit<HandoverIssueBase, 'reportedAt'> {}

export interface HandoverIssueRead extends HandoverIssueBase {
  id: number;
}

export interface HandoverIssueResolutionBase {
  issueId: number;
  resolution: string;
  resolvedBy: number;
  resolvedAt: string;
}

export interface HandoverIssueResolutionCreate extends Omit<HandoverIssueResolutionBase, 'resolvedAt'> {}

export interface HandoverIssueResolutionRead extends HandoverIssueResolutionBase {
  id: number;
}

// =====================================================
// VIEW TYPES (COMPUTED FROM EVENTS)
// =====================================================

export interface CurrentBookingStatus {
  bookingReference: string;
  vehicleId: number;
  borrowerPartyId: number;
  currentStatus: 'DRAFT' | 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW' | 'DISPUTED';
  requestedStart: string;
  requestedEnd: string;
  confirmedStart?: string | null;
  confirmedEnd?: string | null;
  actualStart?: string | null;
  actualEnd?: string | null;
  quotedPrice?: string | null;
  finalPrice?: string | null;
  lastUpdated: string;
}

export interface CurrentVehiclePossession {
  vehicleId: number;
  possessorPartyId: number;
  possessionType: 'OWNER' | 'RENTER' | 'BORROWER' | 'MAINTENANCE';
  possessionStart: string;
  triggerType: string;
  triggerReference?: string | null;
}

export interface CurrentVehicleAccess {
  vehicleId: number;
  partyId: number;
  permissionType: 'OWNER' | 'AUTHORIZED_USER' | 'GROUP_MEMBER';
  effectiveFrom: string;
  grantedBy: number;
}

export interface CurrentHandoverStatus {
  handoverId: number;
  currentStatus: 'SCHEDULED' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'DISPUTED';
  statusTimestamp?: string | null;
  changedBy: number;
}

// =====================================================
// UTILITY TYPES FOR OPERATIONS
// =====================================================

export interface CreateBookingRequest {
  vehicleId: number;
  borrowerPartyId: number;
  requestedStart: string;
  requestedEnd: string;
  purpose?: string;
  specialRequirements?: string;
  notes?: string;
}

export interface UpdateBookingStatusRequest {
  bookingReference: string;
  newStatus: BookingStatus;
  changedBy: number;
  changeReason?: string;
  approvedBy?: number;
}

export interface ScheduleHandoverRequest {
  vehicleId: number;
  handoverType: HandoverType;
  bookingReference?: string;
  fromPartyId: number;
  toPartyId: number;
  scheduledTime: string;
  handoverLocation?: string;
  notes?: string;
  createdBy: number;
}

export interface CompleteInspectionRequest {
  vehicleId: number;
  handoverId?: number;
  inspectorPartyId: number;
  inspectionType: InspectionType;
  odometer: number;
  fuelLevel?: number;
  conditionAssessment: {
    scratches?: ConditionLevel;
    dents?: ConditionLevel;
    tires?: GeneralCondition;
    lights?: LightsCondition;
    cleanliness?: CleanlinessLevel;
    seats?: GeneralCondition;
    dashboardControls?: DashboardCondition;
    odors?: OdorLevel;
    overallCondition: ConditionEnhanced;
  };
  knownIssues?: string;
  newDamage?: string;
  itemsInVehicle?: string;
  inspectorSignature?: string;
  relatedInspectionId?: number;
}

// Booking availability check
export interface BookingAvailabilityRequest {
  vehicleId: number;
  requestedStart: string;
  requestedEnd: string;
}

export interface BookingAvailabilityResponse {
  isAvailable: boolean;
  conflictingBookings?: CurrentBookingStatus[];
  currentPossessor?: CurrentVehiclePossession;
}
