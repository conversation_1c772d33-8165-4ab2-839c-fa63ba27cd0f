export interface TimestampMixin {
  created_at?: string;
  updated_at?: string;
}
export enum ConditionEnum {
  New = "new",
  Used = "used",
}

export enum ListingTypeEnum {
  ShortTermLeaseOut = "SHORT_TERM_LEASE_OUT",
  LongTermLeaseOut = "LONG_TERM_LEASE_OUT",
  CoOwnershipSale = "CO_OWNERSHIP_SALE",
}

export enum AudienceEnum {
  Business = "BUSINESS",
  EHailing = "E_HAILING",
  Consumer = "CONSUMER",
}

export interface ListingBase {
  party_id: number;
  vehicle_id: number;
  effective_from: string;
  effective_to: string;
  fraction: number;
  asking_price: number;
  condition: ConditionEnum;
  mileage?: number;
  listing_type: ListingTypeEnum;
  audience: AudienceEnum;
}

export interface ListingCreate extends ListingBase {}

export interface ListingRead extends ListingBase, TimestampMixin {
  id: number;
}
