/*
export const users = pgTable("users", {
	id: serial().primary<PERSON>ey().notNull(),
	username: varchar({ length: 50 }).notNull(),
	email: varchar({ length: 100 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
	familyName: varchar("family_name", { length: 100 }),
	givenName: varchar("given_name", { length: 100 }),
	phoneNumber: varchar("phone_number", { length: 20 }),
	orgId: varchar("org_id", { length: 16 }),
}, (table) => [
	unique("users_username_key").on(table.username),
	unique("users_email_key").on(table.email),
]);


*/


export interface UserBase {

  username: string;
  email: string;
  familyName: string;
  givenName: string;
  phoneNumber: string;
  
}

export interface UserCreate extends UserBase {}

export interface UserRead extends UserBase {
  id: number;
  createdAt: string;
  updatedAt: string;
  orgId: string;
  
}