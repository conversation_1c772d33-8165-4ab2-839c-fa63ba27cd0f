import { PartyRead } from "./party";
import { VehicleMaintenanceRead } from "./maintanance";
import { deprecated_BookingRead } from "./bookings";

export enum FuelTypeEnum {
  PETROL = "PETROL",
  DIESEL = "DIESEL",
  ELECTRIC = "ELECTRIC",
  HYBRID = "HYBRID",
}

export interface TimestampMixin {
  created_at?: string;
  updated_at?: string;
}

export interface VehicleMakeBase {
  name: string;
  description?: string;
  logo_url?: string;
  is_active: boolean;
}

export interface VehicleMakeCreate extends VehicleMakeBase {}

export interface VehicleMakeRead extends VehicleMakeBase, TimestampMixin {
  id: number;
}

export interface VehicleModelBase {
  make_id: number;
  model: string;
  year_model: number;
  description?: string;
  transmission?: string;
  fuel?: FuelTypeEnum;
  is_active: boolean;
}

export interface VehicleModelCreate extends VehicleModelBase {}

export interface VehicleModelRead extends VehicleModelBase, TimestampMixin {
  id: number;
}

export interface VehicleModelReadWithMake extends VehicleModelRead {
  make?: VehicleMakeRead;
}

export interface VehicleBase {
  party_id?: number;
  model_id: number;
  vin_number: string;
  vehicle_registration?: string;
  country_id?: number;
  manufacturing_year?: number;
  purchase_date?: string;
  color?: string;
  is_active: boolean;
}

export interface VehicleCreate extends VehicleBase {}

export interface VehicleRead extends VehicleBase, TimestampMixin {
  bookings: deprecated_BookingRead[];
  maintenance_items: VehicleMaintenanceRead[];
  inspections: [];
  model: VehicleModelReadWithMake;
  media: VehicleMediaRead[];
  vehicle_documents: VehicleDocumentRead[];
  id: number;
}

export interface VehicleReadWithModelAndParty extends VehicleRead {
  party?: PartyRead;
}

export interface VehicleListingStatus {
  id: number;
  party_id: number;
  listing_type: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  asking_price: number;
  fraction: number;
  effective_from: string;
  effective_to: string;
  condition: "new" | "used";
  audience: "BUSINESS" | "E_HAILING" | "CONSUMER";
  created_at: string;
}

export interface VehicleReadWithListings extends VehicleReadWithModelAndParty {
  listings: VehicleListingStatus[];
}

export interface VehicleMediaBase {
  vehicle_id: number;
  media_path: string;
}

export interface VehicleMediaCreate extends VehicleMediaBase {}

export interface VehicleMediaRead extends VehicleMediaBase {
  id: number;
  created_at: string;
}

export interface VehicleModelMediaBase {
  vehicle_model_id: number;
  media_path: string;
}

export interface VehicleModelMediaCreate extends VehicleModelMediaBase {}

export interface VehicleModelMediaRead extends VehicleModelMediaBase {
  id: number;
}

export interface VehicleUpdate extends VehicleBase {
  id: number;
}

export enum DocumentType {
  REGISTRATION = "registration",
  INSURANCE = "insurance",
  INSPECTION = "inspection",
  OTHER = "other",
}

export interface VehicleDocumentBase {
  vehicle_id: number;
  media_path: string;
  document_type: DocumentType;
  expiration_date?: string;
  name?: string;
  created_at?: string;
}

export interface VehicleDocumentCreate extends VehicleDocumentBase {}

export interface VehicleDocumentRead extends VehicleDocumentBase {
  id: number;
}

export interface SimplifiedVehicleData {
  id: number;
  name: string;
  year?: number;
  image?: string;
  odometer: string;
  lastService: string;
  nextService: string;
}
