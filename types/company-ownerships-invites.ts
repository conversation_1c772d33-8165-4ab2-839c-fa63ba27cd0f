import { CompanyRead } from "./company";
import { PartyRead } from "./party";
export interface CompanyOwnershipBase {
  party_id: number;
  company_id: number;
  fraction: number;
  effective_from: Date;
  effective_to?: Date | null;
  is_active: boolean;
}

export interface CompanyOwnershipCreate extends CompanyOwnershipBase {}

export interface CompanyOwnershipRead extends CompanyOwnershipBase {
  id: number;
  created_at: Date;
  updated_at: Date;
}

export interface CompanyOwnershipReadWithRelations
  extends CompanyOwnershipRead {
  party?: PartyRead | null;
  company?: CompanyRead | null;
}

export interface CompanyOwnershipUpdate extends CompanyOwnershipBase {
  id: number;
}
export enum CompanyOwnershipInviteEnum {
  SENT = "SENT",
  DECLINED = "DECLINED",
  ACCEPTED = "ACCEPTED",
}
export interface CompanyOwnershipInviteBase {
  first_name: string;
  last_name: string;
  company_id: number;
  fraction: number;
  email: string;
  status?: CompanyOwnershipInviteEnum;
}

export interface CompanyOwnershipInviteCreate
  extends CompanyOwnershipInviteBase {}

export interface CompanyOwnershipInviteUpdate
  extends CompanyOwnershipInviteBase {
  id: number;
}

export interface CompanyOwnershipInviteRead extends CompanyOwnershipInviteBase {
  id: number;
  created_at: Date;
  updated_at: Date;
}
