export interface VehicleModelBase {
    make_id: number;
    model: string;
    slug?: string | null;
    first_year?: number | null;
    last_year?: number | null;
    body_type?: string | null;
    description?: string | null;
    is_active: boolean;
}

export interface VehicleModelCreate extends VehicleModelBase {}

export interface VehicleModelRead extends VehicleModelBase {
    id: number;
    created_at: string | null;
    updated_at: string | null;
}

export interface VehicleModelUpdate extends VehicleModelBase {
    id: number;
}

export interface VehicleModelReadWithMake extends VehicleModelRead {
    make?: VehicleMakeRead | null;
}

export interface VehicleMakeRead {
    id: number;
    name: string;
    description?: string | null;
    is_active: boolean;
    created_at: string | null;
    updated_at: string | null;
}
