import { CompanyPurposeEnum } from "./company";
import { VehicleCreate } from "./vehicles";

export enum GroupRoleEnum {
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER'
}

export enum InvitationStatusEnum {
  PENDING = 'PENDING',
  SENT = 'SENT',
  ACCEPTED = 'ACCEPTED',
  DECLINED = 'DECLINED',
  CANCELLED = 'CANCELLED',
  EXPIRED = 'EXPIRED'
}

export interface GroupBase {
  
  name: string;
  description: string;
  cityId: number;
  countryId: number;
  InitialPurpose: CompanyPurposeEnum;
  isManaged: boolean;
  createdBy: number;
  companyId?: number;
  partyId?: number;
}

export interface GroupCreate extends GroupBase {

}

export interface GroupRequest extends GroupBase {
  id: number;
  created_at: string;
  updated_at: string;
  updated_by: number;
}


export interface GroupMembershipBase {
  
  groupId: number;
  partyId: number;
  effectiveFrom?: string;
  effectiveTo?: string;
}

export interface GroupMembershipCreate extends GroupMembershipBase {

}

export interface GroupMembershipRequest extends GroupMembershipBase {
  id: number;
  created_at: string;
  updated_at: string;
  updated_by: number;
}

export interface GroupMemberRoleBase {
  groupId: number;
  partyId: number;
  role: GroupRoleEnum;
}

export interface GroupMemberRoleCreate extends GroupMemberRoleBase {

}


export interface GroupMemberRoleRequest extends GroupMemberRoleBase {
  id: number;
  created_at: string;
  updated_at: string;
  updated_by: number;
}

// Group membership invitation interfaces
export interface GroupMembershipInvitationBase {
  groupId?: number;
  firstName: string;
  lastName: string;
  email: string;
  role: GroupRoleEnum;
  invitedBy?: number;
  expiresAt?: string;
  status?: InvitationStatusEnum;
}

export interface GroupMembershipInvitationCreate extends GroupMembershipInvitationBase {
  invitationToken?: string;
}

export interface GroupMembershipInvitationRequest extends GroupMembershipInvitationBase {
  id: number;
  status: InvitationStatusEnum;
  invitationToken: string;
  acceptedAt?: string;
  acceptedBy?: number;
  createdAt: string;
  updatedAt?: string;
}

// Group shared vehicles interfaces
export interface GroupSharedVehicleBase {
  groupId: number;
  vehicleId: number;
  sharedBy: number;
  effectiveFrom?: string;
  effectiveTo?: string;
  isActive?: boolean;
}

export interface GroupSharedVehicleCreate extends GroupSharedVehicleBase {

}

export interface GroupSharedVehicleRequest extends GroupSharedVehicleBase {
  id: number;
  createdAt: string;
  updatedAt?: string;
}

 // Separate function for group creation
 export interface GroupCreationProps {
  groupCreate: {
    name: string;
    description: string;
    cityId: number;
    countryId: number;
    InitialPurpose: CompanyPurposeEnum;
    isManaged: boolean;
    createdBy: number;
    partyId?: number;
  },
  groupMembershipCreate?: {
    groupId?: number;
    partyId?: number;
 
  },
  groupMemberRoleCreate?: {

    groupId: number;
    partyId: number;
    role: GroupRoleEnum;

  },
  memberInvitations?: GroupMembershipInvitationCreate[];
  groupSharedVehicleCreate?: GroupSharedVehicleCreate;
  vehicleCreate?: VehicleCreate;
}