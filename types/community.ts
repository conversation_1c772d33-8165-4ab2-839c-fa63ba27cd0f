import { GroupRoleEnum } from "./groups";

// Community Group interfaces for the community page
export interface CommunityGroup {
  id: number;
  name: string;
  description: string;
  members: number;
  vehicles: number;
  location: string;
  image: string;
  createdAt: string;
  isManaged: boolean;
  initialPurpose: string;
  memberRole?: GroupRoleEnum;
}

// Detailed group information
export interface GroupDetails {
  id: number;
  name: string;
  description: string;
  location: string;
  members: GroupMember[];
  vehicles: GroupVehicle[];
  upcomingBookings: GroupBooking[];
  createdAt: string;
  isManaged: boolean;
  initialPurpose: string;
  createdBy: {
    id: number;
    name: string;
  };
}

// Group member information
export interface GroupMember {
  id: number;
  partyId: number;
  name: string;
  role: GroupRoleEnum;
  avatar: string;
  joinedAt: string;
}

// Group vehicle information
export interface GroupVehicle {
  id: number;
  name: string;
  registration: string;
  status: 'available' | 'in-use' | 'maintenance';
  image: string;
  make: string;
  model: string;
  year?: number;
  color?: string;
}

// Group booking information
export interface GroupBooking {
  id: number;
  vehicle: string;
  member: string;
  startDate: string;
  endDate: string;
  status: string;
}

// Search filters
export interface GroupSearchFilters {
  location?: string;
  purpose?: string;
  isManaged?: boolean;
  memberCount?: {
    min?: number;
    max?: number;
  };
  vehicleCount?: {
    min?: number;
    max?: number;
  };
}

// Group statistics
export interface GroupStats {
  totalGroups: number;
  totalMembers: number;
  totalVehicles: number;
  activeBookings: number;
}

// User's group membership info
export interface UserGroupMembership {
  groupId: number;
  role: GroupRoleEnum;
  joinedAt: string;
  isActive: boolean;
} 