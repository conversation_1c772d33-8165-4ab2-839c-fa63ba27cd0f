import { z } from "zod";

// =====================================================
// ENUMS AND BASE SCHEMAS
// =====================================================

// Payment status enum
export const PaymentStatusEnum = z.enum([
  "pending",
  "paid",
  "failed",
  "cancelled",
]);

export type PaymentStatus = z.infer<typeof PaymentStatusEnum>;

// Payment method enum
export const PaymentMethodEnum = z.enum([
  "bank_transfer",
  "cash",
  "eft",
  "card",
  "other",
]);

export type PaymentMethod = z.infer<typeof PaymentMethodEnum>;

// Debt source type enum
export const DebtSourceTypeEnum = z.enum([
  "earnings_shortfall",
  "debt_recovery",
  "adjustment",
]);

export type DebtSourceType = z.infer<typeof DebtSourceTypeEnum>;

// Platform name enum
export const PlatformNameEnum = z.enum(["uber", "bolt", "indriver", "other"]);

export type PlatformName = z.infer<typeof PlatformNameEnum>;

// =====================================================
// DEPOSIT MANAGEMENT SCHEMAS
// =====================================================

// Deposit record schema
export const DepositRecordSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  depositAmount: z.number().positive(),
  amountPaid: z.number().min(0).default(0),
  balanceRemaining: z.number().min(0),
  paymentMethod: PaymentMethodEnum.optional(),
  paymentReference: z.string().optional(),
  paymentDate: z.string().optional(), // ISO string
  notes: z.string().optional(),
  createdAt: z.string(), // ISO string
  createdBy: z.number().optional(),
});

export type DepositRecord = z.infer<typeof DepositRecordSchema>;

// Deposit payment input schema
export const DepositPaymentInputSchema = z
  .object({
    assignmentId: z.number().positive(),
    depositAmount: z.number().positive(),
    amountPaid: z.number().min(0),
    paymentMethod: PaymentMethodEnum.optional(),
    paymentReference: z.string().min(1).optional(),
    notes: z.string().optional(),
  })
  .refine((data) => data.amountPaid <= data.depositAmount, {
    message: "Amount paid cannot exceed deposit amount",
    path: ["amountPaid"],
  });

export type DepositPaymentInput = z.infer<typeof DepositPaymentInputSchema>;

// =====================================================
// CONTRACT MANAGEMENT SCHEMAS
// =====================================================

// Contract record schema
export const ContractRecordSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  contractFilePath: z.string(),
  originalFilename: z.string(),
  fileSize: z.number().positive(),
  mimeType: z.string(),
  uploadedAt: z.string(), // ISO string
  uploadedBy: z.number(),
  isActive: z.boolean().default(true),
  replacedBy: z.number().optional(),
  notes: z.string().optional(),
});

export type ContractRecord = z.infer<typeof ContractRecordSchema>;

// Contract upload input schema
export const ContractUploadInputSchema = z.object({
  assignmentId: z.number().positive(),
  originalFilename: z.string().min(1),
  fileSize: z
    .number()
    .positive()
    .max(10 * 1024 * 1024), // 10MB limit
  mimeType: z.enum(["application/pdf", "image/jpeg", "image/png", "image/jpg"]),
  notes: z.string().optional(),
});

export type ContractUploadInput = z.infer<typeof ContractUploadInputSchema>;

// =====================================================
// WEEKLY EARNINGS SCHEMAS
// =====================================================

// Weekly earnings record schema
export const WeeklyEarningsRecordSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  weekStartDate: z.string(), // ISO date string (YYYY-MM-DD)
  weekEndDate: z.string(), // ISO date string (YYYY-MM-DD)
  grossEarnings: z.number().min(0),
  platformName: PlatformNameEnum.default("uber"),
  weeklyTarget: z.number().positive().default(2700),
  earningsShortfall: z.number().min(0).default(0),
  recordedAt: z.string(), // ISO string
  recordedBy: z.number(),
  notes: z.string().optional(),
});

export type WeeklyEarningsRecord = z.infer<typeof WeeklyEarningsRecordSchema>;

// Weekly earnings input schema
export const WeeklyEarningsInputSchema = z
  .object({
    assignmentId: z.number().positive(),
    weekStartDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Must be YYYY-MM-DD format"),
    weekEndDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Must be YYYY-MM-DD format")
      .optional(),
    grossEarnings: z.number().min(0),
    totalEarnings: z.number().min(0),
    platformEarnings: z.record(z.string(), z.number()).optional(),
    fuelCosts: z.number().min(0).optional(),
    otherExpenses: z.number().min(0).optional(),
    platformName: PlatformNameEnum.optional(),
    weeklyTarget: z.number().positive().optional(),
    notes: z.string().optional(),
  })
  .refine(
    (data) => {
      const startDate = new Date(data.weekStartDate);
      return startDate.getDay() === 1; // Monday
    },
    {
      message: "Week start date must be a Monday",
      path: ["weekStartDate"],
    }
  );

export type WeeklyEarningsInput = z.infer<typeof WeeklyEarningsInputSchema>;

// =====================================================
// DRIVER PAYOUT SCHEMAS
// =====================================================

// Payout record schema
export const PayoutRecordSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  periodStart: z.string(), // ISO string
  periodEnd: z.string(), // ISO string
  grossEarnings: z.string(),
  totalExpenses: z.string().optional(),
  netEarnings: z.string(),
  totalDebtDeduction: z.string().optional(),
  payoutAmount: z.string(),
  paymentMethod: PaymentMethodEnum.optional(),
  paymentReference: z.string().optional(),
  bankDetails: z.string().optional(),
  status: z
    .enum(["pending_approval", "approved", "rejected", "processed"])
    .default("processed"),
  processedAt: z.string(), // ISO string
  processedBy: z.number(),
  approvedAt: z.string().optional(), // ISO string
  approvedBy: z.number().optional(),
  notes: z.string().optional(),
});

export type PayoutRecord = z.infer<typeof PayoutRecordSchema>;

// Payout processing input schema
export const PayoutProcessingInputSchema = z.object({
  assignmentId: z.number().positive(),
  periodStart: z.string(),
  periodEnd: z.string(),
  payoutAmount: z.number().min(0),
  paymentMethod: PaymentMethodEnum.optional(),
  paymentReference: z.string().optional(),
  bankDetails: z.string().optional(),
  notes: z.string().optional(),
  requiresApproval: z.boolean().default(false),
});

export type PayoutProcessingInput = z.infer<typeof PayoutProcessingInputSchema>;

// =====================================================
// DEBT MANAGEMENT SCHEMAS
// =====================================================

// Debt record schema
export const DebtRecordSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  debtSourceId: z.number(),
  debtSourceType: DebtSourceTypeEnum,
  debtAmount: z.number(),
  runningBalance: z.number(),
  transactionDate: z.string(), // ISO string
  createdBy: z.number(),
  notes: z.string().optional(),
});

export type DebtRecord = z.infer<typeof DebtRecordSchema>;

// Debt adjustment input schema
export const DebtAdjustmentInputSchema = z.object({
  adjustmentAmount: z.number(),
  reason: z.string().min(1),
  notes: z.string().optional(),
});

export type DebtAdjustmentInput = z.infer<typeof DebtAdjustmentInputSchema>;

// Debt creation input schema
export const DebtCreationInputSchema = z.object({
  assignmentId: z.number().positive(),
  amount: z.number().positive(),
  description: z.string().min(1),
  sourceType: DebtSourceTypeEnum.default("adjustment"),
  sourceRecordId: z.number().optional(),
  sourceWeek: z.string().optional(),
  notes: z.string().optional(),
});

export type DebtCreationInput = z.infer<typeof DebtCreationInputSchema>;

// =====================================================
// AUDIT LOG SCHEMAS
// =====================================================

// Payment audit log schema
export const PaymentAuditLogSchema = z.object({
  id: z.number(),
  assignmentId: z.number(),
  actionType: z.string(),
  tableName: z.string(),
  recordId: z.number(),
  oldValues: z.record(z.any()).optional(),
  newValues: z.record(z.any()).optional(),
  performedAt: z.string(), // ISO string
  performedBy: z.number(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
});

export type PaymentAuditLog = z.infer<typeof PaymentAuditLogSchema>;

// =====================================================
// RESPONSE SCHEMAS
// =====================================================

// Generic action result schema
export const ActionResultSchema = <T extends z.ZodType>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    error: z
      .object({
        code: z.string(),
        message: z.string(),
        field: z.string().optional(),
        details: z.record(z.any()).optional(),
      })
      .optional(),
  });

// Payment error schema
export const PaymentErrorSchema = z.object({
  code: z.string(),
  message: z.string(),
  field: z.string().optional(),
  details: z.record(z.any()).optional(),
});

export type PaymentError = z.infer<typeof PaymentErrorSchema>;

// Specific response types
export type DepositResponse = z.infer<
  ReturnType<typeof ActionResultSchema<typeof DepositRecordSchema>>
>;
export type ContractResponse = z.infer<
  ReturnType<typeof ActionResultSchema<typeof ContractRecordSchema>>
>;
export type WeeklyEarningsResponse = z.infer<
  ReturnType<typeof ActionResultSchema<typeof WeeklyEarningsRecordSchema>>
>;
export type PayoutResponse = z.infer<
  ReturnType<typeof ActionResultSchema<typeof PayoutRecordSchema>>
>;
export type DebtResponse = z.infer<
  ReturnType<typeof ActionResultSchema<typeof DebtRecordSchema>>
>;

// =====================================================
// DASHBOARD AND REPORTING SCHEMAS
// =====================================================

// Driver performance summary schema
export const DriverPerformanceSummarySchema = z.object({
  assignmentId: z.number(),
  driverName: z.string(),
  currentWeekEarnings: z.number().min(0),
  weeklyTarget: z.number().positive(),
  performanceRatio: z.number().min(0),
  totalDebt: z.number().min(0),
  lastPayoutAmount: z.number().min(0).optional(),
  lastPayoutDate: z.string().optional(), // ISO string
  weeksActive: z.number().min(0),
  totalEarnings: z.number().min(0),
  totalPayouts: z.number().min(0),
});

export type DriverPerformanceSummary = z.infer<
  typeof DriverPerformanceSummarySchema
>;

// Financial summary schema
export const FinancialSummarySchema = z.object({
  assignmentId: z.number(),
  totalDeposits: z.number().min(0),
  outstandingDepositBalance: z.number().min(0),
  totalEarnings: z.number().min(0),
  totalPayouts: z.number().min(0),
  totalDebt: z.number().min(0),
  averageWeeklyEarnings: z.number().min(0),
  performanceRate: z.number().min(0).max(1), // 0-1 percentage
  lastActivityDate: z.string().optional(), // ISO string
});

export type FinancialSummary = z.infer<typeof FinancialSummarySchema>;

// =====================================================
// VALIDATION HELPERS
// =====================================================

// Week date validation helper
export const validateWeekDates = (startDate: string, endDate?: string) => {
  const start = new Date(startDate);
  const end = endDate
    ? new Date(endDate)
    : new Date(start.getTime() + 6 * 24 * 60 * 60 * 1000);

  return {
    isValidStartDate: start.getDay() === 1, // Monday
    isValidEndDate: end.getDay() === 0, // Sunday
    isValidRange: end.getTime() - start.getTime() === 6 * 24 * 60 * 60 * 1000, // Exactly 6 days
  };
};

// Debt calculation helper types
export interface DebtCalculation {
  currentBalance: number;
  newDebtAmount: number;
  deductionAmount: number;
  remainingBalance: number;
  canFullyDeduct: boolean;
}

// Payout calculation helper types
export interface PayoutCalculation {
  assignmentId: number;
  periodStart: string;
  periodEnd: string;
  totalGrossEarnings: string;
  totalExpenses: string;
  totalNetEarnings: string;
  totalOutstandingDebt: string;
  debtDeduction: string;
  finalPayoutAmount: string;
  weeksIncluded: number;
  debtsIncluded: number;
  averageWeeklyNet: string;
}

// Payout summary helper types
export interface PayoutSummary {
  assignmentId: number;
  totalPayouts: string;
  totalGrossEarnings: string;
  totalNetEarnings: string;
  totalDebtDeductions: string;
  payoutCount: number;
  pendingApprovalCount: number;
  approvedCount: number;
  rejectedCount: number;
  averagePayoutAmount: string;
  lastPayoutDate?: string;
  lastPayoutAmount?: string;
}

// Weekly earnings summary helper types
export interface WeeklyEarningsSummary {
  assignmentId: number;
  totalEarnings: string;
  totalExpectedEarnings: string;
  totalShortfall: string;
  averageWeeklyEarnings: string;
  weeksRecorded: number;
  performanceRate: string;
  latestWeekDate?: string;
  shortfallPercentage: number;
}

// Debt summary helper types
export interface DebtSummary {
  assignmentId: number;
  totalOutstandingDebt: string;
  totalResolvedDebt: string;
  totalDebtCount: number;
  outstandingDebtCount: number;
  resolvedDebtCount: number;
  averageDebtAmount: string;
  resolutionRate: string;
  earningsShortfallCount: number;
  adjustmentCount: number;
  debtRecoveryCount: number;
  latestDebtDate?: string;
  oldestOutstandingDate?: string;
}

// Debt report data helper types
export interface DebtReportData {
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  summary: {
    totalAssignments: number;
    totalOutstandingDebt: string;
    totalResolvedDebt: string;
    totalDebtCount: number;
    averageDebtPerAssignment: string;
    resolutionRate: string;
  };
  assignmentBreakdown: DebtSummary[];
  debtsByType: {
    earningsShortfall: {
      count: number;
      totalAmount: string;
      averageAmount: string;
    };
    debtRecovery: {
      count: number;
      totalAmount: string;
      averageAmount: string;
    };
    adjustment: {
      count: number;
      totalAmount: string;
      averageAmount: string;
    };
  };
  trends: {
    monthlyTotals: Array<{
      month: string;
      outstandingDebt: string;
      resolvedDebt: string;
      newDebts: number;
    }>;
  };
}
