export interface VehicleVariantBase {
  model_id?: number | null;
  name: string;
  trim_name?: string | null;
  year: number;
  engine?: string | null;
  drivetrain?: string | null; // drivetrainEnum from schema
  body_type?: string | null; // bodyTypeEnum from schema
  seats?: number | null;
  doors?: number | null;
  msrp?: string | null; // decimal field from schema
  features?: any; // jsonb field
  specs?: any; // jsonb field
  fuel_type: string; // fuelTypeEnum from schema
  transmission: string; // transmissionEnum from schema
  description?: string | null;
  is_active: boolean;
}

export interface VehicleVariantCreate extends VehicleVariantBase {}

export interface VehicleVariantRead extends VehicleVariantBase {
  id: number;
  created_at: string | null;
  updated_at: string | null;
}

export interface VehicleVariantUpdate extends VehicleVariantBase {
  id: number;
}

export interface VehicleVariantReadWithModel extends VehicleVariantRead {
  model?: VehicleModelRead | null;
}

// Re-export VehicleModelRead to avoid circular imports
export interface VehicleModelRead {
  id: number;
  make_id: number;
  model: string;
  slug?: string | null;
  first_year?: number | null;
  last_year?: number | null;
  body_type?: string | null;
  description?: string | null;
  is_active: boolean;
  created_at: string | null;
  updated_at: string | null;
} 