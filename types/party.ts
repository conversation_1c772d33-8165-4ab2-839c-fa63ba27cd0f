import { TimestampMixin } from "./base";

export enum PartyStatusEnum {
  ACTIVE = "ACTIVE", 
  INACTIVE = "INACTIVE",
  SUSPENDED = "SUSPENDED",
  DELETED = "DELETED",
}

export interface PartyBase {
  party_type_id: number;
  status_id?: number; 
  external_id?: string | null;
}

export interface PartyCreate extends PartyBase {}

export interface PartyRead extends PartyBase, TimestampMixin {
  id: number;
}
