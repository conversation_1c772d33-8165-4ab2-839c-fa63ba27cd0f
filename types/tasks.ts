export enum TaskTypeEnum {
  GROUP_INVITATION = 'GROUP_INVITATION',
  COMPLIANCE = 'COMPLIANCE',
  ONBOARDING = 'ONBOARDING',
  MAINTENANCE = 'MAINTENANCE',
  FINANCIAL = 'FINANCIAL',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  APPROVAL = 'APPROVAL',
  BOOKING_ACKNOWLEDGMENT = 'BOOKING_ACKNOWLEDGMENT',
  VEHICLE_HANDOVER_GIVER = 'VEHICLE_HANDOVER_GIVER',
  VEHICLE_HANDOVER_RECEIVER = 'VEHICLE_HANDOVER_RECEIVER'
}

export enum TaskStatusEnum {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  DISMISSED = 'DISMISSED',
  EXPIRED = 'EXPIRED'
}

export enum TaskPriorityEnum {
  BLOCKING = 'BLOCKING',
  URGENT = 'URGENT',
  NORMAL = 'NORMAL',
  OPTIONAL = 'OPTIONAL'
}

export interface TaskBase {
  type: TaskTypeEnum;
  status?: TaskStatusEnum;
  priority?: TaskPriorityEnum;
  title: string;
  description?: string;
  email: string;
  partyId?: number;
  relatedEntityId?: number;
  metadata?: any;
  estimatedMinutes?: number;
  expiresAt?: string;
}

export interface TaskCreate extends TaskBase {}

export interface TaskRequest extends TaskBase {
  id: number;
  completedAt?: string;
  completedBy?: number;
  createdAt: string;
  updatedAt?: string;
}

// Task metadata interfaces for different task types
export interface GroupInvitationTaskMetadata {
  groupId: number;
  groupName: string;
  invitationId: number;
  inviterName: string;
  role: string;
}

export interface ComplianceTaskMetadata {
  documentType: string;
  requirements: string[];
  deadline?: string;
}

export interface MaintenanceTaskMetadata {
  vehicleId: number;
  vehicleName: string;
  maintenanceType: string;
  scheduledDate: string;
}

export interface BookingAcknowledgmentTaskMetadata {
  bookingReference: string;
  vehicleId: number;
  vehicleName: string;
  requestedStart: string;
  requestedEnd: string;
  fromPartyName: string;
}

export interface VehicleHandoverGiverTaskMetadata {
  bookingReference: string;
  vehicleId: number;
  vehicleName: string;
  borrowerName: string;
  borrowerEmail: string;
  requestedStart: string;
  requestedEnd: string;
  handoverLocation?: string;
}

export interface VehicleHandoverReceiverTaskMetadata {
  bookingReference: string;
  vehicleId: number;
  vehicleName: string;
  currentHolderName: string;
  currentHolderEmail: string;
  requestedStart: string;
  requestedEnd: string;
  handoverLocation?: string;
} 