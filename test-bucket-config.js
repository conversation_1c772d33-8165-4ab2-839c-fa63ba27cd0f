// Test bucket configuration
// Run this in browser console to verify bucket setup

console.log("=== Bucket Configuration Test ===");

// Check Amplify configuration
try {
  const amplifyConfig = window.aws_amplify?.Amplify?.getConfig?.();
  if (amplifyConfig?.Storage) {
    console.log("✅ Storage config found:");
    console.log("Storage config:", amplifyConfig.Storage);
  } else {
    console.log("❌ No storage config found");
  }
} catch (e) {
  console.log("❌ Error checking Amplify config:", e);
}

// Test bucket access
async function testBucketAccess() {
  try {
    console.log("Testing bucket access...");
    
    // Create a tiny test file
    const testData = new Blob(['test'], { type: 'text/plain' });
    const testFile = new File([testData], 'test.txt', { type: 'text/plain' });
    
    console.log("Created test file:", testFile);
    
    // Try to upload with bucket specified
    const { uploadData } = await import('aws-amplify/storage');
    
    const uploadTask = uploadData({
      path: 'test-uploads/bucket-test.txt',
      data: testFile,
      options: {
        bucket: 'poolly',
        contentType: 'text/plain'
      }
    });
    
    console.log("Upload task created, waiting for result...");
    const result = await uploadTask.result;
    console.log("✅ Bucket test successful:", result);
    
    return result;
  } catch (error) {
    console.log("❌ Bucket test failed:", error);
    
    // Try without bucket specification
    try {
      console.log("Trying without bucket specification...");
      const { uploadData } = await import('aws-amplify/storage');
      
      const uploadTask = uploadData({
        path: 'test-uploads/no-bucket-test.txt',
        data: testFile,
        options: {
          contentType: 'text/plain'
        }
      });
      
      const result = await uploadTask.result;
      console.log("✅ Upload without bucket worked:", result);
      return result;
    } catch (error2) {
      console.log("❌ Upload without bucket also failed:", error2);
      return null;
    }
  }
}

// Test DocumentUpload function
async function testDocumentUpload() {
  try {
    console.log("Testing DocumentUpload function...");
    
    // Create test file
    const canvas = document.createElement('canvas');
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 10, 10);
    
    const blob = await new Promise(resolve => canvas.toBlob(resolve));
    const testFile = new File([blob], 'test-doc.png', { type: 'image/png' });
    
    // Import and test DocumentUpload
    const { DocumentUpload } = await import('/lib/utils.ts');
    const result = await DocumentUpload(testFile, 'test-applications');
    
    console.log("✅ DocumentUpload test successful:", result);
    return result;
  } catch (error) {
    console.log("❌ DocumentUpload test failed:", error);
    return null;
  }
}

// Make functions available globally
window.testBucketAccess = testBucketAccess;
window.testDocumentUpload = testDocumentUpload;

console.log("Run testBucketAccess() to test bucket configuration");
console.log("Run testDocumentUpload() to test DocumentUpload function");
