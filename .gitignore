# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# PWA files
public/sw.js
public/sw.js.map
public/workbox-*.js
public/workbox-*.js.map

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# amplify
.amplify
amplify_outputs*
amplifyconfiguration*
.env

# Sentry Config File
.env.sentry-build-plugin
