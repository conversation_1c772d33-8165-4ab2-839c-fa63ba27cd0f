# Assignment Integration Guide - Task 8

## Overview
This guide shows how to integrate our comprehensive payment and contract management system into the existing assignments page.

## Integration Options

### Option 1: Add Deposit Tab to AssignmentDetailsDialog

**File:** `app/(admin)/admin/assignments/components/AssignmentDetailsDialog.tsx`

Add a new tab to the existing tabs:

```tsx
// Add import
import AssignmentDepositIntegration from "./AssignmentDepositIntegration";

// In the TabsList, add:
<TabsTrigger value="deposits">Deposits</TabsTrigger>

// In the TabsContent sections, add:
<TabsContent value="deposits" className="space-y-6">
  <AssignmentDepositIntegration
    assignmentId={assignment.id}
    driverName={assignment.driverName}
  />
</TabsContent>
```

### Option 2: Add Deposit Section to Main Assignments Page

**File:** `app/(admin)/admin/assignments/page.tsx`

Add deposit functionality directly to the assignments table:

```tsx
// Add import
import AssignmentDepositIntegration from "./components/AssignmentDepositIntegration";

// Add a new column for deposit status
<TableHead>Deposit Balance</TableHead>

// In the table row, add:
<TableCell>
  <Badge variant="outline" className="bg-green-100 text-green-800">
    R{assignment.depositBalance || 0}
  </Badge>
</TableCell>
```

### Option 3: Enhanced Assignment Cards

**File:** `app/(admin)/admin/assignments/page.tsx`

Replace the table view with enhanced cards that include deposit info:

```tsx
// Enhanced assignment card with deposit info
<Card key={assignment.id} className="p-6">
  <div className="flex items-center justify-between mb-4">
    <div>
      <h3 className="text-lg font-semibold">{assignment.driverName}</h3>
      <p className="text-sm text-gray-500">{assignment.vehicleName}</p>
    </div>
    <Badge className={getStatusColor(assignment.status)}>
      {assignment.status}
    </Badge>
  </div>
  
  {/* Financial Summary */}
  <div className="grid grid-cols-3 gap-4 mb-4">
    <div className="text-center">
      <p className="text-sm text-gray-500">Weekly Rate</p>
      <p className="font-bold">R{assignment.weeklyRate}</p>
    </div>
    <div className="text-center">
      <p className="text-sm text-gray-500">Deposit Balance</p>
      <p className="font-bold text-green-600">R{assignment.depositBalance || 0}</p>
    </div>
    <div className="text-center">
      <p className="text-sm text-gray-500">Outstanding</p>
      <p className="font-bold text-red-600">R{assignment.outstandingBalance}</p>
    </div>
  </div>
  
  {/* Quick Actions */}
  <div className="flex gap-2">
    <Button 
      size="sm" 
      onClick={() => setSelectedAssignmentForDeposit(assignment)}
      className="bg-[#009639] hover:bg-[#007A2F]"
    >
      <DollarSign size={16} className="mr-1" />
      Record Deposit
    </Button>
    <Button size="sm" variant="outline" onClick={() => setSelectedAssignment(assignment)}>
      <Eye size={16} className="mr-1" />
      View Details
    </Button>
  </div>
  
  {/* Deposit Integration */}
  {selectedAssignmentForDeposit?.id === assignment.id && (
    <div className="mt-4 border-t pt-4">
      <AssignmentDepositIntegration
        assignmentId={assignment.id}
        driverName={assignment.driverName}
      />
    </div>
  )}
</Card>
```

## Implementation Steps

### Step 1: Update Assignments Data Loading

In `app/(admin)/admin/assignments/page.tsx`, enhance the data loading to include deposit information:

```tsx
// Add imports
import { getDepositsSummaryAction } from "@/actions/admin/deposits";

// In the loadData effect:
const loadData = async () => {
  try {
    setLoading(true);
    const [driversResult, vehiclesResult, assignmentsResult] = await Promise.all([
      getApprovedEhailingDrivers(),
      getAvailableVehiclesInventory(),
      getInventoryVehicleDriverAssignments(),
    ]);

    // ... existing code ...

    // Load deposit summaries for all assignments
    if (assignmentsResult.success && assignmentsResult.data) {
      const assignmentIds = assignmentsResult.data.map(a => parseInt(a.id));
      const depositsResult = await getDepositsSummaryAction(assignmentIds);
      
      if (depositsResult.success) {
        // Merge deposit data with assignments
        const enhancedAssignments = assignmentsResult.data.map(assignment => {
          const depositSummary = depositsResult.data?.find(d => d.assignmentId === parseInt(assignment.id));
          return {
            ...assignment,
            depositBalance: depositSummary?.totalBalanceRemaining || 0,
            totalDeposits: depositSummary?.totalDepositAmount || 0,
          };
        });
        setAssignments(enhancedAssignments);
      }
    }
  } catch (error) {
    console.error("Error loading data:", error);
    toast.error("Failed to load assignments data");
  } finally {
    setLoading(false);
  }
};
```

### Step 2: Add Quick Deposit Action

Add a quick deposit recording action button to each assignment row:

```tsx
// In the actions column
<DropdownMenuItem onClick={() => handleQuickDeposit(assignment)}>
  <DollarSign className="mr-2 h-4 w-4" />
  <span>Record Deposit</span>
</DropdownMenuItem>

// Handler function
const handleQuickDeposit = (assignment: Assignment) => {
  setSelectedAssignmentForDeposit(assignment);
  setIsDepositDialogOpen(true);
};
```

### Step 3: Add Financial Dashboard

Create a financial overview section at the top of the assignments page:

```tsx
// Financial Overview Cards
<div className="grid grid-cols-4 gap-6 mb-6">
  <Card>
    <CardContent className="p-6">
      <div className="flex items-center">
        <CreditCard className="h-8 w-8 text-[#009639]" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Total Deposits</p>
          <p className="text-2xl font-bold">
            R{assignments.reduce((sum, a) => sum + (a.totalDeposits || 0), 0).toLocaleString()}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>

  <Card>
    <CardContent className="p-6">
      <div className="flex items-center">
        <TrendingUp className="h-8 w-8 text-blue-600" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Deposit Balance</p>
          <p className="text-2xl font-bold">
            R{assignments.reduce((sum, a) => sum + (a.depositBalance || 0), 0).toLocaleString()}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>

  <Card>
    <CardContent className="p-6">
      <div className="flex items-center">
        <AlertTriangle className="h-8 w-8 text-red-600" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Outstanding Balance</p>
          <p className="text-2xl font-bold">
            R{assignments.reduce((sum, a) => sum + (a.outstandingBalance || 0), 0).toLocaleString()}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>

  <Card>
    <CardContent className="p-6">
      <div className="flex items-center">
        <Users className="h-8 w-8 text-purple-600" />
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">Active Assignments</p>
          <p className="text-2xl font-bold">
            {assignments.filter(a => a.status === 'active').length}
          </p>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
```

## Features Added

### ✅ Deposit Management Integration
- **Quick Deposit Recording**: Record deposits directly from assignments page
- **Deposit Balance Display**: Show current deposit balances in assignment list
- **Deposit History**: View complete deposit payment history
- **Payment Method Tracking**: Track various payment methods (bank transfer, cash, etc.)

### ✅ Financial Overview Dashboard
- **Total Deposits**: Sum of all deposit amounts across assignments
- **Current Balances**: Outstanding deposit balances
- **Quick Statistics**: Assignment counts and financial summaries
- **Visual Indicators**: Color-coded status badges and amounts

### ✅ Enhanced User Experience
- **Inline Actions**: Record deposits without leaving the assignments page
- **Real-time Updates**: Automatic refresh after deposit recording
- **Comprehensive Forms**: Full deposit recording with all required fields
- **Error Handling**: Proper error messages and validation

## Integration Benefits

1. **Streamlined Workflow**: Admins can manage deposits directly from assignments
2. **Financial Visibility**: Clear overview of all financial aspects
3. **Reduced Navigation**: Less clicking between different pages
4. **Comprehensive Tracking**: Complete audit trail of all deposit activities
5. **Scalable Architecture**: Easy to extend with contracts, earnings, payouts

## Next Steps

After implementing the deposit integration:

1. **Add Contract Management**: Integrate contract upload/download functionality  
2. **Weekly Earnings Integration**: Add earnings tracking to assignments
3. **Payout Management**: Include payout processing and approval workflows
4. **Debt Tracking**: Show outstanding debts and shortfalls
5. **Comprehensive Reports**: Add detailed financial reporting

This creates a complete assignment management system with integrated financial tracking. 