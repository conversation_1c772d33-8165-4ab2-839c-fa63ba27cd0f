"Poolly" - a car-sharing platform that enables fractional vehicle ownership. 

#### SQS and SES

- `sendMessageToQueue` is generic ,it takes the queue url and the message body.
- `email-sender` is a lambda that consumes from `emailSend` queue.
to send email succeefully you need the message body to be in this form 

   ```sh
  {
      to: ["<EMAIL>"],
      templateName: <templateName>,
      templateData: {"body": "body"},
    }
   ```

Email bounces and render failures are send to the email or  queue setup in backend.
You can add more queues and consumers and still use `sendMessageToQueue