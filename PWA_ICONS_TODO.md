# PWA Icons TODO

## Required Icons for PWA Manifest

The following icons need to be created from the Poolly logo (`public/images/poolly-logo.svg`):

### Required Sizes:
- icon-72x72.png
- icon-96x96.png  
- icon-128x128.png
- icon-144x144.png
- icon-152x152.png
- icon-192x192.png
- icon-384x384.png
- icon-512x512.png
- apple-touch-icon.png (180x180)

### Generation Commands:
You can use imagemagick or an online tool to convert the SVG logo:

```bash
# Using imagemagick (if available)
convert public/images/poolly-logo.svg -resize 72x72 public/icon-72x72.png
convert public/images/poolly-logo.svg -resize 96x96 public/icon-96x96.png
convert public/images/poolly-logo.svg -resize 128x128 public/icon-128x128.png
convert public/images/poolly-logo.svg -resize 144x144 public/icon-144x144.png
convert public/images/poolly-logo.svg -resize 152x152 public/icon-152x152.png
convert public/images/poolly-logo.svg -resize 192x192 public/icon-192x192.png
convert public/images/poolly-logo.svg -resize 384x384 public/icon-384x384.png
convert public/images/poolly-logo.svg -resize 512x512 public/icon-512x512.png
convert public/images/poolly-logo.svg -resize 180x180 public/apple-touch-icon.png
```

### Alternative:
Use an online PWA icon generator like:
- https://realfavicongenerator.net/
- https://www.pwabuilder.com/imageGenerator

For now, the PWA will work but may show default icons until these are created. 