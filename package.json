{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "NODE_OPTIONS='--inspect' next dev -p 3008", "dev:lan": "next dev -H 0.0.0.0 -p 3008", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "drizzle-kit push", "db:generate": "drizzle-kit generate"}, "dependencies": {"@aws-amplify/adapter-nextjs": "^1.6.1", "@aws-amplify/ui-react": "^6.11.0", "@aws-amplify/ui-react-storage": "^3.10.3", "@aws-sdk/client-cognito-identity-provider": "^3.821.0", "@aws-sdk/client-secrets-manager": "^3.817.0", "@aws-sdk/client-ses": "^3.830.0", "@aws-sdk/client-sqs": "^3.828.0", "@hookform/resolvers": "^3.10.0", "@opentelemetry/instrumentation": "^0.202.0", "@opentelemetry/instrumentation-connect": "^0.46.0", "@opentelemetry/instrumentation-express": "^0.51.0", "@opentelemetry/instrumentation-fs": "^0.22.0", "@opentelemetry/instrumentation-http": "^0.202.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.7.0", "@sentry/nextjs": "^9.27.0", "autoprefixer": "^10.4.20", "aws-amplify": "^6.14.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.3.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.44.1", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-pwa": "^5.6.0", "next-themes": "^0.4.4", "pg": "^8.16.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "react-redux": "^9.2.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "swr": "^2.3.3", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.9", "web-push": "^3.6.7", "zod": "^3.25.41", "zustand": "^5.0.6"}, "devDependencies": {"@aws-amplify/backend": "^1.14.3", "@aws-amplify/backend-cli": "^1.5.0", "@types/aws-lambda": "^8.10.149", "@types/node": "^20.17.30", "@types/pg": "^8.15.4", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@types/web-push": "^3.6.4", "aws-cdk": "^2.1010.0", "aws-cdk-lib": "^2.191.0", "constructs": "^10.4.2", "cypress": "^14.5.0", "drizzle-kit": "^0.31.4", "esbuild": "^0.23.1", "eslint": "9.30.0", "eslint-config-next": "15.3.4", "postcss": "^8", "sharp": "^0.34.2", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "^5.8.3"}}